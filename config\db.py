import os
from pathlib import Path
from dotenv import load_dotenv
load_dotenv()
BASE_DIR = Path(__file__).resolve().parent.parent

def get_database():
    db_schema = os.getenv('DEFAULT_DATABASE_SCHEMA')
    # db_schema = 'testing'
    pricing_db_schema = os.getenv('PRICING_DATABASE_SCHEMA')
    db={'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': os.path.join(os.path.join(BASE_DIR,'dbs'),'newtestdb.sqlite3'),
            }
        }
    if os.getenv('DATABASE_ENV') == os.getenv('TESTING_ENV'):
        db_schema = os.getenv('TESTING_DATABASE_SCHEMA')
        db={'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': os.path.join(os.path.join(BASE_DIR,'dbs'), 'testing_db.sqlite3'),
                }
        }   

    if os.getenv('DATABASE_ENV') == os.getenv('DEV_ENV'):
        db_schema = os.getenv('DATABASE_SCHEMA')
        
        db  = {
            'default': {
                'ENGINE': 'mssql',
                'NAME': os.getenv('DATABASE_NAME', ''),
                'USER': os.getenv('DATABASE_USER', ''),
                'PASSWORD': os.getenv('DATABASE_PASSWORD', ''),
                'HOST': os.getenv('DATABASE_HOST', ''),
                'PORT': os.getenv('DATABASE_PORT', ''),
                'OPTIONS': {
                    'driver': 'ODBC Driver 17 for SQL Server',
                    "extra_params": "Authentication=ActiveDirectoryServicePrincipal;TrustServerCertificate=Yes",
                },
            },
        }
    
    return db,db_schema,pricing_db_schema
