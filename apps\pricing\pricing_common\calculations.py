import pandas as pd
from django.db.models import Avg, Count, F, Sum
from apps.common.utils import isNan
import numpy as np
from utils import _divide, handle_divison_by_zero
from apps.pricing.pricing_generic import services as psserv
from . import services as serv
from . import constants as CONST


def calc_profit_pool(pricing_scenario_df,level):

    pricing_scenario_df['profit_pool_customer_profit'] = pricing_scenario_df['profit_pool_customer_profit'].astype(float)
    pricing_scenario_df['profit_pool_mars_profit'] = pricing_scenario_df['profit_pool_mars_profit'].astype(float)
    pricing_scenario_df['customer_profit_new'] = pricing_scenario_df['customer_profit_new'].astype(float)
    pricing_scenario_df['gmac_abs_new'] = pricing_scenario_df['gmac_abs_new'].astype(float)
    pricing_scenario_df['gmac_percent_new'] = pricing_scenario_df['gmac_percent_new'].astype(float)
    profit_pool_df = pricing_scenario_df.groupby(level,as_index=False).agg(profit_pool_customer_profit=pd.NamedAgg(column="profit_pool_customer_profit", aggfunc="mean"),
                                                                                profit_pool_mars_profit=pd.NamedAgg(column="profit_pool_mars_profit", aggfunc="mean"),
                                                                                customer_profit=pd.NamedAgg(column="customer_profit_new", aggfunc="sum"),
                                                                                gmac_abs_new=pd.NamedAgg(column="gmac_abs_new", aggfunc="sum"),
                                                                                gmac_percent=pd.NamedAgg(column="gmac_percent_new", aggfunc="mean"),
                                                                                )
    profit_pool_df['profit_pool_customer_profit'] = (profit_pool_df['customer_profit']/(profit_pool_df['customer_profit']+profit_pool_df['gmac_abs_new']).fillna(0).replace([np.inf,-np.inf], 0)).replace([np.inf,-np.inf,np.nan], 0)
    profit_pool_df['profit_pool_mars_profit'] = 1-profit_pool_df['profit_pool_customer_profit'].astype(float)
    
    return profit_pool_df.round(4).to_dict(orient="records")


def calc_gmac_vs_trade_margin(pricing_scenario_df,level):
    pricing_scenario_df[CONST.PROFIT_POOL_COL]=pricing_scenario_df[CONST.PROFIT_POOL_COL].astype(float)
    gmac_trade_margin_df = pricing_scenario_df.groupby(level,as_index=False).agg(non_promo_price=pd.NamedAgg(column="non_promo_price_new", aggfunc="mean"),
                                                                    promo_price=pd.NamedAgg(column="promo_price_new", aggfunc="mean"),
                                                                    tax=pd.NamedAgg(column="tax", aggfunc="mean"),
                                                                    nsv=pd.NamedAgg(column="nsv_new", aggfunc="sum"),
                                                                    promo_share=pd.NamedAgg(column="promo_share", aggfunc="mean"),
                                                                    net_net=pd.NamedAgg(column="net_net_new", aggfunc="mean"),
                                                                    pack_weight_kg=pd.NamedAgg(column="pack_weight_kg_new", aggfunc="mean"),
                                                                    dead_net=pd.NamedAgg(column="dead_net_new", aggfunc="mean"),
                                                                    nsv_t=pd.NamedAgg(column="nsv_t_new", aggfunc="mean"),
                                                                    cogs_t=pd.NamedAgg(column="cogs_t_new", aggfunc="mean"),
                                                                    list_price=pd.NamedAgg(column="list_price_new", aggfunc="mean"),
                                                                    tpr=pd.NamedAgg(column="tpr", aggfunc="mean"),
                                                                    tpr_new=pd.NamedAgg(column="tpr_new", aggfunc="mean"),
                                                                    promo_price_elasticity=pd.NamedAgg(column="promo_price_elasticity", aggfunc="mean"),
                                                                    non_promo_price_elasticity=pd.NamedAgg(column="non_promo_price_elasticity", aggfunc="mean"),
                                                                    net_non_promo_price_elasticity=pd.NamedAgg(column="net_non_promo_price_elasticity", aggfunc="mean"),
                                                                    sell_out_unit=pd.NamedAgg(column="sell_out_unit", aggfunc=np.sum),
                                                                    sell_in_unit=pd.NamedAgg(column="sell_in_unit", aggfunc=np.sum),
                                                                    non_promo_price_per_unit=pd.NamedAgg(column="non_promo_price_per_unit", aggfunc=np.mean))
    gmac_trade_margin_df['percent_trade_margin_promo'] = (((gmac_trade_margin_df['promo_price']/(1+gmac_trade_margin_df['tax']))\
                                                                .replace([np.inf,-np.inf], 0)-(gmac_trade_margin_df['dead_net'])).replace([np.inf,-np.inf], 0)\
                                                                /(gmac_trade_margin_df['promo_price']/(1+gmac_trade_margin_df['tax'])).replace([np.inf,-np.inf], 0)).fillna(0).replace([np.inf,-np.inf], 0)
    
    gmac_trade_margin_df['percent_trade_margin_non_promo'] = (((gmac_trade_margin_df['non_promo_price']/(1+gmac_trade_margin_df['tax']))\
                                                                    .replace([np.inf,-np.inf], 0)-((gmac_trade_margin_df['net_net']))\
                                                                    .replace([np.inf,-np.inf], 0))/(gmac_trade_margin_df['non_promo_price']/(1+gmac_trade_margin_df['tax']))\
                                                                    .replace([np.inf,-np.inf], 0)).fillna(0).replace([np.inf,-np.inf], 0)
   
    gmac_trade_margin_df['sell_out_unit'] = (gmac_trade_margin_df['sell_out_unit']*(1-gmac_trade_margin_df['promo_share'])\
                                                *np.power((gmac_trade_margin_df['non_promo_price']/gmac_trade_margin_df['non_promo_price_per_unit'])\
                                                .replace(-np.inf, 0),gmac_trade_margin_df['net_non_promo_price_elasticity'])\
                                                + gmac_trade_margin_df['sell_out_unit']*(gmac_trade_margin_df['promo_share'])\
                                                *np.power((gmac_trade_margin_df['non_promo_price']/gmac_trade_margin_df['non_promo_price_per_unit'])\
                                                .replace(-np.inf, 0),gmac_trade_margin_df['net_non_promo_price_elasticity'])\
                                                *np.exp(gmac_trade_margin_df['promo_price_elasticity']*(gmac_trade_margin_df['tpr_new']-gmac_trade_margin_df['tpr']))).replace([np.inf,-np.inf], 0).fillna(0)
    
    # gmac_trade_margin_df['non_promo_price_factor'] = ((gmac_trade_margin_df['non_promo_price']/gmac_trade_margin_df['non_promo_price_per_unit']).replace([-np.inf,np.inf,np.nan], 0)**gmac_trade_margin_df['net_non_promo_price_elasticity']).replace([-np.inf,np.inf,np.nan], 0)
    # gmac_trade_margin_df['sell_out_unit'] = gmac_trade_margin_df['sell_out_unit']* gmac_trade_margin_df['non_promo_price_factor']
    gmac_trade_margin_df['avg_trade_margin'] = (gmac_trade_margin_df['percent_trade_margin_non_promo']*(1-gmac_trade_margin_df['promo_share'])+gmac_trade_margin_df['percent_trade_margin_promo']\
                                                       *gmac_trade_margin_df['promo_share']).fillna(0).replace([np.inf,-np.inf], 0)
    gmac_trade_margin_df['rsv'] = (gmac_trade_margin_df['non_promo_price']*(1-gmac_trade_margin_df['promo_share'])*gmac_trade_margin_df['sell_out_unit']\
                                        +gmac_trade_margin_df['promo_price']*(gmac_trade_margin_df['promo_share'])*gmac_trade_margin_df['sell_out_unit']).replace([np.inf,-np.inf], 0).fillna(0)
        
    gmac_trade_margin_df['customer_profit'] = gmac_trade_margin_df['avg_trade_margin']*gmac_trade_margin_df['rsv']
    
    
    gmac_trade_margin_df['list_price'] = gmac_trade_margin_df['list_price']
    gmac_trade_margin_df['sell_in_unit'] = (gmac_trade_margin_df['sell_in_unit']*(1-gmac_trade_margin_df['promo_share'])\
                                                    *np.power((gmac_trade_margin_df['non_promo_price']/gmac_trade_margin_df['non_promo_price_per_unit'])\
                                                    .replace(-np.inf, 0),gmac_trade_margin_df['net_non_promo_price_elasticity'])\
                                                    + gmac_trade_margin_df['sell_in_unit']*(gmac_trade_margin_df['promo_share'])\
                                                    *np.power((gmac_trade_margin_df['non_promo_price']/gmac_trade_margin_df['non_promo_price_per_unit'])\
                                                    .replace(-np.inf, 0),gmac_trade_margin_df['net_non_promo_price_elasticity'])\
                                                    *np.exp(gmac_trade_margin_df['promo_price_elasticity']*(gmac_trade_margin_df['tpr_new']-gmac_trade_margin_df['tpr']))).replace([np.inf,-np.inf], 0).fillna(0)
    # gmac_trade_margin_df['sell_in_unit'] = gmac_trade_margin_df['sell_in_unit']* gmac_trade_margin_df['non_promo_price_factor']
    gmac_trade_margin_df['sell_in_volume_t'] = gmac_trade_margin_df['sell_in_unit']*gmac_trade_margin_df['pack_weight_kg']
    # gmac_trade_margin_df['nsv'] = (gmac_trade_margin_df['net_net']*(1-gmac_trade_margin_df['promo_share'])*gmac_trade_margin_df['sell_in_unit']\
    #                                     +gmac_trade_margin_df['dead_net']*(gmac_trade_margin_df['promo_share'])*gmac_trade_margin_df['sell_in_unit']).replace([np.inf,-np.inf], 0).fillna(0)
    # gmac_trade_margin_df['nsv_t'] = (gmac_trade_margin_df['nsv']/gmac_trade_margin_df['sell_in_volume_t']).replace([np.inf,-np.inf], 0).fillna(0)
    gmac_trade_margin_df['gmac_percent'] = (((gmac_trade_margin_df['nsv_t']-gmac_trade_margin_df['cogs_t']).replace([np.inf,-np.inf], 0)\
                                                /gmac_trade_margin_df['nsv_t']).replace([np.inf,-np.inf,np.nan], 0)).fillna(0).replace([np.inf,-np.inf], 0)
    gmac_trade_margin_df['gmac_abs'] = gmac_trade_margin_df['gmac_percent']*gmac_trade_margin_df['nsv']

    return gmac_trade_margin_df.round(4)

def calc_price_vol_mix_in(pricing_scenario_df,level='customer'):
    implied_price_impact = pricing_scenario_df['implied_price_impact'].astype(float).sum()
    implied_vol_impact = pricing_scenario_df['implied_vol_impact'].astype(float).sum()
    implied_mix_impact = pricing_scenario_df['implied_mix_impact'].astype(float).sum()
    total_growth = pricing_scenario_df['total_growth'].astype(float).sum()
  
    return {
        'implied_price_impact':round(implied_price_impact,2),
        'implied_vol_impact':round(implied_vol_impact,2),
        'implied_mix_impact':round(implied_mix_impact,2),
        'total_growth':round(total_growth,2)
    }

def calc_global_constraints(pricing_scenario_df,level='customer',scenario_type='simualtor',is_scenario_planner=False,from_simulate=False,key='base'):
    pricing_scenario_df['changed_lp_percent'] = pricing_scenario_df['changed_lp_percent'].astype(float)
    pricing_scenario_df['changed_net_net_change_percent'] = pricing_scenario_df['changed_net_net_change_percent'].astype(float)
    if not from_simulate:
        pricing_scenario_df['changed_lp_percent'] = pricing_scenario_df['changed_lp_percent']/100
        pricing_scenario_df['changed_net_net_change_percent'] = pricing_scenario_df['changed_net_net_change_percent']/100

    pricing_scenario_df['base_nsv'] = pricing_scenario_df['base_nsv'].astype(float)
    pricing_scenario_df['net_net_nsv_product'] = ((pricing_scenario_df['changed_net_net_change_percent']*pricing_scenario_df['base_nsv']))
 
    if is_scenario_planner:
        pricing_scenario_df['nn_change_percent'] = pricing_scenario_df['nn_change_percent'].astype(float)
        pricing_scenario_df['list_price_nsv_product']=(pricing_scenario_df['changed_lp_percent']*pricing_scenario_df['base_nsv'])
        pricing_scenario_df['target'] = pricing_scenario_df['nn_change_percent']
        pricing_agg_summary_df = pricing_scenario_df.groupby(level,as_index=False).agg(optimized_nn_target=pd.NamedAgg(column="net_net_nsv_product", aggfunc="sum"),
                                                                                        lp_nsv_product=pd.NamedAgg(column="lp_nsv_product", aggfunc="sum"),
                                                                                        list_price_nsv_product = pd.NamedAgg(column="list_price_nsv_product", aggfunc="sum"),
                                                                                        target=pd.NamedAgg(column="target", aggfunc="mean"),
                                                                                        nsv=pd.NamedAgg(column="base_nsv", aggfunc="sum"),
                                                                                        )
        
        optimized_nn_target = pricing_agg_summary_df['optimized_nn_target'].sum()/pricing_agg_summary_df['nsv'].sum()
        weighted_lp_nsv_product = pricing_agg_summary_df['list_price_nsv_product'].sum()/pricing_agg_summary_df['nsv'].sum()

    else:
        pricing_agg_summary_df = pricing_scenario_df.groupby(level,as_index=False).agg(optimized_nn_target=pd.NamedAgg(column="net_net_nsv_product", aggfunc="sum"),nsv=pd.NamedAgg(column="nsv_new", aggfunc="sum"))
        
        return {
            'optimized_nn_target':100*((pricing_agg_summary_df['optimized_nn_target'].sum()/(pricing_scenario_df['base_nsv'].sum()).round(4)))+100,
            'nsv':pricing_agg_summary_df['nsv'].sum().round(2),
        }

    return {
        'calculated_nn_percent':100*optimized_nn_target+100,
        'optimized_nn_target':100*optimized_nn_target+100,
        'target':pricing_agg_summary_df['target'].mean().round(2),
        'actual_invest':pricing_agg_summary_df['nsv'].sum()*(optimized_nn_target-weighted_lp_nsv_product),
        'invest_budget':pricing_agg_summary_df['nsv'].sum()*(((pricing_agg_summary_df['target'].mean()-100)/100)-weighted_lp_nsv_product),
        'nsv':pricing_agg_summary_df['nsv'].sum().round(2),
        'weighted_lp_nsv_product':pricing_agg_summary_df['list_price_nsv_product'].sum()/pricing_agg_summary_df['nsv'].sum()

    }

def assign_ogsm_tye(df1,df2,level='customer'):
    if df2.shape[0]:
        df1 = pd.merge(df1,df2,how='left',on=level)
        df1.drop(['ogsm_type_x'],axis=1,inplace=True)
        df1.rename(columns={'ogsm_type_y':'ogsm_type'},inplace=True)
        
    return df1
                                                                
def calc_summary_groupby_level(pricing_scenario_df,level='customer',scenario_type='simualtor',is_scenario_planner=True):
    
    pricing_scenario_df[CONST.SUMMARY_COL]=pricing_scenario_df[CONST.SUMMARY_COL].astype(float)
    net_pricing_agg_summary_df = pricing_scenario_df.groupby(level,as_index=False).agg(percent_pricing_impact_total=pd.NamedAgg(column="percent_pricing_impact_total", aggfunc="mean"),
                                                                    nsv_pricing_impact_direct=pd.NamedAgg(column="nsv_pricing_impact_direct", aggfunc="sum"),
                                                                    nsv_pricing_impact_indirect=pd.NamedAgg(column="nsv_pricing_impact_indirect", aggfunc="sum"),
                                                                    nsv_pricing_impact_base=pd.NamedAgg(column="nsv_pricing_impact_base", aggfunc="sum"),
                                                                    nsv_pricing_impact_total=pd.NamedAgg(column="nsv_pricing_impact_total", aggfunc="sum"),
                                                                    total_price_impact_inc_tt_drift=pd.NamedAgg(column="total_price_impact_inc_tt_drift", aggfunc="sum"),
                                                                    total_price_impact_inc_tt_drift_percent=pd.NamedAgg(column="total_price_impact_inc_tt_drift_percent", aggfunc="mean"),
                                                                    tt_drift=pd.NamedAgg(column="tt_drift", aggfunc="sum"),
                                                                    nsv=pd.NamedAgg(column="nsv_new", aggfunc="sum"),
                                                                    customer_profit=pd.NamedAgg(column="customer_profit_new", aggfunc="sum"),
                                                                    ogsm_type=pd.NamedAgg(column="ogsm_type", aggfunc="min")
                                                                    )

    financials_agg_summary_df = pricing_scenario_df.groupby(level,as_index=False).agg(sell_in_volume_t=pd.NamedAgg(column="sell_in_volume_t_new", aggfunc="sum"),
                                                                lsv_new=pd.NamedAgg(column="lsv_new", aggfunc="sum"),
                                                                nsv=pd.NamedAgg(column="nsv_new", aggfunc="sum"),
                                                                nsv_t=pd.NamedAgg(column="nsv_t_new", aggfunc="mean"),
                                                                cogs_t=pd.NamedAgg(column="cogs_t_new", aggfunc="mean"),
                                                                tt_percent=pd.NamedAgg(column="tt_percent_new", aggfunc="mean"),
                                                                gmac_abs=pd.NamedAgg(column="gmac_abs_new", aggfunc="sum"),
                                                                gmac_percent=pd.NamedAgg(column="gmac_percent_new", aggfunc="mean"),
                                                                net_net=pd.NamedAgg(column="net_net_new", aggfunc="mean"),
                                                                dead_net=pd.NamedAgg(column="dead_net_new", aggfunc="mean"),
                                                                list_price=pd.NamedAgg(column="list_price_new", aggfunc="mean"),
                                                                changed_dead_net_change_percent=pd.NamedAgg(column="changed_dead_net_change_percent", aggfunc="mean"),
                                                                changed_net_net_change_percent=pd.NamedAgg(column="changed_net_net_change_percent", aggfunc="mean"),
                                                                changed_lp_percent=pd.NamedAgg(column="changed_lp_percent", aggfunc="mean"),
                                                                ogsm_type=pd.NamedAgg(column="ogsm_type", aggfunc="min")
                                                                )
    
    rsv_agg_summary_df = pricing_scenario_df.groupby(level,as_index=False).agg(rsv=pd.NamedAgg(column="rsv_new", aggfunc="sum"),
                                                                sell_out_volume_sales_t=pd.NamedAgg(column="sell_out_volume_sales_t_new", aggfunc="sum"),
                                                                unit_sales_000=pd.NamedAgg(column="sell_out_unit_new", aggfunc="sum"),
                                                                promo_share=pd.NamedAgg(column="promo_share", aggfunc="mean"),
                                                                nsv=pd.NamedAgg(column="nsv_new", aggfunc="sum"),
                                                                avg_price_per_unit_new=pd.NamedAgg(column="avg_price_per_unit_new", aggfunc="mean"),
                                                                gmac_abs=pd.NamedAgg(column="gmac_abs_new", aggfunc="sum"),
                                                                gmac_percent=pd.NamedAgg(column="gmac_percent_new", aggfunc="mean"),
                                                                ogsm_type=pd.NamedAgg(column="ogsm_type", aggfunc="min")
                                                                )
    
    customer_perspective_agg_summary_df = pricing_scenario_df.groupby(level,as_index=False).agg(percent_trade_margin_non_promo=pd.NamedAgg(column="percent_trade_margin_non_promo_new", aggfunc="sum"),
                                                                percent_trade_margin_promo=pd.NamedAgg(column="percent_trade_margin_promo_new", aggfunc="sum"),
                                                                avg_trade_margin=pd.NamedAgg(column="avg_trade_margin_new", aggfunc="sum"),
                                                                customer_profit=pd.NamedAgg(column="customer_profit_new", aggfunc="sum"),
                                                                gmac_abs=pd.NamedAgg(column="gmac_abs_new", aggfunc="sum"),
                                                                nsv=pd.NamedAgg(column="nsv_new", aggfunc="sum"),
                                                                gmac_percent=pd.NamedAgg(column="gmac_percent_new", aggfunc="sum"),
                                                                ogsm_type=pd.NamedAgg(column="ogsm_type", aggfunc="min")
                                                                )
    
    
    recommonded_price_agg_summary_df = pricing_scenario_df.groupby(level,as_index=False).agg(dead_net=pd.NamedAgg(column="dead_net_new", aggfunc="mean"),
                                                                net_net=pd.NamedAgg(column="net_net_new", aggfunc="mean"),
                                                                list_price=pd.NamedAgg(column="list_price_new", aggfunc="mean"),
                                                                non_promo_price=pd.NamedAgg(column="non_promo_price_new", aggfunc="mean"),
                                                                promo_price=pd.NamedAgg(column="promo_price_new", aggfunc="mean"),
                                                                nsv=pd.NamedAgg(column="nsv_new", aggfunc="sum"),
                                                                ogsm_type=pd.NamedAgg(column="ogsm_type", aggfunc="min")
                                                                )
    sqpq_df = pd.DataFrame()

    if level == 'product_group':
        ppgs = recommonded_price_agg_summary_df['product_group'].unique().tolist()
        spq = psserv.get_ogsm_type_ppg_lvl(ppgs=ppgs)
        sqpq_df = pd.DataFrame(spq)
        
    if level == 'customer':
        customers = recommonded_price_agg_summary_df['customer'].unique().tolist()
        spq = psserv.get_ogsm_type_customer_lvl(customers=customers)
        sqpq_df = pd.DataFrame(spq)
    net_pricing_agg_summary_df = assign_ogsm_tye(net_pricing_agg_summary_df,sqpq_df,level=level)
    financials_agg_summary_df = assign_ogsm_tye(financials_agg_summary_df,sqpq_df,level=level)
    customer_perspective_agg_summary_df = assign_ogsm_tye(customer_perspective_agg_summary_df,sqpq_df,level=level)
    rsv_agg_summary_df = assign_ogsm_tye(rsv_agg_summary_df,sqpq_df,level=level)
    recommonded_price_agg_summary_df = assign_ogsm_tye(recommonded_price_agg_summary_df,sqpq_df,level=level)
    
    net_pricing_agg_summary_df =  net_pricing_agg_summary_df.sort_values('nsv', ascending=False)
    financials_agg_summary_df =  financials_agg_summary_df.sort_values('nsv', ascending=False)
    customer_perspective_agg_summary_df =  customer_perspective_agg_summary_df.sort_values('nsv', ascending=False)
    rsv_agg_summary_df =  rsv_agg_summary_df.sort_values('nsv', ascending=False)
    recommonded_price_agg_summary_df =  recommonded_price_agg_summary_df.sort_values('nsv', ascending=False)
    
    summary_final_dict = {
        'net_pricing':net_pricing_agg_summary_df.to_dict(orient="records")\
            ,'financials':financials_agg_summary_df.to_dict(orient="records")\
            ,'rsv':rsv_agg_summary_df.to_dict(orient="records")\
            ,'customer_perspective':customer_perspective_agg_summary_df.to_dict(orient="records")
    }
  
    summary_final_dict.update({'recommonded_price':recommonded_price_agg_summary_df.to_dict(orient="records")})
        
    return summary_final_dict
    
def calc_overall_summary(pricing_scenario_df,is_scenario_planner=True):

    pricing_scenario_df['sell_out_volume_sales_t_new'] = pricing_scenario_df['sell_out_volume_sales_t_new'].astype(float)
    pricing_scenario_df['list_price'] = pricing_scenario_df['list_price'].astype(float)
    pricing_scenario_df['customer_profit_new'] = pricing_scenario_df['customer_profit_new'].astype(float)
    pricing_scenario_df['tt_drift'] = pricing_scenario_df['tt_drift'].astype(float)
    pricing_scenario_df['cogs_t_new'] = pricing_scenario_df['cogs_t_new'].astype(float)
    pricing_scenario_df['tax'] = pricing_scenario_df['tax'].astype(float)
    pricing_scenario_df['rsv_new'] = pricing_scenario_df['rsv_new'].astype(float)
    pricing_scenario_df['nsv'] = pricing_scenario_df['nsv'].astype(float)
    pricing_scenario_df['lsv_new']=pricing_scenario_df['lsv_new'].astype(float)
    pricing_scenario_df['sell_in_unit_new'] = pricing_scenario_df['sell_in_unit_new'].astype(float)
    total_units = pricing_scenario_df['sell_in_unit_new'].sum()
    pricing_scenario_df['mac'] = pricing_scenario_df['gmac_abs_new'].astype(float)
    pricing_scenario_df['mars_profit'] = pricing_scenario_df['mac']
    pricing_scenario_df['tt_percent_new']=pricing_scenario_df['tt_percent_new'].astype(float)
    mac = pricing_scenario_df['mac'].sum()
    pricing_scenario_df['rsv_wo_vat'] = (pricing_scenario_df['rsv_new']/(1+pricing_scenario_df['tax'])).fillna(0).replace([np.inf,-np.inf], 0)
    trade_margin = pricing_scenario_df['customer_profit_new'].sum()
    rsv_wo_vat = pricing_scenario_df['rsv_wo_vat'].sum()
    lsv = pricing_scenario_df['lsv_new'].astype(float).sum()
    nsv = pricing_scenario_df['nsv_new'].astype(float).sum()
    pricing_scenario_df = pricing_scenario_df.fillna(0)
    return {
            'total_units':isNan(round(total_units,2)),
            'lsv':isNan(round(lsv,2)),
            'nsv':isNan(round(nsv,2)),
            'mac':isNan(round(mac,2)),
            'trade_margin':isNan(round(trade_margin,2)),
            'rsv_wo_vat':isNan(round(rsv_wo_vat,2))
        }

def handle_large_decimals(pricing_scenario_df):
    COLMN = CONST.PRICING_VALUES+CONST.NUMERIC_COL
    for col in COLMN:
        
        pricing_scenario_df.loc[(pricing_scenario_df[col] > 1e30),col] = 1e30
      
    
    return pricing_scenario_df

def user_defined_input_setup(pricing_scenario_df,is_scenario_planner=False,scenario_type='simulator',initial=False,is_base='base'):

    pricing_scenario_df[CONST.PRICING_VALUES]=pricing_scenario_df[CONST.PRICING_VALUES].astype('float64')
    if pricing_scenario_df.loc[pricing_scenario_df['is_divided_by_100']==0].shape[0]:
        pricing_scenario_df['changed_cogs_t_percent'] = pricing_scenario_df['changed_cogs_t_percent']/100
    
    pricing_scenario_df['non_promo_price_per_kg_new'] = (pricing_scenario_df['non_promo_price_new']/pricing_scenario_df['pack_weight_kg_new']).fillna(0)
    pricing_scenario_df['promo_price_per_kg_new'] = (pricing_scenario_df['promo_price_new']/(pricing_scenario_df['sell_out_volume_sales_t']/pricing_scenario_df['unit_sales_000']).replace([np.inf,-np.inf], 0)).fillna(0)
    pricing_scenario_df['changed_lp_percent'] = (pricing_scenario_df['list_price_new'] - pricing_scenario_df['list_price'])/pricing_scenario_df['list_price']
    pricing_scenario_df['changed_pack_weight_kg_percent'] = pricing_scenario_df['changed_pack_weight_kg_percent']
    pricing_scenario_df['cogs_t_new'] = pricing_scenario_df['cogs_t']*(1+pricing_scenario_df['changed_cogs_t_percent'])
    
    pricing_scenario_df['changed_non_promo_price'] = ((pricing_scenario_df['non_promo_price_new'] - pricing_scenario_df['non_promo_price_per_unit'])/pricing_scenario_df['non_promo_price_per_unit'])
    pricing_scenario_df['changed_promo_price'] = (pricing_scenario_df['promo_price_new'] - pricing_scenario_df['promo_price_per_unit'])/pricing_scenario_df['promo_price_per_unit']
    
    if is_scenario_planner:
        if pricing_scenario_df.loc[pricing_scenario_df['is_divided_by_100']==0].shape[0]:
            pricing_scenario_df['changed_net_net_change_percent'] = pricing_scenario_df['changed_net_net_change_percent']/100
            pricing_scenario_df['changed_dead_net_change_percent'] = pricing_scenario_df['changed_dead_net_change_percent']/100
    else:
        pricing_scenario_df['changed_net_net_change_percent'] = pricing_scenario_df['net_net_multiplication_factor']*pricing_scenario_df['changed_non_promo_price']
        pricing_scenario_df['changed_dead_net_change_percent'] = pricing_scenario_df['changed_net_net_change_percent']

    pricing_scenario_df['dead_net_new'] = pricing_scenario_df['dead_net']*(1+pricing_scenario_df['changed_dead_net_change_percent'])
    pricing_scenario_df['net_net_new'] = pricing_scenario_df['net_net']*(1+pricing_scenario_df['changed_net_net_change_percent'])
    pricing_scenario_df['is_divided_by_100']=1
    return pricing_scenario_df

def calculate_pricing_scenario(scenario_pricing_scenario_df:pd.DataFrame=None\
                                    ,saved_scenario_id:int=0\
                                    ,is_scenario_planner=True\
                                    ,scenario_type='simulator'
                                    ,solver_status=True
                                    ):

    """
    Calculating pricing scenario based on change in list price/pack weight/shelf price/net_net/dead_net.
    """
   
    scenario_pricing_scenario_df=scenario_pricing_scenario_df.fillna(0)
    pd.set_option("display.precision", 8)
    main_df = pd.DataFrame()
    category_list = ['base','simulated']
    summary_dict  = {}
    overall_summary_dict = {}   

    for key in category_list:
        pricing_scenario_df = scenario_pricing_scenario_df.copy()
        pricing_scenario_df['is_base'] = 0
      
        if key == 'base':
            pricing_scenario_df['is_base'] = 1
            pricing_scenario_df['list_price_new'] = pricing_scenario_df['list_price']
            pricing_scenario_df['pack_weight_kg_new'] = pricing_scenario_df['pack_weight_kg']
            pricing_scenario_df['dead_net_new'] = pricing_scenario_df['dead_net']
            pricing_scenario_df['net_net_new'] = pricing_scenario_df['net_net']
            pricing_scenario_df['cogs_t_new'] = pricing_scenario_df['cogs_t']
            pricing_scenario_df['non_promo_price_new'] = pricing_scenario_df['non_promo_price_per_unit']
            pricing_scenario_df['promo_price_new'] = pricing_scenario_df['promo_price_per_unit']
            pricing_scenario_df['changed_lp_percent'] = 0
            pricing_scenario_df['changed_dead_net_change_percent'] = 0
            pricing_scenario_df['changed_non_promo_price'] = 0
            pricing_scenario_df['changed_pack_weight_kg_percent'] = 0
            pricing_scenario_df['changed_cogs_t_percent'] = 0
            pricing_scenario_df['changed_net_net_change_percent'] = 0

        pricing_scenario_df = user_defined_input_setup(pricing_scenario_df,is_scenario_planner,scenario_type=scenario_type,is_base=key)
       
        pricing_scenario_df['tpr'] = 100*(1-pricing_scenario_df['promo_price_per_unit']/pricing_scenario_df['non_promo_price_per_unit'])
        pricing_scenario_df['tpr_new'] = 100*(1-pricing_scenario_df['promo_price_new']/pricing_scenario_df['non_promo_price_new'])
        
        pricing_scenario_df['sell_in_unit'] = ((pricing_scenario_df['sell_in_volume_t']/pricing_scenario_df['pack_weight_kg']).replace([np.inf,-np.inf], 0)*1000).fillna(0)
        pricing_scenario_df['sell_out_unit'] = pricing_scenario_df['unit_sales_000']*1000

        # pricing_scenario_df['sell_in_unit'] = ((pricing_scenario_df['sell_in_volume_t']/pricing_scenario_df['pack_weight_kg']).replace([np.inf,-np.inf], 0)*1000).fillna(0)
        pricing_scenario_df['sell_out_unit'] = pricing_scenario_df['unit_sales_000']*1000
        if key == 'base':
            pricing_scenario_df['sell_in_unit_new'] =  pricing_scenario_df['sell_in_unit']
            pricing_scenario_df['sell_out_unit_new'] =  pricing_scenario_df['sell_out_unit']
        else:
            pricing_scenario_df['sell_in_unit_new'] = (pricing_scenario_df['sell_in_unit']*(1-pricing_scenario_df['promo_share'])\
                                                        *np.power((pricing_scenario_df['non_promo_price_new']/pricing_scenario_df['non_promo_price_per_unit'])\
                                                        .replace(-np.inf, 0),pricing_scenario_df['net_non_promo_price_elasticity'])\
                                                        + pricing_scenario_df['sell_in_unit']*(pricing_scenario_df['promo_share'])\
                                                        *np.power((pricing_scenario_df['non_promo_price_new']/pricing_scenario_df['non_promo_price_per_unit'])\
                                                        .replace(-np.inf, 0),pricing_scenario_df['net_non_promo_price_elasticity'])\
                                                        *np.exp(pricing_scenario_df['promo_price_elasticity']*(pricing_scenario_df['tpr_new']-pricing_scenario_df['tpr']))).replace([np.inf,-np.inf], 0).fillna(0)
            
            pricing_scenario_df['sell_out_unit_new'] = (pricing_scenario_df['sell_out_unit']*(1-pricing_scenario_df['promo_share'])\
                                                        *np.power((pricing_scenario_df['non_promo_price_new']/pricing_scenario_df['non_promo_price_per_unit'])\
                                                        .replace(-np.inf, 0),pricing_scenario_df['net_non_promo_price_elasticity'])\
                                                        + pricing_scenario_df['sell_out_unit']*(pricing_scenario_df['promo_share'])\
                                                        *np.power((pricing_scenario_df['non_promo_price_new']/pricing_scenario_df['non_promo_price_per_unit'])\
                                                        .replace(-np.inf, 0),pricing_scenario_df['net_non_promo_price_elasticity'])\
                                                    *np.exp(pricing_scenario_df['promo_price_elasticity']*(pricing_scenario_df['tpr_new']-pricing_scenario_df['tpr']))).replace([np.inf,-np.inf], 0).fillna(0)
        
        # pricing_scenario_df['non_promo_price_factor'] = (pricing_scenario_df['non_promo_price_new']/pricing_scenario_df['non_promo_price_per_unit']).replace(-np.inf, 0)**pricing_scenario_df['net_non_promo_price_elasticity']
        # pricing_scenario_df['sell_in_unit_new'] = pricing_scenario_df['sell_in_unit']* pricing_scenario_df['non_promo_price_factor'] 
        # pricing_scenario_df['sell_out_unit_new'] = pricing_scenario_df['sell_out_unit']* pricing_scenario_df['non_promo_price_factor']
        
        pricing_scenario_df['sell_in_volume_t_new'] = (pricing_scenario_df['sell_in_unit_new']*pricing_scenario_df['pack_weight_kg_new'])/1000
      
        pricing_scenario_df['nsv_new'] = (pricing_scenario_df['net_net_new']*(1-pricing_scenario_df['promo_share'])*pricing_scenario_df['sell_in_unit_new']\
                                        +pricing_scenario_df['dead_net_new']*(pricing_scenario_df['promo_share'])*pricing_scenario_df['sell_in_unit_new']).replace([np.inf,-np.inf], 0).fillna(0)
        pricing_scenario_df['nsv_t_new'] = (pricing_scenario_df['nsv_new']/pricing_scenario_df['sell_in_volume_t_new']).replace([np.inf,-np.inf], 0).fillna(0)
        pricing_scenario_df['base_nsv'] = pricing_scenario_df['nsv_new']
        pricing_scenario_df['nsv_old'] = pricing_scenario_df['nsv']
        if key == 'simulated':
            pricing_scenario_df['base_nsv'] = main_df.loc[main_df['is_base']==True,'nsv_new']
        pricing_scenario_df['percent_pricing_impact_total'] = ((pricing_scenario_df['list_price_new']/pricing_scenario_df['pack_weight_kg_new']).replace([np.inf,-np.inf], 0).fillna(0)\
                                    /(pricing_scenario_df['list_price']/pricing_scenario_df['pack_weight_kg']).replace([np.inf,-np.inf], 0).fillna(0)-1).fillna(0)
     
        pricing_scenario_df['nsv_pricing_impact_direct'] = np.where(pricing_scenario_df['type_of_price_inc']=='direct',pricing_scenario_df['changed_lp_percent']\
                                                            *pricing_scenario_df['base_nsv'],0)
        pricing_scenario_df = handle_divison_by_zero('nsv_pricing_impact_direct','base_nsv','percent_price_impact_direct',pricing_scenario_df)
        pricing_scenario_df['nsv_pricing_impact_indirect'] = np.where(pricing_scenario_df['type_of_price_inc']=='indirect',pricing_scenario_df['percent_pricing_impact_total']\
                                                            *pricing_scenario_df['base_nsv'],0)
        pricing_scenario_df['nsv_pricing_impact_base'] = np.where(pricing_scenario_df['type_of_price_inc']=='base',pricing_scenario_df['changed_lp_percent']\
                                                            *pricing_scenario_df['base_nsv'],0)
        
        pricing_scenario_df['nsv_pricing_impact_total'] = pricing_scenario_df['nsv_pricing_impact_direct']+pricing_scenario_df['nsv_pricing_impact_indirect']\
                                                        +pricing_scenario_df['nsv_pricing_impact_base']
        
            
        pricing_scenario_df['tt_drift'] = (((pricing_scenario_df['changed_lp_percent']-pricing_scenario_df['changed_net_net_change_percent'])*(1-pricing_scenario_df['promo_share']))\
                                            +((pricing_scenario_df['changed_lp_percent']-pricing_scenario_df['changed_dead_net_change_percent'])*(pricing_scenario_df['promo_share'])))*pricing_scenario_df['base_nsv']
        pricing_scenario_df['total_price_impact_inc_tt_drift'] = pricing_scenario_df['nsv_pricing_impact_total']+pricing_scenario_df['tt_drift']
        pricing_scenario_df['total_price_impact_inc_tt_drift_percent'] = (pricing_scenario_df['total_price_impact_inc_tt_drift']/pricing_scenario_df['base_nsv'])\
                                                                        .replace([np.inf,-np.inf], 0)
        pricing_scenario_df['lsv_new'] = pricing_scenario_df['sell_in_unit_new']*pricing_scenario_df['list_price_new']
        
        pricing_scenario_df['tt_percent_new'] = ((pricing_scenario_df['lsv_new']-pricing_scenario_df['nsv_new'])/pricing_scenario_df['lsv_new']).replace([np.inf,-np.inf], 0).fillna(0)
    
        pricing_scenario_df['gmac_percent_new'] = (((pricing_scenario_df['nsv_t_new']-pricing_scenario_df['cogs_t_new']).replace([np.inf,-np.inf], 0)\
                                                /pricing_scenario_df['nsv_t_new']).replace([np.inf,-np.inf], 0)).fillna(0).replace([np.inf,-np.inf], 0)
        pricing_scenario_df['gmac_abs_new'] = pricing_scenario_df['gmac_percent_new']*pricing_scenario_df['nsv_new']
        pricing_scenario_df['sell_out_volume_sales_t_new'] = pricing_scenario_df['sell_out_unit_new']*pricing_scenario_df['pack_weight_kg_new']
        
        pricing_scenario_df['avg_price_per_unit_new'] = (pricing_scenario_df['promo_share']*pricing_scenario_df['promo_price_new']\
                                                        +pricing_scenario_df['non_promo_price_new']*(1-pricing_scenario_df['promo_share'])).replace([np.inf,-np.inf], 0).fillna(0)
 
        pricing_scenario_df['rsv_new'] = (pricing_scenario_df['non_promo_price_new']*(1-pricing_scenario_df['promo_share'])*pricing_scenario_df['sell_in_unit_new']\
                                        +pricing_scenario_df['promo_price_new']*(pricing_scenario_df['promo_share'])*pricing_scenario_df['sell_in_unit_new']).replace([np.inf,-np.inf], 0).fillna(0)
        
        pricing_scenario_df['percent_trade_margin_promo'] = (((pricing_scenario_df['promo_price_per_unit']/(1+pricing_scenario_df['tax']))\
                                                                    .replace([np.inf,-np.inf], 0)-((pricing_scenario_df['dead_net']))\
                                                                    .replace([np.inf,-np.inf], 0))/(pricing_scenario_df['promo_price_per_unit']/(1+pricing_scenario_df['tax']))\
                                                                    .replace([np.inf,-np.inf], 0)).fillna(0).replace([np.inf,-np.inf], 0)

        pricing_scenario_df['percent_trade_margin_non_promo'] = (((pricing_scenario_df['non_promo_price_per_unit']/(1+pricing_scenario_df['tax']))\
                                                                    .replace([np.inf,-np.inf], 0)-((pricing_scenario_df['net_net']))\
                                                                    .replace([np.inf,-np.inf], 0))/(pricing_scenario_df['non_promo_price_per_unit']/(1+pricing_scenario_df['tax']))\
                                                                    .replace([np.inf,-np.inf], 0)).fillna(0).replace([np.inf,-np.inf], 0)
        
        pricing_scenario_df['percent_trade_margin_non_promo_new'] = (((pricing_scenario_df['non_promo_price_new']/(1+pricing_scenario_df['tax']))\
                                                                    .replace([np.inf,-np.inf], 0)-((pricing_scenario_df['net_net_new']))\
                                                                    .replace([np.inf,-np.inf], 0))/(pricing_scenario_df['non_promo_price_new']/(1+pricing_scenario_df['tax']))\
                                                                    .replace([np.inf,-np.inf], 0)).fillna(0).replace([np.inf,-np.inf], 0)
     
        pricing_scenario_df['percent_trade_margin_promo_new'] = (((pricing_scenario_df['promo_price_new']/(1+pricing_scenario_df['tax']))\
                                                                .replace([np.inf,-np.inf], 0)-(pricing_scenario_df['dead_net_new'])).replace([np.inf,-np.inf], 0)\
                                                                /(pricing_scenario_df['promo_price_new']/(1+pricing_scenario_df['tax'])).replace([np.inf,-np.inf], 0)).fillna(0).replace([np.inf,-np.inf], 0)
        pricing_scenario_df['avg_trade_margin'] = (pricing_scenario_df['percent_trade_margin_non_promo']*(1-pricing_scenario_df['promo_share'])+pricing_scenario_df['percent_trade_margin_promo']\
                                                       *pricing_scenario_df['promo_share']).fillna(0).replace([np.inf,-np.inf], 0)
        pricing_scenario_df['avg_trade_margin_new'] = (pricing_scenario_df['percent_trade_margin_non_promo_new']*(1-pricing_scenario_df['promo_share'])+pricing_scenario_df['percent_trade_margin_promo_new']\
                                                       *pricing_scenario_df['promo_share']).fillna(0).replace([np.inf,-np.inf], 0)
        pricing_scenario_df['customer_profit_new'] = pricing_scenario_df['avg_trade_margin_new']*pricing_scenario_df['rsv_new']
        pricing_scenario_df['customer_profit'] = pricing_scenario_df['avg_trade_margin']*pricing_scenario_df['value_sales_rsv'] 

        pricing_scenario_df['customer_profit_percent_change'] = (pricing_scenario_df['customer_profit_new']/pricing_scenario_df['customer_profit']).fillna(0).replace([np.inf,-np.inf], 0)-1
        pricing_scenario_df['nsv_t_change'] = (pricing_scenario_df['nsv_t_new']-pricing_scenario_df['nsv_t']).fillna(0).replace([np.inf,-np.inf], 0)
        pricing_scenario_df['gmac_change'] = (pricing_scenario_df['gmac_abs_new']-pricing_scenario_df['gmac_abs']).fillna(0).replace([np.inf,-np.inf], 0)
        pricing_scenario_df['rsv_change'] = (pricing_scenario_df['rsv_new']-pricing_scenario_df['value_sales_rsv']).fillna(0).replace([np.inf,-np.inf], 0)
        pricing_scenario_df['nsv_t_percent_change'] = (pricing_scenario_df['nsv_t_new']/pricing_scenario_df['nsv_t']).replace([np.inf,-np.inf], 0)-1
        pricing_scenario_df['gmac_percent_change'] = (pricing_scenario_df['gmac_abs_new']/pricing_scenario_df['gmac_abs']).replace([np.inf,-np.inf], 0)-1
        pricing_scenario_df['rsv_percent_change'] = (pricing_scenario_df['rsv_new']/pricing_scenario_df['value_sales_rsv']).replace([np.inf,-np.inf], 0)-1
        pricing_scenario_df['profit_pool_customer_profit'] = pricing_scenario_df['customer_profit_new']/(pricing_scenario_df['customer_profit_new']+pricing_scenario_df['gmac_abs_new']).fillna(0).replace([np.inf,-np.inf], 0)
        pricing_scenario_df['profit_pool_mars_profit'] = 1-pricing_scenario_df['profit_pool_customer_profit']
        
        pricing_scenario_df['nsv_percent_change'] = (pricing_scenario_df['nsv_new']/pricing_scenario_df['nsv_old']).replace([np.inf,-np.inf], 0)-1
        pricing_scenario_df['nsv_change'] = (pricing_scenario_df['nsv_new']-pricing_scenario_df['nsv_old']).fillna(0).replace([np.inf,-np.inf], 0)
        pricing_scenario_df['net_price_per_unit_old'] = (pricing_scenario_df['nsv_old']/pricing_scenario_df['sell_in_unit']).replace([np.inf,-np.inf], 0)
        pricing_scenario_df['net_price_per_unit_new'] = (pricing_scenario_df['nsv_new']/pricing_scenario_df['sell_in_unit_new']).replace([np.inf,-np.inf], 0)
        pricing_scenario_df['overall_units_old'] = (pricing_scenario_df['sell_in_volume_t']*1000)/pricing_scenario_df['pack_weight_kg'].fillna(0).replace([np.inf,-np.inf], 0)
        pricing_scenario_df['overall_units_new'] = pricing_scenario_df['sell_in_volume_t_new']*(1-pricing_scenario_df['pack_weight_kg_new'])
        pricing_scenario_df['implied_price_impact'] = (pricing_scenario_df['net_price_per_unit_new']-pricing_scenario_df['net_price_per_unit_old'])*pricing_scenario_df['sell_in_unit']
        pricing_scenario_df['implied_vol_impact'] = pricing_scenario_df['net_price_per_unit_old']*(pricing_scenario_df['sell_in_unit_new']-pricing_scenario_df['sell_in_unit'])
        pricing_scenario_df['implied_mix_impact'] = (pricing_scenario_df['net_price_per_unit_new']-pricing_scenario_df['net_price_per_unit_old'])*(pricing_scenario_df['sell_in_unit_new']-pricing_scenario_df['sell_in_unit'])
        pricing_scenario_df['total_growth'] = pricing_scenario_df['nsv_new']-pricing_scenario_df['nsv_old']
        pricing_scenario_df['cogs'] = pricing_scenario_df['cogs_t_new']*pricing_scenario_df['sell_out_volume_sales_t_new']
        pricing_scenario_df = pricing_scenario_df.replace(-0,0)
        pricing_scenario_df = pricing_scenario_df.fillna(0).replace([np.inf,-np.inf], 0)
        pricing_scenario_df2 = pricing_scenario_df.loc[pricing_scenario_df['exclude_retailer']==True]
        if not is_scenario_planner:
            
            pricing_scenario_df = pricing_scenario_df.loc[pricing_scenario_df['exclude_retailer']==False]
        summary_dict[key] = calc_summary_groupby_level(pricing_scenario_df,scenario_type=scenario_type,is_scenario_planner=is_scenario_planner)

        global_constraints = calc_global_constraints(pricing_scenario_df,level='customer',is_scenario_planner=is_scenario_planner,from_simulate=True,key=key)

        #OVERALL SUMMARY CALCULATION                                                                
        overall_summary_dict[key] = calc_overall_summary(pricing_scenario_df,is_scenario_planner=is_scenario_planner)
        final_dict = {**summary_dict}
        final_dict[key]['overall_summary'] = {**overall_summary_dict[key]}
     
        pricing_scenario_df = handle_large_decimals(pricing_scenario_df)

        pricing_scenario_df = pd.concat([pricing_scenario_df,pricing_scenario_df2],sort=False).sort_index()
        pricing_scenario_df['is_planner'] = is_scenario_planner
    
        main_df = main_df.append(pricing_scenario_df)
    
    pricing_scenario_df = pricing_scenario_df.fillna(0)
    main_df = main_df.fillna(0)
    b_s_columns = CONST.PRICING_BASE_AND_SIMULATED_SCENARIO_COLUMNS[6:]
    b_s_columns.remove('is_base')
    main_df[b_s_columns]=main_df[b_s_columns].astype(str)
    serv.save_summary_promo_scenaro(request_data={**summary_dict,**global_constraints},saved_scenario_id=saved_scenario_id)
    serv.save_overall_summary_promo_scenaro(request_data=overall_summary_dict,saved_scenario_id=saved_scenario_id)
    serv.save_base_and_simulated_promo_scenaro(request_data=main_df.to_dict(orient="records"),saved_scenario_id=saved_scenario_id)
    
    final_dict['non_committed_id'] = saved_scenario_id
    final_dict['solver_staus'] = solver_status
    return {**final_dict,**global_constraints}

def calculate_summary_by_filter(pricing_scenario_df,level='customer'):
    base_df =pricing_scenario_df.loc[pricing_scenario_df['is_base']==True]
    sim_df =pricing_scenario_df.loc[pricing_scenario_df['is_base']==False]
    category_list = ['base','simulated']
    summary_dict  = {}
    
    for idx,psdf in enumerate([base_df,sim_df]):
        summary_dict[category_list[idx]]= calc_summary_groupby_level(psdf,level=level)

    return summary_dict

def calculate_overall_summary_by_filter(pricing_scenario_df):
    category_list = ['base','simulated']
    base_df =pricing_scenario_df.loc[pricing_scenario_df['is_base']==True]
    sim_df =pricing_scenario_df.loc[pricing_scenario_df['is_base']==False]

    overall_summary_dict  = {}
    for idx,psdf in enumerate([base_df,sim_df]):
        overall_summary_dict[category_list[idx]] = calc_overall_summary(psdf)
    return overall_summary_dict

def calculate_deep_drive_summary(pricing_scenario_df):

    pricing_scenario_df['avg_trade_margin_new'] = pricing_scenario_df['avg_trade_margin_new'].astype(float)
    pricing_scenario_df['rsv_new'] = pricing_scenario_df['rsv_new'].astype(float)
    pricing_scenario_df['gmac_abs_new'] =pricing_scenario_df['gmac_abs_new'].astype(float)
    pricing_scenario_df['nsv_new'] =pricing_scenario_df['nsv_new'].astype(float)
    pricing_scenario_df['nsv_t_new'] =pricing_scenario_df['nsv_t_new'].astype(float)
    pricing_scenario_df['dead_net_new'] =pricing_scenario_df['dead_net_new'].astype(float)
    pricing_scenario_df['net_net_new'] =pricing_scenario_df['net_net_new'].astype(float)
    pricing_scenario_df['promo_share'] =pricing_scenario_df['promo_share'].astype(float)
    pricing_scenario_df['cogs_t_new'] = pricing_scenario_df['cogs_t_new'].astype(float)
    pricing_scenario_df['tax'] = pricing_scenario_df['tax'].astype(float)
    pricing_scenario_df['promo_price_new'] = pricing_scenario_df['promo_price_new'].astype(float)
    pricing_scenario_df['non_promo_price_new'] = pricing_scenario_df['non_promo_price_new'].astype(float)
    pricing_scenario_df['customer_profit_new'] = pricing_scenario_df['customer_profit_new'].astype(float)
    gmac_percent = pricing_scenario_df['gmac_abs_new'].sum()/pricing_scenario_df['nsv_new'].sum()
    avg_trade_margin = pricing_scenario_df['customer_profit_new'].sum()/pricing_scenario_df['rsv_new'].sum()
    sell_in_unit_new = pricing_scenario_df['sell_in_unit_new'].astype(float).sum()
    rsv_new = pricing_scenario_df['rsv_new'].astype(float).sum()
    nsv_new = pricing_scenario_df['nsv_new'].astype(float).sum()
    
    return {
        'sell_in_unit_new':round(sell_in_unit_new,4),
        'rsv_new':round(rsv_new,4),
        'nsv_new':round(nsv_new,4),
        'gmac_percent':round(gmac_percent,4),
        'avg_trade_margin':round(avg_trade_margin,4)
    }

def calc_deep_drive_summary(pricing_scenario_df):
    base_df =pricing_scenario_df.loc[pricing_scenario_df['is_base']==True]
    sim_df =pricing_scenario_df.loc[pricing_scenario_df['is_base']==False]
    category_list = ['base','simulated']
    deep_drive_summary  = {}

    for idx,psdf in enumerate([base_df,sim_df]):
        deep_drive_summary[category_list[idx]]= calculate_deep_drive_summary(psdf)
    return deep_drive_summary
 
def calculate_deep_drive_customer_level(pricing_scenario_df,param='price_vol_mix_nsv',level='customer'):
    base_df =pricing_scenario_df.loc[pricing_scenario_df['is_base']==True]
    sim_df =pricing_scenario_df.loc[pricing_scenario_df['is_base']==False]
 
    if param =='total_tt_drift':
        sim_df['tt_drift'] = sim_df['tt_drift'].astype(float)
        tt_drift_df = sim_df.groupby(level,as_index=False).agg(total_tt_drift=pd.NamedAgg(column="tt_drift", aggfunc="sum")).round(2)
        return tt_drift_df.to_dict(orient="records")

    category_list = ['base','simulated']
    profit_pool_dict  = {}
 
    if param == 'profit_pool':
        for idx,psdf in enumerate([base_df,sim_df]):
            profit_pool_dict[category_list[idx]]= calc_profit_pool(psdf,level)
        return profit_pool_dict
    
    if param == 'price_vol_mix_nsv':
        profit_vol_mix_nsv_dict = calc_price_vol_mix_in(sim_df,level)
        return profit_vol_mix_nsv_dict
    
def calculate_deep_drive_top_10_level(pricing_scenario_df,level,level_param,agg_func='SUM'):
    agg_column = CONST.BASE_SIM_CONFIG.get(level_param,level_param)
    pricing_scenario_df[agg_column] =pricing_scenario_df[agg_column].astype(float)
    
    if level_param in ['dead_net','net_net']:
        pricing_scenario_df['nsv_new'] =pricing_scenario_df['nsv_new'].astype(float)
        pricing_scenario_df = pricing_scenario_df[pricing_scenario_df[agg_column] > .01]
        top_10_df = pricing_scenario_df.groupby(level,as_index=False).agg(max_value=pd.NamedAgg(column=agg_column, aggfunc="max"),
                                                                          min_value=pd.NamedAgg(column=agg_column, aggfunc="min"),
                                                                          nsv_sum=pd.NamedAgg(column='nsv_new', aggfunc="sum"),
                                                                          )
        top_10_df[f'{level_param}_spread'] = top_10_df['max_value']-top_10_df['min_value']
    else:
        percentage_change_column = f'{level_param}_percent_change'
        pricing_scenario_df[percentage_change_column] =pricing_scenario_df[percentage_change_column].astype(float)
        
        top_10_df = pricing_scenario_df.groupby(level,as_index=False).agg({col:agg_func for col in [agg_column,percentage_change_column]})
        top_10_df = top_10_df.rename(columns={agg_column:f'{level_param}_sum'})
    return top_10_df

def get_compare_scenario_kpis(pricing_scenario_df):
    pricing_scenario_df['avg_trade_margin_new'] = pricing_scenario_df['avg_trade_margin_new'].astype(float)
    pricing_scenario_df['rsv_new'] = pricing_scenario_df['rsv_new'].astype(float)
    pricing_scenario_df['gmac_abs_new'] =pricing_scenario_df['gmac_abs_new'].astype(float)
    gmac_abs_new = pricing_scenario_df['gmac_abs_new'].mean()
    pricing_scenario_df['nsv_new'] =pricing_scenario_df['nsv_new'].astype(float)
    pricing_scenario_df['cogs_t_new'] = pricing_scenario_df['cogs_t_new'].astype(float)
    cogs_t = pricing_scenario_df['cogs_t_new'].mean()
    pricing_scenario_df['customer_profit_new'] = pricing_scenario_df['customer_profit_new'].astype(float)
    sell_in_volume_t = pricing_scenario_df['sell_in_volume_t_new'].astype(float).sum()
    sell_out_unit = pricing_scenario_df['sell_out_unit_new'].astype(float).sum()
    pricing_scenario_df['rsv_new'] = pricing_scenario_df['rsv_new'].astype(float)
    pricing_scenario_df['nsv_new'] = pricing_scenario_df['nsv_new'].astype(float)
    pricing_scenario_df['lsv_new'] =pricing_scenario_df['lsv_new'].astype(float)
    rsv = pricing_scenario_df['rsv_new'].astype(float).sum()
    nsv = pricing_scenario_df['nsv_new'].astype(float).sum()
    lsv =pricing_scenario_df['lsv_new'].astype(float).sum()
    pricing_scenario_df['list_price'] = pricing_scenario_df['list_price'].astype(float)
    pricing_scenario_df['list_price_new'] = pricing_scenario_df['list_price_new'].astype(float)
    pricing_scenario_df['pack_weight_kg'] = pricing_scenario_df['pack_weight_kg'].astype(float)
    pricing_scenario_df['pack_weight_kg_new'] = pricing_scenario_df['pack_weight_kg_new'].astype(float)
    tt_drift  = pricing_scenario_df['tt_drift'].astype(float).sum()
    pricing_scenario_df['tt_percent_new'] = ((pricing_scenario_df['lsv_new']-pricing_scenario_df['nsv_new'])/pricing_scenario_df['lsv_new']).replace([np.inf,-np.inf], 0).fillna(0)
    total_price_impact_inc_tt_drift = pricing_scenario_df['total_price_impact_inc_tt_drift'].astype(float).sum()
    pricing_scenario_df['percent_pricing_impact_total'] = ((pricing_scenario_df['list_price_new']/pricing_scenario_df['pack_weight_kg_new']).replace([np.inf,-np.inf], 0).fillna(0)\
                                    /(pricing_scenario_df['list_price']/pricing_scenario_df['pack_weight_kg']).replace([np.inf,-np.inf], 0).fillna(0)-1).fillna(0)

    percent_pricing_impact_total =  pricing_scenario_df['percent_pricing_impact_total'].mean()
    tt_percent = pricing_scenario_df['tt_percent_new'].mean()
    gmac_percent = pricing_scenario_df['gmac_abs_new'].sum()/pricing_scenario_df['nsv_new'].sum()
    avg_trade_margin = pricing_scenario_df['customer_profit_new'].sum()/pricing_scenario_df['rsv_new'].sum()
    trade_margin = pricing_scenario_df['customer_profit_new'].sum()

    return {
        'sell_in_volume':sell_in_volume_t,
        'units':sell_out_unit,
        'rsv':rsv,
        'lsv':lsv,
        'nsv':nsv,
        'tt_drift':tt_drift,
        'total_price_impact_inc_tt_drift':total_price_impact_inc_tt_drift,
        'percent_price_impact_total':percent_pricing_impact_total,
        'tt_percent':tt_percent,
        'gmac_percent':gmac_percent,
        'trade_margin_percent':avg_trade_margin,
        'trade_margin':trade_margin,
        'gmac_abs':gmac_abs_new,
        'cogs_t':cogs_t
    }
    
def calc_compare_scenario_kpi(pricing_scenario_df):
    base_df = pricing_scenario_df.loc[pricing_scenario_df['is_base']==True]
    sim_df = pricing_scenario_df.loc[pricing_scenario_df['is_base']==False]
    base_df = base_df.fillna(0)
    sim_df = sim_df.fillna(0)
    category_list = ['base','simulated']
    ppg = base_df['product_group'].unique().tolist()
    brand = base_df['brand'].unique().tolist()
    customer = base_df['customer'].unique().tolist()
    technology = base_df['technology'].unique().tolist()
    ogsm_type = base_df['ogsm_type'].unique().tolist()
    compare_scenario_kpis_dict  = {}
    for idx,psdf in enumerate([base_df,sim_df]):
        compare_scenario_kpis_dict[category_list[idx]] =  get_compare_scenario_kpis(psdf)
    meta_obj = {'product_group':ppg,'brand':brand,'customer':customer,'ogsm_type':ogsm_type,'technology':technology} 
    return {**meta_obj,**compare_scenario_kpis_dict}
    