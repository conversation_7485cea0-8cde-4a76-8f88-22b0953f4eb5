""" Django Admin"""
from django.contrib import admin
from django.contrib import messages
from django.shortcuts import render, redirect
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.urls import path
from django import forms
from django.contrib.auth.models import Group
from django.contrib.auth.forms import ReadOnlyPasswordHashField
from django.contrib.auth import get_user_model
from core.generics import excel #pylint: disable=E0401
from apps.common import models as db_model #pylint: disable=E0401

User = get_user_model()

admin.site.unregister(Group)
class UserAdminChangeForm(forms.ModelForm):
    """A form for updating users. Includes all the fields on
    the user, but replaces the password field with admin's
    password hash display field.
    """
    password = ReadOnlyPasswordHashField()

    class Meta:
        """ Meta Class for UserAdminChangeForm"""
        model = User
        fields = ('email', 'password', )

    def clean_password(self):
        """ Clean Password"""
        # Regardless of what the user provides, return the initial value.
        # This is done here, rather than on the field, because the
        # field does not have access to the initial value
        return self.initial["password"]

class UserAdminCreationForm(forms.ModelForm):
    """A form for creating new users. Includes all the required
    fields, plus a repeated password."""
    password1 = forms.CharField(label='Password', widget=forms.PasswordInput)
    password2 = forms.CharField(label='Password confirmation', widget=forms.PasswordInput)

    class Meta:
        model = User
        fields = ('email','is_superuser',)

    def clean_password2(self):
        """ Check that the two password entries match"""
        # Check that the two password entries match
        password1 = self.cleaned_data.get("password1")
        password2 = self.cleaned_data.get("password2")
        if password1 and password2 and password1 != password2:
            raise forms.ValidationError("Passwords don't match")
        return password2

    def save(self, commit=True):
        """Save the provided password in hashed format """
        # Save the provided password in hashed format
        user = super().save(commit=False)
        user.set_password(self.cleaned_data["password1"])
        if commit:
            user.save()
        return user

class UserAdmin(BaseUserAdmin):
    """ User Admin"""
    ordering = ['id']
    list_display = ['email', 'name','get_groups' , 'is_active' ]

    fieldsets = (
        (None,{'fields':('email' , 'name' ,) }),
        ('permissions' , {'fields' : ('is_staff' , 'is_active' ,'is_superuser'\
                                            , 'groups' , 'allowed_retailers')}),
    )
    add_fieldsets = (
        (None, {
            'classes' : ('wide' ,),
            'fields' : ('email' , 'name' , 'password1' , 'password2' , 'groups' , 'is_active' ,
                        'is_staff','is_superuser','allowed_retailers')
        }),)

    def get_groups(self,obj):
        return "\n, ".join([p.name for p in obj.groups.all()])

class CsvImportForm(forms.Form):
    '''Creating fields '''
    model_file = forms.FileField(required=False)
    model_data = forms.BooleanField(required=False, initial=False)
    coeff = forms.BooleanField(required=False, initial=False)
    coeff_map = forms.BooleanField(required=False, initial=False)
    roi = forms.FileField(required=False)
    retailer_ppg_mapping=forms.FileField(required=False)
    tactic=forms.FileField(required=False)
    item_mapping=forms.FileField(required=False)
 
class ModelMetaAdmin(admin.ModelAdmin):
    '''Creating Class for ModelMetaAdmin'''
    list_display = [field.name for field in db_model.ModelMeta._meta.fields] # pylint: disable=no-member
    list_filter = ('account_name', 'corporate_segment', 'product_group','is_delete')
    change_list_template = "admin/promo_upload.html"
    actions = ['remove_non_model_meta']

    def remove_non_model_meta(self, queryset):
        '''Checking data is exists or not if not exists it should remove'''
        for _q in queryset:
            if not _q.data.exists():
                _q.delete()
    remove_non_model_meta.short_description = "Remove non model meta"

    def get_urls(self):
        '''Method to get the Urls'''
        urls = super().get_urls()
        my_urls = [
            path('import-promo/', self.import_csv),
        ]
        return my_urls + urls

    def import_csv(self, request):
        '''Method tp post the Dta'''
        if request.method == "POST":
            try:
                form = CsvImportForm(request.POST)
                total_model = 0
                slug_memory = {}
                if 'model_file' in request.FILES:
                    csv_file = request.FILES["model_file"]
                    is_coeff = form['coeff'].value()
                    is_model = form['model_data'].value()
                    is_coeff_map = form['coeff_map'].value()
                    slug_memory = excel.read_model_files(csv_file,
                                                        slug_memory,
                                                        is_coeff,
                                                        is_model,
                                                        is_coeff_map)

                if 'roi' in request.FILES:
                    roi = request.FILES["roi"]
                    excel.read_roi_data(roi, slug_memory)
                if 'tactic' in request.FILES:
                    tactic = request.FILES["tactic"]
                    excel.read_tactic_data(tactic, slug_memory)
                if 'retailer_ppg_mapping' in request.FILES:
                    retailer_ppg_mapping = request.FILES["retailer_ppg_mapping"]
                    excel.read_retailer_ppg_mapping_data(retailer_ppg_mapping, slug_memory)
                if 'item_mapping' in request.FILES:
                    item_mapping = request.FILES["item_mapping"]
                    excel.read_item_mapping_data(item_mapping, slug_memory)
                self.message_user(request, f"Total {total_model} model data imported")
                return redirect("..")
            except Exception as _e:
                self.message_user(request, _e, level=messages.ERROR)
                return redirect("..")
        form = CsvImportForm()
        payload = {"form": form}
        return render(
            request, "admin/excel_form.html", payload
        )


class ModelCoefficientAdmin(admin.ModelAdmin):
    '''Creating Class for ModelCoefficientAdmin'''
    search_fields = ['model_meta__id', 'model_meta__slug', 'model_meta__account_name','is_delete']
    list_display = [field.name for field in db_model.ModelCoefficient._meta.fields]  # pylint: disable=no-member'
    list_filter = ('model_meta__account_name', 'model_meta__product_group','is_delete')

class ModelDataAdmin(admin.ModelAdmin):
    ''' Creating Class for ModelDataAdmin'''
    search_fields = ['model_meta__id', 'model_meta__slug', 'model_meta__account_name']
    list_display = [field.name for field in db_model.ModelData._meta.fields]  # pylint: disable=no-member
    list_filter = ('model_meta__account_name', 'model_meta__product_group','is_delete','year')

class ModelROIAdmin(admin.ModelAdmin):
    '''Creating class for ModelROI'''
    search_fields = ['model_meta__id', 'model_meta__slug', 'model_meta__account_name']
    list_display = [field.name for field in db_model.ModelROI._meta.fields]  # pylint: disable=no-member
    list_filter = ('model_meta__account_name', 'model_meta__product_group','is_delete','year')

class CoeffMapAdmin(admin.ModelAdmin):
    '''Creating Class for CoeffMapAdmin'''
    search_fields = ['model_meta__id', 'model_meta__slug', 'model_meta__account_name']
    list_display = [field.name for field in db_model.CoefficientMapping._meta.fields]  # pylint: disable=no-member
    list_filter = ('model_meta__account_name', 'model_meta__product_group','is_delete')

class SavedScenarioAdmin(admin.ModelAdmin):
    list_filter = ('scenario_type','promo_type','status_type','name','is_delete')
    list_display = [field.name for field in db_model.SavedScenario._meta.fields] # pylint: disable=no-member

class ScenarioPromotionSaveAdmin(admin.ModelAdmin):
    '''Creating Class for CoeffMapAdmin'''
    # search_fields = ['name']
    list_display = [field.name for field in db_model.ScenarioPromotionSave._meta.fields]  # pylint: disable=no-member
    list_filter = ('saved_scenario__status_type', 'saved_scenario__scenario_type'\
                                        ,'saved_scenario__promo_type','is_delete')

class TacticAdmin(admin.ModelAdmin):
    '''Creating class for TacticAdmin'''
    search_fields = ['retailer_ppg_map__account_name', 'retailer_ppg_map__product_group','is_delete']
    list_display = [field.name for field in db_model.ModelTactic._meta.fields]  # pylint: disable=no-member
    list_filter = ['retailer_ppg_map__account_name', 'retailer_ppg_map__product_group','is_delete']

class RetailerPPGMappingAdmin(admin.ModelAdmin):
    '''Creating class for RetailerPPGMappingAdmin'''
    search_fields = ['account_name', 'product_group', 'type_of_promo','is_delete']
    list_display = [field.name for field in db_model.RetailerPPGMapping._meta.fields]  # pylint: disable=no-member
    list_filter = ['account_name', 'product_group', 'type_of_promo','is_delete']

class ITEMapAdmin(admin.ModelAdmin):
    '''Creating class for ITEMapAdmin'''
    search_fields = ['account_name', 'product_group', 'ppg_item_no','is_delete']
    list_display = [field.name for field in db_model.ItemMap._meta.fields]  # pylint: disable=no-member
    list_filter = ['account_name', 'product_group', 'ppg_item_no','is_delete']

admin.site.register(User, UserAdmin)
admin.site.register(db_model.ModelMeta, ModelMetaAdmin)
admin.site.register(db_model.ModelCoefficient, ModelCoefficientAdmin)
admin.site.register(db_model.ModelData, ModelDataAdmin)
admin.site.register(db_model.ModelROI, ModelROIAdmin)
admin.site.register(db_model.CoefficientMapping, CoeffMapAdmin)
admin.site.register(db_model.RetailerPPGMapping, RetailerPPGMappingAdmin)
admin.site.register(db_model.ModelTactic, TacticAdmin)
admin.site.register(db_model.ItemMap, ITEMapAdmin)
admin.site.register(db_model.SavedScenario, SavedScenarioAdmin)
admin.site.register(db_model.ScenarioPromotionSave, ScenarioPromotionSaveAdmin)
