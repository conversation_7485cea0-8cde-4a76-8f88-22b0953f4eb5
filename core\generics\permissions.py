from rest_framework import permissions
from apps.pricing.pricing_common import models as db_model


def handle_permission(func):
    def wrapper(*args, **kwargs):
        IsOwnerOrReadOnly().has_object_permission()
        return func(*args, **kwargs)
    return wrapper

class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    Object-level permission to only allow owners of an object to edit it.
    Assumes the model instance has an `owner` attribute.
    """

    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed to any request,
        # so we'll always allow GET, HEAD or OPTIONS requests.
        breakpoint()
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Instance must have an attribute named `owner`.
        return obj.owner == request.user
    