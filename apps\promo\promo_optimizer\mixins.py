""" Optimizer Mixin"""
import copy
import json
from apps.promo.promo_optimizer import generic as opt_generic
from core.generics import (units_calculations as uc,#pylint: disable=E0401
                            calculations as cal\
                            ,constants as core_const)
from . import calculations as opt_cal

def calculate_finacial_metrics_for_optimizer(
                                            value_dict:dict,
                                            coeff_list:list,
                                            data_list:list,
                                            roi_list:list,
                                            scenario_name:str,
                                            status='Infeasible',
                                            is_one_shot_ppg=False,
                                            extra_columns:list= None
                                            )->dict:
    """calculate_finacial_metrics_for_optimizer

    Args:
        value_dict (dict): dict consists of optimized values
        coeff_list (list): list of coeff data
        data_list (list): list of model data
        roi_list (list): list of roi data

    Returns:
        dict: financial metrics
    """
    
    account_name,segment,ppg,brand,brand_tech,product_type,*_ = coeff_list[0]
    meta_dict = {
    'account_name' : account_name,
    'corporate_segment' : segment,
    'product_group' : opt_generic.format_ppg(ppg),
    'brand':brand,
    'brand_tech':brand_tech,
    'product_type':product_type,
    'scenario_name':scenario_name,
    "type_of_promo":'single',
    }

    data_list = copy.deepcopy(data_list)
    roi_list = copy.deepcopy(roi_list)
    coeff_list = copy.deepcopy(coeff_list)
    data_values = core_const.DATA_VALUES.copy()
    data_columns = data_values + extra_columns

    base_incremental_split = json.loads(uc.list_to_frame(coeff_list,
                                                         data_list,
                                                         roi_list,
                                                         flag='base',
                                                         extra_columns=extra_columns,
                                                         ).to_json(orient="records"))
    
    base_finalcial_metrics = cal.calculate_financial_mertrics(data_list ,
                                                             roi_list,
                                                             base_incremental_split ,
                                                            'base',
                                                            d_columns=data_columns,                                                            
                                                            )
    
    simulated_data_list,simulated_roi_list = opt_cal.update_for_optimizer(data_list,
                                                                            roi_list,
                                                                            value_dict,
                                                                            d_columns=data_columns,
                                                                            status=status,
                                                                            is_one_shot_ppg=is_one_shot_ppg
                                                                            )

    simulated_incremental_split = json.loads(uc.list_to_frame(coeff_list,
                                                              simulated_data_list,
                                                              roi_list,
                                                              'simulated',
                                                              extra_columns=extra_columns,
                                                              base_incremental_split=base_incremental_split
                                                              )\
                                                            .to_json(orient="records"))

    simulated_financial_metrics = cal.calculate_financial_mertrics(simulated_data_list ,
                                                                    simulated_roi_list,
                                                                    simulated_incremental_split ,
                                                                    'simulated',
                                                                    d_columns=data_columns,
                                                                    )

    return {**meta_dict,
            **base_finalcial_metrics,
            **simulated_financial_metrics,
            **{'holiday_calendar' : []}
            }
