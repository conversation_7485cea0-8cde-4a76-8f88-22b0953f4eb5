""" Test Optimizer Models"""
import pytest
from .. import models  as db_model

@pytest.mark.django_db
def test_create_model_meta(_django_data_setup):
    """test_create_model_meta

    Args:
        _django_data_setup (_type_): db
    """
    assert set(u.id for u in db_model.ModelMeta.objects.all()) == {1,2} # pylint: disable=no-member

@pytest.mark.django_db
def test_create_model_data(_django_data_setup):
    """test_create_model_data

    Args:
        _django_data_setup (_type_): db
    """
    assert set(u.model_meta_id for u in db_model.ModelData.objects.all()) == {1,2} # pylint: disable=no-member

@pytest.mark.django_db
def test_create_model_coeff(_django_data_setup):
    """test_create_model_coeff

    Args:
        _django_data_setup (_type_): db
    """
    assert set(u.model_meta_id for u in db_model.ModelCoefficient.objects.all()) == {1,2} # pylint: disable=no-member

@pytest.mark.django_db
def test_create_coeff_map(_django_data_setup):
    """test_create_coeff_map

    Args:
        _django_data_setup (_type_): db
    """
    assert set(u.model_meta_id for u in db_model.CoefficientMapping.objects.all()) == {1,2} # pylint: disable=no-member

@pytest.mark.django_db
def test_create_model_roi(_django_data_setup):
    """test_create_model_roi

    Args:
        _django_data_setup (_type_): db
    """
    assert set(u.model_meta_id for u in db_model.ModelROI.objects.all()) == {1,2} # pylint: disable=no-member

@pytest.mark.django_db
def test_create_rpm(_django_data_setup):
    """test_create_rpm

    Args:
        _django_data_setup (_type_): db
    """
    assert set(u.id for u in db_model.RetailerPPGMapping.objects.all()) == {1,2,3} # pylint: disable=no-member

@pytest.mark.django_db
def test_create_tactic(_django_data_setup):
    """test_create_tactic

    Args:
        _django_data_setup (_type_): db
    """
    assert set(u.retailer_ppg_map_id for u in db_model.ModelTactic.objects.all()) == {1,2,3} # pylint: disable=no-member

@pytest.mark.django_db
def test_create_item(_django_data_setup):
    """test_create_item

    Args:
        _django_data_setup (_type_): db
    """
    assert set(u.id for u in db_model.ItemMap.objects.all()) == {1,2} # pylint: disable=no-member
