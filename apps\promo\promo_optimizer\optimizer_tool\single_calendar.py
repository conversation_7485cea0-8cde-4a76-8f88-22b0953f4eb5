""" Single Promotion. """
import copy
from functools import partial
import numpy as np
import pandas as pd
from pulp import (
                  lpSum,
                  LpStatus,
                  LpVariable,
                  LpProblem,
                  LpMaximize,
                  LpMinimize,
                  PULP_CBC_CMD
                  )
from apps.promo.promo_optimizer import generic as opt_generic

def get_dftactic_combo_4(stg1_df):

    col_stg1_new_dict = {"Newsletter_flag": "Flag_promotype_newsletter", "Leaflet_flag": "Flag_promotype_Leaflet", 
                          "Advertising_without_price_flag": "Flag_promotype_advertising_without_price",                         
                          "Bonus_flag": "Flag_promotype_bonus", "Coupon_flag" : "Flag_promotype_coupon",
                          "EDLP_flag": "Flag_promotype_edlp", "Pas_flag": "Flag_promotype_pas", 
                          "Multibuy_flag": "Flag_promotype_multibuy"}
    col_new_old_dict = {"Flag_promotype_Leaflet": "Tactic_Leaflet", "Flag_promotype_newsletter": "Tactic_Medium_TZ",
                         "Flag_promotype_advertising_without_price": "Tactic_Advertising_without_price",                        
                         "Flag_promotype_bonus": "Tactic_Bonus", "Flag_promotype_edlp": "Tactic_EDLP",
                         "Flag_promotype_coupon": "Tactic_Coupon",
                         "Flag_promotype_pas": "Tactic_PAS", "Flag_promotype_multibuy": "Tactic_Multibuy"}
    
    model_cols = stg1_df.columns
    new_model_cols = [col_new_old_dict[col_stg1_new_dict[i]] for i in model_cols if "_flag" in i]
    tactic_list = new_model_cols + ["TPR", "Promo_Type",'Mechanic']
    
    temp_stg1_df = stg1_df.copy()
    temp_stg1_df = temp_stg1_df.rename(columns=col_stg1_new_dict)
    temp_stg1_df = temp_stg1_df.rename(columns=col_new_old_dict)

    df_new1 = temp_stg1_df[tactic_list]        
    df_new1 = df_new1.reset_index(drop=True)
    return df_new1

def get_required_base_3(baseline_data:pd.DataFrame,
                        model_coeff:pd.DataFrame,
                        df_new1:pd.DataFrame,
                        _summary_df:pd.DataFrame,
                        ppg_item_num:str)->pd.DataFrame:
    """_summary_

    Args:
        baseline_data (pd.DataFrame): baseline_data
        model_coeff (pd.DataFrame): model_coeff
        df_new1 (pd.DataFrame): df_new1
        _summary_df (pd.DataFrame): _summary_df
        ppg_item_num (str): ppg_item_num

    Returns:
        pd.DataFrame: Required base
    """
    # Optimizer scenario creation
    # getting model variables

    model_cols = model_coeff['names'].to_list()
    model_cols.remove('Intercept')
    model_cols = [col.lower() if col != 'TPR' else col for col in model_cols]
    # breakpoint() #vinilbp()
    """below 2 if loops are added as a patch until we find out why are we getting these 2 tactic_visibility columns instead of flag_visibility columns"""
    if 'Tactic_Visibility_gazetka_moduł_aranżowany' in baseline_data.columns:
        baseline_data.rename(columns = {'Tactic_Visibility_gazetka_moduł_aranżowany':'flag_visibility_gazetka_modul_aranzowany'}, inplace = True)
    if 'Tactic_Visibility_promocja_pólkowa' in baseline_data.columns:
         baseline_data.rename(columns = {'Tactic_Visibility_promocja_pólkowa':'flag_visibility_promocja_polkowa'}, inplace = True) 
    base=baseline_data[['wk_base_price_perunit_byppg','Promo', 'GSV_Per_Unit_Future','COGS_Per_Unit_Future','NSV_Per_Unit_Future']+model_cols]
    i=1 ##0(iteration starting from 1 to match with iteration numbers from stage1)
    df_new_1 = df_new1.sort_values(by='Promo_Type', ascending = True).reset_index(drop=True)
    tpr_list=df_new_1[['TPR', 'Promo_Type','Mechanic']].values

    if "Tactic_Leaflet" in df_new1.columns:
        mech_list=list(df_new1['Tactic_Leaflet'])
    if "Tactic_Display" in df_new1.columns:    
        disp_list=list(df_new_1['Tactic_Display'])

    ret = _summary_df['Retailer'].unique()[0]
  
    for k,_val in enumerate(tpr_list): ## change        
        required_base=base.copy() 
        required_base['TPR']=tpr_list[k][0]  ## change
        if 'TPR_lag1' in model_cols:
            required_base['TPR_lag1'] =0
        if 'TPR_lag2' in model_cols:
            required_base['TPR_lag2'] =0
        required_base['Promo']=required_base['wk_base_price_perunit_byppg']\
            -(required_base['wk_base_price_perunit_byppg']*required_base['TPR']/100)

        if 'Tactic_Leaflet' in model_cols:
            if mech_list[k]>0:
                required_base['Tactic_Leaflet'] = np.where(required_base['TPR']>0,1,0)
            else:
                required_base['Tactic_Leaflet'] = 0

        if 'Tactic_Display' in model_cols:
            if disp_list[k]>0:
#                 ret = config['Retailer']
                _l1 = ['Penny','ReweVoSo']
                if ret in _l1:
                    display_val = required_base['Tactic_Display'].max()
                    required_base['Tactic_Display'] = np.where(required_base['TPR']>0,display_val,0)
                else:
                    required_base['Tactic_Display'] = np.where(required_base['TPR']>0,1,0)
            else:
                required_base['Tactic_Display'] = 0
    
        
        required_base['Units']=opt_generic.predict_sales(model_coeff,required_base)
        required_base['Promo Price']=required_base['wk_base_price_perunit_byppg']\
                        -(required_base['wk_base_price_perunit_byppg']*(required_base['TPR']/100))
        required_base['Sales']=required_base['Units'] *required_base['Promo Price']
        # creating flag for promo price based on the promo price constraint
        # calculating the financial metrics
        required_base["GSV"] = required_base['Units'] * required_base['GSV_Per_Unit_Future']
        required_base["NSV"] = required_base['Units'] * required_base["NSV_Per_Unit_Future"]
        required_base["Trade_Expense"] =  required_base["GSV"] - required_base["NSV"]
        required_base["MAC"] = required_base["NSV"] - (required_base['Units'] * 
                                                       required_base['COGS_Per_Unit_Future'])
        required_base["RP"] = required_base['Sales']-required_base["NSV"]
        required_base["TPR"]=tpr_list[k][0]
        promo_type = tpr_list[k][1]
        required_base["Mechanic"]=tpr_list[k][2]
        required_base["Iteration"]=i
        required_base['OptimizedWeeksPromo'] = _summary_df[_summary_df['Promo_Type']\
                                            ==promo_type]['Optimized_Weeks'].unique()[0] ## Added new
        if i==1:
            new_base=required_base
        else:
            new_base=new_base.append(required_base)
        i=i+1
    required_base=new_base
    required_base=required_base.reset_index(drop=True)
    required_base['WK_ID']=required_base.index
    # creating unique ids for optimization
    required_base['WK_ID'] = 'WK_' + required_base['WK_ID'].astype(str)+'_'\
                            +required_base['Iteration'].astype(str)
    required_base['Prod'] = ppg_item_num

    return required_base 

def handle_joint_ppg(_d,acc_name,ppg):
    new_ppg = "-".join(ppg.split(','))
    new_ppg = " ".join(new_ppg.split('_'))
    single_ppg = new_ppg.split('_-_',maxsplit=1)[0]
    if _d['promo_type'] == 'joint' and _d['account_name'] == acc_name:
        if _d['product_group']==new_ppg:
            return True
        if _d['product_group']==single_ppg:
            return True
    if _d['account_name'] == acc_name and _d['product_group']==new_ppg:
        return True
    return False

def optimizer_fun_4(baseline_data:pd.DataFrame, 
                    required_base:pd.DataFrame, 
                    config:dict, 
                    joint_ppg_week_nums:pd.DataFrame)->pd.DataFrame: ##df_stg2a_calendar_1
    """optimizer_fun_4

    Args:
        baseline_data (pd.DataFrame): baseline_data
        required_base (pd.DataFrame): required_base
        config (pd.DataFrame): dict
        joint_ppg_week_nums (pd.DataFrame): joint_ppg_week_nums
    """
  # calculating baseline numbers from baseline data
    
    baseline_df =baseline_data[['Baseline_Prediction',
                                'Baseline_Sales',
                                "Baseline_GSV",
                                "Baseline_Trade_Expense",
                                "Baseline_Trade_Expense_onPromo",
                                "Baseline_NSV",
                                "Baseline_MAC",
                                "Baseline_RP"]].sum().astype(int) ## change added new ROI
    # getting the user input
    config_constrain = config['config_constrain']
    constrain_params = config['constrain_params']
    
    # getting the number of promotions (including zero)
    promo_loop=required_base['Iteration'].nunique()
    # defining variable type
    wk_dv_vars = list(required_base['WK_ID'])
    wk_vars = LpVariable.dicts("RP",wk_dv_vars,cat='Binary')
  # selecting the objective based on user input
    if config['Objective']=='Maximize':
        print("Maximize")
        prob = LpProblem("Simple_Workaround_problem",LpMaximize)
    else:
        print("Minimize")
        prob = LpProblem("Simple_Workaround_problem",LpMinimize)
#   slack = pulp.LpVariable('slack', lowBound=0, cat='Continuous')
  # defining objective function
    obj_metric = config['Objective_metric']
    print("The objective metric is: ", obj_metric)
    prob+=lpSum([wk_vars[required_base['WK_ID'][i]]*(required_base[obj_metric][i])\
                    for i in range(0,required_base.shape[0])])
    if obj_metric=='MAC':
        _obj_basevalue = baseline_df['Baseline_MAC']
    elif obj_metric=='RP':
        _obj_basevalue = baseline_df['Baseline_RP']
    elif obj_metric=='Trade_Expense':
        _obj_basevalue = baseline_df['Baseline_Trade_Expense'] ## change added new ROI
    elif obj_metric=='Units':
        _obj_basevalue = baseline_df['Baseline_Prediction']
    elif obj_metric=='NSV':
        _obj_basevalue = baseline_df['Baseline_NSV']
    elif obj_metric=='GSV':
        _obj_basevalue = baseline_df['Baseline_GSV']
    elif obj_metric=='Sales':
        _obj_basevalue = baseline_df['Baseline_Sales']
 
    if config_constrain['MAC']:
        prob+=lpSum([wk_vars[required_base['WK_ID'][i]]*required_base['MAC'][i]\
                for i in range(0,required_base.shape[0])])
    
  # RP constraint
    if config_constrain['RP']:
        prob+=lpSum([wk_vars[required_base['WK_ID'][i]]*(required_base['RP'][i])\
        for i in range(0,required_base.shape[0])])

    # Set up constraints such that only one tpr is chose for a week
    for i in range(0,52):
        prob+=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]  for j in range(0,promo_loop)])<=1
        prob+=lpSum([wk_vars[required_base['WK_ID'][i+j*52]] for j in range(0,promo_loop)])>=1  

  ##Set up constraints such that 52 weeks chosen
    if config_constrain['52Weeks']:
        prob+=lpSum([wk_vars[required_base['WK_ID'][i]]  for i in range(0,required_base.shape[0])])<=52
        prob+=lpSum([wk_vars[required_base['WK_ID'][i]]  for i in range(0,required_base.shape[0])])>=52

#########-------------------------------------New Constraints#########################    
    
    ### Constraints for Weeks of different PromoTypes
    # For promo_type = 0
    default_promo_loop = 11
    if (promo_loop < default_promo_loop):
        for j in range(0,promo_loop):
            prob+=lpSum([wk_vars[required_base['WK_ID'][i+j*52]] for i in range(0,52)])\
                <=required_base[required_base['Iteration']==j+1]['OptimizedWeeksPromo'].unique()[0]
            prob+=lpSum([wk_vars[required_base['WK_ID'][i+j*52]] for i in range(0,52)])\
                >=required_base[required_base['Iteration']==j+1]['OptimizedWeeksPromo'].unique()[0]
    else:
        custom_tpr_promo = []
        for j in range(0, promo_loop):
            if (j!=1) and (j < default_promo_loop):
                prob+=lpSum([wk_vars[required_base['WK_ID'][i+j*52]] for i in range(0,52)])\
                    <=required_base[required_base['Iteration']==j+1]['OptimizedWeeksPromo'].unique()[0]
                prob+=lpSum([wk_vars[required_base['WK_ID'][i+j*52]] for i in range(0,52)])\
                    >=required_base[required_base['Iteration']==j+1]['OptimizedWeeksPromo'].unique()[0]
            else:
                custom_tpr_promo.append(j)
        prob+=lpSum([wk_vars[required_base['WK_ID'][i+j*52]] 
                     for j in custom_tpr_promo
                     for i in range(0,52)]) <= required_base[required_base['Iteration']==2]\
                         ['OptimizedWeeksPromo'].unique()[0]
        prob+=lpSum([wk_vars[required_base['WK_ID'][i+j*52]] 
                     for j in custom_tpr_promo
                     for i in range(0,52)]) >= required_base[required_base['Iteration']==2]\
                         ['OptimizedWeeksPromo'].unique()[0]

    # constraint for compulsory promo weeks
    if len(constrain_params['compul_promo_weeks'])>0: 
        promo_list = constrain_params['compul_promo_weeks'].copy() ## change added new
        promo_list[:]=[i-1 for i in promo_list]    
        prob+=lpSum([wk_vars[required_base['WK_ID'][i+j*52]] 
                     for j in range(1,promo_loop) for i in promo_list])>=len(promo_list)
    
    # constraint for compulsory no promo weeks
    for i in joint_ppg_week_nums:
        for j in range(1, promo_loop):
            prob += wk_vars[required_base["WK_ID"][i+j*52]] <= 0
            prob += wk_vars[required_base["WK_ID"][i+j*52]] >= 0
  
    r_0=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop)\
        for i in range(0,1)])
    r_1=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(1,2)])
    r_2=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(2,3)])
    r_3=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(3,4)])
    r_4=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(4,5)])
    r_5=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(5,6)])
    r_6=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(6,7)])
    r_7=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop)\
        for i in range(7,8)])
    r_8=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop)\
        for i in range(8,9)])

    # Sub constraint for minimum weeks
    r_51=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop)\
        for i in range(50,51)])
    r_52=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop)\
        for i in range(51,52)])
    r_50=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop)\
        for i in range(49,50)])
    r_49=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop)\
        for i in range(48,49)])
    r_48=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop)\
        for i in range(47,48)])
    r_47=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop)\
        for i in range(46,47)])
    r_46=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop)\
        for i in range(45,46)])
    r_45=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop)\
        for i in range(44,45)])
    r_44=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop)\
        for i in range(43,44)])
    
    if config_constrain['min_consecutive_promo']:
        if constrain_params['min_consecutive_promo']==2:
            prob+=r_51-r_52 >=0
            prob+=r_1-r_0 >=0
        if constrain_params['min_consecutive_promo']==3:
            prob+=r_51-r_52 >=0
            prob+=r_1-r_0 >=0
            prob+=2*r_50-r_51-r_52 >=0
            prob+= 2*r_2-r_1-r_0 >=0
        if constrain_params['min_consecutive_promo']==4:
            prob+=r_51-r_52 >=0
            prob+=r_1-r_0 >=0
            prob+=2*r_50-r_51-r_52 >=0
            prob+= 2*r_2-r_1-r_0 >=0
            prob+= 3*r_49-r_50-r_51-r_52 >=0
            prob+= 3*r_3-r_2-r_1-r_0 >=0
        if constrain_params['min_consecutive_promo']==5:
            prob+=r_51-r_52 >=0
            prob+=r_1-r_0 >=0
            prob+=2*r_50-r_51-r_52 >=0
            prob+= 2*r_2-r_1-r_0 >=0
            prob+= 3*r_49-r_50-r_51-r_52 >=0
            prob+= 3*r_3-r_2-r_1-r_0 >=0
            prob+= 4*r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 4*r_4-r_3-r_2-r_1-r_0 >=0
        if constrain_params['min_consecutive_promo']==6:
            prob+=r_51-r_52 >=0
            prob+=r_1-r_0 >=0
            prob+=2*r_50-r_51-r_52 >=0
            prob+= 2*r_2-r_1-r_0 >=0
            prob+= 3*r_49-r_50-r_51-r_52 >=0
            prob+= 3*r_3-r_2-r_1-r_0 >=0
            prob+= 4*r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 4*r_4-r_3-r_2-r_1-r_0 >=0
            prob+= 5*r_47-r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 5*r_5-r_4-r_3-r_2-r_1-r_0 >=0
        if constrain_params['min_consecutive_promo']==7:
            prob+=r_51-r_52 >=0
            prob+=r_1-r_0 >=0
            prob+=2*r_50-r_51-r_52 >=0
            prob+= 2*r_2-r_1-r_0 >=0
            prob+= 3*r_49-r_50-r_51-r_52 >=0
            prob+= 3*r_3-r_2-r_1-r_0 >=0
            prob+= 4*r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 4*r_4-r_3-r_2-r_1-r_0 >=0
            prob+= 5*r_47-r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 5*r_5-r_4-r_3-r_2-r_1-r_0 >=0
            prob+= 6*r_46-r_47-r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 6*r_6-r_5-r_4-r_3-r_2-r_1-r_0 >=0
        if constrain_params['min_consecutive_promo']==8:
            prob+=r_51-r_52 >=0
            prob+=r_1-r_0 >=0
            prob+=2*r_50-r_51-r_52 >=0
            prob+= 2*r_2-r_1-r_0 >=0
            prob+= 3*r_49-r_50-r_51-r_52 >=0
            prob+= 3*r_3-r_2-r_1-r_0 >=0
            prob+= 4*r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 4*r_4-r_3-r_2-r_1-r_0 >=0
            prob+= 5*r_47-r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 5*r_5-r_4-r_3-r_2-r_1-r_0 >=0
            prob+= 6*r_46-r_47-r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 6*r_6-r_5-r_4-r_3-r_2-r_1-r_0 >=0
            prob+= 7*r_45-r_46-r_47-r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 7*r_7-r_6-r_5-r_4-r_3-r_2-r_1-r_0 >=0
        if constrain_params['min_consecutive_promo']==9:
            prob+=r_51-r_52 >=0
            prob+=r_1-r_0 >=0
            prob+=2*r_50-r_51-r_52 >=0
            prob+= 2*r_2-r_1-r_0 >=0
            prob+= 3*r_49-r_50-r_51-r_52 >=0
            prob+= 3*r_3-r_2-r_1-r_0 >=0
            prob+= 4*r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 4*r_4-r_3-r_2-r_1-r_0 >=0
            prob+= 5*r_47-r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 5*r_5-r_4-r_3-r_2-r_1-r_0 >=0
            prob+= 6*r_46-r_47-r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 6*r_6-r_5-r_4-r_3-r_2-r_1-r_0 >=0
            prob+= 7*r_45-r_46-r_47-r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 7*r_7-r_6-r_5-r_4-r_3-r_2-r_1-r_0 >=0
            prob+= 8*r_44-r_45-r_46-r_47-r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 8*r_8-r_7-r_6-r_5-r_4-r_3-r_2-r_1-r_0 >=0
 
  # contraint for max promo length
    if config_constrain['max_consecutive_promo']:
        for k in range(0,52-constrain_params['max_consecutive_promo']):
            for j in range(1,promo_loop):
                prob+=lpSum([wk_vars[required_base['WK_ID'][i+j*52]] for i in range(k,\
                k+constrain_params['max_consecutive_promo']+1)])<=constrain_params['max_consecutive_promo']
        for k in range(0,52-constrain_params['max_consecutive_promo']):  
            prob+=lpSum([wk_vars[required_base['WK_ID'][i+j*52]] for i in range(k,\
                k+constrain_params['max_consecutive_promo']+1) for j in range(1,promo_loop)])\
                <=constrain_params['max_consecutive_promo']
  
  # Constrain for min promo wave length
    if(config_constrain['min_consecutive_promo']):
        for k in range(0,52-constrain_params['min_consecutive_promo']): ##(0,50)
            r_1_sum = lpSum([wk_vars[required_base['WK_ID'][i+j*52]] for i in range(k+1,\
                    min(k+constrain_params['min_consecutive_promo']+1,52)) for j in range(1,promo_loop)])
            r_2_sum = lpSum([wk_vars[required_base['WK_ID'][k+j*52]] for j in range(1,promo_loop)])
            r_3_sum = lpSum([wk_vars[required_base['WK_ID'][k+1+j*52]] for j in range(1,promo_loop)])
            gap_weeks = len(range(k+1, min(52, k+constrain_params['min_consecutive_promo']+1)))
            prob+= r_1_sum + gap_weeks * r_2_sum >= gap_weeks * r_3_sum

        for k in range(0,52-constrain_params['min_consecutive_promo']): ##(0,50)
            for j in range(1,promo_loop):
                r_1_sum = lpSum([wk_vars[required_base['WK_ID'][i+j*52]] for i in range(k+1,\
                        min(k+constrain_params['min_consecutive_promo']+1,52))])
                r_2_sum = lpSum([wk_vars[required_base['WK_ID'][k+j*52]] ])
                r_3_sum = lpSum([wk_vars[required_base['WK_ID'][k+1+j*52]]])
                gap_weeks = len(range(k+1, min(52, k+constrain_params['min_consecutive_promo']+1)))
                prob+= r_1_sum + gap_weeks * r_2_sum >= gap_weeks * r_3_sum
 
  #  week gap constraint
    if(config_constrain['promo_gap']):
        for k in range(0,51):
            r_1_sum = lpSum([wk_vars[required_base['WK_ID'][i+j*52]] for j in range(0,1) for i in range(k+1,\
                    min(k+constrain_params['promo_gap']+1,52))])
            r_2_sum= lpSum([wk_vars[required_base['WK_ID'][k+j*52]] for j in range(0,1)])
            r_3_sum = lpSum([wk_vars[required_base['WK_ID'][k+1+j*52]] for j in range(0,1)])
            gap_weeks = len(range(k+1, min(52, k+constrain_params['promo_gap']+1)))
            prob+= r_1_sum + gap_weeks * r_2_sum >= gap_weeks * r_3_sum

    # max seconds to execute optimizer is 1min
    # prob.solve(PULP_CBC_CMD(msg=False,maxSeconds=60))
    prob.solve(PULP_CBC_CMD(msg=False,timeLimit=60))
    weeks =[]
    value = []

  # creating the optimal calendar from the prob output
    for variable in prob.variables():
        if variable.varValue==1.0:
            weeks.append(str(variable.name))
            value.append(variable.varValue)

    _df= pd.DataFrame(list(zip(weeks, value)), 
                 columns =['Weeks', 'val']) 
  
    _df['Iteration']=_df["Weeks"].apply(lambda x: x.split('_')[3]).astype(int)
    _df['Week_no']=_df["Weeks"].apply(lambda x: x.split('_')[2]).astype(int)
    _df['Week_no']=(_df['Week_no']-((_df['Iteration']*52)-52))+1  ## Subtract 52 because iteration starts from 1
    _df = _df.sort_values('Week_no',ascending = True).reset_index(drop=True)
    col_req_base = list(required_base.columns) ## change
    list_tact = ['Iteration','TPR','Mechanic']+[i for i in col_req_base if "Tactic_Leaflet" in i ]\
                +[i for i in col_req_base if "Tactic_Display" in i ] ## change
    tprs = required_base[list_tact].drop_duplicates().reset_index(drop=True)
    _df = pd.merge(_df,tprs,on=['Iteration'],how='left')
    _df = _df.sort_values('Week_no',ascending = True).reset_index(drop=True)
    # getting the solution status and optimum value
    _df['Solution']=LpStatus[prob.status]

    return(_df,prob)


def master_func_4(baseline_data:pd.DataFrame, 
                  model_coeff:pd.DataFrame, 
                  _summary_df:pd.DataFrame,
                  ppg_item_num:pd.DataFrame,
                  config:pd.DataFrame,
                  joint_ppg_week_nums:pd.DataFrame)->pd.DataFrame:
    """master_func_4

    Args:
        baseline_data (pd.DataFrame): baseline_data
        model_coeff (pd.DataFrame): model_coeff
        _summary_df (pd.DataFrame): _summary_df
        ppg_item_num (pd.DataFrame): ppg_item_num
        config (pd.DataFrame): config
        joint_ppg_week_nums (pd.DataFrame): joint_ppg_week_nums

    Returns:
        pd.DataFrame: new df
    """
 
    df_new1 = get_dftactic_combo_4(_summary_df)
    required_base = get_required_base_3(baseline_data, model_coeff, df_new1, _summary_df, ppg_item_num)        
    required_base["week_val"] = required_base["WK_ID"].map(lambda x: int(x.split("_")[1])).astype(int)
    required_base["joint_promo_week"] = 0
    required_base["week_num"] = required_base["week_val"].map(lambda x: x%52)
    required_base.loc[(required_base["week_num"].isin(joint_ppg_week_nums)), "joint_promo_week"] = 1

    # creating a copy of config for ppgs with lag variables
    config_temp = copy.deepcopy(config)
    # increasing/decresing the limit to satisfy the actual constraints in ppgs with lag variables
    config_temp = opt_generic.config_lag_function(model_coeff, config_temp)
    # config and config temp will be same in all the ppgs without lag variables
    
    optimal_calendar, prob = optimizer_fun_4(baseline_data, 
                                            required_base, 
                                            config, 
                                            joint_ppg_week_nums) # optimal_calendar
    opt_pop_up_flag = 0
    if(optimal_calendar.shape[0]==52) and (optimal_calendar['Solution'].unique()=='Optimal'):
    #Default Run Based On User's Config Step 101
        opt_pop_up_flag = 1
 
    if opt_pop_up_flag==0: #optimal_calendar_fin.shape[0]!=52
        config_temp = config.copy()
        for _i in range(0, 5):
            config_temp['TE_threshold'] = config_temp['TE_threshold']+0.01 ## add steps of 0.01
      
            optimal_calendar, prob = optimizer_fun_4(baseline_data, 
                                                    required_base,
                                                    config_temp, 
                                                    joint_ppg_week_nums)
            if((optimal_calendar.shape[0]==52) and (optimal_calendar['Solution'].unique()=='Optimal')):
                opt_pop_up_flag = 1

                break

    if (optimal_calendar.shape[0]!=52) or (optimal_calendar['Solution'].unique()!='Optimal'):
        optimal_calendar = pd.DataFrame()
        optimal_calendar['TPR']=baseline_data['TPR']
        optimal_calendar['Mechanic'] = ''
        baseline_tactic_cols = [x for x in baseline_data.columns if (("Tactic_" in x) and 
                                                            ("Tactic_JP_" not in x) and 
                                                            ("Tactic_WHI" not in x))]
        col_dict = {"Tactic_Medium_HZ": "Leaflet_flag", "Tactic_Medium_TZ": "Newsletter_flag", 
                    "Tactic_Advertising_without_price": "Advertising_without_price_flag", 
                    "Tactic_Bonus": "Bonus_flag", "Tactic_Coupon": "Coupon_flag",
                    "Tactic_EDLP": "EDLP_flag","Tactic_PAS": "Pas_flag","Tactic_Multibuy": "Multibuy_flag"}                 
        tactic_cols = list(col_dict.keys())
        for col in tactic_cols:
            if col in baseline_tactic_cols:
                optimal_calendar[col] = baseline_data[col]
            else:
                optimal_calendar[col] = 0
        optimal_calendar = optimal_calendar.rename(columns=col_dict)
    return optimal_calendar, prob, df_new1,required_base

def optimal_summary_fun_4(baseline_data, model_coeff, opt_calendar_tactics): #optimal_calendar
    model_cols = model_coeff['names'].to_list()
    model_cols.remove('Intercept')
    model_cols = [col.lower() if col != 'TPR' else col for col in model_cols]
    base = baseline_data[["Date", 'Retailer', 'wk_base_price_perunit_byppg', 'Promo',
                          'GSV_Per_Unit_Future', 'COGS_Per_Unit_Future', 'NSV_Per_Unit_Future'] + model_cols] ## change added 0902 
    new_data = base.copy()
    new_data = new_data.reset_index(drop=True)
    
    col_stg1_new_dict = {"Multipu_tpr_flag": "flag_promotype_multibuy_tpr", "Leaflet_flag": "promo_present", 
                         "Multibuy_flag": "flag_promotype_multibuy",                         
                         "Lottery_flag": "flag_promotype_lottery", "Unpublished_flag" : "flag_promotype_unpublished",
                         "TPR_flag": "flag_promotype_tpr", "miscellaneous_flag": "flag_promotype_miscellaneous", 
                        }
    opt_calendar_tactics = opt_calendar_tactics.rename(columns=col_stg1_new_dict)
    new_data["TPR"] = opt_calendar_tactics["TPR"]
    new_data["Mechanic"] = opt_calendar_tactics["Mechanic"]
    flag_cols = [x for x in new_data.columns if ('Flag_promotype' in x) and ("Flag_promotype_joint_promo" not in x)]
    for col in flag_cols:
        if col in opt_calendar_tactics.columns:
            new_data[col] = opt_calendar_tactics[col]

    # changing the variable that related to promotion
    # lag variable creation
    if 'TPR_lag1' in model_cols:
        new_data['TPR_lag1'] = new_data['TPR'].shift(1).fillna(0)
    if 'TPR_lag2' in model_cols:
        new_data['TPR_lag2'] = new_data['TPR'].shift(2).fillna(0)

    new_data['Promo Price'] = (new_data['wk_base_price_perunit_byppg'] - 
                               (new_data['wk_base_price_perunit_byppg'] * (new_data['TPR']/100)))

    new_data['wk_sold_avg_price_byppg'] = new_data['Promo Price'] ## needed change?
    new_data['Units'] = opt_generic.predict_sales(model_coeff, new_data)
    new_data['Sales'] = new_data['Units'] * new_data['Promo Price']
    new_data["GSV"] = new_data['Units'] * new_data['GSV_Per_Unit_Future']
    new_data["NSV"] = new_data['Units'] * new_data["NSV_Per_Unit_Future"]

    new_data["Trade_Expense"] = new_data["GSV"] - new_data["NSV"]
    new_data["Trade_Expense_onPromo"] = np.where(new_data['TPR'] > 0, 
                                                 new_data["GSV"] - new_data["NSV"], 0) ## change added new ROI

    new_data["MAC"] = new_data["NSV"] - (new_data['Units'] * new_data['COGS_Per_Unit_Future'])
    new_data["RP"] = new_data['Sales'] - new_data["NSV"]
    
    new_data_1 = new_data[['Date', 'Sales', "GSV", "Trade_Expense", "Trade_Expense_onPromo", 
                           "NSV", "MAC", "RP", "Units"]]
    
    col_stg1_new_rev_dict = {v: k for k, v in col_stg1_new_dict.items()}
    new_data = new_data.rename(columns=col_stg1_new_rev_dict)
    return(new_data_1, new_data)

def optimal_summary_fun_3(baseline_data, model_coeff): #optimal_calendar
    model_cols = model_coeff['names'].to_list()
    model_cols.remove('Intercept')
    model_cols = [col.lower() if col != 'TPR' else col for col in model_cols]
    # selecting the required columns
    base = baseline_data[["Date", 'Retailer', 'wk_base_price_perunit_byppg', 'Promo',
                          'GSV_Per_Unit_Future', 'COGS_Per_Unit_Future', 'NSV_Per_Unit_Future'] + model_cols] ## change added 0902 
    new_data = base.copy()

    # changing the variable that related to promotion
    # lag variable creation
    if 'TPR_lag1' in model_cols:
        new_data['TPR_lag1'] = new_data['TPR'].shift(1).fillna(0)
    if 'TPR_lag2' in model_cols:
        new_data['TPR_lag2'] = new_data['TPR'].shift(2).fillna(0)

    new_data['Promo Price'] = (new_data['wk_base_price_perunit_byppg'] - 
                               (new_data['wk_base_price_perunit_byppg'] * (new_data['TPR']/100)))

    new_data['wk_sold_avg_price_byppg'] = new_data['Promo Price'] ## needed change?

    new_data['Units'] = opt_generic.predict_sales(model_coeff, new_data)
    new_data['Sales'] = new_data['Units'] * new_data['Promo Price']
    new_data["GSV"] = new_data['Units'] * new_data['GSV_Per_Unit_Future']
    new_data["NSV"] = new_data['Units'] * new_data["NSV_Per_Unit_Future"]

    new_data["Trade_Expense"] = new_data["GSV"] - new_data["NSV"]
    new_data["Trade_Expense_onPromo"] = np.where(new_data['TPR'] > 0, 
                                                 new_data["GSV"] - new_data["NSV"], 0) ## change added new ROI

    new_data["MAC"] = (new_data["GSV_Per_Unit_Future"] - 
                       (((new_data["Trade_Expense"]/new_data["GSV"]) + (new_data["TPR"]/100)) * new_data["GSV_Per_Unit_Future"]) - 
                       new_data["COGS_Per_Unit_Future"]) * new_data["Units"]
    new_data["RP"] = new_data['Sales'] - new_data["NSV"]

    new_data_1 = new_data[['Date', 'Sales', "GSV", "Trade_Expense", "Trade_Expense_onPromo", 
                           "NSV", "MAC", "RP", "Units"]]
    
    return(new_data_1, new_data)


def process_single_promo(constraints:dict)->pd.DataFrame:
    """process_single_promo

    Args:
        constraints (dict): dict consists of dataframe

    Returns:
        pd.DataFrame: dataframe
    """
    
    item_map = constraints.get('item_map_df')
    item_map["PPG"] = item_map["PPG"]
    summary_df = constraints.get('summary_df')
    
    model_data_all = constraints.get('mdl_df')
    model_coeff = constraints.get('coeff_df')
    coeff_mapping = constraints.get('coeff_mapping_df')
    roi_data=constraints.get('roi_df')
    cols = ['Retailer', 'PPG', 'Ret_idx','PPG_idx','Type_of_Promo'\
        ,'maxWeek', 'minWeek']
    # one_shot_ppg_df = summary_df.loc[(summary_df["PPG_Type"]=="One Shot")]
    # breakpoint()
    one_shot_ppg_df = pd.DataFrame(columns=summary_df.columns)
    exclude_ppgs_list = []
    if one_shot_ppg_df.shape[0]:
        one_shot_ppg_ids = list(one_shot_ppg_df["PPG"].unique())
        exclude_ppgs_list += one_shot_ppg_ids
        print(f"exclude_ppg_list: {exclude_ppgs_list}")
    
    summary_df = summary_df.loc[(~summary_df["PPG"].isin(exclude_ppgs_list))].reset_index(drop=True)
    
    ret_ppg_id_new = summary_df[cols]
    ret_ppg_id_new = ret_ppg_id_new.drop_duplicates(subset=cols).reset_index(drop=True)

    ret_ppg_id_new["PPG_Item_No"] = None
    ret_ppg_id_new["Promo_Cat"] = None
    
    for ind in range(ret_ppg_id_new.shape[0]):
        type_of_promo = ret_ppg_id_new.loc[ind, "Type_of_Promo"] 
        retailer = ret_ppg_id_new.loc[ind, "Retailer"]
        ppg_name = ret_ppg_id_new.loc[ind, "PPG"].replace('&',' ').replace(',',' ').replace(' ','_')

        if type_of_promo.lower() == 'single':     
            ppg_item_no = item_map.loc[(item_map["Retailer"]==retailer) \
            & (item_map["PPG"]==ppg_name), "PPG_Item_No"].values[0] 
            ret_ppg_id_new.loc[ind, "PPG_Item_No"] = ppg_item_no
        elif type_of_promo.lower()  == 'joint' or  type_of_promo.lower()  == 'all_brand':
            if "all_brand" in ppg_name:
                ret_ppg_id_new.loc[ind, "Promo_Cat"] = "all_brand"
            ppg_name = ppg_name.replace('-all_brand','')
            ppg_name = ppg_name.split('_-_')
            joint_ppgs = []
            joint_ppg_items = []
            for ppg_name in ppg_name:
                joint_ppgs.append(ppg_name)
                ppg_item_no = item_map.loc[(item_map["Retailer"]==retailer) & 
                                        (item_map["PPG"]==ppg_name), "PPG_Item_No"].values[0]
                joint_ppg_items.append(ppg_item_no)
                
            joint_ppg_name = ",".join(joint_ppgs)
            ret_ppg_id_new.loc[ind, "PPG"] = joint_ppg_name
            joint_ppg_item_no = ",".join(joint_ppg_items)
            ret_ppg_id_new.loc[ind, "PPG_Item_No"] = joint_ppg_item_no
    
    ret_ppg_id_new = ret_ppg_id_new.drop_duplicates(subset=["Retailer", "PPG", "Ret_idx", 
                                                            "PPG_idx", "PPG_Item_No"]).reset_index(drop=True)
    ret_ppg_id_new.loc[ret_ppg_id_new["Promo_Cat"]=="all_brand", 
                   "PPG"] = ret_ppg_id_new.loc[ret_ppg_id_new["Promo_Cat"]=="all_brand", 
                                                    "PPG"] + ",all_brand"
    ret_ppg_id_new.loc[ret_ppg_id_new["Promo_Cat"]=="all_brand", 
                   "PPG_Item_No"] = ret_ppg_id_new.loc[ret_ppg_id_new["Promo_Cat"]=="all_brand", 
                                                       "PPG_Item_No"] + ",all_brand"
    summary_df_new = summary_df.merge(ret_ppg_id_new[['Ret_idx','PPG_idx']],on=['Ret_idx','PPG_idx'],
                                how='left')
    summary_df_new_1 = summary_df_new.merge(ret_ppg_id_new[['Ret_idx','PPG_idx','PPG_Item_No']],
                                        on=['Ret_idx','PPG_idx'],
                                        how='left')

    ### To get avg tpr values in df_new2

    item_map1 = item_map.copy()
    item_map1.rename(columns={'PPG':'PPG_Own'},inplace=True)
    model_data_all["PPG"] = model_data_all["PPG"]
    roi_data["PPG"] = roi_data["PPG"]
  # breakpoint #vinilbp()
    coeff_mapping["PPG"] = coeff_mapping["PPG"]
    coeff_mapping = coeff_mapping.merge(item_map1[['Retailer','PPG_Own']],left_on=['Retailer','PPG'],
                        right_on=['Retailer','PPG_Own'],how='left')
    coeff_mapping.drop(['PPG_Own'],axis=1,inplace=True)
    df_promo_weeks=constraints.get('df_promo_weeks')
    ret_ppg_to_slct = model_data_all[["Retailer", "PPG", "Segment"]].drop_duplicates()
    ## Removing this beacuse TPR 0 
    df_tpr_0 = model_data_all.groupby(['Retailer','PPG','Segment']).agg({'TPR':'sum'}).reset_index()
    df_tpr_0 = df_tpr_0[df_tpr_0['TPR']==0].reset_index(drop=True)
    df_tpr_0 = df_tpr_0[['Retailer','PPG','Segment']]
    df_tpr_0['flag_rem'] = 1

    ret_ppg_df = ret_ppg_to_slct.copy()
    ret_ppg_df = ret_ppg_df.reset_index(drop=True)
    ret_ppg_df = ret_ppg_df.merge(df_tpr_0,on=['Retailer','PPG','Segment'],how='left')
    ret_ppg_df = ret_ppg_df[ret_ppg_df['flag_rem'].isnull()].reset_index(drop=True)
    ret_ppg_df = ret_ppg_df[['Retailer','PPG','Segment']]
    ret_ppg_df1 = ret_ppg_df.copy()
    ret_ppg_df1['flag_keep'] = 1
    ret_ppg_df1.head()


    final_pred_data_all = pd.DataFrame()
    baseline_data_all = pd.DataFrame()
    model_coeff_all = pd.DataFrame()
    median_acv_df = model_data_all.groupby(["Retailer", "PPG"], as_index=False).agg(ACV_median=("acv_selling", np.median))
    
    for i in range(ret_ppg_df.shape[0]):
        slct_retailer = ret_ppg_df['Retailer'][i]
        slct_ppg = ret_ppg_df['PPG'][i]
        slct_segment = ret_ppg_df['Segment'][i]
        coeff_mapping_temp = coeff_mapping.loc[(coeff_mapping['Retailer']==slct_retailer) & 
                                            (coeff_mapping['PPG']==slct_ppg)]
        col_dict = dict(zip(coeff_mapping_temp['Coefficient_new'], coeff_mapping_temp['Coefficient']))
        col_dict_2 = dict(zip(coeff_mapping_temp['Coefficient'], coeff_mapping_temp['Coefficient_new']))
        if "Intercept" in col_dict_2.keys():
            col_dict_2.pop('Intercept')
        if "Intercept" in col_dict.keys():
            col_dict.pop('Intercept')

        # getting idvs present for the retailer, ppg
        idvs = [x.lower() for x in coeff_mapping_temp['Coefficient_new'].tolist()]
        if "intercept" in idvs:
            idvs.remove('intercept')
        if "tpr_discount_byppg" in idvs:
            index = idvs.index('tpr_discount_byppg')
            idvs[index] = 'TPR'
        model_data = model_data_all.loc[(model_data_all['Retailer']==slct_retailer) & 
                                        (model_data_all['PPG']==slct_ppg)].reset_index(drop=True)
        ### Replacing Baseline TPRs with AVG tpr value
        # avg_tpr_replace = round(model_data[model_data['Flag_promotype_Leaflet']!=0]['TPR'].mean())
        # model_data['TPR'] = np.where(model_data['Flag_promotype_Leaflet']>0, avg_tpr_replace, model_data['TPR'])
        no_leaflet_flag = False
        num_promo_weeks = model_data[model_data['promo_present']!=0].shape[0]
        is_leaflet_present = len([1 for x in coeff_mapping_temp['Coefficient_new'].unique() if 'promo_present' in x])

        if (num_promo_weeks == 0) or (is_leaflet_present == 0):
            avg_tpr_replace = 0
            if "promo_present" not in coeff_mapping_temp["Coefficient_new"].to_list():
      
                no_leaflet_flag = True
        else:
            avg_tpr_replace = round(model_data[model_data['promo_present']!=0]['TPR'].mean()) ## Leaflet promo change

        model_data['TPR'] = np.where(model_data['promo_present']>0, avg_tpr_replace, model_data['TPR'])
        model_data = model_data.merge(median_acv_df, on=["Retailer", "PPG"])
        model_data["ACV_Selling"] = model_data["ACV_median"]

        # Replace Median_Base_Price_log with 4 weeks rolling mean
        # model_data["Median_Base_Price_log"] = model_data["MBP_log_rolling_mean"]
        # model_data["TPR_10_above"] = np.where(model_data["Flag_promotype_Leaflet"]>0, model_data["TPR"], 0)
        # model_data["TPR_5_10"] = 0
        # model_data["tpr_discount_byppg_2019"] = 0 
        model_data = model_data[['Date','Retailer'] +  [ i for i in idvs if i in model_data.columns]]   
        model_data.rename(columns=col_dict,inplace=True)
        model_data.rename(columns={"tpr_discount_byppg": "TPR"}, inplace=True)
        # getting model coefficients values with original names and format 
      # breakpoint #vinilbp()
        model_coeff = coeff_mapping_temp[['Coefficient','Value','PPG','Retailer'\
                    ,'Coefficient_new','PPG_Item']]
        model_coeff.rename(columns={'Value':'model_coefficients',
                                    'Coefficient_new':'names'},inplace=True)
        promo_list_ppg = roi_data[(roi_data['Retailer']\
             == slct_retailer) & (roi_data['PPG'] == slct_ppg)]\
            .reset_index(drop=True)
        period_data=promo_list_ppg[['Date','GSV_Per_Unit_Future',
                                    'NSV_Per_Unit_Future','COGS_Per_Unit_Future']] ## change added 0902

        model_coeff_list_keep=list(model_coeff['names'])
        if "Intercept" in model_coeff_list_keep:
            model_coeff_list_keep.remove('Intercept')
        model_coeff.loc[model_coeff["names"]=="tpr_discount_byppg", "names"] = "TPR"
        period_data.loc[:, 'Date']=pd.to_datetime(period_data['Date'], format='%Y-%m-%d')

        model_data.loc[:, 'Date']=pd.to_datetime(model_data['Date'], format='%Y-%m-%d')
        # breakpoint() #vinilbp()
        final_pred_data=pd.merge(period_data,model_data,how="left",on="Date")
        final_pred_data['wk_base_price_perunit_byppg'] = np.exp(final_pred_data\
                                        ['wk_sold_median_base_price_byppg_log'])-1

        final_pred_data['Promo'] = np.where(final_pred_data['TPR'] == 0\
            , final_pred_data['wk_base_price_perunit_byppg'],
            final_pred_data['wk_base_price_perunit_byppg'] \
            * (1-(final_pred_data['TPR']/100)))

        final_pred_data['wk_sold_avg_price_byppg'] = final_pred_data['Promo']
    

        if 'TPR_lag1' in model_coeff_list_keep:
            final_pred_data['TPR_lag1']= final_pred_data['TPR'].shift(1).fillna(0)
        if 'TPR_lag2' in model_coeff_list_keep:
            final_pred_data['TPR_lag2']= final_pred_data['TPR'].shift(2).fillna(0)


        final_pred_data['Baseline_Prediction']=opt_generic.predict_sales(model_coeff,final_pred_data)
        final_pred_data['Baseline_Sales']=final_pred_data['Baseline_Prediction']\
                                            *final_pred_data['Promo']
        final_pred_data["Baseline_GSV"] = final_pred_data['Baseline_Prediction']\
                                        * final_pred_data['GSV_Per_Unit_Future']
        final_pred_data["Baseline_NSV"] = final_pred_data['Baseline_Prediction']\
                                        * final_pred_data['NSV_Per_Unit_Future']
        ## Only in promo weeks
        final_pred_data["Baseline_Trade_Expense"] = final_pred_data["Baseline_GSV"]\
                                                    - final_pred_data["Baseline_NSV"]
        # Tactic_Medium_HZ
        if no_leaflet_flag:
            final_pred_data["Baseline_Trade_Expense_onPromo"] = 0
        else:
            final_pred_data["Baseline_Trade_Expense_onPromo"] \
                = np.where(final_pred_data['Tactic_Medium_HZ']>0, 
                final_pred_data["Baseline_GSV"] - final_pred_data["Baseline_NSV"],
                0) ## Leaflet promo change

        final_pred_data["Baseline_MAC"] = final_pred_data["Baseline_NSV"]\
            -(final_pred_data['Baseline_Prediction'] * final_pred_data['COGS_Per_Unit_Future'])
        final_pred_data["Baseline_RP"] = final_pred_data['Baseline_Sales']\
                                        -final_pred_data["Baseline_NSV"]
        final_pred_data['Retailer'] = slct_retailer
        final_pred_data['PPG'] = slct_ppg
        final_pred_data['Segment'] = slct_segment
        final_pred_data['Mechanic'] = ''

        baseline_data = final_pred_data.copy()
        baseline_data_all=baseline_data_all.append(baseline_data)
        final_pred_data_all=final_pred_data_all.append(final_pred_data)
        model_coeff_all = model_coeff_all.append(model_coeff)
 
    config_default_3 = {
        "Retailer":'Default',"PPG":'Default','Segment':'Default','TE_threshold':1,
        'Objective_metric': "MAC", "Objective":"Maximize",
        'config_constrain':{
            'MAC': False,'RP': False,'Trade_Expense': False,'Units': False, "NSV": False\
            , "GSV": False,
            "Sales":False,'MAC_Perc':False,
            "RP_Perc":False,'min_consecutive_promo':True,'max_consecutive_promo':True,
            'promo_gap':True,'tot_promo_min':False,'tot_promo_max':False,
            'promo_price':False,'automation':False,'52Weeks':True
        },
        'constrain_params': {
            'MAC':1,'RP':1,'Trade_Expense':1,'Units':1,'NSV':1,
            'GSV':1,'Sales':1,'MAC_Perc':1,'RP_Perc':1,
            'min_consecutive_promo':1,'max_consecutive_promo':1,
            'promo_gap':2,'tot_promo_min':2,'tot_promo_max':20,'compul_no_promo_weeks':[],
            'compul_promo_weeks' :[],'promo_price':0
        }
    } 
    df_only_normal_new = ret_ppg_id_new.copy()
    df_only_normal_new = df_only_normal_new[["Retailer", "PPG", "Ret_idx", "PPG_idx", "PPG_Item_No", "Promo_Cat"]]
    df_only_normal_new.loc[:, ["PPG"]] = df_only_normal_new["PPG"].map(lambda x: x.split(",all_brand")[0])
    df_only_normal_new.loc[:, ["PPG_Item_No"]] = df_only_normal_new["PPG_Item_No"].map(lambda x: x.split(",all_brand")[0])
    df_only_normal_new["num_ppgs"] = df_only_normal_new["PPG"].map(lambda x: len(x.split("-")))

    temp_joint_ppg_item_list = df_only_normal_new.loc[df_only_normal_new["num_ppgs"]>\
                                1, "PPG_Item_No"].to_list()
    joint_ppg_list = []
    for ppg in temp_joint_ppg_item_list:
        temp_ppg_list = ppg.split(",")
        for temp_ppg in temp_ppg_list:
            if temp_ppg not in joint_ppg_list:
                joint_ppg_list.append(temp_ppg)

    df_only_normal_new_single = df_only_normal_new.loc[(df_only_normal_new["num_ppgs"]==1) & (df_only_normal_new['Promo_Cat']!='all_brand')]\
                        .reset_index(drop=True)
    df_only_normal_new_single["TPR_list"] = -1
    df_only_normal_new_single["TPR_Mech"] = -1
    
    temp_model_coeff_all = model_coeff_all.copy()
    temp_model_coeff_all["PPG"] = temp_model_coeff_all["PPG"]

    temp_baseline_data_all = baseline_data_all.copy()
    temp_baseline_data_all["PPG"] = temp_baseline_data_all["PPG"]

    opt_summary_filtered_final = pd.DataFrame()
    opt_sum_filtered_final = pd.DataFrame()
    opt_sum_filtered = pd.DataFrame()
    for i in range(df_only_normal_new_single.shape[0]):
        joint_ppg_week_nums = []
        joint_ppg_week_nums_promo_gap = [] 
        slct_retailer = df_only_normal_new_single['Retailer'][i]
        slct_ppg = df_only_normal_new_single['PPG'][i].replace('&',' ').replace(',',' ') 
        filtered_data = list(filter(partial(opt_generic.handle_joint_and_all_brand_ppg\
                                            ,acc_name=slct_retailer\
                                            ,ppg=slct_ppg,
                                            top='single'
                                            )
                        ,constraints.get('input_data')['data']))
        try:
            config_default_3 = opt_generic.update_params(config_default_3,
                                            filtered_data[0],
                                            constraints.get('input_data')['no_of_leaflet'],
                                            constraints.get('input_data')['min_length_gap'],
                                            constraints.get('input_data')['no_of_promo'],
                                            )
        except:
            breakpoint()
        tpr_list = config_default_3['MARS_TPRS']
        tpr_mech = config_default_3['TPR_Mech']
        tpr_list = ",".join([str(x) for x in tpr_list])
        tpr_mech = ",".join([str(x) for x in tpr_mech])

         
        df_only_normal_new_single.loc[(df_only_normal_new_single["Retailer"]==slct_retailer) & 
                        (df_only_normal_new_single["PPG"]==slct_ppg) , "TPR_list"] = tpr_list
        df_only_normal_new_single.loc[(df_only_normal_new_single["Retailer"]==slct_retailer) & 
                        (df_only_normal_new_single["PPG"]==slct_ppg) , "TPR_Mech"] = tpr_mech
        ppg_item_num = df_only_normal_new_single["PPG_Item_No"][i]
      # breakpoint #vinilbp()
        baseline_data = temp_baseline_data_all[(temp_baseline_data_all['Retailer']\
            ==slct_retailer) & (temp_baseline_data_all['PPG']==slct_ppg.replace(' ','_').replace('&','_').replace(',','_'))] 
        model_coeff = temp_model_coeff_all[(temp_model_coeff_all['Retailer']==slct_retailer) & 
                                        (temp_model_coeff_all['PPG']==slct_ppg.replace(' ','_').replace('&','_').replace(',','_'))]   
        ## Passing Optimal weeks & Optimal TE from stage 1 output
        _summary_df = summary_df_new_1[(summary_df_new_1['Retailer']==slct_retailer) & 
                                (summary_df_new_1['PPG'].str.replace('&',' ').str.replace(',',' ')==slct_ppg)].reset_index(drop=True) 

        tpr_list = df_only_normal_new_single.loc[i, "TPR_list"]
        tpr_mech = df_only_normal_new_single.loc[i, "TPR_Mech"]
        _summary_df['Mechanic']=''
    
        if tpr_list and tpr_list != -1:
            tpr_list = tpr_list.split(",")
            tpr_mech = tpr_mech.split(",")
            tpr_list = [int(x) for x in tpr_list]
            promo_ind = _summary_df.loc[_summary_df["Promo_Type"]==2].index[-1]
            num_rows_before = _summary_df.shape[0]
            for tpr_ind, tpr_val in enumerate(tpr_list):
                new_ind = num_rows_before + tpr_ind
                tpr_mech_val = tpr_mech[tpr_ind]
                _summary_df.loc[new_ind, :] = _summary_df.loc[promo_ind, :]
                _summary_df.loc[new_ind, "Promo_Type"] = _summary_df.loc[(num_rows_before-1)\
                                                        , "Promo_Type"] + tpr_ind + 1        
                _summary_df.loc[new_ind, "TPR"] = tpr_val 
                _summary_df.loc[new_ind, "Mechanic"] = tpr_mech_val
        
        exclude_dates=[]
        ## Passing utilized week_numbers from stage 2A output     
        joint_all_brand_ppg_list = joint_ppg_list
        joint_all_brand_ppg_list = list(np.unique(joint_all_brand_ppg_list))
        if ppg_item_num in joint_ppg_list:
            temp_joint_all_brand_ppg_list = temp_joint_ppg_item_list
            for joint_ppg_item_val in temp_joint_all_brand_ppg_list:
                if ppg_item_num in joint_ppg_item_val:
                    temp_df_promo_weeks = df_promo_weeks.loc[(df_promo_weeks["PPG_No"]==joint_ppg_item_val) |
                                                             (df_promo_weeks["PPG_No"]==ppg_item_num)].reset_index()
                    temp_df_promo_weeks = temp_df_promo_weeks.loc[temp_df_promo_weeks['PPG_MAIN']==opt_generic.format_ppg(slct_ppg)]
                    temp_df_promo_weeks = temp_df_promo_weeks.rename(columns={"index": "week_num"})
                    # *Change*
                    temp_df_promo_weeks["promo_mask"] = False
                    promo_mask = temp_df_promo_weeks["promo_mask"]
                    for promo_week_col in temp_df_promo_weeks.columns:
                        if ("_flag" in promo_week_col) or ("TPR" == promo_week_col):
                            promo_mask |= (temp_df_promo_weeks[promo_week_col] > 0)
                    temp_joint_ppg_week_nums = temp_df_promo_weeks.loc[promo_mask, "week_num"].unique()
                    temp_joint_ppg_week_nums = [(x%52) for x in temp_joint_ppg_week_nums] # *change*
                    joint_ppg_week_nums.append(temp_joint_ppg_week_nums)

            joint_ppg_week_nums = [y for x in joint_ppg_week_nums for y in x]
            joint_ppg_week_nums = np.unique(joint_ppg_week_nums)
            
            promo_gap_val = config_default_3["constrain_params"]["promo_gap"]
            if promo_gap_val < 0:
                promo_gap_val = 1            
            for joint_ppg_week_num_val in joint_ppg_week_nums:
                temp_dates = []
                for val in range(1, promo_gap_val+1):
                    temp_prev_dates = joint_ppg_week_num_val - val
                    temp_next_dates = joint_ppg_week_num_val + val
                    temp_dates.append([temp_prev_dates, temp_next_dates])
                temp_dates = [y for x in temp_dates for y in x]
                temp_dates.append(joint_ppg_week_num_val)
                temp_dates.sort()
                exclude_dates.append(temp_dates)

            exclude_dates = np.unique([y for x in exclude_dates for y in x])# if y!=52])
            exclude_dates = [x for x in exclude_dates if (x>=0) and (x<52)]
      # breakpoint #vinilbp()
        optimal_calendar1,prob,*_ = master_func_4(baseline_data, 
                                            model_coeff, 
                                            _summary_df, 
                                            ppg_item_num, 
                                            config_default_3, 
                                            exclude_dates)

        opt_summary_filtered, opt_summary_all = optimal_summary_fun_3(baseline_data, 
                                                                      model_coeff)
        opt_summary_filtered = opt_summary_filtered.reset_index(drop=True)
        opt_summary_all = opt_summary_all.reset_index(drop=True)    
        flag_cols = [x for x in _summary_df.columns if "_flag" in x]
        flag_cols = flag_cols + ["Promo_Type"]
        stg1_tactics_df = _summary_df[flag_cols]
        stg1_tactics_df = stg1_tactics_df.rename(columns={"Promo_Type": "Iteration"})
        if LpStatus[prob.status]!="Optimal":
            opt_summary_filtered.loc[:, "TPR"] = optimal_calendar1["TPR"]
            opt_summary_filtered.loc[:, "Mechanic"] = optimal_calendar1["Mechanic"]
            for col in flag_cols[:-1]:
                opt_summary_filtered[col] = optimal_calendar1[col]            
        else:   
            opt_summary_filtered.loc[:, "TPR"] = optimal_calendar1["TPR"]
            opt_summary_filtered.loc[:, "Mechanic"] = optimal_calendar1["Mechanic"]
            opt_calendar_tactics = (optimal_calendar1.merge(right=stg1_tactics_df, 
                                    on=["Iteration"]).sort_values(by="Week_no").reset_index(drop=True))
            _, opt_sum_filtered = optimal_summary_fun_4(baseline_data, model_coeff, opt_calendar_tactics)
            opt_sum_filtered = opt_sum_filtered.reset_index(drop=True)
            for col in flag_cols[:-1]:
                opt_summary_filtered[col] = opt_calendar_tactics[col]
                opt_sum_filtered[col] = opt_calendar_tactics[col]    
            opt_sum_filtered['PPG_No'] = ppg_item_num
            opt_sum_filtered['Retailer'] = slct_retailer
            opt_sum_filtered['PPG'] = opt_generic.format_ppg(slct_ppg)
            opt_sum_filtered["PPG_MAIN"] = opt_generic.format_ppg(slct_ppg)
            opt_sum_filtered["Brand"] = opt_generic.format_ppg(slct_ppg).split(" ",maxsplit=1)[0]
            opt_sum_filtered["Week"] = opt_sum_filtered.groupby(["Retailer",'PPG']).cumcount()+1
            opt_sum_filtered["IS_ALL_BRAND"] = config_default_3.get('is_all_brand',False)
            opt_sum_filtered["TYPE_OF_PROMO"] = "single"
            opt_sum_filtered['status'] = optimal_calendar1['Solution'].unique()[0]
            opt_sum_filtered_final = opt_sum_filtered_final.append(opt_sum_filtered)
        
        promo_tprs = optimal_calendar1.loc[optimal_calendar1["TPR"]>0, "TPR"].values
        num_promo_weeks = len(promo_tprs)
        promo_tprs = np.unique(promo_tprs)  
        opt_summary_filtered['PPG_No'] = ppg_item_num
        opt_summary_filtered['Retailer'] = slct_retailer
        opt_summary_filtered['PPG'] = opt_generic.format_ppg(slct_ppg)
        opt_summary_filtered["PPG_MAIN"] = opt_generic.format_ppg(slct_ppg)
        opt_summary_filtered["Brand"] = opt_generic.format_ppg(slct_ppg).split(" ",maxsplit=1)[0]
        opt_summary_filtered["Week"] = opt_summary_filtered.groupby(["Retailer",'PPG']).cumcount()+1
        opt_summary_filtered["IS_ALL_BRAND"] = config_default_3.get('is_all_brand',False)
        opt_summary_filtered["TYPE_OF_PROMO"] = "single"
        opt_summary_filtered['status'] =  LpStatus[prob.status]
        opt_summary_filtered_final = opt_summary_filtered_final.append(opt_summary_filtered)

    single_df = pd.DataFrame()
    if opt_summary_filtered_final.shape[0] > 0 or opt_sum_filtered_final.shape[0] > 0:
        if opt_sum_filtered_final.shape[0]>0:
            opt_sum_filtered_final = opt_sum_filtered_final.loc[opt_sum_filtered_final['status']=='Optimal']
        if opt_summary_filtered_final.shape[0]>0:
            opt_summary_filtered_final = opt_summary_filtered_final.loc[opt_summary_filtered_final['status']=='Infeasible']
        final_opt_sumary =  pd.concat([opt_sum_filtered_final,opt_summary_filtered_final],ignore_index = False)
        final_opt_sumary = final_opt_sumary.reset_index(drop=True)
        single_df = final_opt_sumary[['Date', 'Retailer', 'PPG'\
                        , 'PPG_No', 'TPR','PPG_MAIN','IS_ALL_BRAND','Mechanic','status','TYPE_OF_PROMO',"Week",'Brand'] + flag_cols[:-1]]
    # single_df.to_excel('kmm.xlsx')
    return single_df
