""" Test Common Factories"""
import factory
from pytest_factoryboy import register
from django.core.cache import cache as base_data
from core.generics.respository import AbstractRepository #pylint: disable=E0401
from core.generics.unit_of_work import AbstractUnitOfWork #pylint: disable=E0401
from .. import models as db_model
from ...user.models import User

user_instance = base_data.get('user',{})
user_groups = user_instance.get('user_groups')
allowed_groups = user_instance.get('allowed_groups')


@register
class UserGroupRetailerFactory(factory.Factory):
    id=1,
    region= "test"
    group_name= "test_group"
    group_id=1

    class Meta:
        model = db_model.UserGroupRetailer

@register
class USERFactory(factory.Factory):
    name ="test_admin"
    email = "<EMAIL>"

    class Meta:
        model = User

class FakeModel:
    def __init__(self, model):
        super().__init__([])
        self._model = dict(model)
        super().__init__([])

    def add(self, entity):
        self._model[entity[0]] = entity[1:]

    def delete(self, _id):
        self._model.pop(_id)

    def get(self, _id=0):
        return [id] + self._model.get(_id, [])

    def update(self, entity):
        self._model.update({entity[0]: entity[1:]})
    

class FakeRepository(AbstractRepository):
    def __init__(self, model):
        super().__init__([])
        self._model = dict(model)
        super().__init__([])

    def add(self, entity):
        self._model[entity[0]] = entity[1:]

    def delete(self, _id):
        self._model.pop(_id)

    def get(self, _id=0):
        return [_id] + self._model.get(_id, [])

    def get_all(self):
        return [0] + self._model.get(0, [])

    def update(self, entity):
        self._model.update({entity[0]: entity[1:]})
    
    def filter_by_group(self,allowed_groups):
        return [0] + self._model.get(0, [])


class FakeUnitOfWork(AbstractUnitOfWork):
    def __init__(self):
        self.model = FakeRepository([])
        self.repo_obj = FakeRepository([])
        self.committed = False

    def commit(self):
        self.committed = True

    def rollback(self):
        return True

    def add(self, entity):
        self.model.add(entity)

    def get_data_dict(self):
        return self.model.get()

    def search(self,_query_string,params):
        """_summary_

        Args:
            _query_string (str): sql query string
            params (list): parameters

        Returns:
            list: searched data from db
        """
        if params:
            return [
                [i[0]] + list(i[1])
                for i in self.model._model.items()
                if i[1][0] in params
            ]
        
        return list(self.model._model.items())
