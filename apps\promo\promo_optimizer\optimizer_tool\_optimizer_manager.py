""" Optimizer Manager"""
import ast
from django.db import transaction
from rest_framework import serializers
from core.generics import constants as CONST #pylint: disable=E0401
from apps.common import (services as sc,#pylint: disable=E0401
                               unit_of_work as uow,
                               )
from .. import generic,serializers as ser,utils

class OptimizerManager:
    def __init__(self,
                product_group:str='',
                account_name:str='',
                is_fetch_constraints:bool=False,
                is_load_promo:bool=False,
                data:list=None,
                promo_inputs:list=None,
                param_mac:int=1,
                param_nsv:int=1,
                param_rp:int=1,
                param_units:int=1,
                config_mac:bool=False,
                config_rp:bool=False,
                config_units:bool=False,
                config_nsv:bool=False,
                objective_fun:str='MAC',
                no_of_leaflet:int=0,
                min_length_gap:int=3,
                no_of_promo:int=12,
                scenario_name='',
                plvl_data=None,
                all_slot_utilization=False,
                visibility:list=None
                ):
        self.all_slot_utilization = all_slot_utilization
        self.is_fetch_constraints = is_fetch_constraints
        self._multippg_data = data
        self.visibility = []
        self.product_group = product_group
        self.account_name = account_name
        self.is_load_promo = is_load_promo
        self.promo_inputs = promo_inputs
        self.param_mac=param_mac
        self.param_nsv=param_nsv
        self.param_rp=param_rp
        self.param_units=param_units
        self.config_mac=config_mac
        self.config_rp=config_rp
        self.config_units=config_units
        self.config_nsv=config_nsv
        self.objective_fun = objective_fun
        self.max_consecutive_promo = 1
        self.min_consecutive_promo = 1
        self.tot_promo_min = 0
        self.tot_promo_max = 0
        self.min_length_gap = min_length_gap
        self.no_of_promo = no_of_promo
        self.no_of_leaflet = no_of_leaflet
        self.mechanic = []
        self.promo_price = []
        self.plvl_data = plvl_data
        self.brand=''
        self.segment=''
        self.promo_type='single'
        self.rpm=[],
        self.data=[],
        self.no_of_slots=0
        self.model_data_all = []
        self.param_compulsory_no_promo_weeks \
        = self.param_compulsory_promo_weeks= []
        self.ppg = ''
        self.promo_depth=[]
        self.scenario_name=scenario_name
        self.is_all_brand =False
        self.brand_tech=''
        self.product_type=''
        self.roi_data=[]
        self.xtrdata=[]
        self.rpm_data=[]
        self.ppg_belongs_to=''
        self.coeff_map = []
        self.overall_leaflet = []
        self.is_single=False
        self.is_joint = False

    def _get_serializer_data(self):
        serializer = ser.OptimizerSerializer({
                    'param_total_promo_min': self.tot_promo_min,
                    'param_total_promo_max': self.tot_promo_max,
                    'param_promo_gap': self.min_length_gap,
                    'param_max_consecutive_promo': self.max_consecutive_promo,
                    'param_min_consecutive_promo': self.min_consecutive_promo,
                    'param_compulsory_no_promo_weeks':self.param_compulsory_no_promo_weeks,
                    'param_compulsory_promo_weeks':self.param_compulsory_promo_weeks,
                    'param_no_of_promo':self.no_of_promo,
                    'param_no_of_leaflet':self.no_of_leaflet,
                    'param_mac': self.param_mac,
                    'param_rp': self.param_rp,
                    'param_trade_expense': 1.0,
                    'visibility':self.visibility,
                    'param_units': self.param_units,
                    'param_nsv': self.param_nsv,
                    'param_gsv': 1.0,
                    'param_sales': 1.0,
                    'param_mac_perc': 1.0,
                    'param_rp_perc': 1.0,
                    'config_mac':self.config_mac,
                    'config_rp': self.config_rp,
                    'config_trade_expense': False,
                    'config_units': self.config_units,
                    'config_nsv': self.config_nsv,
                    'config_gsv': False,
                    'config_sales': False,
                    'config_mac_perc': False,
                    'config_rp_perc': False,
                    'config_automation':True,
                    'config_no_of_promo': True,
                    'config_no_leaflet':True,
                    'config_min_consecutive_promo': True,
                    'config_max_consecutive_promo': True,
                    'objective_function':self.objective_fun,
                    'config_promo_gap': True,
                    'account_name': self.account_name,
                    'corporate_segment': self.segment,
                    'brand': self.brand,
                    'product_group':self.ppg,
                    'fin_pref_order': ['Units', 'NSV', 'RP', 'MAC'],
                    'mechanic':self.mechanic,
                    'promo_price':self.promo_price,
                    'promo_type':self.promo_type,
                    'promo_depth':self.promo_depth,
                    'brand_tech':self.brand_tech,
                    'product_type':self.product_type,
                    'is_all_brand':self.is_all_brand,
                    "ppg_belongs_to":self.ppg_belongs_to,
                    "no_of_slots":self.no_of_slots,
                    "is_single":self.is_single,
                    'is_joint':self.is_joint
                    
                })

        _data = []
        
        if serializer.is_valid():
            _data = serializer.validated_data
        if serializer.errors:
            raise serializers.ValidationError(serializer.errors)
        return _data                            

    def get_model_data(self):
        _ppg = generic.format_all_brand(self.ppg)
        joint_ppg = _ppg.split('_-_')
        self.ppg = generic.format_ppg(_ppg)
        self.data = list(sc.get_list_dict_value_from_query(
                                            uow.MetaUnitOfWork(transaction),
                                            [CONST.ModelData],
                                            self.account_name,
                                            joint_ppg,
                                            start_index=1
                                            ))[0]
        
        self.model_data_all,self.roi_data,self.coeff_map = list(sc.get_df_from_query(
                                                uow.MetaUnitOfWork(transaction),
                                                [CONST.ModelData,CONST.ModelROI,CONST.ModelCoeffMap],
                                                self.account_name,
                                                joint_ppg,
                                                start_index=2
                                                )
                                            )

        self.xtrdata = list(sc.get_list_dict_value_from_query(
                                            uow.MetaUnitOfWork(transaction),
                                            [CONST.ModelData],
                                            self.account_name,
                                            joint_ppg[:1],
                                            start_index=1
                                            ))[0]
        

        self.segment=self.xtrdata[0]['corporate_segment']
        self.brand=self.xtrdata[0]['brand']
        self.brand_tech=self.xtrdata[0]['brand_tech']
        self.product_type=self.xtrdata[0]['product_type']

    def get_updated_inputs(self):
        input_data_list = []
        if not self.is_fetch_constraints:
            for _d in self._multippg_data:
                self.param_compulsory_no_promo_weeks = ast.literal_eval(_d['ignored_weeks'])\
                    if isinstance(_d['ignored_weeks'],str)\
                    else _d['ignored_weeks']
                self.param_compulsory_promo_weeks = ast.literal_eval(_d['compulsory_week'])\
                    if isinstance(_d['compulsory_week'],str)\
                    else _d['compulsory_week']
                self.account_name = _d['account_name']
                self.tot_promo_min=_d['tot_promo_min']
                self.tot_promo_max = _d['tot_promo_max']
                self.ppg = generic.format_ppg5(_d['product_group'])
                self.segment = _d.get('corporate_segment',None)
                self.brand = _d.get('brand',None)
                self.ppg_belongs_to = _d.get("ppg_belongs_to",None)
                self.brand_tech = _d.get('brand_tech',None)
                self.product_type = _d.get('product_type',None)
                self.mechanic = _d.get('mechanic',None)
                self.promo_price = _d.get('promo_price',[])
                self.promo_depth = _d.get('promo_depth',None)
                self.promo_type = _d.get('type_of_promo',None)
                _data = self._get_serializer_data()
                input_data_list.append(_data)

        final_res = {}
        final_res.update({'data': input_data_list,
        'no_of_leaflet': self.no_of_leaflet,
        'overall_no_of_leaflet': sum(self.overall_leaflet),
        'min_length_gap': self.min_length_gap,
        'no_of_promo': self.no_of_promo,
        'scenario_name': self.scenario_name,
        'all_slot_utilization': self.all_slot_utilization,
        'objective_func': utils.get_objective_func(),
        })
        return final_res
