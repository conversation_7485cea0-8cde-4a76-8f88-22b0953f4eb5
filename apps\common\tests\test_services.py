""" Test Common Service"""
from .factories import FakeUnitOfWork
from .. import services
from ..tests import factories

def test_meta_data_services():
    """test_meta_data
    """
    uow = FakeUnitOfWork()
    values = list(factories.meta_data.values())
    uow.add(values)
    
    assert services.get_meta_data(uow) is not None

def test_scenario_data_services():
    """test_scenario_data
    """
    uow = FakeUnitOfWork()
    values = list(factories.meta_data.values())
    uow.add(values)
    assert services.get_meta_data(uow) is not None
