trigger:
  branches:
    include:
      - develop
  paths:
    include:
      - "*"

resources:
  repositories:
    - repository: devops
      type: git
      name: ANZ Promotion Optimisation/AU-PN-TPO-BACKEND
      ref: develop
    

extends:
  template: apps/ci-cd/dev-backend-deployment-pipeline.yml@devops
  parameters:
    project: ANZ Promotion Optimisation
    dev_project: AU-PN-TPO-BACKEND
    devops_project: AU-PN-TPO-BACKEND
    cliProjectKey: ANZ_Trade_Promotion_Optimizer
    cliProjectName: ANZ_Trade_Promotion_Optimizer
    build_args: --build-arg REACT_APP_API_URL=$(REACT_APP_API_URL)
