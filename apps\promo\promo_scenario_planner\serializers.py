""" Scenario Planner serializers"""
from rest_framework import serializers
from . import constants as sp_const
from .tests.factories import save_completed_promo_scenario_with_data_for_api

class ScenarioPlannerRequestSerializer(serializers.Serializer):
    """ Scenario Planner Serializer."""
    promo_data = serializers.JSONField(default=sp_const.SIMULATE_API_PAYLOAD)

class ScenarioPlannerRequestAndResponseSerializer(serializers.Serializer):
    """ Save Scenario Planner Serializer."""
    data = serializers.JSO<PERSON>ield(default=sp_const.PLANNER_PAYLOAD)

class ScenarioPlannerUpdatedResponseSerializer(serializers.Serializer):
    """ Save Scenario Planner Serializer."""
    updated_id = 1

class ScenarioPlannerSavedResponseSerializer(serializers.Serializer):
    """ Save Scenario Planner Serializer."""
    saved_id = 1

class ScenarioPlannerSaveRequestSerializer(serializers.Serializer):
    """ Save Scenario Planner Serializer."""
    data = save_completed_promo_scenario_with_data_for_api