""" Common Services"""
import re
import json
import ast
import logging
from contextlib import closing
from django.conf import settings
from django.db import connection
from django.http import HttpResponse
import pandas as pd
from apps.pricing.pricing_common.services import get_df_from_query
from apps.pricing.set_pricing.db_selector import get_common_db, get_db
from tasks.parallel_task import sync_to_async
import utils #pylint: disable=E0401
from django.utils import timezone
import pytz #needed because whenever you use timezone.now() (of django.utils) stores the zone information making the timestamp not naive
from apps.common import utils as cmn_utils
from apps.pricing.pricing_common import calculations as calc, queries,constants as cmn_const,utils as pc_utils
from core.generics import  exceptions, unit_of_work as _uow,excel,resp_utils
from apps.pricing.pricing_optimizer import stage_1,config as opt_config
from . import queries as set_pricing_queries,constants as CONST

logger = logging.getLogger(__name__)

def changed_scenario_batch_insert(
                     models,
                     n_records,
                     instance_id,
                    is_row_column=False):
    # breakpoint()
    values = models['MODEL_VALUES'].value.copy()
    columns = models['MODEL_COLUMN'].value.copy() if not is_row_column else []
    new_columns = models['REQUIRED_COLUMN'].value+models['EXTRA_COLUMNS'].value
    if new_columns:
        for index,(name,_mask_name) in enumerate(new_columns):
            cmn_utils.insert_extra_var(values,index,name)
            if not is_row_column:
                cmn_utils.insert_extra_var(columns,index,_mask_name)
    basedata_query = set_pricing_queries.changed_scenario_batch_insert_query(utils.convert_list_to_string_list(values),
                                    len(n_records))                           
    basedata_query = re.sub("[\"\']", "", basedata_query)
    params = []
    # breakpoint()
    for i in n_records:
        params.append([instance_id\
                        ,i['level_param']\
                        ,i['ogsm_param']\
                        ,i['changed_non_promo_price']\
                        ,i['changed_promo_price']\
                        ,i['changed_lp_percent']\
                        ,i['changed_pack_weight_kg_percent']\
                        ,i['changed_cogs_t_percent']\
                        ,i['status_flag']\
                        ,i['type_of_price_inc']\
                        ,i['competitor_follows']\
                        ,i['list_price_change_percent']\
                        ,i['list_price_change_percent_kg']\
                        ,i['nsv_price_impact_direct']\
                        ,i['nsv_price_impact_indirect']\
                        ,i['nsv_price_impact_base']\
                        ,i['new_lp_after_base_price']\
                        ,i['type_of_base_inc_after_change']\
                        ,i['list_price_new']\
                        ,i['pack_weight_kg_new']\
                        ,i['dead_net_new']\
                        ,i['net_net_new']\
                        ,i['non_promo_price_new']\
                        ,i['promo_price_new']\
                        ,i['changed_net_net_change_percent']\
                        ,i['changed_dead_net_change_percent']\
                        ,i['cogs_t_new']\
                        ,i['promo_sold_volume']\
                        ,i['promo_sold_volume_new']\
                        ,i['non_promo_sold_volume']\
                        ,i['non_promo_sold_volume_new']\
                        ,i['promo_sold_volume_percent']\
                        ,i['promo_sold_volume_percent_new']              
                    ])
    # breakpoint()
    with closing(connection.cursor()) as cursor:
        # cursor.fast_executemany=True
        cursor.execute(queries.delete_changed_scenario_query(instance_id))
        cursor.executemany(basedata_query, params)

def get_scenario(uow: _uow.AbstractUnitOfWork\
                 ,scenario_id: int=None\
                ,user=None\
                ,scenario_view=''\
                ,scenario_type='simulator'\
                ,module_type='set_pricing'):
    """Return the Baseline data based on with or without scenario id input.

    Parameters
    ----------
    scenario id(Optional)
        Saved Scenario Id.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    list
        list of dicts if successful, empty list otherwise.

    """
    
    with uow as unit_of_work:
        if scenario_id:
            return unit_of_work.repo_obj.filter(scenario_id)
        return unit_of_work.repo_obj.get_by_scenario_type(scenario_type,module_type,user)

def save_changed_promo_scenaro(**data):
    # breakpoint()
    changed_scenario_batch_insert(CONST.ChangedPricingScenario\
                     ,data.get('request_data').get('changed_records')\
                    ,data.get('request_data').get('pricing_saved_scenario_id')
                     )

def save_scenario(request_data: dict\
                  , uow: _uow.AbstractUnitOfWork\
                    ,user\
                    ,is_commit=False):
    """Save Scenrio

    Parameters
    ----------
    data
        To insert the data into the database.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    str
        Success or Failure in inserting the data

    """
    """ we are inserting meta_data of ec into a table called pricing_saved_scenario where we have pricing scenarios
      (Which are configured by user) information  and main data into changed_pricing_scenaio, if we are updating already existing
      data(each configured line as a seperate row) we will delete the record in changed_pricing_scenario and then re-insert the data again."""
    data = request_data.copy()
    non_committed_id = data.pop('non_committed_id',0)
    changed_records = {
        'changed_records':data.pop('pricing_payload'), #after we remove pricing_payload from changed_records will contain meta_data (information like module_type, multiplication_factor , scenario_type and other things) about scenario
    }
    
    scenario_instance_id = non_committed_id
    with uow as unit_of_work:
        """
        here in the below if, else we are writing meta data to pricing_saved_scenario table.
        """
        if non_committed_id:
            data['is_committed'] = True
            data['modified_by'] = user
            data['modified_at'] = str(timezone.now()) #converting to string as serializaing is not happening because of timestamp object
            data['user'] = user
            # breakpoint()
            unit_of_work.repo_obj.update(scenario_instance_id,ast.literal_eval(str(data)))
            unit_of_work.commit()
        else:
            data['created_by'] = user
            data['modified_by'] = user
            data['user'] = user
            data['is_committed'] = is_commit
            # breakpoint()
            scenario_instance = unit_of_work.repo_obj.add(ast.literal_eval(str(data)))
            scenario_instance_id = scenario_instance.id
            unit_of_work.commit()
    changed_records.update({
        'pricing_saved_scenario_id':scenario_instance_id
    })
    # breakpoint()
    save_changed_promo_scenaro(request_data=changed_records)
    return {'saved_id':scenario_instance_id}


def publish_scenario(
                    uow: _uow.AbstractUnitOfWork\
                    ,non_committed_id=0\
                    ,user=None):
    
    with uow as unit_of_work:
       unit_of_work.repo_obj.update(non_committed_id,{'is_published':True})
       unit_of_work.repo_obj.update(non_committed_id,{'is_committed':False})
       unit_of_work.commit()
        
    return {'published successfully'}

def get_raw_dict_from_query(uow:_uow.AbstractUnitOfWork,
                              models_list:list,
                              scenario_id:int=0,
                              mode:str='all',
                              start_index:int=0,
                              is_row_column=False,
                              serach_query=None,
                              is_optimizer=False,
                              db_table=''
                              ):
    """Return the  Baseline data data frame based on account name and product group.
    Parameters
    ----------
    models_list : list of baseline models 
    account name: retailer name
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    DataFrame
        dataframe.
    """
    # breakpoint()
    for models in models_list: 
        with uow as unit_of_work:

            values = models['MODEL_VALUES'].value.copy()
            columns = models['MODEL_COLUMN'].value.copy() if not is_row_column else []
            new_columns = models['REQUIRED_COLUMN'].value+models['EXTRA_COLUMNS'].value
            if new_columns:
                for index,(name,_mask_name) in enumerate(new_columns):
                    cmn_utils.insert_extra_var(values,index,name)
                    if not is_row_column:
                        cmn_utils.insert_extra_var(columns,index,_mask_name)
            # breakpoint()
            if is_optimizer:
                basedata_query = set_pricing_queries.all_agg_optimized_output_query(scenario_id)
            elif mode == 'all':
                if serach_query:
                    basedata_query = set_pricing_queries.search_all_agg_changed_inputs_query(scenario_id,serach_query)
                else:
                    # breakpoint()
                    basedata_query = set_pricing_queries.all_agg_changed_inputs_query(scenario_id,db_table)
            else:
                if serach_query:
                    basedata_query = set_pricing_queries.search_agg_changed_inputs_query(scenario_id,serach_query)
                else:
                    basedata_query = set_pricing_queries.agg_changed_inputs_query(scenario_id,db_table)
            # breakpoint()
            base_data_df = unit_of_work.get_raw_query_data(
                basedata_query,
                start_index=start_index
                )      
      
        yield base_data_df

@sync_to_async
def save_optimizer_input_output(uow: _uow.AbstractUnitOfWork,opt_data):

    with uow as unit_of_work:
        unit_of_work.repo_obj.add(opt_data)
        unit_of_work.commit()

def load_saved_optimizer_input_and_output(uow: _uow.AbstractUnitOfWork,scenario_saved_id,is_input=False):
    with uow as unit_of_work:
        opt_queryset = unit_of_work.repo_obj._filter(saved_id=scenario_saved_id,is_input=is_input)
    
    if not scenario_saved_id:
        raise  exceptions.NoDataError(scenario_saved_id)
    
    return opt_queryset
    
def saved_input_load_scenario(uow: _uow.AbstractUnitOfWork,suow,scenario_saved_id,is_optimizer=False):
    # breakpoint()
    with suow as unit_of_work:
        saved_scenario = unit_of_work.repo_obj.get(_id=scenario_saved_id)
    
    if not saved_scenario:
        raise  exceptions.NoDataError(scenario_saved_id)
    db_table = get_db(saved_scenario.scenario_type)
    return {'scenario_name':saved_scenario.name,'data':list(get_raw_dict_from_query(uow,[CONST.ChangedPricingScenario]\
                                   ,scenario_id=scenario_saved_id\
                                    ,mode=saved_scenario.mode,
                                    is_optimizer=is_optimizer,
                                    db_table=db_table
                                    ))[0]}

def get_constraints(uow: _uow.AbstractUnitOfWork,scenario_saved_id):

    constraints_dict = {}
    with uow as unit_of_work:
        saved_scenario = unit_of_work.repo_obj.filter_published_scenario()

    if scenario_saved_id:
        with uow as unit_of_work:
            saved_scenario = unit_of_work.repo_obj.filter(scenario_saved_id)
            if not saved_scenario.exists():
                raise exceptions.NoDataError(scenario_saved_id)
            
    all_obj_func = pc_utils.get_objective_func()
    if saved_scenario.exists():
        constraints_dict =  list(saved_scenario.values('objective_func'\
                                    ,'net_net_multiplication_factor'\
                                    ,'lp_multiplication_factor'\
                                    ,'nationa_nn_index'))[0]
    
        constraints_dict['objective_func'] = [{k: v} for k, v in all_obj_func.items() if k == constraints_dict['objective_func']][0]
    if not scenario_saved_id:
        constraints_dict['objective_func'] = all_obj_func
        return {
        'objective_func':pc_utils.get_objective_func(),
        'net_net_multiplication_factor':CONST.NET_NET_MULTIPLICATION_FACTOR,
        'lp_multiplication_factor':CONST.LP_MULTIPLICATION_FACTOR,
        'nationa_nn_index':CONST.NATIONA_NN_INDEX
    }
    return constraints_dict
        
    
def load_published_scenario(uow: _uow.AbstractUnitOfWork,suow,scenario_saved_id):
    with suow as unit_of_work:
        saved_scenario = unit_of_work.repo_obj.get(_id=scenario_saved_id)
    
    if not saved_scenario:
        raise  exceptions.NoDataError(scenario_saved_id)
    
    return list(get_raw_dict_from_query(uow,[CONST.ChangedPricingScenario]\
                                   ,scenario_id=scenario_saved_id\
                                    ,mode=saved_scenario.mode))[0]

def get_pricing_scenario_meta_data(uow:_uow.AbstractUnitOfWork,level=None,user:dict=None):
    """Return the  Baseline data based on meta id.

    Parameters
    ----------
    meta_id : model meta id to get baseline meta data. 
    allowed_groups(Optionl): Contains list of groups that assigned to user.
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    QuerySet
        queryset.
    """

    with uow as unit_of_work:
        if level=='ppg':
            return unit_of_work.repo_obj.get_meta_data_ppg_level()
        return unit_of_work.repo_obj.get_all()

def get_config(data):
    config={'objective':data.get('objective_func','MAC'),
            'method':'SLSQP',
            'adjust_percentage':data.get('net_net_multiplication_factor',1),
            'national_nn_target':data.get('nationa_nn_index',100)-100}
    return config

def download_input_and_output_optimizer(opt_data,is_input=True):
  
    if is_input:
        opt_resp = excel.create_pricing_excel_input(opt_data)
    else:
        opt_resp = excel.create_pricing_excel_output(opt_data)

    filename = 'optimizer_inpt.xlsx' if is_input else 'optimizer_output.xlsx' 
    response = HttpResponse(opt_resp,content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename=%s' % filename
    return response
    
def simulate_pricing_scenario(uow:_uow.AbstractUnitOfWork\
                              ,suow:_uow.AbstractUnitOfWork\
                            ,data=None\
                            ,user:dict=None,
                            is_scenario_planner=False,
                            opt_uow:_uow.AbstractUnitOfWork=None\
                            ):
    """Return the  Baseline data based on meta id.

    Parameters
    ----------
    meta_id : model meta id to get baseline meta data. 
    allowed_groups(Optionl): Contains list of groups that assigned to user.
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    QuerySet
        queryset.
    """

    #save scenario
    # breakpoint()
    scenario_saved = save_scenario(data, suow,user) 
    
    level_params = list(map(lambda x:x['level_param'],data.get('pricing_payload'))) #fetching PPGs (I think)
    db_table = get_common_db(data.get('scenario_type'))
 
    scenario_pricing_scenario_df = list(get_df_from_query(
                                uow,
                                [cmn_const.PricingScenario],
                                data.get('level'),
                                level_params,
                                scenario_id=scenario_saved.get('saved_id'),
                                mode=data.get('mode'),
                                is_row_column=True,
                                db_table=db_table
                            ))[0]
    # breakpoint()
    scenario_pricing_scenario_df['optimized_trade_margin'] = 0
    scenario_pricing_scenario_df['optimized_units'] = 0
    scenario_pricing_scenario_df['is_divided_by_100'] = 0
    solver_status=True
    # scenario_pricing_scenario_df = scenario_pricing_scenario_df.loc[scenario_pricing_scenario_df['exclude_retailer']==False]
    if data.get('scenario_type')=='optimizer':
        #Stage 1 optimizer code will be called here.
        config = get_config(data)
        
        updated_pricing_df = calc.user_defined_input_setup(scenario_pricing_scenario_df,scenario_type=data.get('scenario_type'),initial=True)
        equal_ppg_df = pc_utils.convert_to_df(opt_config.EQUAL_PPGS)
        price_per_kg_df = pc_utils.convert_to_df(opt_config.PRICING_PER_KG)
        # equal_ppg_df['PPG'] = equal_ppg_df['PPG'].str.upper()
        price_per_kg_df['PPG'] = price_per_kg_df['PPG'].str.upper()
        # breakpoint()
        optimized_df,solver_status, report_solver1, lp_problem = stage_1.optimizer(updated_pricing_df,config,equal_ppg_df,price_per_kg_df) #the extra variables(which are not being used) that are being passed are for logging purpose(in future versions) if the solver status is not giving expected values
        optimized_df = optimized_df.sort_values(by=['Customer','PPG'])
        scenario_pricing_scenario_df= scenario_pricing_scenario_df.sort_values(by=['customer','product_group'])
        scenario_pricing_scenario_df = pd.merge(scenario_pricing_scenario_df, optimized_df,  how='left', left_on=['customer','product_group'], right_on = ['Customer','PPG'])
        scenario_pricing_scenario_df.drop(['non_promo_price_new', 'promo_price_new','optimized_trade_margin','optimized_units'], axis=1, inplace=True)
        scenario_pricing_scenario_df.rename(columns={'opt_shelf_price_rounded':'non_promo_price_new'\
                                                    ,'new_promo_price_rounded':'promo_price_new'\
                                                    ,'OptTradeMargin%':'optimized_trade_margin'\
                                                    ,'OptUnit_rounded':'optimized_units'\
                                                    ,'nn_change_percent_x':'nn_change_percent'},inplace=True)
        
    
    return calc.calculate_pricing_scenario(scenario_pricing_scenario_df=scenario_pricing_scenario_df\
                                           ,saved_scenario_id=scenario_saved.get('saved_id')\
                                            ,is_scenario_planner=False,
                                            scenario_type=data.get('scenario_type'),
                                            solver_status=solver_status
                                           )
 
def get_pricing_scenario_data(uow:_uow.AbstractUnitOfWork,ppg_data,user:dict=None):
    """Return the  Baseline data based on meta id.

        Parameters
        ----------
        meta_id : model meta id to get baseline meta data. 
        allowed_groups(Optionl): Contains list of groups that assigned to user.
        uow: Unit of work to get the data from the DB.

        Returns
        -------
        QuerySet
            queryset.
    """

    with uow as unit_of_work:
        if ppg_data:
            return unit_of_work.repo_obj.filter_by_ppg(ppg_data)
        return unit_of_work.repo_obj.get_all()

def search_scenario(uow: _uow.AbstractUnitOfWork,query_params:str=''):
    """Returns searched data based on given query.

    Parameters
    ----------
    query_params:
        search query.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    Queryset
        raw query set.
    """

    with uow as unit_of_work:
        search_query = query_params.get('q','')
        search_query = '%' + search_query + '%'
        query = queries.search_pricing_global_scenario_query(search_query) 
        _data = unit_of_work.search(query,search_query)
    if not _data:
        raise  exceptions.NoDataError(query_params)
    return _data

def calculate_min_shelf_price(uow: _uow.AbstractUnitOfWork,factor:int=0,ppgs=None):
    
    with uow as unit_of_work:
        min_shelf_price_result = unit_of_work.repo_obj.get_min_shelf_price(factor,ppgs)
    
    return min_shelf_price_result