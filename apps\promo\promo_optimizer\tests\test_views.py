""" Test Optimizer Views"""
import json
import pytest
from ..tests import factories,_constants as CONST
from rest_framework.test import APIClient

apiclient = APIClient()

@pytest.mark.django_db(transaction=True)
def test_optimizer_save_and_retrieve_scenario_view(_django_data_setup,auth):
    apiclient.credentials(**auth['headers'])
    data_resp = apiclient.post(
        f"{CONST.OPTIMIZER_BASEURL}/save/", 
        format="json",
        data={**factories.save_active_optimizer_scenario_partial_data_for_api,'name':'optimizer test'}
    )

    assert data_resp.status_code == 200
    assert json.loads(data_resp.content)["data"] is not None
    assert json.loads(data_resp.content)["data"] !=[]
    assert "saved_id" in json.loads(data_resp.content)["data"]

    # save with empty data
    apiclient.credentials(**auth['headers'])
    data_resp = apiclient.post(
        f"{CONST.OPTIMIZER_BASEURL}/save/", format="json",data={}
    )
    assert data_resp.status_code == 406

    # compare scenario with saved id
    data_resp = apiclient.get(
        f"{CONST.OPTIMIZER_BASEURL}/compare/?saved_ids=[3]", format="json"
    )
    assert data_resp.status_code == 200
    assert json.loads(data_resp.content)["data"] is not None
    assert json.loads(data_resp.content)["data"] !=[]

    # compare scenario with random value
    data_resp = apiclient.get(
        f"{CONST.OPTIMIZER_BASEURL}/compare/?saved_ids=[8888]", format="json"
    )
    assert data_resp.status_code == 404
    assert json.loads(data_resp.content)["status"]=="NO DATA FOUND"

    #load scenario by saved id
    data_resp = apiclient.get(
        f"{CONST.OPTIMIZER_BASEURL}/load/3/", format="json"
    )
    assert data_resp.status_code == 200
    assert json.loads(data_resp.content)["data"] is not None
    assert json.loads(data_resp.content)["data"] !=[]

    #load scenario by random id
    data_resp = apiclient.get(
        f"{CONST.OPTIMIZER_BASEURL}/load/1000/", format="json"
    )

    assert data_resp.status_code == 404
    assert json.loads(data_resp.content)["status"]=="NO DATA FOUND"

    #search active optimizer scenario
 
    data_resp = apiclient.get(
        f"{CONST.OPTIMIZER_BASEURL}/search/?q=optimizer test&status_type=active", format="json"
    )
    assert data_resp.status_code == 200
    assert json.loads(data_resp.content)["data"] is not None
    assert json.loads(data_resp.content)["data"] !=[]

    #search active optimizer scenario with random value
    data_resp = apiclient.get(
        f"{CONST.OPTIMIZER_BASEURL}/search/?q=randomvalue&status_type=active", format="json"
    )
    assert data_resp.status_code == 404
    assert json.loads(data_resp.content)["status"]=="NO DATA FOUND"

    apiclient.credentials(**auth['headers'])
    # save with data

    data_resp = apiclient.post(
        f"{CONST.OPTIMIZER_BASEURL}/save/", 
        format="json",
        data={**factories.save_completed_optimizer_scenario_with_data_for_api,'name':'optimizer test 1'}
    )
    assert data_resp.status_code ==200
    assert json.loads(data_resp.content)["data"] is not None
    assert "saved_id" in json.loads(data_resp.content)["data"]
    assert json.loads(data_resp.content)["data"] !=[]

    # save with empty data
    apiclient.credentials(**auth['headers'])
    data_resp = apiclient.post(
        f"{CONST.OPTIMIZER_BASEURL}/save/", format="json",data={}
    )
    assert data_resp.status_code == 406

    #load scenario by saved id
    data_resp = apiclient.get(
        f"{CONST.OPTIMIZER_BASEURL}/load/4/", format="json"
    )
    assert data_resp.status_code == 200
    assert json.loads(data_resp.content)["data"] is not None
    assert json.loads(data_resp.content)["data"] !=[]

    #load scenario by random id
    data_resp = apiclient.get(
        f"{CONST.OPTIMIZER_BASEURL}/load/1000/", format="json"
    )

    assert data_resp.status_code == 404
    assert json.loads(data_resp.content)["status"]=="NO DATA FOUND"

    # compare scenario with saved id
    data_resp = apiclient.get(
        f"{CONST.OPTIMIZER_BASEURL}/compare/?saved_ids=[4]", format="json"
    )
    assert data_resp.status_code == 200
    assert json.loads(data_resp.content)["data"] is not None
    assert json.loads(data_resp.content)["data"] !=[]

    # compare scenario with random value
    data_resp = apiclient.get(
        f"{CONST.OPTIMIZER_BASEURL}/compare/?saved_ids=[8888]", format="json"
    )
    assert data_resp.status_code == 404
    assert json.loads(data_resp.content)["status"]=="NO DATA FOUND"

    #search completed optimizer scenario
    data_resp = apiclient.get(
        f"{CONST.OPTIMIZER_BASEURL}/search/?q=optimizer test 1&status_type=completed", format="json"
    )
    assert data_resp.status_code == 200
    assert json.loads(data_resp.content)["data"] is not None
    assert json.loads(data_resp.content)["data"] !=[]


    #search completed optimizer scenario with random value
    data_resp = apiclient.get(
        f"{CONST.OPTIMIZER_BASEURL}/search/?q=randomvalue&status_type=completed", format="json"
    )
    assert data_resp.status_code == 404
    assert json.loads(data_resp.content)["status"]=="NO DATA FOUND"
