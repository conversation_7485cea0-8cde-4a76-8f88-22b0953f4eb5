from abc import ABC, abstractmethod
from rest_framework.serializers import ValidationError
from django.db.models import Q
from datetime import datetime

class AbstractRepository(ABC):
    """Abstracts the notion of an object/entity store."""

    def __init__(self, uow_man):
        print('inside abs repo 1')
        self.uow = uow_man

    @abstractmethod
    def add(self, entity):
        """Insert a new entity into the data store."""
        raise NotImplementedError("add")

    @abstractmethod
    def delete(self, id):
        """Remove a persistent entity from the datastore."""
        raise NotImplementedError("delete")

    @abstractmethod
    def get(self, id):
        """Fetch an entity from the datastore by its identifier."""
        raise NotImplementedError("get")

    @abstractmethod
    def update(self, id):
        """Update an entity in the datastore by its identifier."""
        raise NotImplementedError("update")


class ORMModelRepository(AbstractRepository):
    def __init__(self, model_object):
        print('inside orm model 1')
        self._model = model_object.objects
        self._model_name = model_object._meta.db_table

    def get_name(self):
        return self._model_name

    def add(self, entity):
        
        add_obj = self._model.create(**entity)
        return add_obj

    def get(self, _id):
        return self._model.filter(id=_id).first()

    def filter(self,uid):
        return self._model.filter(id=uid)

    def get_all(self, **kwargs):
        return self._model.filter(is_delete=False)

    def getRaw(self, query):
        
        return self._model.raw(query)
    
    def bulk_update(self):
        self._model.all().update(is_delete=True)
    
    def bulk_inactive(self,ids):
        self._model.filter(~Q(id__in=ids)).update(is_delete=True)

    def update_status_type(self, record_id, update_data):

        record = self._model.get(id=record_id)
        if record:
            record.status_type = 'completed'
            record.is_committed = True
            record.save()
            return record
    

    def update(self, uid, entity):
        if self._model.filter(id=uid, is_delete=False).first():
            self._model.filter(id=uid).update(**entity)
        else:
            raise ValidationError("cannot update inactive records")

    def delete(self, uid,user={}):
        if self._model.filter(id=uid, is_delete=False).first():
            self._model.filter(id=uid).update(is_delete=True,deleted_by=user,deleted_at=datetime.now())
        else:
            raise ValidationError("cannot delete inactive records")

    def multi_delete(self,uids):
        if self._model.filter(id__in=uids).exists():
            self._model.filter(id__in=uids).delete()
        else:
            print('skip deleting')
