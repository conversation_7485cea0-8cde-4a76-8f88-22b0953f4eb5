""" Test Common Unit of work"""
import pytest
import factory
from ..tests.factories import FakeRepository
from .. import unit_of_work as uow

@pytest.mark.django_db
def test_user_group_uow():
    """test_user_group
    """
    unit_of_work = uow.UserGroupRetailerUnitOfWork(factory.Faker("set_autocommit"))
    unit_of_work.user_group_model = FakeRepository(
        [
            ("id", 1),
        ]
    )
    assert len(unit_of_work.repo_obj.get_all()) > 0
