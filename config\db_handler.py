""" DB Models handler"""
from django.conf import settings
from django.db import models

def db_table_format(table_name,schema=settings.DATABASE_SCHEMA):
    db_backend = settings.DATABASES['default']['ENGINE']
    if 'mssql' in db_backend:
        return f"[{schema}].[{table_name}]"
    if 'postgresql' in db_backend:
        return f'{schema}\".\"{table_name}'
    
    return f"{schema}.{table_name}"

def db_table_format_in_sql_query_str(table_name,schema=settings.DATABASE_SCHEMA):
    db_backend = settings.DATABASES['default']['ENGINE']
    if 'sqlite' in db_backend:
        return f"[{schema}.{table_name}]"
    return f"{schema}.{table_name}"

def model_fields_handler(attrs:dict=None):
    db_backend = settings.DATABASES['default']['ENGINE']
    if 'sqlite' in db_backend:
        return models.TextField(default='',verbose_name=attrs.get('verbose_name')\
                ,null=True,blank=True)
    if 'mssql' in db_backend:
        return models.JSONField(default=dict,
                                encoder=attrs.get('encoder'),
                                verbose_name=attrs.get('verbose_name'),
                                null=True,
                                blank=True)
    return models.JSONField
