""" Test Common Factories"""
import factory
from pytest_factoryboy import register
from django.core.cache import cache as base_data
from core.generics.respository import AbstractRepository #pylint: disable=E0401
from core.generics.unit_of_work import AbstractUnitOfWork #pylint: disable=E0401
from .. import models as db_model
from ...user.models import User

cmn_instance = base_data.get('common',{})
meta_data = cmn_instance.get('meta_data')
coeff_data = cmn_instance.get('coeff_data')
model_data_dict = cmn_instance.get('model_data_dict')
roi_data = cmn_instance.get('roi_data')
coeff_map_data = cmn_instance.get('coeff_map_data')
rpm_data = cmn_instance.get('rpm_data')
tactic_data = cmn_instance.get('tactic_data')
item_data = cmn_instance.get('item_data')

@register
class RetailerPPGMappingFactory(factory.Factory):
    id=1,
    account_name= "test 1"
    product_group= "test_product_group_1"
    retailer_index=1
    ppg_index=1
    created_by= "2021-10-27T20:11:55.586456Z"
    modified_by= {"name":"testadmin","email":"<EMAIL>","user_id":1}
    modified_at= "2021-12-30T07:39:45.789439Z"
    created_at= "2021-11-13T14:14:00Z"

    class Meta:
        model = db_model.RetailerPPGMapping

@register
class ModelTacticFactory(factory.Factory):
    id=1
    retailer_ppg_map_id=1
    promo_type='single'
    avg_units_per_week=0.0
    tpr_discount_byppg=0.0
    leaflet_flag=0.0
    newsletter_flag=0.0
    advertising_without_price_flag=0.0
    bonus_flag=0.0
    pas_flag=0.0
    coupon_flag=0.0
    edlp_flag=0.0
    multibuy_flag=0.0
    te_per_week=0.0
    list_price_per_week=0.0
    cogs_per_week=0.0
    max_week=0.0
    min_week=0.0
    min_week_edlp=0.0
    max_week_edlp=0.0
    gsv_per_week=0.0
    created_by= "2021-10-27T20:11:55.586456Z"
    modified_by= {"name":"testadmin","email":"<EMAIL>","user_id":1}
    modified_at= "2021-12-30T07:39:45.789439Z"
    created_at= "2021-11-13T14:14:00Z"

    class Meta:
        model = db_model.ModelTactic

@register
class ItemMapFactory(factory.Factory):
    id= 1
    account_name= "test 1"
    product_group= "test_product_group_1" 
    ppg_item_no="item_no_1"
    created_by= "2021-10-27T20:11:55.586456Z"
    modified_by= {"name":"testadmin","email":"<EMAIL>","user_id":1}
    modified_at= "2021-12-30T07:39:45.789439Z"
    created_at= "2021-11-13T14:14:00Z"

    class Meta:
        model = db_model.ItemMap

@register
class ModelMetaFactory(factory.Factory):
    id= 1
    account_name= "test 1"
    corporate_segment= "test segment"
    product_group= "test_product_group_1"
    brand= "test brand"
    product_type= "test product_type"
    created_by= {"name":"testadmin","email":"<EMAIL>","user_id":1}
    modified_by= {"name":"testadmin","email":"<EMAIL>","user_id":1}
    modified_at= factory.Faker("date")
    created_at= factory.Faker("date")
    is_active=True
    slug="test_1-test_segment-test_product_group_1"

    class Meta:
        model = db_model.ModelMeta

@register
class CoefficientFactory(factory.Factory):
    id=1
    model_meta_id= 1
    wmape= 0.061
    rsq= 0.9656
    acv_selling=0.0
    c_1_promoted_discount=0.0
    c_1_regular_price=0.0
    c_2_promoted_discount=0.0
    c_2_regular_price=0.0
    c_3_promoted_discount=0.0
    c_3_regular_price=0.0
    c_4_regular_price=0.0
    flag_promotype_leaflet=0.0
    flag_promotype_advertising_without_price=0.0
    flag_promotype_all_brand=0.0
    flag_promotype_back_page=0.0
    flag_promotype_bonus=0.0
    flag_promotype_coupon=0.0
    flag_promotype_edlp=0.0
    flag_promotype_front_page=0.0
    flag_promotype_joint_promo_1=0.0
    flag_promotype_joint_promo_2=0.0
    flag_promotype_joint_promo_3=0.0
    flag_promotype_joint_promo_4=0.0
    flag_promotype_joint_promo_5=0.0
    flag_promotype_logo=0.0
    flag_promotype_multibuy=0.0
    flag_promotype_newsletter=0.0
    flag_promotype_pas=0.0
    holiday_Flag_1=0.0
    holiday_Flag_2=0.0
    holiday_flag1=0.0
    holiday_flag2=0.0
    holiday_flag3=0.0
    holiday_flag4=0.0
    holiday_flag5=0.0
    median_base_price_log=0.0
    si_week=0.0
    si_period=0.0
    si_quarter=0.0
    tpr_discount_byppg=0.0
    year_trend=10
    intercept=1
    created_by= {"name":"testadmin","email":"<EMAIL>","user_id":1}
    modified_by= {"name":"testadmin","email":"<EMAIL>","user_id":1}
    modified_at= factory.Faker("date")
    created_at= factory.Faker("date")

    class Meta:
        model = db_model.ModelCoefficient

@register
class CoefficientMappingFactory(factory.Factory):
    id=1
    model_meta_id= 1
    coefficient_old="wk_sold_median_base_price_byppg_log"
    coefficient_new= "Median_Base_Price_log"
    value=7.66258
    ppg_item_no="item_no_1"
    created_by= {"name":"testadmin","email":"<EMAIL>","user_id":1}
    modified_by={"name":"testadmin","email":"<EMAIL>","user_id":1}
    modified_at= factory.Faker("date")
    created_at= factory.Faker("date")

    class Meta:
        model = db_model.CoefficientMapping

@register
class ModelDataFactory(factory.Factory):
    id=1
    model_meta_id= 1
    year= 2021
    date="2021-11-13T14:14:00Z"
    month=1
    week= 1
    wk_sold_doll_byppg=0.0
    wk_sold_qty_byppg=0.0
    wk_sold_avg_price_byppg=0.0
    wk_sold_qty_byppg_log=0.0 
    acv_selling=0.0
    c_1_promoted_discount=0.0
    c_1_regular_price=0.0
    c_2_promoted_discount=0.0
    c_2_regular_price=0.0
    c_3_promoted_discount=0.0
    c_3_regular_price=0.0
    c_4_regular_price=0.0
    flag_promotype_leaflet=0.0
    flag_promotype_advertising_without_price=0.0
    flag_promotype_all_brand=0.0
    flag_promotype_back_page=0.0
    flag_promotype_bonus=0.0
    flag_promotype_coupon=0.0
    flag_promotype_edlp=0.0
    flag_promotype_front_page=0.0
    flag_promotype_joint_promo_1=0.0
    flag_promotype_joint_promo_2=0.0
    flag_promotype_joint_promo_3=0.0
    flag_promotype_joint_promo_4=0.0
    flag_promotype_joint_promo_5=0.0
    flag_promotype_logo=0.0
    flag_promotype_multibuy=0.0
    flag_promotype_newsletter=0.0
    flag_promotype_pas=0.0
    holiday_Flag_1=0.0
    holiday_Flag_2=0.0
    holiday_flag1=0.0
    holiday_flag2=0.0
    holiday_flag3=0.0
    holiday_flag4=0.0
    holiday_flag5=0.0
    median_base_price_log=0.0
    si_week=0.0
    si_period=0.0
    si_quarter=0.0
    tpr_discount_byppg=0.0
    year_trend=1
    created_by= {"name":"testadmin","email":"<EMAIL>","user_id":1}
    modified_by= {"name":"testadmin","email":"<EMAIL>","user_id":1}
    modified_at= factory.Faker("date")
    created_at= factory.Faker("date")

    class Meta:
        model = db_model.ModelData

@register
class ROIDataFactory(factory.Factory):
    id=1
    model_meta_id= 1
    year= 2021
    quarter= 1
    period="P01"
    date=factory.Faker("date")
    week=1
    gsv=9878.4
    nsv=5180.82
    volume=0.96768
    nsv_per_unit_future=1.21542032601749
    cogs_per_unit_future=0.94629287810167
    gsv_per_unit_future=2.50843871508045
    total_sold_unit=754804.259999999
    total_sold_volume=300906
    pack_weight=10
    created_by= {"name":"testadmin","email":"<EMAIL>","user_id":1}
    modified_by= {"name":"testadmin","email":"<EMAIL>","user_id":1}
    modified_at= factory.Faker("date")
    created_at= factory.Faker("date")

    class Meta:
        model = db_model.ModelROI

@register
class USERFactory(factory.Factory):
    name ="test_admin"
    email = "<EMAIL>"

    class Meta:
        model = User

class FakeModel:
    def __init__(self, model):
        super().__init__([])
        self._model = dict(model)
        super().__init__([])

    def add(self, entity):
        self._model[entity[0]] = entity[1:]

    def delete(self, _id):
        self._model.pop(_id)

    def get(self, _id=0):
        return [id] + self._model.get(_id, [])

    def update(self, entity):
        self._model.update({entity[0]: entity[1:]})


class FakeRepository(AbstractRepository):
    def __init__(self, model):
        super().__init__([])
        self._model = dict(model)
        super().__init__([])

    def add(self, entity):
        self._model[entity[0]] = entity[1:]

    def delete(self, _id):
        self._model.pop(_id)

    def get(self, _id=0):
        return [_id] + self._model.get(_id, [])

    def get_all(self):
        return [0] + self._model.get(0, [])

    def update(self, entity):
        self._model.update({entity[0]: entity[1:]})


class FakeUnitOfWork(AbstractUnitOfWork):
    def __init__(self):
        self.model = FakeRepository([])
        self.repo_obj = FakeRepository([])
        self.committed = False

    def commit(self):
        self.committed = True

    def rollback(self):
        return True

    def add(self, entity):
        self.model.add(entity)

    def get_data_dict(self):
        return self.model.get()

    def search(self,_query_string,params):
        """_summary_

        Args:
            _query_string (str): sql query string
            params (list): parameters

        Returns:
            list: searched data from db
        """
        if params:
            return [
                [i[0]] + list(i[1])
                for i in self.model._model.items()
                if i[1][0] in params
            ]
        
        return list(self.model._model.items())

