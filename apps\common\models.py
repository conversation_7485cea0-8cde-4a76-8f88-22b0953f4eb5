""" Common Models"""
from decimal import Decimal
from django.conf import settings
from django.db import models
from django.core.validators import MaxValueValida<PERSON>, MinValueValidator
from config.db_handler import model_fields_handler, db_table_format #pylint: disable=E0401
from core.generics.resp_utils import Encoder #pylint: disable=E0401

class BaseModel(models.Model):
    """Base Model."""
    
    created_by = model_fields_handler(attrs={'verbose_name':'created by'})
    created_at = models.DateTimeField(auto_now_add=True,verbose_name='Created At')
    modified_by = model_fields_handler(attrs={'verbose_name':'modified by'})
    modified_at = models.DateTimeField(auto_now=True,verbose_name='Modified At')
    is_delete = models.BooleanField(default=False,verbose_name='Delete')
    is_active = models.BooleanField(default=True,verbose_name='Active')

    class Meta:
        """ Meta Class For Base Model."""
        abstract = True

class ModelMeta(BaseModel):
    """ Model Meta."""
    account_name = models.CharField(max_length=100,verbose_name="Account Name")
    corporate_segment =  models.CharField(max_length=100,verbose_name="Corporate Segment")
    product_group = models.CharField(max_length=100,verbose_name="Product Group")
    product_type =  models.CharField(max_length=100,verbose_name="Product Type")
    brand = models.CharField(max_length=100,verbose_name="Brand")
    brand_tech = models.CharField(max_length=100,verbose_name="Brand Tech")
    slug = models.SlugField(max_length=255, unique=True,blank=True , default=0.0\
                                , null=False,verbose_name='Slug')
    sub_category = models.CharField(max_length=100,verbose_name="Sub Category")
    tech = models.CharField(max_length=100,verbose_name="Tech")

    class Meta:
        """ Meta Class For Model Meta."""
        constraints = [
            models.UniqueConstraint(fields=['account_name','corporate_segment','product_group']\
                                                                            ,name='retailers')
        ]
        verbose_name = "Model Meta"
        db_table = "model_meta"
        ordering = ('account_name',)
        if hasattr(settings, 'DATABASE_SCHEMA'):
            db_table = db_table_format('model_meta')
        
    def __str__(self):
        "Returns Combined String of Account Name, Segment and Product Group."
        return "{}-{}-{}".format(self.account_name,self.corporate_segment,self.product_group)

class CoefficientMapping(BaseModel):
    """Coeff Map Model."""
    model_meta = models.ForeignKey('common.ModelMeta' , related_name="coeff_map" \
                                    ,on_delete=models.CASCADE,verbose_name='Model Meta fk')
    coefficient_old = models.CharField(max_length=100, default=0.0\
                                    , null=False,verbose_name='Coefficient Old')
    coefficient_new =  models.CharField(max_length=100, default=0.0\
                                    , null=False,verbose_name='Coefficient New')
    coefficient_other =  models.CharField(max_length=100, default=0.0\
                                    , null=False,verbose_name='Coefficient Other')                     
    value = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Value')
    ppg_item_no =  models.CharField(max_length=100, default=0.0\
                                    , null=False,verbose_name='PPG Item No')
    class Meta:
        """ Meta Class For Coefficient Mapping Model."""
        verbose_name = "Coefficient Mapping"
        db_table = 'coefficient_mapping'
        if hasattr(settings, 'DATABASE_SCHEMA'):
            db_table = db_table_format('coefficient_mapping')

class ModelCalculationMetrics(BaseModel):
    """Model Calculation Metrics."""
    intercept = models.DecimalField(max_digits=30, decimal_places=15,default=0.0\
                                        , null=False,verbose_name='Intercept')
    median_base_price_log = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name="Median Base Price Log",)
    weight = models.DecimalField(max_digits=30, decimal_places=15,
                                 verbose_name='weight', default=0.0, null=True,
                                 db_column='weight')
    tpr_discount_byppg = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0\
                                , null=False,validators=[MinValueValidator(Decimal(0.0))\
                                , MaxValueValidator(Decimal(100.0))],verbose_name="TPR Discount")
    tpr_discount_byppg_lag1 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0\
                                    , null=False,validators=[MaxValueValidator(Decimal(100.0))]\
                                    ,verbose_name="TPR Discount Lag 1")
    tpr_discount_byppg_lag2 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0\
                                    , null=False,validators=[MaxValueValidator(Decimal(100.0))]\
                                    ,verbose_name="TPR Discount Lag 2")
    year_trend = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0\
                                        , null=False,verbose_name='Year Trend')
    quarter_trend = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0\
                                        , null=False,verbose_name='Quarter Trend')
    y_2020 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0\
                                        , null=False,verbose_name='Y 2020')
    y_2021 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0\
                                        , null=False,verbose_name='Y 2021')
    y_2022 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0\
                                        , null=False,verbose_name='Y 2022')
    acv_selling = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0\
                                        , null=False,verbose_name='ACV Selling')
    si_week = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0\
                                        , null=False,verbose_name='SI Week')
    si_period = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0\
                                        , null=False,verbose_name='SI Period')
    si_quarter = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0\
                                        , null=False,verbose_name='SI Quarter')
    c_1_promoted_discount = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        ,verbose_name='C1 promoted Discount')
    c_2_promoted_discount = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        ,verbose_name='C2 promoted Discount')
    c_3_promoted_discount = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        ,verbose_name='C3 promoted Discount')
    c_1_regular_price = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        ,verbose_name='C1 regular price')
    c_2_regular_price = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        ,verbose_name='C2 regular price')
    c_3_regular_price = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        ,verbose_name='C3 regular price')
    c_4_regular_price = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        ,verbose_name='C4 regular price')
    down_t_lag = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        ,verbose_name='Down T Lag')
    flag_promotype_leaflet = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0\
                                                , null=False,verbose_name='Flag_promotype_Leaflet')
    flag_promotype_newsletter = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0\
                                                , null=False,verbose_name='Flag_promotype_newsletter')
    flag_promotype_advertising_without_price = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_advertising_without_price')
    flag_promotype_back_page = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_back_page')
    flag_promotype_bonus = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_bonus')
    flag_promotype_coupon = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_coupon')
    flag_promotype_edlp = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_edlp')
    flag_promotype_front_page = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_front_page')
    flag_promotype_joint_promo_1 = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_joint_promo_1')
    flag_promotype_joint_promo_2 = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_joint_promo_2')
    flag_promotype_joint_promo_3 = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_joint_promo_3')
    flag_promotype_joint_promo_4 = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_joint_promo_4')
    flag_promotype_joint_promo_5 = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_joint_promo_5')
    flag_promotype_joint_promo_6 = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_joint_promo_6')
    flag_promotype_joint_promo_7 = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_joint_promo_7')
    flag_promotype_joint_promo_8 = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_joint_promo_8')
    flag_promotype_joint_promo_9 = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_joint_promo_9')
    flag_promotype_joint_promo_10 = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_joint_promo_10')
    flag_promotype_joint_promo_11 = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_joint_promo_11')
    flag_promotype_joint_promo_12 = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_joint_promo_12')
    flag_promotype_joint_promo_13 = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_joint_promo_13')
    flag_promotype_joint_promo_14 = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_joint_promo_14')
    flag_promotype_joint_promo_15 = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_joint_promo_15')
    flag_promotype_all_brand = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_all_brand')
    flag_promotype_logo = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_logo')
    flag_promotype_multibuy = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_multibuy')
    flag_promotype_pas = models.DecimalField(max_digits=30 , decimal_places=15\
                        , default=0.0, null=False,verbose_name='Flag_promotype_pas')
    flag_promo_1 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Flag Promo 1')
    flag_promo_2 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Flag Promo 2')
    flag_promo_3 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Flag Promo 3')
    holiday_Flag_1 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Holiday Flag_1')
    holiday_Flag_2 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Holiday Flag_2')
    holiday_Flag_3 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Holiday Flag_3')
    holiday_Flag_4 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Holiday Flag_4')
    holiday_flag1 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Holiday Flag 1')
    holiday_flag2 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Holiday Flag 2')
    holiday_flag3 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Holiday Flag 3')
    holiday_flag4 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Holiday Flag 4')
    holiday_flag5 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Holiday Flag 5')
    # holiday_flag6 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
    #                                     ,verbose_name='Holiday Flag 6')
    # holiday_flag7 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
    #                                     ,verbose_name='Holiday Flag 7')
    non_promo_flag_date_1 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Non Promo  Flag  date 1')
    non_promo_flag_date_2 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Non Promo  Flag  date 2')
    non_promo_flag_date_3 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Non Promo  Flag  date 3')
    non_promo_flag_date_4 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Non Promo  Flag  date 4')
    nov_trend = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Nov Trend')
    nov_2022_trend = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Nov 2022 Trend')
    oct_2022_trend = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Oct 2022 Trend')
    oct_trend = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Oct Trend')
    p09_2022 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='p09 2022')
    p10_11_2022 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='p10 11 2022')
    p10_2022 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='p10 2022')
    p10_2022_trend = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='p10 2022 trend')
    p11_2022 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='p11 2022')
    p11_2022_trend = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='p11 2022 trend')
    p9_2022 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='p9 2022')
    p9_2022_trend = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='p9 2022 Trend')
    promo_flag_1 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Promo Flag 1')
    promo_flag_2 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Promo Flag 2')
    promo_flag_3 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Promo Flag 3')
    promo_flag_date_1 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Promo Flag_date 1')
    promo_flag_date_2 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Promo Flag date 2')
    promo_flag_date_3 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Promo Flag date 3')
    promo_flag_date_4 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Promo Flag date 4')
    promo_flag_date_correction_2020_08_29 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Promo Flag date correction 2020 08 29')
    promo_flag_date_correction_2020_10_10 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Promo Flag date correction 2020 10 10')
    promo_flag_date_correction_2022_04_23 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Promo Flag date correction 2022 04 23')
    promo_flag_date_correction_2022_08_13 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Promo Flag date correction 2022 08 13')
    quarter_trend_2021_1 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Quarter Trend 2021')
    sept_2022_trend = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Sept 2022 Trend')
    tpr_discount_byppg_10_above = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='TPR 10 above')
    tpr_discount_byppg_5_10 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='TPR 5 10')
    year_trend_2020 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Year Trend 2020')
    year_trend_2022 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Year Trend 2022')
    down_promo_flag = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Down Promo flag')
    mars_period = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Mars Period')
    mars_period_2021_first_half_trend = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Mars Period 2021 first half trend')
    mars_period_2021_second_half_trend = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Mars Period 2021 Second half trend')
    mars_quarter = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Mars Quarter')
    quarter2_2022 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Quarter2  2022')
    quarter_4_2022 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Quarter 4 2022')
    quarter_trend_2019 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Quarter Trend 2019')
    quarter_trend_2020 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Quarter Trend 2020')
    quarter_trend_2021 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Quarter Trend 2021 2')
    tpr_discount_byppg_2019 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='TPR 2019')
    year_trend_less_than_2022 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Year Trend less than 2022')
    promo_flag_date_correction_2020_05_02 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Promo Flag date correction 2020 05 02')
    promo_flag_date_correction_2021_09_25 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Promo Flag date correction 2020 09 25')
    promo_flag_date_correction_2022_05_14 = models.DecimalField(max_digits=30 , decimal_places=15, default=0.0, null=False\
                                        ,verbose_name='Promo Flag date correction 2020 05 14')
                                        
    class Meta:
        """ Meta Class For Model Calculation Metrics."""
        abstract = True

class ModelData(ModelCalculationMetrics):
    """Model Data."""
    model_meta = models.ForeignKey(
        'common.ModelMeta', related_name="data", on_delete=models.CASCADE
    )
    year = models.IntegerField(verbose_name="Year")
    week = models.IntegerField(verbose_name="Week",validators=[MinValueValidator(1)\
        , MaxValueValidator(52)])
    date = models.DateField(verbose_name="Date")
    month = models.IntegerField(verbose_name="Month",
                            validators=[MinValueValidator(1), MaxValueValidator(12)])
    wk_sold_doll_byppg = models.DecimalField(max_digits=30, decimal_places=15\
        , default=0.0,verbose_name='Week Sold Doll PPG', null=True)
    wk_sold_qty_byppg = models.DecimalField(max_digits=30, decimal_places=15\
        , default=0.0,verbose_name='Week Sold Average Qty PPG', null=True)
    wk_sold_avg_price_byppg = models.DecimalField(max_digits=30, decimal_places=15\
        , default=0.0,verbose_name='Week Sold Average Price by PPG', null=True)
    wk_sold_qty_byppg_log = models.DecimalField(max_digits=30, decimal_places=15\
        , default=0.0,verbose_name='Week Sold Average Qty PPG Log', null=True)
    promotion_levels = models.CharField(max_length=250,verbose_name="Promotion Levels", default='')

    objects = models.Manager()

    class Meta:
        """ Meta Class For Model Data."""
        verbose_name = "Model Data"
        db_table = 'model_data'
        if hasattr(settings, 'DATABASE_SCHEMA'):
            db_table = db_table_format('model_data')

class ModelCoefficient(ModelCalculationMetrics):
    '''Model Coefficient.'''
    model_meta = models.ForeignKey(
        'common.ModelMeta', related_name="coefficient", on_delete=models.CASCADE
    )
    wmape = models.DecimalField(max_digits=30, decimal_places=2,verbose_name='WMAPE')
    rsq = models.DecimalField(max_digits=30, decimal_places=2,verbose_name='RSQ')

    class Meta:
        '''Meta Class For Model Coefficient.'''
        verbose_name = "Model Coefficient"
        db_table = 'model_coefficient'
        if hasattr(settings, 'DATABASE_SCHEMA'):
            db_table = db_table_format('model_coefficient')

    def __str__(self):
        """ Returns Model Coefficient Name."""
        return "{}-{}-{}".format(self.model_meta.account_name\
                                ,self.model_meta.corporate_segment\
                                ,self.model_meta.product_group) # pylint: disable=no-member

class ModelROI(BaseModel):
    '''Model ROI.'''
    model_meta = models.ForeignKey(
        'common.ModelMeta' , related_name="roi" , on_delete=models.CASCADE
    )

    date = models.DateField(verbose_name="Date")
    year = models.IntegerField(verbose_name="Year")
    quarter = models.IntegerField(verbose_name="Quarter")
    week = models.IntegerField(verbose_name="Week",
                            validators=[MinValueValidator(1), MaxValueValidator(52)])
    period = models.CharField(max_length=250,verbose_name="Period", default='')
    promo_price = models.DecimalField(verbose_name="Promo Price", max_digits=30\
        , decimal_places=15, default=0.0)
    list_price = models.DecimalField(verbose_name="List price", max_digits=30\
        , decimal_places=15, default=0.0)
    gsv = models.DecimalField(verbose_name="GSV", max_digits=30\
        , decimal_places=15, default=0.0)
    nsv = models.DecimalField(verbose_name="NSV", max_digits=30\
        , decimal_places=15, default=0.0)
    volume = models.DecimalField(verbose_name="Volume", max_digits=30\
        , decimal_places=15, default=0.0)
    units = models.DecimalField(verbose_name="Units", max_digits=30\
        , decimal_places=15, default=0.0)
    nsv_per_unit_future = models.DecimalField(verbose_name="NSV_Per_Unit_Future", max_digits=30\
        , decimal_places=15, default=0.0)
    cogs_per_unit_future = models.DecimalField(verbose_name="COGS_Per_Unit_Future", max_digits=30\
        , decimal_places=15, default=0.0)
    gsv_per_unit_future = models.DecimalField(verbose_name="GSV_Per_Unit_Future", max_digits=30\
        , decimal_places=15, default=0.0)
    cogs = models.DecimalField(verbose_name="COGS", max_digits=30\
        , decimal_places=15, default=0.0)
    total_sold_unit = models.DecimalField(verbose_name="TOtal Sold Unit", max_digits=30\
        , decimal_places=15, default=0.0)
    total_sold_volume = models.DecimalField(verbose_name="TOtal Sold Volume", max_digits=30\
        , decimal_places=15, default=0.0)
    pack_weight = models.DecimalField(verbose_name="Pack Weight", max_digits=30\
    , decimal_places=15, default=0.0)
    total_trade_investment = models.DecimalField(verbose_name="Total Trade Investment", max_digits=30\
    , decimal_places=15, default=0.0)
    tactic_medium_hz = models.DecimalField(verbose_name="Tactic Medium HZ", max_digits=30\
    , decimal_places=15, default=0.0)
    sum_non_promo_units = models.DecimalField(verbose_name="Sum Non Promo Units", max_digits=30\
    , decimal_places=15, default=0.0)
    sum_promo_units = models.DecimalField(verbose_name="Sum Promo Units", max_digits=30\
    , decimal_places=15, default=0.0)


    class Meta:
        '''Meta Class For Model ROI.'''
        verbose_name = "Model ROI"
        db_table = 'model_roi'
        if hasattr(settings, 'DATABASE_SCHEMA'):
            db_table = db_table_format('model_roi')

class RetailerPPGMapping(BaseModel):
    """RetailerPPGMapping."""
    ppg_item = models.CharField(max_length=500,verbose_name="Product Group retailer")
    account_name = models.CharField(max_length=100,verbose_name="Account Name")
    product_group = models.CharField(max_length=500,verbose_name="Product Group")
    retailer_index = models.IntegerField(verbose_name="Retailer Index")
    ppg_index = models.CharField(max_length=100,verbose_name="PPG Index")
    type_of_promo = models.CharField(max_length=100,verbose_name="Type of Promo",default='single')

    class Meta:
        '''Meta Class For Retailer PPG Mapping.'''
        verbose_name = "Retailer PPG Mapping"
        db_table = 'retailer_ppg_mapping'
        if hasattr(settings, 'DATABASE_SCHEMA'):
            db_table = db_table_format('retailer_ppg_mapping')

class ModelTactic(BaseModel):
    """Model Data."""
    retailer_ppg_map = models.ForeignKey(
        'common.RetailerPPGMapping', related_name="tactic", on_delete=models.CASCADE
    )

    promo_type = models.IntegerField(verbose_name="Promo Type")
    # avg_units_per_week = models.DecimalField(max_digits=30, decimal_places=15\
    #                         , default=0.0,verbose_name='Avergae Units Per Kg')
    tpr_discount_byppg = models.DecimalField(max_digits=30, decimal_places=15\
                            , default=0.0,verbose_name='TPR')
    leaflet_flag = models.DecimalField(max_digits=30, decimal_places=15\
                            , default=0.0,verbose_name='Leaflet')
    newsletter_flag = models.DecimalField(max_digits=30, decimal_places=15\
                            , default=0.0,verbose_name='NewsLetter Flag')
    advertising_without_price_flag = models.DecimalField(max_digits=30, decimal_places=15\
                            , default=0.0,verbose_name='AD Without Price')
    back_page_flag = models.DecimalField(max_digits=30, decimal_places=15\
                            , default=0.0,verbose_name='Back Page')
    bonus_flag = models.DecimalField(max_digits=30, decimal_places=15\
                            , default=0.0,verbose_name='Bonus')
    front_page_flag = models.DecimalField(max_digits=30, decimal_places=15\
                            , default=0.0,verbose_name='Front Page')
    logo_flag = models.DecimalField(max_digits=30, decimal_places=15\
                            , default=0.0,verbose_name='Logo')
    pas_flag = models.DecimalField(max_digits=30, decimal_places=15\
    , default=0.0,verbose_name='Pas')
    coupon_flag = models.DecimalField(max_digits=30, decimal_places=15\
    , default=0.0,verbose_name='Coupon_flag')
    edlp_flag = models.DecimalField(max_digits=30, decimal_places=15\
    , default=0.0,verbose_name='EDLP_flag')
    multibuy_flag = models.DecimalField(max_digits=30, decimal_places=15\
    , default=0.0,verbose_name='multibuy')
    # te_per_week = models.DecimalField(max_digits=30, decimal_places=15\
    #                         , default=0.0,verbose_name='TE Per Week')
    # list_price_per_week = models.DecimalField(max_digits=30, decimal_places=15\
    #                         , default=0.0,verbose_name='List Price Per Week')
    # cogs_per_week = models.DecimalField(max_digits=30, decimal_places=15\
    #                         , default=0.0,verbose_name='COGS Per Week')
    max_week = models.DecimalField(max_digits=30, decimal_places=15\
                            , default=0.0,verbose_name='Max Week')
    min_week = models.DecimalField(max_digits=30, decimal_places=15\
                            , default=0.0,verbose_name='Min Week')
    min_week_edlp = models.DecimalField(max_digits=30, decimal_places=15\
                            , default=0.0,verbose_name='Min Week Edlp')
    max_week_edlp = models.DecimalField(max_digits=30, decimal_places=15\
                            , default=0.0,verbose_name='Max Week Edlp')
    # gsv_per_week = models.DecimalField(max_digits=30, decimal_places=15\
    #                         , default=0.0,verbose_name='GSV Per Week')
    is_all_brand = models.CharField(max_length=100,verbose_name="ALL BRAND",null=True)
    promo_flag = models.CharField(max_length=100,verbose_name="Promo Flag",null=True)
    class Meta:
        '''Meta Class For Model Tactic.'''
        verbose_name = "Model Tactic"
        db_table = 'model_tactic'
        if hasattr(settings, 'DATABASE_SCHEMA'):
            db_table = db_table_format('model_tactic')

class RetailerPPGMappingWeekly(BaseModel):
    """Model Data."""
    retailer_ppg_map = models.ForeignKey(
        'common.RetailerPPGMapping', related_name="retailer_ppg_map_weekly", on_delete=models.CASCADE
    )
    account_name = models.CharField(max_length=100,verbose_name="Account Name")
    product_group = models.CharField(max_length=500,verbose_name="Product Group")
    type_promo = models.CharField(max_length=100,verbose_name="Type Promo")
    ref_ppg = models.CharField(max_length=100,verbose_name="Ref PPG")
    ref_ppg_idx = models.IntegerField(verbose_name="Ref PPG Id")
    promo_type = models.IntegerField(verbose_name="Promo Type")
    type_promo_id = models.IntegerField(verbose_name="Type Promo Id")
    avg_units_per_week = models.DecimalField(max_digits=30, decimal_places=15\
                            , default=0.0,verbose_name='Avergae Units Per Kg')
    tpr_discount_byppg = models.DecimalField(max_digits=30, decimal_places=15\
                            , default=0.0,verbose_name='TPR')
    te_per_week = models.DecimalField(max_digits=30, decimal_places=15\
                            , default=0.0,verbose_name='TE Per Week')
    list_price_per_week = models.DecimalField(max_digits=30, decimal_places=15\
                            , default=0.0,verbose_name='List Price Per Week')
    cogs_per_week = models.DecimalField(max_digits=30, decimal_places=15\
                            , default=0.0,verbose_name='COGS Per Week')
    gsv_per_week = models.DecimalField(max_digits=30, decimal_places=15\
                            , default=0.0,verbose_name='GSV Per Week')
    nsv_per_week = models.DecimalField(max_digits=30, decimal_places=15\
                            , default=0.0,verbose_name='GSV Per Week')

    class Meta:
        '''Meta Class For Retailer PPG Mapping Weekly.'''
        verbose_name = "Retailer PPG Mapping Weekly"
        db_table = 'retailer_ppg_mapping_weekly'
        if hasattr(settings, 'DATABASE_SCHEMA'):
            db_table = db_table_format('retailer_ppg_mapping_weekly')

class SavedScenario(BaseModel):
    '''Saved Scenario Model.'''

    SCENARIO_CHOICES = (
        ("promo", "promo"),
        ("optimizer", "optimizer"),
    )
    PROMO_CHOICES = (
        ("single", "single"),
        ("joint", "joint"),
        ("all_brand", "all_brand")
    )
    STATUS_CHOICES = (
        ("active", "active"),
        ("completed", "completed"),
    )
    promo_type = models.CharField(
        max_length=20,
        choices=PROMO_CHOICES,
        default='single'
    )
    scenario_type = models.CharField(
        max_length=20,
        choices=SCENARIO_CHOICES,
        default='promo'
    )
    status_type = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active'
    )
    name = models.CharField(max_length=255)
    comments = models.CharField(max_length=500, default='',null=True,blank=True)
    user = model_fields_handler(attrs={'verbose_name':'saved_user'})
    shared_user = model_fields_handler(attrs={'verbose_name':'shared_user'})
    deleted_by = model_fields_handler(attrs={'verbose_name':'deleted by','allow_null':True})
    deleted_at = models.DateTimeField(auto_now_add=True,verbose_name='deleted At',null=True)
    is_review = models.BooleanField(default=False,verbose_name='is review')
    
    class Meta:
        '''Meta Class For Model Saved Scenario.'''
        verbose_name = "Model Saved Scenario"
        db_table = 'saved_scenario'
        if hasattr(settings, 'DATABASE_SCHEMA'):
            db_table = db_table_format('saved_scenario')

    def __str__(self):
        """ Returns Scenario Promotion Save name"""

        return f"{self.name}-{self.promo_type}-{self.scenario_type}-{self.status_type}\
                                                            -{self.user.get('user_id')}"

class ScenarioPromotionSave(BaseModel):
    '''Scenario Promotion Save.'''
    
    saved_scenario = models.ForeignKey(
        'common.SavedScenario' , related_name="saved_promotion_save" \
        , on_delete=models.CASCADE,null=True
    )
    input_constraints = model_fields_handler(attrs={'verbose_name':'Input Constraints'})
    data = model_fields_handler(attrs={'encoder':Encoder,'verbose_name':'scenario_output_data'})

    class Meta:
        '''Meta Class For Scenario Promotion Save.'''
        verbose_name = 'Scenario Promotion Save'
        db_table = 'scenario_promotion_save'
        if hasattr(settings, 'DATABASE_SCHEMA'):
            db_table = db_table_format('scenario_promotion_save')
        

class ItemMap(BaseModel):
    '''ITEM Map'''
    
    account_name = models.CharField(max_length=100,verbose_name="Account Name")
    product_group = models.CharField(max_length=100,verbose_name="Product Group")
    ppg_item_no = models.CharField(max_length=100,verbose_name="PPG Item No")

    class Meta:
        '''Meta Class For ITEM Map.'''
        verbose_name = 'ITEM Map'
        db_table = 'item_map'
        if hasattr(settings, 'DATABASE_SCHEMA'):
            db_table = db_table_format('item_map')
    
class PromotionLevels(BaseModel):
    """Promotion Levels"""

    SCENARIO_CHOICES = (
        ("national", "national"),
        ("regional", "regional"),
    )

    level_type = models.CharField(
        max_length=20,
        choices=SCENARIO_CHOICES,
        default='national'
    )

    promotion_level_user = model_fields_handler(attrs={'verbose_name':'promo_level_user'}) 
    is_updated = models.BooleanField(default=False,verbose_name='Updated')
    promotion_level_data = model_fields_handler(attrs={'encoder':Encoder,'verbose_name':'Promotion Level Data'})
    region = models.CharField(max_length=100,verbose_name="Region")

    class Meta:
        '''Meta Class For Promotion Levels.'''
        verbose_name = 'Promotion Levels'
        db_table = 'promotion_levels'
        if hasattr(settings, 'DATABASE_SCHEMA'):
            db_table = db_table_format('promotion_levels')
    

class BaselineDataView(models.Model):

    account_name = models.CharField(max_length=100,verbose_name="Account Name")
    corporate_segment =  models.CharField(max_length=100,verbose_name="Corporate Segment")
    product_group = models.CharField(max_length=100,verbose_name="Product Group")
    product_type =  models.CharField(max_length=100,verbose_name="Product Type")
    brand = models.CharField(max_length=100,verbose_name="Brand")
    brand_tech = models.CharField(max_length=100,verbose_name="Brand Tech")
    
    class Meta:
        '''Meta Class For Promotion Levels.'''
        verbose_name = 'Promotion Levels'
        db_table = 'baseline_data_view'
        if hasattr(settings, 'DATABASE_SCHEMA'):
            db_table = db_table_format('baseline_data_view')

class CONSTANTS(BaseModel):
    
    common = model_fields_handler(attrs={'verbose_name':'common'}) 
    scenario_planner = model_fields_handler(attrs={'verbose_name':'scenario_planner'}) 
    optimizer = model_fields_handler(attrs={'verbose_name':'optimizer'}) 

    class Meta:
        '''Meta Class For Constants.'''
        verbose_name = 'Constants'
        db_table = 'constants'
        if hasattr(settings, 'DATABASE_SCHEMA'):
            db_table = db_table_format('constants')

class BaseAndNationalPromoDetails(BaseModel):
    """Coeff Map Model."""
    account_name = models.CharField(max_length=100,verbose_name="Account Name")
    product_group = models.CharField(max_length=100,verbose_name="Product Group")
    base_and_national_promo = model_fields_handler(attrs={'verbose_name':'Base and National'})
    reatiler_ppgs_promp_types=model_fields_handler(attrs={'verbose_name':'Retailer PPG Prom Types'})
    data_headers=model_fields_handler(attrs={'verbose_name':'Data Headers'})
    raw_headers=model_fields_handler(attrs={'verbose_name':'Raw Headers'})

    class Meta:
        """ Meta Class For Coefficient Mapping Model."""
        verbose_name = "Base And National PromoDetails"
        db_table = 'base_and_national_promo_details'
        if hasattr(settings, 'DATABASE_SCHEMA'):
            db_table = db_table_format('base_and_national_promo_details')

class SelectedBaselineDataView(models.Model):
    
    account_name = models.CharField(max_length=100,verbose_name="Account Name")
    corporate_segment =  models.CharField(max_length=100,verbose_name="Corporate Segment")
    product_group = models.CharField(max_length=100,verbose_name="Product Group")
    product_type =  models.CharField(max_length=100,verbose_name="Product Type")
    brand = models.CharField(max_length=100,verbose_name="Brand")
    brand_tech = models.CharField(max_length=100,verbose_name="Brand Tech")
    # sub_category = models.CharField(max_length=100,verbose_name="Sub Category")
    # tech = models.CharField(max_length=100,verbose_name="Tech")
    
    class Meta:
        '''Meta Class For Promotion Levels.'''
        verbose_name = 'Promotion Levels'
        db_table = 'selected_baseline_data_view'
        if hasattr(settings, 'DATABASE_SCHEMA'):
            db_table = db_table_format('selected_baseline_data_view')
