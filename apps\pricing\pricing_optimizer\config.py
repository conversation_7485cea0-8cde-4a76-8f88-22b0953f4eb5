EQUAL_PPGS = [
    #  {
    #   "PPG": "Sheba Pouch 85g",
    #   "group": "group1"
    # },
    #  {
    #   "PPG": "She<PERSON> Tray 85g",
    #   "group": "group1"
    # },
    #  {
    #   "PPG": "Sheba Pouch 85g Craft Collection",
    #   "group": "group1"
    # },
    #  {
    #   "PPG": "Sheba Tray 85g Craft Collection",
    #   "group": "group1"
    # },
]

#latest
PRICING_PER_KG = [
    {"PPG": "SHEBA POUCH 12X85G", "group": "g1"},
    {"PPG": "WHISKAS POUCH 12X85G", "group": "g1"},
    {"PPG": "SHEBA POUCH 4X85G", "group": "g2"},
    {"PPG": "WHISKAS POUCH 4X85G", "group": "g2"},
    {"PPG": "SHEBA POUCH 85G", "group": "g3"},
    {"PPG": "WHISKAS POUCH 85G", "group": "g3"},
    {"PPG": "SHEBA POUCH MINI 6X50G", "group": "g4"},
    {"PPG": "WHISKAS POUCH 6X50G", "group": "g4"},
    {"PPG": "PERFECT FIT CAT DRY 1,4KG", "group": "g5"},
    {"PPG": "WHISKAS DRY 1,3KG", "group": "g5"},
    {"PPG": "PERFECT FIT CAT DRY 1,4KG", "group": "g6"},
    {"PPG": "WHISKAS DRY 1,5KG", "group": "g6"},
    {"PPG": "PERFECT FIT CAT DRY 1KG", "group": "g7"},
    {"PPG": "WHISKAS DRY 1KG", "group": "g7"},
    {"PPG": "WHISKAS DRY 300G", "group": "g8"},
    {"PPG": "PERFECT FIT CAT DRY 280G", "group": "g8"},
    {"PPG": "PERFECT FIT CAT DRY 750G", "group": "g9"},
    {"PPG": "WHISKAS DRY 800G", "group": "g9"},
    {"PPG": "PEDIGREE POUCH 12X100G", "group": "g10"},
    {"PPG": "PEDIGREE CAN 1,2KG", "group": "g10"},
    {"PPG": "PEDIGREE CAN 400G", "group": "g11"},
    {"PPG": "PEDIGREE POUCH 4X100G", "group": "g11"},
    {"PPG": "PERFECT FIT DOG DRY 2,6KG", "group": "g12"},
    {"PPG": "PEDIGREE DRY 2,6KG", "group": "g12"},
    {"PPG": "CESAR CAN 400G", "group": "g13"},
    {"PPG": "PEDIGREE CAN 400G", "group": "g13"},
    # {"PPG": "CESAR TRAY 150G", "group": "R1_g1"},# Comment : No Rule | 3x150 is in-out in BDR & Netto, 150g is in-out in BDR
    # {"PPG": "CESAR TRAY 3X150G", "group": "R1_g1"},# Comment : No Rule | 3x150 is in-out in BDR & Netto, 150g is in-out in BDR
    {"PPG": "CHAPPI DRY 2,7KG", "group": "R1_g2"},
    {"PPG": "CHAPPI DRY 9KG", "group": "R1_g2"},
    {"PPG": "CRAVE DOG C&T 55G", "group": "R1_g3"},
    {"PPG": "CRAVE DOG C&T 76G", "group": "R1_g3"},
    {"PPG": "DREAMIES C&T MEATY 30G", "group": "R1_g4"},# Comment : Removed 3x60g & 350g - in&Out
    {"PPG": "DREAMIES C&T CREAMY 40G", "group": "R1_g4"},# Comment : Removed 3x60g & 350g - in&Out
    {"PPG": "DREAMIES C&T 60G", "group": "R1_g4"},# Comment : Removed 3x60g & 350g - in&Out
    {"PPG": "DREAMIES C&T 180G", "group": "R1_g4"},# Comment : Removed 3x60g & 350g - in&Out
    {"PPG": "FROLIC DRY 750G", "group": "R1_g5"},
    {"PPG": "FROLIC DRY 2,8KG", "group": "R1_g5"},
    {"PPG": "PEDIGREE C&T BISCROK 200G", "group": "R1_g6"},# Comment : Create Multiple Rules R1 - Biscrok
    {"PPG": "PEDIGREE C&T BISCROK 500G", "group": "R1_g6"},# Comment : Create Multiple Rules R1 - Biscrok
    {"PPG": "PEDIGREE C&T DTX 45G", "group": "R1_g7"},# Comment : Create Multiple Rules R2 - DTX | Removed 77g, 180g, 270g due to ambiguity, & 3x77g, 2x154g, 3x128g, 4x180g - in&Out
    {"PPG": "PEDIGREE C&T DTX 72G", "group": "R1_g7"},# Comment : Create Multiple Rules R2 - DTX | Removed 77g, 180g, 270g due to ambiguity, & 3x77g, 2x154g, 3x128g, 4x180g - in&Out
    {"PPG": "PEDIGREE C&T DTX 110G", "group": "R1_g7"},# Comment : Create Multiple Rules R2 - DTX | Removed 77g, 180g, 270g due to ambiguity, & 3x77g, 2x154g, 3x128g, 4x180g - in&Out
    {"PPG": "PEDIGREE C&T DTX 128G", "group": "R1_g7"},# Comment : Create Multiple Rules R2 - DTX | Removed 77g, 180g, 270g due to ambiguity, & 3x77g, 2x154g, 3x128g, 4x180g - in&Out
    {"PPG": "PEDIGREE C&T DTX 154G", "group": "R1_g7"},# Comment : Create Multiple Rules R2 - DTX | Removed 77g, 180g, 270g due to ambiguity, & 3x77g, 2x154g, 3x128g, 4x180g - in&Out
    {"PPG": "PEDIGREE C&T DTX FRESH 4X110G", "group": "R1_g8"},# Comment : Create Multiple Rules R3 - Fresh | Exception - 270g has lowest price/kg
    {"PPG": "PEDIGREE C&T DTX FRESH 4X180G", "group": "R1_g8"},# Comment : Create Multiple Rules R3 - Fresh | Exception - 270g has lowest price/kg
    {"PPG": "PEDIGREE C&T DTX FRESH 4X270G", "group": "R1_g8"},# Comment : Create Multiple Rules R3 - Fresh | Exception - 270g has lowest price/kg
    {"PPG": "PEDIGREE C&T DTX FRESH 270G", "group": "R1_g8"},# Comment : Create Multiple Rules R3 - Fresh | Exception - 270g has lowest price/kg
    {"PPG": "PEDIGREE C&T GOOD CHEW 58G", "group": "R1_g9"},# Comment : Create Multiple Rules R4 - Good Chew
    {"PPG": "PEDIGREE C&T GOOD CHEW 88G", "group": "R1_g9"},# Comment : Create Multiple Rules R4 - Good Chew
    {"PPG": "PEDIGREE C&T JUMBONE 90G", "group": "R1_g10"},# Comment : Create Multiple Rules R5 - Jumbone | Removed 2x160 due to ambiguity
    {"PPG": "PEDIGREE C&T JUMBONE 360G", "group": "R1_g10"},# Comment : Create Multiple Rules R5 - Jumbone | Removed 2x160 due to ambiguity
    {"PPG": "PEDIGREE C&T JUMBONE 2X180G", "group": "R1_g10"},# Comment : Create Multiple Rules R5 - Jumbone | Removed 2x160 due to ambiguity
    {"PPG": "PEDIGREE C&T MARKIES 150G", "group": "R1_g11"},# Comment : Create Multiple Rules R6 - Markies
    {"PPG": "PEDIGREE C&T MARKIES 500G", "group": "R1_g11"},# Comment : Create Multiple Rules R6 - Markies
    ##############################################
    {"PPG": "PEDIGREE C&T RANCHOS 40G", "group": "R1_g12"},# Comment : Create Multiple Rules R7 - Ranchos | Removed 2x70g due to ambiguity
    {"PPG": "PEDIGREE C&T RANCHOS 65G", "group": "R1_g12"},# Comment : Create Multiple Rules R7 - Ranchos | Removed 2x70g due to ambiguity
    {"PPG": "PEDIGREE C&T RANCHOS 70G", "group": "R1_g12"},# Comment : Create Multiple Rules R7 - Ranchos | Removed 2x70g due to ambiguity #aman
    # {"PPG": "PEDIGREE C&T RANCHOS 60G", "group": "R1_g12"},# Comment : Create Multiple Rules R7 - Ranchos | Removed 2x70g due to ambiguity
    ##############################################
    # {"PPG": "PEDIGREE C&T RANCHOS 40G", "group": "R1_g12"},# Comment : Create Multiple Rules R7 - Ranchos | Removed 2x70g due to ambiguity 
    # {"PPG": "PEDIGREE C&T RANCHOS 70G", "group": "R1_g12"},# Comment : Create Multiple Rules R7 - Ranchos | Removed 2x70g due to ambiguity #aman
    # {"PPG": "PEDIGREE C&T RANCHOS 60G", "group": "R1_g12"},# Comment : Create Multiple Rules R7 - Ranchos | Removed 2x70g due to ambiguity
    # {"PPG": "PEDIGREE C&T RANCHOS 70G", "group": "R1_g12"},# Comment : Create Multiple Rules R7 - Ranchos | Removed 2x70g due to ambiguity
    # {"PPG": "PEDIGREE C&T RANCHOS 65G", "group": "R1_g12"},# Comment : Create Multiple Rules R7 - Ranchos | Removed 2x70g due to ambiguity
    ##############################################
    {"PPG": "PEDIGREE C&T RODEO 122G", "group": "R1_g13"},# Comment : Create Multiple Rules R8 - Rodeo | Remove 70g due to ambiguity
    {"PPG": "PEDIGREE C&T RODEO 123G", "group": "R1_g13"},# Comment : Create Multiple Rules R8 - Rodeo | Remove 70g due to ambiguity
    {"PPG": "PEDIGREE C&T RODEO 3X122G", "group": "R1_g13"},# Comment : Create Multiple Rules R8 - Rodeo | Remove 70g due to ambiguity
    {"PPG": "PEDIGREE C&T RODEO 3X123G", "group": "R1_g13"},# Comment : Create Multiple Rules R8 - Rodeo | Remove 70g due to ambiguity
    {"PPG": "PEDIGREE C&T RODEO 3X140G", "group": "R1_g13"},# Comment : Create Multiple Rules R8 - Rodeo | Remove 70g due to ambiguity
    {"PPG": "PEDIGREE C&T RODEO 4X123G", "group": "R1_g13"},# Comment : Create Multiple Rules R8 - Rodeo | Remove 70g due to ambiguity
    {"PPG": "PEDIGREE C&T SCHMACKOS 86G", "group": "R1_g14"},# Comment : Create Multiple Rules R9 - Schmackos | Remove 43g due to ambiguity
    {"PPG": "PEDIGREE C&T SCHMACKOS 144G", "group": "R1_g14"},# Comment : Create Multiple Rules R9 - Schmackos | Remove 43g due to ambiguity
    {"PPG": "PEDIGREE C&T TASTY MINIS 130G", "group": "R1_g15"},# Comment : Create Multiple Rules R10 - Rodeo | Remove 125g due to ambiguity
    {"PPG": "PEDIGREE C&T TASTY MINIS 140G", "group": "R1_g15"},# Comment : Create Multiple Rules R10 - Rodeo | Remove 125g due to ambiguity
    {"PPG": "PEDIGREE C&T TASTY MINIS 155G", "group": "R1_g15"},# Comment : Create Multiple Rules R10 - Rodeo | Remove 125g due to ambiguity
    {"PPG": "PEDIGREE C&T TASTY MINIS 3X140G", "group": "R1_g15"},# Comment : Create Multiple Rules R10 - Rodeo | Remove 125g due to ambiguity
    {"PPG": "PEDIGREE CAN 800G", "group": "R1_g16"},# Comment : Exception 800g > 400g, 4x400g > 1.2Kg
    {"PPG": "PEDIGREE CAN 400G", "group": "R1_g16"},# Comment : Exception 800g > 400g, 4x400g > 1.2Kg
    {"PPG": "PEDIGREE CAN 4X400G", "group": "R1_g16"},# Comment : Exception 800g > 400g, 4x400g > 1.2Kg
    {"PPG": "PEDIGREE CAN 1,2KG", "group": "R1_g16"},# Comment : Exception 800g > 400g, 4x400g > 1.2Kg
    {"PPG": "PEDIGREE DRY 500G", "group": "R1_g17"},# Comment : Creating Multiple Rule R1
    {"PPG": "PEDIGREE DRY 1,8KG", "group": "R1_g17"},# Comment : Creating Multiple Rule R1
    {"PPG": "PEDIGREE DRY 1,5KG", "group": "R1_g18"},# Comment : Creating Multiple Rule R2
    {"PPG": "PEDIGREE DRY 1,8KG", "group": "R1_g18"},# Comment : Creating Multiple Rule R2
    {"PPG": "PEDIGREE DRY 2KG", "group": "R1_g18"},# Comment : Creating Multiple Rule R2
    {"PPG": "PEDIGREE DRY 2,6KG", "group": "R1_g18"},# Comment : Creating Multiple Rule R2
    {"PPG": "PEDIGREE DRY 7KG", "group": "R1_g18"},# Comment : Creating Multiple Rule R2
    {"PPG": "PEDIGREE DRY 2KG", "group": "R1_g19"},# Comment : Creating Multiple Rule R3
    {"PPG": "PEDIGREE DRY 2,2KG", "group": "R1_g19"},# Comment : Creating Multiple Rule R3
    {"PPG": "PEDIGREE POUCH 4X100G", "group": "R1_g20"},# Comment : Creating Multiple Rule R1 | Removed 100g - ambiguity in rule
    {"PPG": "PEDIGREE POUCH 12X100G", "group": "R1_g20"},# Comment : Creating Multiple Rule R1 | Removed 100g - ambiguity in rule
    {"PPG": "PEDIGREE POUCH 6X100G", "group": "R1_g21"},# Comment : Creating Multiple Rule R2 | Removed 100g - ambiguity in rule
    {"PPG": "PEDIGREE POUCH 12X100G", "group": "R1_g21"},# Comment : Creating Multiple Rule R2 | Removed 100g - ambiguity in rule
    {"PPG": "PEDIGREE TRAY 300G", "group": "R1_g22"},
    {"PPG": "PEDIGREE TRAY 2X300G", "group": "R1_g22"},
    {"PPG": "PERFECT FIT CAT DRY 750G", "group": "R1_g23"},# Comment : Exception 750g > 650g, 1Kg > 900g
    {"PPG": "PERFECT FIT CAT DRY 650G", "group": "R1_g23"},# Comment : Exception 750g > 650g, 1Kg > 900g
    {"PPG": "PERFECT FIT CAT DRY 1KG", "group": "R1_g23"},# Comment : Exception 750g > 650g, 1Kg > 900g
    {"PPG": "PERFECT FIT CAT DRY 900G", "group": "R1_g23"},# Comment : Exception 750g > 650g, 1Kg > 900g
    {"PPG": "PERFECT FIT DOG DRY 825G", "group": "R1_g24"},
    {"PPG": "PERFECT FIT DOG DRY 1,4KG", "group": "R1_g24"},
    {"PPG": "SHEBA POUCH MINI 6X50G", "group": "R1_g25"},# Comment : Removed 85g - in&Out in BDR, Ambiguity in rule
    {"PPG": "SHEBA POUCH 4X85G", "group": "R1_g25"},# Comment : Removed 85g - in&Out in BDR, Ambiguity in rule
    {"PPG": "SHEBA POUCH 12X85G", "group": "R1_g25"},# Comment : Removed 85g - in&Out in BDR, Ambiguity in rule
    {"PPG": "SHEBA TRAY 85G", "group": "R1_g26"},# Comment : Creating Multiple Rule R1 | Removed 8x85KG - in&out
    {"PPG": "SHEBA TRAY 3X85G", "group": "R1_g26"},# Comment : Creating Multiple Rule R1 | Removed 8x85KG - in&out
    {"PPG": "SHEBA TRAY 4X85G", "group": "R1_g26"},# Comment : Creating Multiple Rule R1 | Removed 8x85KG - in&out
    {"PPG": "SHEBA TRAY 85G", "group": "R1_g27"},# Comment : Creating Multiple Rule R2 | Removed 8x85KG - in&out
    {"PPG": "SHEBA TRAY 5X85G", "group": "R1_g27"},# Comment : Creating Multiple Rule R2 | Removed 8x85KG - in&out
    {"PPG": "WHISKAS C&T FUNCTIONAL 40G", "group": "R1_g28"},
    {"PPG": "WHISKAS C&T FUNCTIONAL 50G", "group": "R1_g28"},
    {"PPG": "WHISKAS CAN 400G", "group": "R1_g29"},
    {"PPG": "WHISKAS CAN 4X400G", "group": "R1_g29"},
    {"PPG": "WHISKAS DRY 1,4KG", "group": "R1_g30"},# Comment : Creating Multiple Rule R1 | Removed 7KG - in&out
    {"PPG": "WHISKAS DRY 800G", "group": "R1_g30"},# Comment : Creating Multiple Rule R1 | Removed 7KG - in&out
    {"PPG": "WHISKAS DRY 1,75KG", "group": "R1_g31"},# Comment : Creating Multiple Rule R2 | Removed 7KG - in&out
    {"PPG": "WHISKAS DRY 2KG", "group": "R1_g31"},# Comment : Creating Multiple Rule R2 | Removed 7KG - in&out
    {"PPG": "WHISKAS DRY 2,8KG", "group": "R1_g31"},# Comment : Creating Multiple Rule R2 | Removed 7KG - in&out
    {"PPG": "WHISKAS POUCH 85G", "group": "R1_g32"},# Comment : Removed 12x85g & 24x85g - in&Out
    {"PPG": "WHISKAS POUCH 4X85G", "group": "R1_g32"},# Comment : Removed 12x85g & 24x85g - in&Out
    {"PPG": "WHISKAS POUCH 6X85G", "group": "R1_g32"},# Comment : Removed 12x85g & 24x85g - in&Out
    {"PPG": "WHISKAS POUCH 8X85G", "group": "R1_g32"},# Comment : Removed 12x85g & 24x85g - in&Out

]
