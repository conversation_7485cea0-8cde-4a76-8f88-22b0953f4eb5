class MissingRequestParamsError(Exception):
    def __init__(self, key, value):
        self.key = key
        self.value = value

    def __str__(self):
        return (
            "{0}:{1} is invalid input, Request can only accept valid "
            "values".format(self.key, self.value)
        )


class MissingRequestParamsandPayloadError(Exception):
    def __init__(self, key):
        self.key = key

    def __str__(self):
        return (
            "{0} is missing,please pass valid parameter".format(self.key)
        )

class NoDataError(Exception):
    def __init__(self, value):
        self.value = value

    def __str__(self):
        return "Data not available for the parameters received {0}".format(self.value)


class EmptyObjectError(Exception):
    def __init__(self, value):
        self.value = value

    def __str__(self):
        return self.value

class DataNotFound(Exception):
    def __init__(self, value):
        self.value = value

    def __str__(self):
        return "No Data available for  SCenario Id : {0}".format(self.value)

class NoParameter(Exception):
    
    def __str__(self):
        return "No Parameters found.Please pass valid parameters."

class BadRequest(Exception):
    status_code = 400

class AlreadyExists(Exception):
    status_code = 409
    def __init__(self, value):
        self.value = value

    def __str__(self):
        return "{0} is already exists".format(self.value)

class ProgramingException(Exception):
    status_code = 500
    def __str__(self):
        return "Not able to process further."
    
class InvalidException(Exception):
    status_code = 400
    def __str__(self):
        return "Invalid parameter."
    