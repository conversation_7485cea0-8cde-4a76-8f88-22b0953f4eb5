""" Test Common Confetest"""
import os
import sys
import random
import datetime
from pathlib import Path
import factory
import pytest
from pytest_factoryboy import register
from django.contrib.auth import get_user_model
from rest_framework.authtoken.models import Token
from ...user.serializers import UserSerializer
from .. import repository
from . import factories,_helper
# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent.parent.parent
sys.path.append(str(BASE_DIR))

@pytest.fixture(scope="session")
def django_db_setup():
    from django.conf import settings
    settings.DATABASES['default'] = {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': os.path.join(os.path.join(BASE_DIR,'dbs'), 'testing_db1.sqlite3'),
    }

# this fixture is used to temporarily create data in the model for testing

def add_data_to_db(data,repo):
    repo.add(data)

@pytest.fixture(scope="session")
def _django_data_setup(django_db_blocker):
    print("setup")
    with django_db_blocker.unblock():
        try:
            quarter_and_period=_helper.handle_quarter_and_period()
            _meta_data = [{**factories.meta_data,'id':index+1\
                        ,'product_group':f"test_product_group_{index+1}"\
                        ,'slug':f'test_1-test_segment-test_product_group_{index+1}'}
                        
                        for index in range(2)]
            _coeff_data = [{**factories.coeff_data,'id':index+1\
                        ,'model_meta_id':index+1}\
                        for index in range(2)]

            roi_model_data_start_index=0
            roi_model_data_end_index = 52
            coeff_map_start_index=0
            coeff_map_end_index=4
            
            for mta,coeff in zip(_meta_data,_coeff_data):
                meta_repo = repository.MetaRepository()
                add_data_to_db(mta,meta_repo)
                model_coeff_repo = repository.CoeffRepository()
                add_data_to_db(coeff,model_coeff_repo)
                for index in range(roi_model_data_start_index,roi_model_data_end_index):
                    qp_index =index - roi_model_data_start_index
                    
                    _model_data = {**factories.model_data_dict,'id':index+1,'week':index+1\
                            ,'month':quarter_and_period['month'][qp_index]\
                            ,'date':datetime.datetime(2021,quarter_and_period['month'][qp_index]\
                                ,random.randint(1,28)),'model_meta_id':mta['id']}
                    model_data_repo = repository.ModelDataRepository()
                    add_data_to_db(_model_data,model_data_repo)                  
                    _roi_data = {**factories.roi_data,'id':index+1,'week':index+1\
                                ,'date':datetime.datetime(2021,quarter_and_period['month'][qp_index]\
                                    ,random.randint(1,28))\
                                ,'period':'P{}'.format(str(quarter_and_period['period'][qp_index]).zfill(2))\
                                ,'quarter':quarter_and_period['quarter'][qp_index],'model_meta_id':mta['id']}
                    model_roi_repo = repository.ModelROIRepository()
                    add_data_to_db(_roi_data,model_roi_repo)

                for index in range(coeff_map_start_index,coeff_map_end_index):
                    model_coeff_map_repo = repository.CoeffMapRepository()
                    _coeff_val_new =''
                    if index == 0:
                        _coeff_val = 'TPR'
                    if index == 1:
                        _coeff_val = 'Intercept'
                    if index == 2:
                        _coeff_val = 'ACV_Selling'
                    if index == 3:
                        _coeff_val = 'Median_Base_Price_log'
                        _coeff_val_new = 'wk_sold_median_base_price_byppg_log'
                    if not _coeff_val_new:
                        _coeff_val_new = _coeff_val
                    _coeff_map_data = {**factories.coeff_map_data,'id':index+1,'coefficient_new'\
                                        :f'{_coeff_val}','coefficient_old':f'{_coeff_val_new}'\
                                        ,'model_meta_id':mta['id']
                                        }
                    add_data_to_db(_coeff_map_data,model_coeff_map_repo)
                roi_model_data_start_index=roi_model_data_end_index
                roi_model_data_end_index=roi_model_data_end_index*2
                coeff_map_start_index = coeff_map_end_index
                coeff_map_end_index=coeff_map_end_index*2

            end_tactic_index = 30
            start_tact_index = 1
            for _rpm in range(3):
                index = _rpm+1
                rpm_repo = repository.RetailerPPGMappingRepository()
                
                if _rpm == 2:
                    # create joint ppg
                    _rpm_data = {**factories.rpm_data,'id':index\
                        ,'product_group':f"test_product_group_1_-_test_product_group_2",
                        "ppg_index":f'{index}',"type_of_promo":"joint"
                        }
                    add_data_to_db(_rpm_data,rpm_repo)
                    for tac in range(start_tact_index,end_tactic_index):
                        tactic_repo = repository.TacticRepository()
                        _tactic_data = {**factories.tactic_data,'id':tac\
                            ,"retailer_ppg_map_id":f'{index}','promo_type':f"{tac}",
                            }
                        add_data_to_db(_tactic_data,tactic_repo)
                    continue
                _rpm_data = {**factories.rpm_data,'id':index\
                        ,'product_group':f"test_product_group_{index}",
                        "ppg_index":f'{index}'
                        }
                add_data_to_db(_rpm_data,rpm_repo)
                for tac in range(start_tact_index,end_tactic_index):
                    tactic_repo = repository.TacticRepository()
                    _tactic_data = {**factories.tactic_data,'id':tac\
                            ,'promo_type':f"{tac}",
                            "retailer_ppg_map_id":f'{index}'
                            }
                    add_data_to_db(_tactic_data,tactic_repo)
                start_tact_index=end_tactic_index
                end_tactic_index=end_tactic_index*2
            for _t in range(2):
                item_repo = repository.ItemMapRepository()
                _item_data = {**factories.item_data,'id':_t+1\
                        ,'product_group':f"test_product_group_{_t+1}",
                        "ppg_item_no":f'item_no_{_t+1}'
                        }
                add_data_to_db(_item_data,item_repo)
        except Exception as _exc:
            print(_exc,'Skip adding data in common app.')
@pytest.fixture
@pytest.mark.django_db
def auth_factory():
    print('************** user factory *************')

    def auth_info():

        user = get_user_model().objects.get_or_create(email='<EMAIL>')[0]
        token, _created = Token.objects.get_or_create(user=user) # pylint: disable=no-member
        user_ser = UserSerializer(token.user)
        headers = {'HTTP_AUTHORIZATION': f'Token {token.key}'}
        return {
            'token': token.key,
            'user': user_ser.data,
            'headers': headers
        }

    return auth_info

@pytest.fixture
@pytest.mark.django_db
def auth(auth_factory):

    print('************** user info *************')
    return auth_factory()

register(factories.RetailerPPGMappingFactory,
        '_rpm',
        id=1,
        account_name= "test 1",
        product_group= "test_product_group_1",
        retailer_index=1,
        ppg_index=1,
        created_by= "2021-10-27T20:11:55.586456Z",
        modified_by= {"name":"testadmin","email":"<EMAIL>","user_id":1},
        modified_at= "2021-12-30T07:39:45.789439Z",
        created_at= "2021-11-13T14:14:00Z"
        )

register(factories.ModelTacticFactory,
        '_mtf',
        id=1,
        retailer_ppg_map_id=1,
        promo_type='single',
        avg_units_per_week=0.0,
        tpr_discount_byppg=0.0,
        leaflet_flag=0.0,
        display_flag=0.0,
        ad_without_price_flag=0.0,
        back_page_flag=0.0,
        bonus_flag=0.0,
        front_page_flag=0.0,
        logo_flag=0.0,
        pas_flag=0.0,
        multibuy_flag=0.0,
        te_per_week=0.0,
        list_price_per_week=0.0,
        cogs_per_week=0.0,
        max_week=0.0,
        min_week=0.0,
        min_week_edlp=0.0,
        max_week_edlp=0.0,
        gsv_per_week=0.0,
        created_by= "2021-10-27T20:11:55.586456Z",
        modified_by= {"name":"testadmin","email":"<EMAIL>","user_id":1},
        modified_at= "2021-12-30T07:39:45.789439Z",
        created_at= "2021-11-13T14:14:00Z"
        )

register(factories.ItemMapFactory,
        '_imf',
        id=1,
        account_name= "test 1",
        product_group= "test_product_group_1",
        ppg_item_no="item_no_1",
        created_by= "2021-10-27T20:11:55.586456Z",
        modified_by= {"name":"testadmin","email":"<EMAIL>","user_id":1},
        modified_at= "2021-12-30T07:39:45.789439Z",
        created_at= "2021-11-13T14:14:00Z"
        )

register(factories.ModelMetaFactory,
        '_mmf',
        id= 1,
        account_name= "test 1",
        corporate_segment= "test segment",
        product_group= "test_product_group_1",
        brand= "test brand",
        brand_tech= "test brand tech",
        product_type= "test product_type",
        created_by= {"name":"testadmin","email":"<EMAIL>","user_id":1},
        modified_by= {"name":"testadmin","email":"<EMAIL>","user_id":1},
        modified_at= factory.Faker("date"),
        created_at= factory.Faker("date"),
        slug="test_1-test_segment-test_product_group_1"
        )

register(factories.CoefficientFactory,
        '_cf',
        model_meta_id= 1,
        weight=400,
        wmape= 0.061,
        rsq= 0.9656,
        acv_selling=0.0,
        c_1_external_discount=0.0,
        c_1_external_log_price=0.0,
        c_1_internal_discount=0.0,
        c_1_internal_log_price=0.0,
        c_1_privatelabel_discount=0.0,
        c_1_privatelabel_log_price=0.0,
        c_2_external_discount=0.0,
        flag_promotype_display=0.0,
        flag_promotype_leaflet=0.0,
        flag_promotype_advertising_without_price=0.0,
        flag_promotype_back_page=0.0,
        flag_promotype_bonus=0.0,
        flag_promotype_front_page=0.0,
        flag_promotype_joint_promo_1=0.0,
        flag_promotype_joint_promo_2=0.0,
        flag_promotype_joint_promo_3=0.0,
        flag_promotype_joint_promo_all_brand=0.0,
        flag_promotype_logo=0.0,
        flag_promotype_pas=0.0,
        holiday_flag1=0.0,
        holiday_flag2=0.0,
        intercept=0.0,
        median_base_price_log=0.0,
        flag_nonpromo_1=0.0,
        flag_nonpromo_2=0.0,
        si_week=0.0,
        si_period=0.0,
        si_quarter=0.0,
        tpr_discount_byppg=0.0,
        year_trend=10,
        created_by= {"name":"testadmin","email":"<EMAIL>","user_id":1},
        modified_by= {"name":"testadmin","email":"<EMAIL>","user_id":1},
        modified_at= factory.Faker("date"),
        created_at= factory.Faker("date"),
        )

register(factories.CoefficientMappingFactory,
        '_cmf',
        model_meta_id= 1,
        coefficient_old="wk_sold_median_base_price_byppg_log",
        coefficient_new= "Median_Base_Price_log",
        value=7.66258,
        created_by= {"name":"testadmin","email":"<EMAIL>","user_id":1},
        modified_by={"name":"testadmin","email":"<EMAIL>","user_id":1},
        modified_at= factory.Faker("date"),
        created_at= factory.Faker("date")
        )

register(factories.ModelDataFactory,
        '_mdf',    
        model_meta_id= 1,
        year= 2021,
        date=datetime.datetime(2021, 1, 2),
        month=1,
        week= 1,
        wk_sold_doll_byppg=0.0,
        wk_sold_qty_byppg=0.0,
        wk_sold_avg_price_byppg=0.0,
        wk_sold_qty_byppg_log=0.0, 
        acv_selling=0.0,
        c_1_external_discount=0.0,
        c_1_external_log_price=0.0,
        c_1_internal_discount=0.0,
        c_1_internal_log_price=0.0,
        c_1_privatelabel_discount=0.0,
        c_1_privatelabel_log_price=0.0,
        c_2_external_discount=0.0,
        flag_promotype_display=0.0,
        flag_promotype_leaflet=0.0,
        flag_promotype_advertising_without_price=0.0,
        flag_promotype_back_page=0.0,
        flag_promotype_bonus=0.0,
        flag_promotype_front_page=0.0,
        flag_promotype_joint_promo_1=0.0,
        flag_promotype_joint_promo_2=0.0,
        flag_promotype_joint_promo_3=0.0,
        flag_promotype_joint_promo_all_brand=0.0,
        flag_promotype_logo=0.0,
        flag_promotype_pas=0.0,
        holiday_flag1=0.0,
        holiday_flag2=0.0,
        median_base_price_log=0.0,
        flag_nonpromo_1=0.0,
        flag_nonpromo_2=0.0,
        si_week=0.0,
        si_period=0.0,
        si_quarter=0.0,
        tpr_discount_byppg=0.0,
        year_trend=1,
        created_by= {"name":"testadmin","email":"<EMAIL>","user_id":1},
        modified_by= {"name":"testadmin","email":"<EMAIL>","user_id":1},
        modified_at= factory.Faker("date"),
        created_at= factory.Faker("date")
        )

register(factories.ROIDataFactory,
        '_rdf',
        model_meta_id= 1,
        year= 2021,
        quarter= 1,
        period="P01",
        date=factory.Faker("date"),
        week=1,
        gsv=9878.4,
        nsv=5180.82,
        volume=0.96768,
        nsv_per_unit_future=1.21542032601749,
        cogs_per_unit_future=0.94629287810167,
        gsv_per_unit_future=2.50843871508045,
        total_sold_unit=754804.259999999,
        total_sold_volume=300906,
        pack_weight=10,
        created_by= {"name":"testadmin","email":"<EMAIL>","user_id":1},
        modified_by= {"name":"testadmin","email":"<EMAIL>","user_id":1},
        modified_at= factory.Faker("date"),
        created_at= factory.Faker("date")
        )
