import ast
import pandas as pd
from apps.common import services as cmn_serv,utils as cmn_utils
from apps.promo.promo_optimizer import generic as opt_generic,utils as opt_utils
from core.generics import constants as const, exceptions as excep
from core.generics.unit_of_work import AbstractUnitOfWork

def get_base_promotion(_data,_bwr):
    
    df = pd.DataFrame(_data,columns=const.DATA_HEADER) if isinstance(_data,list) else _data
    df_group = df.groupby(['Retailer','PPG'])
    result_dict = dict.fromkeys(const.DATA_VALUES)
    # iterating through the dictionary and updating values
    for key, value in zip(result_dict.keys(), const.DATA_HEADER):
        result_dict[key] = value
    
    for key,item in df_group:
        flags_and_mech_columns = []
        flags_and_mech_columns = [x for x in df.columns if 'Flag' in x]+['Promotion_Level'\
                                                                         ,'is_standalone'\
                                                                        ,'joint_flag'\
                                                                        ,'type_of_promo'\
                                                                        ,'mechanic']
        a_group = df_group.get_group(key).reset_index()
        a_group = a_group.drop(['account_name_new','product_group_new'],axis=1, errors='ignore')
        acc_name = key[0]
        ppg = key[1]
        df2 = list(filter(lambda x:x[f'{acc_name}_{ppg}_df'],_bwr))[0]
        df2 = _bwr
        df2 = df2.rename(columns={**result_dict})
        df1 = a_group.merge(df2, on='Week', how='left')
        df1=df1.reset_index(drop=True).drop(columns=['index_x'])
        df1=df1.rename(columns={'Retailer_y':'account_name_new','PPG_y':'product_group_new'})
        df1.columns = df1.columns.str.replace(r'_y$', '')
        flags_and_mech_columns=flags_and_mech_columns+['account_name_new','product_group_new']
        df.loc[(df['Retailer']==acc_name) & (df['PPG']==ppg),flags_and_mech_columns] = df1[flags_and_mech_columns].values
  
    result = df.values.tolist() if isinstance(_data,list) else df

    return result
