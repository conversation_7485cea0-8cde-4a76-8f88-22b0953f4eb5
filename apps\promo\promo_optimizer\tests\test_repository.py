""" Test Optimizer Repository"""
import pytest
from .. import repository

@pytest.mark.django_db
def test_active_save_scenario_repo(_saosf):
    _ossr = repository.OptimizerSavedScenarioRepository()
    assert _ossr.get(_id=0) is None
    assert _ossr.get(_id=1) is not None
    assert _saosf.id == 1
    assert _saosf.status_type == 'active'

@pytest.mark.django_db
def test_completed_save_scenario_repo(_scosf):
    _opr = repository.OptimizerSavedScenarioRepository()
    assert _opr.get(_id=0) is None
    assert _opr.get(_id=2) is not None
    assert _scosf.id == 2
    assert _scosf.status_type == 'completed'

@pytest.mark.django_db
def test_completed_save_scenario_wo_data_repo(_scoswodf):
    _opr = repository.OptimizerPromotionRepository()
    assert _opr.get(_id=0) is None
    assert _opr.get(_id=1) is not None
    assert _scoswodf.id == 1
    assert _scoswodf.saved_scenario_id == 1

@pytest.mark.django_db
def test_completed_save_scenario_with_data_repo(_scoswdf):
    _opr = repository.OptimizerPromotionRepository()
    assert _opr.get(_id=0) is None
    assert _opr.get(_id=2) is not None
    assert _scoswdf.id == 2
    assert _scoswdf.saved_scenario_id == 2
