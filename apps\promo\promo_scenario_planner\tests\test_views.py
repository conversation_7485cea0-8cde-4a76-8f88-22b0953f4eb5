""" Test promo Views"""
import json
import pytest
from ..tests import factories,_constants as CONST
from rest_framework.test import APIClient

apiclient = APIClient()

@pytest.mark.django_db(transaction=True)
def test_promo_save_and_retrieve_scenario_view(_django_data_setup,auth):

    apiclient.credentials(**auth['headers'])
    # save with partial data
    data_resp = apiclient.post(
        f"{CONST.PROMO_BASEURL}/save/", 
        format="json",
        data={**factories.save_active_promo_scenario_partial_data_for_api,'name':'promo save 1'}
    )
    
    assert data_resp.status_code == 200
    assert json.loads(data_resp.content)["data"] is not None
    assert json.loads(data_resp.content)["data"] !=[]
    assert "saved_id" in json.loads(data_resp.content)["data"]

    # save with empty data
    apiclient.credentials(**auth['headers'])
    data_resp = apiclient.post(
        f"{CONST.PROMO_BASEURL}/save/", format="json",data={}
    )
    assert data_resp.status_code == 406
    
    #load scenario by saved id
    data_resp = apiclient.get(
        f"{CONST.PROMO_BASEURL}/load/7/", format="json"
    )
    assert data_resp.status_code == 200
    assert json.loads(data_resp.content)["data"] is not None
    assert json.loads(data_resp.content)["data"] !=[]

    #load scenario by random id
    data_resp = apiclient.get(
        f"{CONST.PROMO_BASEURL}/load/1000/", format="json"
    )

    assert data_resp.status_code == 404
    assert json.loads(data_resp.content)["status"]=="NO DATA FOUND"

    # compare scenario with saved id
    data_resp = apiclient.get(
        f"{CONST.PROMO_BASEURL}/compare/?saved_ids=[7]", format="json"
    )
    assert data_resp.status_code == 200
    assert json.loads(data_resp.content)["data"] is not None
    assert json.loads(data_resp.content)["data"] !=[]

    # compare scenario with random value
    data_resp = apiclient.get(
        f"{CONST.PROMO_BASEURL}/compare/?saved_ids=[777777]", format="json"
    )
    assert data_resp.status_code == 404
    assert json.loads(data_resp.content)["status"]=="NO DATA FOUND"

    #search active promo scenario
    data_resp = apiclient.get(
        f"{CONST.PROMO_BASEURL}/search/?q=promo save 1&status_type=active", format="json"
    )
    assert data_resp.status_code == 200
    assert json.loads(data_resp.content)["data"] is not None
    assert json.loads(data_resp.content)["data"] !=[]

    #search active promo scenario with random value
    data_resp = apiclient.get(
        f"{CONST.PROMO_BASEURL}/search/?q=randomvalue&status_type=active", format="json"
    )
    assert data_resp.status_code == 404
    assert json.loads(data_resp.content)["status"]=="NO DATA FOUND"

# @pytest.mark.django_db(transaction=True)
# def test_promo_save_with_data_post_view(_django_data_setup,auth):
#     apiclient.credentials(**auth['headers'])
    # save with data
    data_resp = apiclient.post(
        f"{CONST.PROMO_BASEURL}/save/", 
        format="json",
        data={**factories.save_completed_promo_scenario_with_data_for_api,'name':'promo save 2'}
    )
    assert data_resp.status_code == 200
    assert json.loads(data_resp.content)["data"] is not None
    assert "saved_id" in json.loads(data_resp.content)["data"]
    assert json.loads(data_resp.content)["data"] !=[]

    # save with empty data
    apiclient.credentials(**auth['headers'])
    data_resp = apiclient.post(
        f"{CONST.PROMO_BASEURL}/save/", format="json",data={}
    )
    assert data_resp.status_code == 406

    #load scenario by saved id
    data_resp = apiclient.get(
        f"{CONST.PROMO_BASEURL}/load/8/", format="json"
    )
    assert data_resp.status_code == 200
    assert json.loads(data_resp.content)["data"] is not None
    assert json.loads(data_resp.content)["data"] !=[]

    #load scenario by random id
    data_resp = apiclient.get(
        f"{CONST.PROMO_BASEURL}/load/1000/", format="json"
    )

    assert data_resp.status_code == 404
    assert json.loads(data_resp.content)["status"]=="NO DATA FOUND"

    # compare scenario with saved id
    data_resp = apiclient.get(
        f"{CONST.PROMO_BASEURL}/compare/?saved_ids=[8]", format="json"
    )
    assert data_resp.status_code == 200
    assert json.loads(data_resp.content)["data"] is not None
    assert json.loads(data_resp.content)["data"] !=[]

    # compare scenario with random value
    data_resp = apiclient.get(
        f"{CONST.PROMO_BASEURL}/compare/?saved_ids=[8888]", format="json"
    )
    assert data_resp.status_code == 404
    assert json.loads(data_resp.content)["status"]=="NO DATA FOUND"

    #search completed promo scenario
    data_resp = apiclient.get(
        f"{CONST.PROMO_BASEURL}/search/?q=promo save 2&status_type=completed", format="json"
    )
    assert data_resp.status_code == 200
    assert json.loads(data_resp.content)["data"] is not None
    assert json.loads(data_resp.content)["data"] !=[]

   
    #search completed promo scenario with random value
    data_resp = apiclient.get(
        f"{CONST.PROMO_BASEURL}/search/?q=randomvalue&status_type=completed", format="json"
    )
    assert data_resp.status_code == 404
    assert json.loads(data_resp.content)["status"]=="NO DATA FOUND"
