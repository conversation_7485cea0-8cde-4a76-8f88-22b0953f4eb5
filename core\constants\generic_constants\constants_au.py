META_HEADER=[
    'Id',
    'Created By',
    'Created At',
    'Modified By',
    'Modified At',
    'Is Delete',
    'Is Active',
    'Account Name',
    'Corporate Segment',
    'Product Group',
    'Product Type',
    'Brand',
    'Brand Tech',
    'Slug',   
    'Sub Category',
    'Tech', 
]
META_VALUES=[
    'id',
    'created_by',
    'created_at',
    'modified_by',
    'modified_at',
    'is_delete',
    'is_active',
    'account_name',
    'corporate_segment',
    'product_group',
    'product_type',
    'brand',
    'brand_tech',
    'slug',
    'sub_category',
    'tech',
]

META_DATA_API_VALUES = [
    #primary key (id) needs to be passed here as we are doing raw query, account_name i.e., first column should be passed as query param if you want to pass the columns below it as query params, else raises an exception
    'id',
    'account_name',
    'product_type',
    'sub_category',
    'brand',
    'tech',
    'product_group',
]

