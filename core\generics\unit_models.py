import utils

class UnitModel:
    """ Unit Model."""
    def __init__(self,**inputs):
    
        self.is_standalone = inputs.get('is_standalone')
        self.total_promo_units = inputs.get('total_promo_units')
        self.total_non_promo_total_units = inputs.get('total_non_promo_total_units')
        self.total_promo_nsv = inputs.get('total_promo_nsv')
        self.total_non_promo_nsv = inputs.get('total_non_promo_nsv')
        # self.flag_promotype_joint_promo_1 = inputs.get('flag_promotype_joint_promo_1')
        # self.flag_promotype_joint_promo_2 = inputs.get('flag_promotype_joint_promo_2')
        # self.flag_promotype_joint_promo_3 = inputs.get('flag_promotype_joint_promo_3')
        # self.flag_promotype_joint_promo_4 = inputs.get('flag_promotype_joint_promo_4')
        # self.flag_promotype_joint_promo_5 = inputs.get('flag_promotype_joint_promo_5')
        # self.flag_promotype_joint_promo_6 = inputs.get('flag_promotype_joint_promo_6')
        # self.flag_promotype_joint_promo_7 = inputs.get('flag_promotype_joint_promo_7')
        # self.flag_promotype_joint_promo_8 = inputs.get('flag_promotype_joint_promo_8')
        # self.flag_promotype_joint_promo_9 = inputs.get('flag_promotype_joint_promo_9')
        # self.flag_promotype_joint_promo_10 = inputs.get('flag_promotype_joint_promo_10')
        # self.flag_promotype_joint_promo_11 = inputs.get('flag_promotype_joint_promo_11')
        # self.flag_promotype_joint_promo_12 = inputs.get('flag_promotype_joint_promo_12')
        # self.flag_promotype_joint_promo_13 = inputs.get('flag_promotype_joint_promo_13')
        # self.flag_promotype_joint_promo_14 = inputs.get('flag_promotype_joint_promo_14')
        # self.flag_promotype_joint_promo_15 = inputs.get('flag_promotype_joint_promo_15')
        self.flag_promotype_all_brand = inputs.get('flag_promotype_all_brand')
        self.flag_promotype_multibuy_tpr = inputs.get('flag_promotype_multibuy_tpr')
        self.flag_promotype_lottery = inputs.get('flag_promotype_lottery')
        self.flag_promotype_multibuy = inputs.get('flag_promotype_multibuy')
        self.flag_promotype_unpublished = inputs.get('flag_promotype_unpublished')
        self.flag_promotype_tpr = inputs.get('flag_promotype_tpr')
        # self.flag_promotype_advertising_without_price = inputs.get('flag_promotype_advertising_without_price')
        self.flag_promotype_miscellaneous = inputs.get('flag_promotype_miscellaneous')
        self.leaflet_flag = float(inputs.get('leaflet_flag'))
        self.promo_present = self.leaflet_flag
        # self.promotion_levels = inputs.get('national_promo')
        self.product_group = inputs.get('ppg')
        self.trade_rate = inputs.get('trade_rate')
        self.list_price_per_unit = inputs.get('list_price_per_unit')
        self.date = inputs.get('date')
        self.day_month = utils.day_month(self.date)
        self.week = inputs.get('week')
        self.year = inputs.get('year')
        self.quarter = inputs.get('quarter')
        self.month = inputs.get('month')
        self.period = inputs.get('period')
        self.type_of_promo = inputs.get('type_of_promo')
        # self.si = inputs.get('si')
        self.base_unit = inputs.get('base_unit')
        self.list_price=inputs.get('list_price')
        self.mechanic=inputs.get('mechanic')
        self.promo_depth = float(inputs.get('promo_depth'))
        self.tpr_discount_byppg = self.promo_depth
        self.incremental_unit_before = inputs.get('incremental_unit')
        self.predicted_units=inputs.get('predicted_units')
        self.incremental_unit = self.incremental_unit_before
        self.incremental_unit_old = inputs.get('incremental_unit_old')
        self.base_old = inputs.get('base_unit_old')
        self.median_base_price_log = inputs.get('median_base_price_log')
        self.total_trade_investment = inputs.get('total_trade_investment')
        self.lift = (self.incremental_unit / self.base_unit ) if self.base_unit else 0
        self.asp =  self.median_base_price_log * (1 - ((self.promo_depth )/100))
        self.total_rsv_w_o_vat = self.predicted_units * (self.asp)
        self.promo_asp = 0 if not (self.promo_depth ) else utils._divide(self.total_rsv_w_o_vat,
                                                                    self.predicted_units)
        self.gsv_per_unit_future = inputs.get('gsv_per_unit_future')
        self.nsv_per_unit_future = inputs.get('nsv_per_unit_future')
        self.nsv_per_unit_future = inputs.get('nsv_per_unit_future')
        self.total_lsv = self.predicted_units * float(self.gsv_per_unit_future)
        self.total_nsv = self.predicted_units * float(self.nsv_per_unit_future)
        self.trade_expense = self.total_lsv-self.total_nsv
        self.mars_cogs_per_unit = float(inputs.get('cogs_per_unit_future'))
        self.cogs = inputs.get('total_cogs_per_unit')
        self.retailer_margin =  self.total_rsv_w_o_vat - self.total_nsv
        self.incremental_rsv = self.incremental_unit * float(self.asp)
        self.incremental_lsv = self.incremental_unit * float(self.gsv_per_unit_future)
        self.incremental_nsv = self.incremental_unit * float(self.nsv_per_unit_future)
        self.incremental_rp = self.incremental_rsv-self.incremental_nsv
        self.te_per_units = utils._divide(self.trade_expense,self.predicted_units)
        self.trade_term_percentage = utils._divide(self.trade_expense,self.total_lsv)
        self.incremental_te = self.incremental_lsv * self.trade_term_percentage
        self.total_cogs = self.mars_cogs_per_unit * self.predicted_units
        self.mars_mac = self.total_nsv - self.total_cogs
        self.incremental_cogs = self.incremental_unit * self.mars_cogs_per_unit
        self.incremental_mac = self.incremental_nsv - self.incremental_cogs
        self.incremental_gmac = self.incremental_lsv - self.incremental_cogs
        self.retailer_margin_percent_of_nsv = utils.percentile(self.retailer_margin,self.total_nsv)
        self.retailer_margin_percent_of_rsp = utils.percentile(self.retailer_margin,self.total_rsv_w_o_vat)
        self.mars_mac_percent_of_nsv = utils.percentile(self.mars_mac,self.total_nsv)
        self.te_percent_of_lsv = utils.percentile(self.trade_expense,self.total_lsv)
        self.gmac_roi = utils._divide(self.incremental_mac,self.incremental_te)+1
        self.roi = self.gmac_roi
        self.total_sold_volume = inputs.get('total_sold_volume')
        self.total_sold_unit = inputs.get('total_sold_unit')
        self.vol_on_deal = utils._divide(self.total_sold_unit,self.total_sold_volume)
        self.total_sold_volume = inputs.get('total_sold_volume')
        self.vol_on_deal_percent = utils.percentile(self.total_sold_unit,self.total_sold_volume)

class TotalUnit:
    """ Total Unit."""
    total_rsv_w_o_vat = 0
    units = 0
    te= 0
    lsv = 0
    nsv = 0
    mac = 0
    rp = 0
    asp = 0
    avg_promo_selling_price = 0
    roi = 0
    rp_percent = 0
    mac_percent = 0
    volume = 0
    te_per_unit = 0
    te_percent_of_lsv = 0
    base_units = 0
    increment_units = 0
    lift = 0
    cogs = 0
    uplift_gmac_gsv = 0
    total_uplift_cost = 0
    gmac_roi = 0
    incremental_te=0
    incremental_gmac=0
    promo_base_units=0
    promo_increment_units=0
    promo_incremental_gmac=0
    promo_incremental_te=0
    lift_percent=0
    total_sold_volume=0
    total_sold_unit=0
    vol_on_deal_percent=0
    vol_on_deal=0
    trade_term_percentage=0
    promo_te=0
    promo_lsv=0
    gsv_per_unit_future=0
    promo_gsv_per_unit_future=0
    incremental_mac=0
    promo_incremental_mac=0
    incremental_nsv=0
    incremental_lsv=0
    incremental_rsv=0
    promo_incremental_nsv=0
    promo_incremental_lsv=0
    promo_incremental_rsv=0
    promo_mac=0
    non_promo_mac=0
    national_mac=0
    no_of_leaflet_count = 0
    incremental_rp=0
    promo_incremental_rp=0
