import datetime
import numpy as np
from . import _constants as CONST

def handle_quarter_and_period():
    weeks_52_data_dict = {'quarter':np.arange(CONST.WEEKS_COUNT),\
                            'period':np.arange(CONST.WEEKS_COUNT)\
                            ,'date':([datetime.datetime(2020, d+1, 27)\
                            for d in range(10)]+[datetime.datetime(2021, d+1, 27)\
                            for d in range(0,3)]),
                            'month':np.arange(CONST.WEEKS_COUNT)
                            }
    weeks_52_period_data_list  = np.arange(int(CONST.WEEKS_COUNT/CONST.PERIOD_COUNT))
    weeks_52_quarter_data_list  = np.arange(int(CONST.WEEKS_COUNT/CONST.QUARTER_COUNT))
    period_starts = 0
    period_ends = CONST.PERIOD_COUNT
    quarter_starts = 0
    quarter_ends = CONST.QUARTER_COUNT
    for period in weeks_52_period_data_list:
        weeks_52_data_dict['period'][period_starts:period_ends] = period+1
        weeks_52_data_dict['month'][period_starts:period_ends] = period+1
        if (period+1) == 13:
            weeks_52_data_dict['month'][period_starts:period_ends]=3
        
        if period in [10,11,12]:
            period = int(str(period)[1])
        weeks_52_data_dict['date'][period_starts:period_ends] = [datetime.datetime(2020\
                                                                , period+1, 27)]*4
        period_starts = period_ends
        period_ends+=4
    for quarter in weeks_52_quarter_data_list:
        weeks_52_data_dict['quarter'][quarter_starts:quarter_ends] = quarter+1
        quarter_starts = quarter_ends
        quarter_ends+=13

    return weeks_52_data_dict