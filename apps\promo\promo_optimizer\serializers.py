""" Optimizer serializers"""
from rest_framework import serializers
from apps.common import models as db_model
from . import constants as opt_const

class OptimizerSerializer(serializers.Serializer):
    """OptimizerSerializer"""
    OBJ_CHOICES = (
        ("MAC", "MAC"),
        ("RP", "RP"),
        ("Trade_Expense", "Trade Expense"),
        ("Units", "Units"),
        ("NSV", "NSV"),
    )

    account_name = serializers.CharField()
    corporate_segment = serializers.CharField()
    brand = serializers.CharField(allow_blank=True,allow_null=True)
    product_group = serializers.CharField()
    product_type = serializers.CharField(allow_blank=True,allow_null=True)
    brand_tech = serializers.CharField(allow_blank=True,allow_null=True)
    promo_type = serializers.CharField()
    visibility = serializers.ListField()
    promo_price = serializers.ListField()
    promo_depth = serializers.ListField()
    ppg_belongs_to = serializers.Char<PERSON><PERSON>(allow_blank=True,allow_null=True)
    mechanic = serializers.ListField()
    objective_function = serializers.ChoiceField(choices = OBJ_CHOICES)
    fin_pref_order = serializers.ListField()
    config_mac = serializers.BooleanField(default=True,initial=True)
    config_rp = serializers.BooleanField(default=True,initial=True)
    config_trade_expense = serializers.BooleanField(default=False,initial=False)
    config_units = serializers.BooleanField(default=False,initial=False)
    config_nsv = serializers.BooleanField(default=False,initial=False)
    config_gsv = serializers.BooleanField(default=False,initial=False)
    config_sales = serializers.BooleanField(default=False,initial=False)
    config_mac_perc = serializers.BooleanField(default=False,initial=False)
    config_rp_perc = serializers.BooleanField(default=True,initial=True)
    config_min_consecutive_promo = serializers.BooleanField(default=True,initial=True)
    config_max_consecutive_promo = serializers.BooleanField(default=True,initial=True)
    config_promo_gap = serializers.BooleanField(default=True,initial=True)
    config_automation = serializers.BooleanField(default=False,initial=False)
    config_no_of_promo = serializers.BooleanField(default=False,initial=False)
    config_no_of_leaflet = serializers.BooleanField(default=False,initial=False)
    no_of_slots = serializers.FloatField(default=0,initial=0)
    is_all_brand = serializers.BooleanField(default=False,initial=False)
    is_single = serializers.BooleanField(default=False,initial=False)
    is_joint = serializers.BooleanField(default=False,initial=False)
    param_mac = serializers.FloatField(max_value = 10 ,min_value=0,default=1.0,initial=1.0)
    param_rp = serializers.FloatField(max_value = 10 ,min_value=0,default=1.0,initial=1.0)
    param_trade_expense = serializers.FloatField(max_value = 10 ,min_value=0,default=1.0,initial=1.0)
    param_units = serializers.FloatField(max_value = 10 ,min_value=0,default=1.0,initial=1.0)
    param_nsv = serializers.FloatField(max_value = 10 ,min_value=0,default=1.0,initial=1.0)
    param_gsv = serializers.FloatField(max_value = 10 ,min_value=0,default=1.0,initial=1.0)
    param_sales =serializers.FloatField(max_value = 10 ,min_value=0,default=1.0,initial=1.0)
    param_mac_perc = serializers.FloatField(max_value = 10 ,min_value=0,default=1.0,initial=1.0)
    param_rp_perc = serializers.FloatField(max_value = 10 ,min_value=0,default=1.0,initial=1.0)
    param_min_consecutive_promo = serializers.IntegerField(max_value = 1 ,min_value=0,default=1,initial=1)
    param_max_consecutive_promo = serializers.IntegerField(max_value = 4 ,min_value=0,default=1,initial=1)
    param_total_promo_min = serializers.IntegerField(max_value = 52 ,min_value=0,default=0,initial=0)
    param_total_promo_max  = serializers.IntegerField(max_value = 52 ,min_value=0,default=0,initial=0)
    param_compulsory_no_promo_weeks =serializers.ListField()
    param_compulsory_promo_weeks = serializers.ListField()

    def __init__(self,*args, **kwargs):
        super().__init__(self,*args, **kwargs)

    class Meta:
        ref_name = None

class PromotionLevelSerializer(serializers.Serializer):
    """ Promotion Levels Serializer."""
    class Meta:
        """ Class Meta for PromotionLevels Serializer."""
        model = db_model.PromotionLevels
        fields = '__all__'
        ref_name = None

class OptimizerRequestSerializer(serializers.Serializer):
    """ Optimizer Serializer."""
    promo_data = serializers.JSONField(default=opt_const.OPTIMIZER_API_PAYLOAD)

class OptimizerRequestAndResponseSerializer(serializers.Serializer):
    """ Save Optimizer Serializer."""
    data = serializers.JSONField(default=opt_const.OPTIMIZER_OUTPUT_PAYLOAD)

class OptimizerUpdatedResponseSerializer(serializers.Serializer):
    """ Save Optimizer Serializer."""
    updated_id = 1

class OptimizerSavedResponseSerializer(serializers.Serializer):
    """ Save Optimizer Serializer."""
    saved_id = 1

class OptimizerSaveRequestSerializer(serializers.Serializer):
    """ Save Optimizer Serializer."""
    data = opt_const.OPTIMIZER_API_SAVE_PAYLOAD

class OptimizerListResponseSerializer(serializers.Serializer):
    """ Save Optimizer Serializer."""
    data = opt_const.OPTIMIZER_LIST_RESPONSE
