from django.urls import path

from .views import PricingTargetView, SetTargetSavedScenarioView

urlpatterns = [
    path('get_pricing_target_data/', PricingTargetView.as_view({'get':'get_pricing_target_data'})),
    path("save/", SetTargetSavedScenarioView.as_view({'post':'save_scenario'})),
    path('load/<int:saved_id>/', SetTargetSavedScenarioView.as_view({'get':'load_scenario'})),
    path("download/<int:saved_id>/",SetTargetSavedScenarioView.as_view({'post':'download'})),
    path("search/", SetTargetSavedScenarioView.as_view({'get':'search'})),
    path("publish/", SetTargetSavedScenarioView.as_view({'post':'publish_scenario'})),
    path('customer_targets/',SetTargetSavedScenarioView.as_view({'post':'get_customer_targets'}))

]
