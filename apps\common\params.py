""" Parameters Enum"""
from enum import Enum

class SearchParam(Enum):
    """ Search Parameter"""
    Q = 'test scenario'
    STATUS_TYPE='active'
    
    @classmethod
    def optional_param(cls):
        """ Return optional Parameter"""
        return ['status_type']

class GetModelDataParam(Enum):
    """ Model Data Parameter"""
    META_ID = 1

class GetScenarioConstraintsParam(Enum):
    """ Scenario Constraints Parameter"""
    META_IDS = []
