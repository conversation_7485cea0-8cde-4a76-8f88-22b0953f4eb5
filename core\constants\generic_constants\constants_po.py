from enum import Enum
META_HEADER=[
    'Id',
    'Created By',
    'Created At',
    'Modified By',
    'Modified At',
    'Is Delete',
    'Is Active',
    'Account Name',
    'Corporate Segment',
    'Product Group',
    'Product Type',
    'Brand',
    'Brand Tech',
    'Slug',    
]

META_VALUES=[
    'id',
    'created_by',
    'created_at',
    'modified_by',
    'modified_at',
    'is_delete',
    'is_active',
    'account_name',
    'corporate_segment',
    'product_group',
    'product_type',
    'brand',
    'brand_tech',
    'slug',
]

META_VALUES_EXT_LIST = list(set(META_VALUES) - set(['id','created_by','created_at','modified_by','modified_at','is_delete','is_active',]))
META_HEADER_EXT_LIST = list(set(META_HEADER) - set(['Id','Created By','Created At','Modified By','Modified At','Is Delete','Is Active',]))

DATA_VALUES = [
# 'ppg_item',
'date',
'account_name',
'product_group',
'brand',
'brand_tech',
'product_type',
'corporate_segment',
'year',
'wk_sold_polish_zloty_byppg',
'wk_sold_qty_byppg',
'wk_sold_qty_byppg_log',
'wk_sold_avg_price_byppg',
'wk_sold_median_base_price_byppg_log',
'wk_sold_median_base_price_byppg_log_previous_years',
'tpr_discount_byppg',
'tpr_discount_byppg_lag1',
'tpr_discount_byppg_lag2',
'acv',
'trend_year',
'trend_quarter',
'si_periodicaly_commodity',
'si_quarterly_commodity',
'si_periodicaly_brand',
'si_quarterly_brand',
'holiday_flag7',
'holiday_flag10',
'flag_promotype_miscellaneous',
'flag_promotype_multibuy',
'flag_promotype_multibuy_tpr',
'flag_promotype_tpr',
#  'pred_vol_log',
#  'pred_vol',
'nonpromo_flag_date_1',
'nonpromo_flag_date_2',
'nonpromo_flag_date_3',
#  'nonpromo_flag_date_4',
#  'nonpromo_flag_date_5',
'promo_flag_date_4',
#  'promo_flag_date_5',
#  'ppg_category',
'holiday_flag4',
'holiday_flag6',
'promo_flag_date_1',
'promo_flag_date_2',
'promo_flag_date_3',
'holiday_flag9',
'holiday_flag11',
'holiday_flag3',
'flag_promotype_unpublished',
'promo_flag_1',
'holiday_flag8',
'promo_flag_2',
'non_promo_flag_1',
'holiday_flag2',
'promo_flag_3',
'holiday_flag1',
'holiday_flag5',
'promo_flag_4',
'flag_promotype_defects',
'flag_promotype_lottery',
'c_1_promoteddiscount',
'c_1_regularprice',
'c_2_promoteddiscount',
'c_2_regularprice',
'c_3_promoteddiscount',
'c_3_regularprice',
'c_4_promoteddiscount',
'c_4_regularprice',
'c_5_promoteddiscount',
'c_5_regularprice',
'c_6_promoteddiscount',
'c_6_regularprice',
'c_7_promoteddiscount',
'c_7_regularprice',
'intercept',
'model_meta_id',
'promo_present',
'week',
'month',
#  'ppg',
'period',
'quarter',
#  'type_of_promo',
'c_1_acv',
'c_2_acv', 
'c_3_acv',
'flag_promotype_regular_price_communication',
'flag_promotype_all_brand',
'flag_visibility_gazetka',
'flag_visibility_gazetka_bystrzaki',
'flag_visibility_gazetka_k_card',
'flag_visibility_gazetka_mocniaki',
'flag_visibility_gazetka_tv',
'flag_visibility_gazetka_modul_aranzowany',
'flag_visibility_promocja_polkowa',
]

DATA_HEADER = [
# 'ppg_item',
'Date',
'Retailer',
'PPG',
'Brand',
'brand_tech',
'product_type',
'Segment',
'year',
'wk_sold_polish_zloty_byppg',
'wk_sold_qty_byppg',
'wk_sold_qty_byppg_log',
'wk_sold_avg_price_byppg',
'wk_sold_median_base_price_byppg_log',
'wk_sold_median_base_price_byppg_log_previous_years',
'TPR',
'tpr_discount_byppg_lag1',
'tpr_discount_byppg_lag2',
'acv',
'trend_year',
'trend_quarter',
'si_periodicaly_commodity',
'si_quarterly_commodity',
'si_periodicaly_brand',
'si_quarterly_brand',
'holiday_flag7',
'holiday_flag10',
'flag_promotype_miscellaneous',
'flag_promotype_multibuy',
'flag_promotype_multibuy_tpr',
'flag_promotype_tpr',
#  'pred_vol_log',
#  'pred_vol',
'nonpromo_flag_date_1',
'nonpromo_flag_date_2',
'nonpromo_flag_date_3',
#  'nonpromo_flag_date_4',
#  'nonpromo_flag_date_5',
'promo_flag_date_4',
#  'promo_flag_date_5',
#  'ppg_category',
'holiday_flag4',
'holiday_flag6',
'promo_flag_date_1',
'promo_flag_date_2',
'promo_flag_date_3',
'holiday_flag9',
'holiday_flag11',
'holiday_flag3',
'flag_promotype_unpublished',
'promo_flag_1',
'holiday_flag8',
'promo_flag_2',
'non_promo_flag_1',
'holiday_flag2',
'promo_flag_3',
'holiday_flag1',
'holiday_flag5',
'promo_flag_4',
'flag_promotype_defects',
'flag_promotype_lottery',
'c_1_promoteddiscount',
'c_1_regularprice',
'c_2_promoteddiscount',
'c_2_regularprice',
'c_3_promoteddiscount',
'c_3_regularprice',
'c_4_promoteddiscount',
'c_4_regularprice',
'c_5_promoteddiscount',
'c_5_regularprice',
'c_6_promoteddiscount',
'c_6_regularprice',
'c_7_promoteddiscount',
'c_7_regularprice',
'Intercept',
'model_meta_id',
'promo_present',
'Week',
'month',
#  'ppg',
'period',
'quarter',
#  'type_of_promo',
'c_1_acv',
'c_2_acv', 
'c_3_acv',
'flag_promotype_regular_price_communication',
'flag_promotype_all_brand',
'flag_visibility_gazetka',
'flag_visibility_gazetka_bystrzaki',
'flag_visibility_gazetka_k_card',
'flag_visibility_gazetka_mocniaki',
'flag_visibility_gazetka_tv',
'flag_visibility_gazetka_modul_aranzowany',
'flag_visibility_promocja_polkowa',
]

RETAILER_PPG_MAPPING_VALUES = [
    'id',
'created_by',
'created_at',
'modified_by',
'modified_at',
'is_delete',
'is_active',
'account_name',
'product_group',
'retailer_index',
'ppg_index',
'ppg_item',
'type_of_promo'
]
RETAILER_PPG_MAPPING_HEADER =[
    'id',
'created_by',
'created_at',
'modified_by',
'modified_at',
'is_delete',
'is_active',
'account_name',
'product_group',
'Retailer_Index',
'ppg_index',
'ppg_item',
'type_of_promo'
]

ROI_HEADER = [
    'Retailer',
    'Segment',
    'PPG',
    'Brand',
    'Brand_Tech',
    'Product_Type',
    'Date',
    'Year',
    'Quarter',
    'Week',
    'Period',
    'Promo Price',
    'List Price',
    'GSV',
    'NSV',
    'Volume',
    'Units',
    'NSV_Per_Unit_Future',
    'COGS_Per_Unit_Future',
    'GSV_Per_Unit_Future',
    'COGS',
    'Total Sold Unit',
    'Total Sold Volume',
    'Pack Weight',
    'Total Trade Investment',
    'Tactic Medium Hz',
    'Sum Non Promo Units',
    'Sum Promo Units',
]

ROI_VALUES = [
    'account_name', 
    'corporate_segment', 
    'product_group',
    'brand',
    'brand_tech',
    'product_type',
    'date',
    'year',
    'quarter',
    'week',
    'period',
    'promo_price',
    'list_price',
    'gsv',
    'nsv',
    'volume',
    'units',
    'nsv_per_unit_future',
    'cogs_per_unit_future',
    'gsv_per_unit_future',
    'cogs',
    'total_sold_unit',
    'total_sold_volume',
    'pack_weight',
    'total_trade_investment',
    'tactic_medium_hz',
    'sum_non_promo_units',
    'sum_promo_units',
]

class ModelRetailerPPGMap(Enum):
    MODEL_NAME = 'retailer_ppg_mapping'
    MODEL_COLUMN = RETAILER_PPG_MAPPING_HEADER
    MODEL_VALUES = RETAILER_PPG_MAPPING_VALUES
    REQUIRED_COLUMN = [('a.id','retailer_ppg_map_id')]
    EXTRA_COLUMNS=[('type_of_promo','Type_of_Promo')]

class ModelData(Enum):
    MODEL_NAME = 'model_data'
    MODEL_COLUMN = DATA_HEADER
    MODEL_VALUES = DATA_VALUES
    FK_MODEL = 'model_meta'
    FK_NAME = 'model_meta'
    YEAR_WISE = False
    FIN_YEAR_WISE = False
    REQUIRED_COLUMN = [('a.id','model_data_id')]
    EXTRA_COLUMNS = [('b.id','meta_id')]
class ModelROI(Enum):
    MODEL_NAME = 'model_roi'
    MODEL_COLUMN = ROI_HEADER
    MODEL_VALUES = ROI_VALUES
    FK_MODEL = 'model_meta'
    FK_NAME = 'model_meta'
    YEAR_WISE = False
    FIN_YEAR_WISE = True
    REQUIRED_COLUMN = [('a.id','roi_id')]
    EXTRA_COLUMNS = [('b.id','meta_id')]

COEFF_MAP_HEADER =[
#     'Id',
# 'Created By',
# 'Created At',
# 'Modified By',
# 'Modified At',
# 'Is Delete',
# 'Is Active',
    'Retailer',
    'PPG',
'Coefficient',
'Coefficient_new',
'Value',
'PPG_Item',
]

COEFFICIENT_MAP_VALUES =[
#     'id',
# 'created_by',
# 'created_at',
# 'modified_by',
# 'modified_at',
# 'is_delete',
# 'is_active',
'account_name',
'product_group',    
'coefficient_old',
'coefficient_new',
'value',
'ppg_item',
]


class ModelCoeffMap(Enum):
    MODEL_NAME = 'coefficient_mapping'
    MODEL_COLUMN = COEFF_MAP_HEADER
    MODEL_VALUES = COEFFICIENT_MAP_VALUES
    FK_MODEL = 'model_meta'
    FK_NAME = 'model_meta'
    YEAR_WISE = False
    REQUIRED_COLUMN = [('a.id','coeff_map_id')]
    EXTRA_COLUMNS = [('b.id','meta_id')]

COEFF_HEADER = [
    'Retailer',
    'Segment',
    'PPG',
    'Brand',
    'Brand_Tech',
    'Product_Type',
'acv',
'c_1_promoteddiscount',
'c_1_regularprice',
'c_2_promoteddiscount',
'c_2_regularprice',
'c_3_promoteddiscount',
'c_3_regularprice',
'c_4_promoteddiscount',
'c_4_regularprice',
'c_5_promoteddiscount',
'c_5_regularprice',
'c_6_promoteddiscount',
'c_6_regularprice',
'c_7_promoteddiscount',
'c_7_regularprice',
'flag_promotype_defects',
'flag_promotype_lottery',
'flag_promotype_miscellaneous',
'flag_promotype_multibuy',
'flag_promotype_multibuy_tpr',
'flag_promotype_tpr',
'flag_promotype_unpublished',
'holiday_flag1',
'holiday_flag10',
'holiday_flag11',
'holiday_flag2',
'holiday_flag3',
'holiday_flag5',
'holiday_flag6',
'holiday_flag7',
'holiday_flag8',
'holiday_flag9',
'intercept',
'non_promo_flag_1',
'promo_flag_1',
'promo_flag_2',
'promo_flag_3',
'promo_flag_4',
'promo_flag_date_1',
'si_periodicaly_brand',
'si_periodicaly_commodity',
'si_quarterly_brand',
'si_quarterly_commodity',
'trend_year',
'tpr_discount_byppg',
'tpr_discount_byppg_lag1',
'tpr_discount_byppg_lag2',
'wk_sold_median_base_price_byppg_log',
'wk_sold_median_base_price_byppg_log_previous_years',
'model_meta_id',
'Rsq',
'WMAPE'
]

COEFFICIENT_VALUES = [
    'account_name',
    'corporate_segment',
    'product_group',
    'brand',
    'brand_tech',
    'product_type,'
'acv',
'c_1_promoteddiscount',
'c_1_regularprice',
'c_2_promoteddiscount',
'c_2_regularprice',
'c_3_promoteddiscount',
'c_3_regularprice',
'c_4_promoteddiscount',
'c_4_regularprice',
'c_5_promoteddiscount',
'c_5_regularprice',
'c_6_promoteddiscount',
'c_6_regularprice',
'c_7_promoteddiscount',
'c_7_regularprice',
'flag_promotype_defects',
'flag_promotype_lottery',
'flag_promotype_miscellaneous',
'flag_promotype_multibuy',
'flag_promotype_multibuy_tpr',
'flag_promotype_tpr',
'flag_promotype_unpublished',
'holiday_flag1',
'holiday_flag10',
'holiday_flag11',
'holiday_flag2',
'holiday_flag3',
'holiday_flag5',
'holiday_flag6',
'holiday_flag7',
'holiday_flag8',
'holiday_flag9',
'intercept',
'non_promo_flag_1',
'promo_flag_1',
'promo_flag_2',
'promo_flag_3',
'promo_flag_4',
'promo_flag_date_1',
'si_periodicaly_brand',
'si_periodicaly_commodity',
'si_quarterly_brand',
'si_quarterly_commodity',
'trend_year',
'tpr_discount_byppg',
'tpr_discount_byppg_lag1',
'tpr_discount_byppg_lag2',
'wk_sold_median_base_price_byppg_log',
'wk_sold_median_base_price_byppg_log_previous_years',
'model_meta_id',
'rsq',
'wmape'
]

class ModelCoeff(Enum):
    MODEL_NAME = 'model_coefficient'
    MODEL_COLUMN = COEFF_HEADER
    MODEL_VALUES = COEFFICIENT_VALUES
    FK_MODEL = 'model_meta'
    FK_NAME = 'model_meta'
    YEAR_WISE = False
    REQUIRED_COLUMN = [('a.id','coeff_id')]
    EXTRA_COLUMNS = [('b.id','meta_id')]

RETAILER_PPG_MAPPING_WEEKLY_VALUES =[
   'account_name', 
   'product_group',
'promo_type',
'ret_ppg_idx',
'type_promo_id',
# 'type_promo',
'avg_units_per_week',
'cogs_per_week',
'nsv_per_week',
'te_per_week',
'gsv_per_week',
# 'list_price_per_week',
]
RETAILER_PPG_MAPPING_WEEKLY_HEADER=[
    'Retailer',
    'PPG',
'Promo_Type',
'ref_ppg_idx',
'type_promo_id',
# 'Type Promo',
'Avg_units_per_Week',
'COGS_perweek',
'NSV_perweek',
'TE_perweek',
'GSV_perweek',
# 'List Price Per Week'
]

class ModelRETAILER_PPG_MAPPING_WEEKLY(Enum):
    MODEL_NAME = 'retailer_ppg_mapping_weekly'
    MODEL_COLUMN = RETAILER_PPG_MAPPING_WEEKLY_HEADER
    MODEL_VALUES = RETAILER_PPG_MAPPING_WEEKLY_VALUES
    FK_MODEL = 'retailer_ppg_mapping_weekly'
    FK_NAME = 'retailer_ppg_map'
    REQUIRED_COLUMN = [('a.id','rpm_weekly_id')]
    EXTRA_COLUMNS = [('retailer_index','Retailer_Index'),
                    ('ppg_index','PPG_Index'),
                    ('type_of_promo','Type_of_Promo')]
    
TACTIC_VALUES =[
        # 'ppg_item',
    #   'manufacturer',
      'customer',
      'promo_type',
      'effective_tpr',
      'ppg',
      'tactic_miscellaneous',
      'tactic_multibuy_tpr',
      'tactic_multibuy',
      'tactic_tpr',
      'tactic_unpublished',
      'tactic_defects',
      'tactic_lottery',
      'leaflet_flag',
      'min_week',
      'max_week',
      'retailer_ppg_map_id',
    #   'type_of_promo',
]

TACTIC_HEADER =[
        # 'ppg_item',
    #   'manufacturer',
      'Retailer',
      'Promo_Type',
      'TPR',
      'PPG',
      'tactic_miscellaneous',
      'tactic_multibuy_tpr',
      'tactic_multibuy',
      'tactic_tpr',
      'tactic_unpublished',
      'tactic_defects',
      'tactic_lottery',
      'Leaflet_flag',
      'min_week',
      'max_week',
      'retailer_ppg_map_id',
    #   'type_of_promo',
]

class ModelTactic(Enum):
    MODEL_NAME = 'model_tactic'
    MODEL_COLUMN = TACTIC_HEADER
    MODEL_VALUES = TACTIC_VALUES
    FK_MODEL = 'retailer_ppg_mapping'
    FK_NAME = 'retailer_ppg_map'
    REQUIRED_COLUMN = [('a.id','tactic_id')]
    EXTRA_COLUMNS = [('retailer_index','Retailer_Index'),
                    ('ppg_index','PPG_Index'),
                    ('type_of_promo','Type_of_Promo')
                    ]
    
ITEM_MAPPING_HEADER = [
    'Id',
'Created By',
'Created At',
'Modified By',
'Modified At',
'Is Delete',
'Is Active',
'Retailer',
'PPG',
'PPG_Item_No',
]
ITEM_MAPPING_VALUES = [
    'id',
'created_by',
'created_at',
'modified_by',
'modified_at',
'is_delete',
'is_active',
'account_name',
'product_group',
'ppg_item',
]
class ITEMMap(Enum):
    MODEL_NAME = 'item_map'
    MODEL_COLUMN = ITEM_MAPPING_HEADER
    MODEL_VALUES = ITEM_MAPPING_VALUES
    REQUIRED_COLUMN = [('a.id','item_map_id')]
    EXTRA_COLUMNS=[]
