from enum import Enum


class PricingPayload(Enum):
    promo_data:list=[]
    
    @classmethod
    def get_method(self):
        return 'POST'

class PricingPayload(Enum):
    level = "product_group"
    name= "test scenario"
    scenario_type= "simulator"
    module_type= "set_pricing"
    status_type= "completed"
    mode="filtered"
    non_committed_id=168
    pricing_payload=[]
    optimized_nn_target=10
    target=10
    actual_budget=10
    invest_budget=10
    calculated_nn_percent=10

    @classmethod
    def get_method(self):
        return 'POST'
    
    @classmethod
    def optional_param(cls):
        """ Return optional Parameter"""
        return ['non_committed_id','optimized_nn_target','target','invest_budget','actual_budget','calculated_nn_percent']
    
class CustomerPayload(Enum):
    level = "product_group"
    name= "test scenario"
    scenario_type= "simulator"
    module_type= "set_pricing"
    status_type= "completed"
    mode="filtered"
    non_committed_id=168
    pricing_payload=[]

    @classmethod
    def get_method(self):
        return 'POST'
    
    @classmethod
    def optional_param(cls):
        """ Return optional Parameter"""
        return ['non_committed_id']
    
class PricingInputConstraints(Enum):
    meta_ids:list=[]