""" Joint Promotion"""
import numpy as np
import pandas as pd
from pulp import (pulp,
                  lpSum,
                  LpStatus,
                  LpVariable,
                  LpProblem,
                  LpMaximize,
                  LpMinimize,
                  PULP_CBC_CMD
                  )
from functools import partial,reduce
from apps.common import utils as cmn_utils
from apps.promo.promo_optimizer import utils as opt_utils,mixins as opt_mixin
from tasks.parallel_task import parallel_task_executioner
from .. import generic as opt_generic                               
from ..optimizer_tool.single_calendar import process_single_promo

def get_required_base_3(baseline_data:pd.DataFrame, 
                        model_coeff:pd.DataFrame,
                        df_new1:pd.DataFrame,
                        _summary_df:pd.DataFrame,
                        ppg_item_num:pd.DataFrame):
    """get_required_base_3

    Args:
        baseline_data (pd.DataFrame): baseline_data
        model_coeff (pd.DataFrame): model_coeff
        df_new1 (pd.DataFrame): df_new1
        _summary_df (pd.DataFrame): _summary_df
        ppg_item_num (pd.DataFrame): ppg_item_num

    Returns:
        pd.DataFrame: Required Base 
    """
    ## Also need to pass config mutliple retailer * ppg used
    # Optimizer scenario creation
    # getting model variables
    model_cols = model_coeff['names'].to_list()
    model_cols.remove('Intercept')
    base = baseline_data[['wk_base_price_perunit_byppg','Promo', 'GSV_Per_Unit_Future',
                          'COGS_Per_Unit_Future','NSV_Per_Unit_Future'] + [c for c in model_cols if c in baseline_data.columns]]
    
    i = 1 ##0(iteration starting from 1 to match with iteration numbers from stage1)
    df_new_1 = df_new1.sort_values("Promo_Type", ascending=True).reset_index(drop=True)
    tpr_list = df_new_1[["TPR", "Promo_Type",'Mechanic']].values

    if "Tactic_Leaflet" in df_new1.columns:
        mech_list=list(df_new1['Tactic_Leaflet'])
    if "Tactic_Display" in df_new1.columns:    
        disp_list=list(df_new_1['Tactic_Display'])

    ret = _summary_df['Retailer'].unique()[0]
    
    # len(tpr_list) effectively contains the number of Promo_Types
    for k,_tpr in enumerate(tpr_list): ## change
        required_base = base.copy() 
        required_base['TPR'] = tpr_list[k][0]  ## change
        if 'TPR_lag1' in model_cols:
            required_base['TPR_lag1'] = 0
        if 'TPR_lag2' in model_cols:
            required_base['TPR_lag2'] = 0
        required_base['Promo'] = required_base['wk_base_price_perunit_byppg']\
            -(required_base['wk_base_price_perunit_byppg']*required_base['TPR']/100)

        if 'Tactic_Leaflet' in model_cols:
            if mech_list[k]>0:
                required_base['Tactic_Leaflet'] = np.where(required_base['TPR']>0,1,0)
            else:
                required_base['Tactic_Leaflet'] = 0

        if 'Tactic_Display' in model_cols:
            if disp_list[k] > 0:
                _l1 = ['Penny','ReweVoSo']
                if ret in _l1:
                    display_val = required_base['Tactic_Display'].max()
                    required_base['Tactic_Display'] = np.where(required_base['TPR']>0,display_val,0)
                else:
                    required_base['Tactic_Display'] = np.where(required_base['TPR']>0,1,0)
            else:
                required_base['Tactic_Display'] = 0
        
        required_base['Units'] = opt_generic.predict_sales(model_coeff,required_base)
        required_base['Promo Price'] = required_base['wk_base_price_perunit_byppg']\
        -(required_base['wk_base_price_perunit_byppg']*(required_base['TPR']/100))
        required_base['Sales'] = required_base['Units'] * required_base['Promo Price']
        # creating flag for promo price based on the promo price constraint
        # calculating the financial metrics
        required_base["GSV"] = required_base['Units'] * required_base['GSV_Per_Unit_Future']
        required_base["NSV"] = required_base['Units'] * required_base["NSV_Per_Unit_Future"]
        required_base["Trade_Expense"] =  required_base["GSV"] - required_base["NSV"]
        required_base["MAC"] = required_base["NSV"] - (required_base['Units'] * 
                                                       required_base['COGS_Per_Unit_Future'])
        required_base["RP"] = required_base['Sales'] - required_base["NSV"]
        required_base["TPR"] = tpr_list[k][0]
        promo_type = tpr_list[k][1]      
        required_base["Mechanic"]=tpr_list[k][2]  
        required_base["Iteration"] = i       
        required_base['OptimizedWeeksPromo'] = _summary_df[_summary_df['Promo_Type']==promo_type]\
                                                ['Optimized_Weeks'].unique()[0] ## Added new
        if i==1:
            new_base = required_base
        else:
            new_base = new_base.append(required_base)

        i = i + 1
    required_base = new_base
    required_base = required_base.reset_index(drop=True)
    required_base['WK_ID'] = required_base.index
    # creating unique ids for optimization
    required_base['WK_ID'] = 'WK_' + required_base['WK_ID'].astype(str) + '_' \
                             + required_base['Iteration'].astype(str)
    required_base['Prod'] = ppg_item_num

    return required_base

def optimizer_fun_3(baseline_data:pd.DataFrame, 
                    required_base:pd.DataFrame,
                    config:dict,
                    joint_ppg_week_nums:list
                    ): ##df_stg2a_calendar_1
    """optimizer_fun_3

    Args:
        baseline_data (pd.DataFrame): baseline_data
        baseline_data (pd.DataFrame): baseline_data
        config (dict): config
    """
    # getting the user input
    config_constrain = config['config_constrain']
    constrain_params = config['constrain_params']
    
    # getting the number of promotions (including zero)
    promo_loop = required_base['Iteration'].nunique()
        
    # defining variable type
    wk_dv_vars = list(required_base['WK_ID'])
    wk_vars = LpVariable.dicts("RP",wk_dv_vars,cat='Binary')
    # selecting the objective based on user input
    if config['Objective']=='Maximize':
        print("Maximize")
        prob = LpProblem("Simple_Workaround_problem", LpMaximize)
    else:
        print("Minimize")
        prob = LpProblem("Simple_Workaround_problem", LpMinimize)

    # defining objective function
    obj_metric = config['Objective_metric']

    prob += lpSum([wk_vars[required_base['WK_ID'][i]] * (required_base[obj_metric][i]) 
                   for i in range(0, required_base.shape[0])])


    if config_constrain['MAC'] :
        prob+=lpSum([wk_vars[required_base['WK_ID'][i]] * required_base['MAC'][i] 
                     for i in range(0, required_base.shape[0])])
    # RP constraint
    if config_constrain['RP']:
        prob+=lpSum([wk_vars[required_base['WK_ID'][i]] * (required_base['RP'][i]) 
                     for i in range(0, required_base.shape[0])])

    # Set up constraints such that only one tpr is chose for a week
    for i in range(0,52):
        prob+=lpSum([wk_vars[required_base['WK_ID'][i + j*52]] for j in range(0, promo_loop)]) <= 1
        prob+=lpSum([wk_vars[required_base['WK_ID'][i + j*52]] for j in range(0, promo_loop)]) >= 1

  ##Set up constraints such that 52 weeks chosen
    if config_constrain['52Weeks']:
        prob+=lpSum([wk_vars[required_base['WK_ID'][i]]  for i in \
                range(0, required_base.shape[0])]) <= 52
        prob+=lpSum([wk_vars[required_base['WK_ID'][i]]  for i in \
                range(0, required_base.shape[0])]) >= 52

#########-------------------------------------New Constraints#########################    
    
    ### Constraints for Weeks of different PromoTypes
    # For promo_type = 0
    default_promo_loop = 11
    if (promo_loop < default_promo_loop):
        for j in range(0,promo_loop):
            prob+=lpSum([wk_vars[required_base['WK_ID'][i+j*52]] for i in range(0,52)])\
                <=required_base[required_base['Iteration']==j+1]['OptimizedWeeksPromo'].unique()[0]
            prob+=lpSum([wk_vars[required_base['WK_ID'][i+j*52]] for i in range(0,52)])\
                >=required_base[required_base['Iteration']==j+1]['OptimizedWeeksPromo'].unique()[0]
    else:
        custom_tpr_promo = []
        for j in range(0, promo_loop):
            if (j!=1) and (j < default_promo_loop):
                prob+=lpSum([wk_vars[required_base['WK_ID'][i+j*52]] for i in range(0,52)])\
                    <=required_base[required_base['Iteration']==j+1]['OptimizedWeeksPromo'].unique()[0]
                prob+=lpSum([wk_vars[required_base['WK_ID'][i+j*52]] for i in range(0,52)])\
                    >=required_base[required_base['Iteration']==j+1]['OptimizedWeeksPromo'].unique()[0]
            else:
                custom_tpr_promo.append(j)

        prob+=lpSum([wk_vars[required_base['WK_ID'][i+j*52]] 
                     for j in custom_tpr_promo
                     for i in range(0,52)]) <= required_base[required_base['Iteration']==2]\
                         ['OptimizedWeeksPromo'].unique()[0]
        prob+=lpSum([wk_vars[required_base['WK_ID'][i+j*52]] 
                     for j in custom_tpr_promo
                     for i in range(0,52)]) >= required_base[required_base['Iteration']==2]\
                         ['OptimizedWeeksPromo'].unique()[0] 
    
    # constraint for compulsory promo weeks
    if len(constrain_params['compul_promo_weeks'])>0:
        promo_list = constrain_params['compul_promo_weeks'].copy() ## change added new
        promo_list[:]=[i-1 for i in promo_list]
        prob+=lpSum([wk_vars[required_base['WK_ID'][i+j*52]] 
                     for j in range(1,promo_loop) for i in promo_list]) >= len(promo_list)    
    
    # constraint for compulsory no promo weeks
    for i in joint_ppg_week_nums:        
        for j in range(1, promo_loop):            
            prob += wk_vars[required_base["WK_ID"][i+j*52]] <= 0
            prob += wk_vars[required_base["WK_ID"][i+j*52]] >= 0
    
    # Sub constraint for minimum weeks
    r_0=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(0,1)])
    r_1=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(1,2)])
    r_2=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(2,3)])
    r_3=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(3,4)])
    r_4=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(4,5)])
    r_5=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(5,6)])
    r_6=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(6,7)])
    r_7=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(7,8)])
    r_8=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(8,9)])

    # Sub constraint for minimum weeks
    r_51=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(50,51)])
    r_52=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(51,52)])
    r_50=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(49,50)])
    r_49=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(48,49)])
    r_48=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(47,48)])
    r_47=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(46,47)])
    r_46=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(45,46)])
    r_45=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(44,45)])
    r_44=lpSum([wk_vars[required_base['WK_ID'][i+j*52]]for j in range(1,promo_loop) \
        for i in range(43,44)])

    if config_constrain['min_consecutive_promo']:
        if constrain_params['min_consecutive_promo']==2:
            prob+=r_51-r_52 >=0
            prob+=r_1-r_0 >=0
        if constrain_params['min_consecutive_promo']==3:
            prob+=r_51-r_52 >=0
            prob+=r_1-r_0 >=0
            prob+=2*r_50-r_51-r_52 >=0
            prob+= 2*r_2-r_1-r_0 >=0
        if constrain_params['min_consecutive_promo']==4:
            prob+=r_51-r_52 >=0
            prob+=r_1-r_0 >=0
            prob+=2*r_50-r_51-r_52 >=0
            prob+= 2*r_2-r_1-r_0 >=0
            prob+= 3*r_49-r_50-r_51-r_52 >=0
            prob+= 3*r_3-r_2-r_1-r_0 >=0
        if constrain_params['min_consecutive_promo']==5:
            prob+=r_51-r_52 >=0
            prob+=r_1-r_0 >=0
            prob+=2*r_50-r_51-r_52 >=0
            prob+= 2*r_2-r_1-r_0 >=0
            prob+= 3*r_49-r_50-r_51-r_52 >=0
            prob+= 3*r_3-r_2-r_1-r_0 >=0
            prob+= 4*r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 4*r_4-r_3-r_2-r_1-r_0 >=0
        if constrain_params['min_consecutive_promo']==6:
            prob+=r_51-r_52 >=0
            prob+=r_1-r_0 >=0
            prob+=2*r_50-r_51-r_52 >=0
            prob+= 2*r_2-r_1-r_0 >=0
            prob+= 3*r_49-r_50-r_51-r_52 >=0
            prob+= 3*r_3-r_2-r_1-r_0 >=0
            prob+= 4*r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 4*r_4-r_3-r_2-r_1-r_0 >=0
            prob+= 5*r_47-r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 5*r_5-r_4-r_3-r_2-r_1-r_0 >=0
        if constrain_params['min_consecutive_promo']==7:
            prob+=r_51-r_52 >=0
            prob+=r_1-r_0 >=0
            prob+=2*r_50-r_51-r_52 >=0
            prob+= 2*r_2-r_1-r_0 >=0
            prob+= 3*r_49-r_50-r_51-r_52 >=0
            prob+= 3*r_3-r_2-r_1-r_0 >=0
            prob+= 4*r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 4*r_4-r_3-r_2-r_1-r_0 >=0
            prob+= 5*r_47-r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 5*r_5-r_4-r_3-r_2-r_1-r_0 >=0
            prob+= 6*r_46-r_47-r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 6*r_6-r_5-r_4-r_3-r_2-r_1-r_0 >=0
        if constrain_params['min_consecutive_promo']==8:
            prob+=r_51-r_52 >=0
            prob+=r_1-r_0 >=0
            prob+=2*r_50-r_51-r_52 >=0
            prob+= 2*r_2-r_1-r_0 >=0
            prob+= 3*r_49-r_50-r_51-r_52 >=0
            prob+= 3*r_3-r_2-r_1-r_0 >=0
            prob+= 4*r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 4*r_4-r_3-r_2-r_1-r_0 >=0
            prob+= 5*r_47-r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 5*r_5-r_4-r_3-r_2-r_1-r_0 >=0
            prob+= 6*r_46-r_47-r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 6*r_6-r_5-r_4-r_3-r_2-r_1-r_0 >=0
            prob+= 7*r_45-r_46-r_47-r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 7*r_7-r_6-r_5-r_4-r_3-r_2-r_1-r_0 >=0
        if constrain_params['min_consecutive_promo']==9:
            prob+=r_51-r_52 >=0
            prob+=r_1-r_0 >=0
            prob+=2*r_50-r_51-r_52 >=0
            prob+= 2*r_2-r_1-r_0 >=0
            prob+= 3*r_49-r_50-r_51-r_52 >=0
            prob+= 3*r_3-r_2-r_1-r_0 >=0
            prob+= 4*r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 4*r_4-r_3-r_2-r_1-r_0 >=0
            prob+= 5*r_47-r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 5*r_5-r_4-r_3-r_2-r_1-r_0 >=0
            prob+= 6*r_46-r_47-r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 6*r_6-r_5-r_4-r_3-r_2-r_1-r_0 >=0
            prob+= 7*r_45-r_46-r_47-r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 7*r_7-r_6-r_5-r_4-r_3-r_2-r_1-r_0 >=0
            prob+= 8*r_44-r_45-r_46-r_47-r_48-r_49-r_50-r_51-r_52 >=0
            prob+= 8*r_8-r_7-r_6-r_5-r_4-r_3-r_2-r_1-r_0 >=0
    
    # contraint for max promo length
    if config_constrain['max_consecutive_promo']:
        for k in range(0,52-constrain_params['max_consecutive_promo']):
            for j in range(1,promo_loop):
                prob+=lpSum([wk_vars[required_base['WK_ID'][i+j*52]] 
                             for i in range(k,k+constrain_params['max_consecutive_promo']+1)])\
                            <= constrain_params['max_consecutive_promo']
        for k in range(0,52-constrain_params['max_consecutive_promo']):  
            prob+=lpSum([wk_vars[required_base['WK_ID'][i+j*52]] 
                         for i in range(k,k+constrain_params['max_consecutive_promo']+1) 
                         for j in range(1,promo_loop)])<=constrain_params['max_consecutive_promo']
 
  # Constrain for min promo wave length
    if config_constrain['min_consecutive_promo']:
        for k in range(0,52-constrain_params['min_consecutive_promo']): ##(0,50)
            r_1_sum = lpSum([wk_vars[required_base['WK_ID'][i+j*52]] 
                            for i in range(k+1,min(k+constrain_params['min_consecutive_promo']\
                            +1,52)) for j in range(1,promo_loop)])
            r_2_sum = lpSum([wk_vars[required_base['WK_ID'][k+j*52]] for j in \
                    range(1,promo_loop)])
            r_3_sum = lpSum([wk_vars[required_base['WK_ID'][k+1+j*52]] for j in \
                    range(1,promo_loop)])
            gap_weeks = len(range(k+1, min(52, k+constrain_params['min_consecutive_promo']+1)))
            prob+= r_1_sum + gap_weeks * r_2_sum >= gap_weeks * r_3_sum

        for k in range(0,52-constrain_params['min_consecutive_promo']): ##(0,50)
            for j in range(1,promo_loop):
                r_1_sum = lpSum([wk_vars[required_base['WK_ID'][i+j*52]] 
                                for i in range(k+1,min(k+constrain_params['min_consecutive_promo']\
                                +1,52))])
                r_2_sum = lpSum([wk_vars[required_base['WK_ID'][k+j*52]] ])
                r_3_sum = lpSum([wk_vars[required_base['WK_ID'][k+1+j*52]]])
                gap_weeks = len(range(k+1, min(52, k+constrain_params['min_consecutive_promo']+1)))
                prob+= r_1_sum + gap_weeks * r_2_sum >= gap_weeks * r_3_sum
    #  week gap constraint
    if config_constrain['promo_gap']:
        for k in range(0,51):
            r_1_sum = lpSum([wk_vars[required_base['WK_ID'][i+j*52]] 
                            for j in range(0,1) 
                            for i in range(k+1,min(k+constrain_params['promo_gap']+1,52))])
            r_2_sum= lpSum([wk_vars[required_base['WK_ID'][k+j*52]] for j in range(0,1)])
            r_3_sum = lpSum([wk_vars[required_base['WK_ID'][k+1+j*52]] for j in range(0,1)])
            gap_weeks = len(range(k+1, min(52, k+constrain_params['promo_gap']+1)))
            prob+= r_1_sum + gap_weeks * r_2_sum >= gap_weeks * r_3_sum
  
    # max seconds to execute optimizer is 1min
    prob.solve(PULP_CBC_CMD(msg=False,maxSeconds=60))
    weeks =[]
    value = []

  # creating the optimal calendar from the prob output
    for variable in prob.variables():
        if variable.varValue==1.0:
            weeks.append(str(variable.name))
            value.append(variable.varValue)
    _df= pd.DataFrame(list(zip(weeks, value)), 
                 columns =['weeks', 'val']) 

    _df['Iteration'] = _df["weeks"].apply(lambda x: x.split('_')[3]).astype(int)
    _df['Week_no'] = _df["weeks"].apply(lambda x: x.split('_')[2]).astype(int)
    _df['Week_no'] = (_df['Week_no'] - ((_df['Iteration'] * 52) - 52)) + 1
    _df = _df.sort_values('Week_no',ascending = True).reset_index(drop=True)
   
    col_req_base = list(required_base.columns) ## change
    list_tact = (['Iteration','TPR','Mechanic'] + 
                 [i for i in col_req_base if "Tactic_Leaflet" in i ] + 
                 [i for i in col_req_base if "Tactic_Display" in i ]) ## change      
    tprs = required_base[list_tact].drop_duplicates().reset_index(drop=True)    
    
    _df = pd.merge(_df, tprs, on=['Iteration'], how='left')
    _df = _df.sort_values('Week_no', ascending = True).reset_index(drop=True)
 
    # getting the solution status and optimum value
    _df['Solution'] = LpStatus[prob.status]
    return(_df, prob)  

def master_func_4(baseline_data:pd.DataFrame, 
                  required_base:pd.DataFrame,
                  config:dict,
                  joint_ppg_week_nums:list
                  )->pd.DataFrame:
    """master_func_4

    Args:
        baseline_data (pd.DataFrame): baseline_data
        required_base (pd.DataFrame): required_base
        config (dict): config

    Returns:
        pd.DataFrame: dataframe
    """
    optimal_calendar_fin = pd.DataFrame()
    # config and config temp will be same in all the ppgs without lag variables

    optimal_calendar, prob = optimizer_fun_3(baseline_data, required_base, config,joint_ppg_week_nums) #optimal_calendar
    
    opt_pop_up_flag = 0
    if (optimal_calendar.shape[0]==52) and (optimal_calendar['Solution'].unique()=='Optimal'):
    #Default Run Based On User's Config Step 101
        opt_pop_up_flag = 1
        optimal_calendar_fin = optimal_calendar.copy()
   
    if opt_pop_up_flag==0 : #optimal_calendar_fin.shape[0]!=52
        config_temp = config.copy()
        for _i in range(0,5):
            config_temp['TE_threshold'] = config_temp['TE_threshold']+0.01 ## add steps of 0.01
            optimal_calendar,prob = optimizer_fun_3(baseline_data, required_base, config_temp,joint_ppg_week_nums)
            if (optimal_calendar.shape[0]==52) and (optimal_calendar['Solution']\
                .unique()=='Optimal'):
                opt_pop_up_flag = 1
                break

    if (optimal_calendar.shape[0]!=52) or (optimal_calendar['Solution'].unique()!='Optimal'):
        return optimal_calendar_fin, prob

    return optimal_calendar, prob

def optimal_summary_fun_4(baseline_data, model_coeff): #optimal_calendar
    """optimal_summary_fun_4

    Args:
        baseline_data (_type_): baseline_data
        model_coeff (_type_): model_coeff
    """
    model_cols = model_coeff['names'].to_list()
    model_cols.remove('Intercept')
    # selecting the required columns
    base = baseline_data[["Date", 'Retailer', 'wk_base_price_perunit_byppg', 'Promo',
                          'GSV_Per_Unit_Future', 'COGS_Per_Unit_Future'\
                        , 'NSV_Per_Unit_Future'] + [c for c in model_cols if c in baseline_data.columns]] ## change added 0902 
    new_data = base.copy()

    # changing the variable that related to promotion
    # lag variable creation
    if 'TPR_lag1' in model_cols:
        new_data['TPR_lag1'] = new_data['TPR'].shift(1).fillna(0)
    if 'TPR_lag2' in model_cols:
        new_data['TPR_lag2'] = new_data['TPR'].shift(2).fillna(0)

    new_data['Promo Price'] = (new_data['wk_base_price_perunit_byppg'] - 
                    (new_data['wk_base_price_perunit_byppg'] * (new_data['TPR']/100)))

    new_data['wk_sold_avg_price_byppg'] = new_data['Promo Price'] ## needed change?

    new_data['Units'] = opt_generic.predict_sales(model_coeff, new_data)
    new_data['Sales'] = new_data['Units'] * new_data['Promo Price']
    new_data["GSV"] = new_data['Units'] * new_data['GSV_Per_Unit_Future']
    new_data["NSV"] = new_data['Units'] * new_data["NSV_Per_Unit_Future"]

    new_data["Trade_Expense"] = new_data["GSV"] - new_data["NSV"]
    new_data["Trade_Expense_onPromo"] = np.where(new_data['TPR'] > 0, 
                                new_data["GSV"] - new_data["NSV"], 0) ## change added new ROI

    new_data["MAC"] = new_data["NSV"] - (new_data['Units'] * new_data['COGS_Per_Unit_Future'])
    new_data["RP"] = new_data['Sales'] - new_data["NSV"]
    
    new_data_1 = new_data[['Date', 'Sales', "GSV", "Trade_Expense", "Trade_Expense_onPromo", 
                           "NSV", "MAC", "RP", "Units"]]
    
    return(new_data_1, new_data)


def process_joint_promo(**constraints):
    """process_joint_promo

    Args:
        constraints (dict): dict consists of dataframe

    Returns:
        pd.DataFrame: dataframe
    """
    item_map = constraints.get('item_map_df')
    item_map.dropna(how="all", inplace=True)
    model_data_all = constraints.get('mdl_df')
    coeff_mapping = constraints.get('coeff_mapping_df')
    roi_data=constraints.get('roi_df')
    summary_df = constraints.get('summary_df')
    # breakpoint()
    one_shot_ppg_df = pd.DataFrame(columns=summary_df.columns)
    exclude_ppgs_list = []
    if one_shot_ppg_df.shape[0]:
        one_shot_ppg_ids = list(one_shot_ppg_df["PPG"].unique())
        exclude_ppgs_list += one_shot_ppg_ids
        print(f"exclude_ppg_list: {exclude_ppgs_list}")

    summary_df = summary_df.loc[(~summary_df["PPG"].isin(exclude_ppgs_list))].reset_index(drop=True)
   
    cols = ['Retailer', 'PPG', 'Ret_idx','PPG_idx','Type_of_Promo','maxWeek', 'minWeek']
    ret_ppg_id_new = summary_df[cols]
    ret_ppg_id_new = ret_ppg_id_new.drop_duplicates(subset=cols).reset_index(drop=True)

    ret_ppg_id_new["PPG_Item_No"] = None
    ret_ppg_id_new["Promo_Cat"] = None
    
    for ind in range(ret_ppg_id_new.shape[0]):
        type_of_promo = ret_ppg_id_new.loc[ind, "Type_of_Promo"] 
        retailer = ret_ppg_id_new.loc[ind, "Retailer"]
        ppg_name = ret_ppg_id_new.loc[ind, "PPG"].replace('&',' ').replace(',',' ').replace(' ','_')
        if type_of_promo.lower() == 'single':   
            
            ppg_item_no = item_map.loc[(item_map["Retailer"]==retailer) \
            & (item_map["PPG"]==ppg_name), "PPG_Item_No"].values[0] 
            ret_ppg_id_new.loc[ind, "PPG_Item_No"] = ppg_item_no

        elif type_of_promo.lower()  == 'joint' or  type_of_promo.lower()  == 'all_brand':
            if "all_brand" in ppg_name:
                ret_ppg_id_new.loc[ind, "Promo_Cat"] = "all_brand"

            ppg_name = opt_generic.format_all_brand(ppg_name).split('_-_')
            
            joint_ppgs = []
            joint_ppg_items = []
            for ppg_name in ppg_name:
                joint_ppgs.append(ppg_name)
   
                ppg_item_no = item_map.loc[(item_map["Retailer"]==retailer) & 
                                            (item_map["PPG"]==ppg_name), "PPG_Item_No"].values[0]
        
                joint_ppg_items.append(ppg_item_no)
                
            joint_ppg_name = ",".join(joint_ppgs)
            ret_ppg_id_new.loc[ind, "PPG"] = joint_ppg_name
            joint_ppg_item_no = ",".join(joint_ppg_items)
            ret_ppg_id_new.loc[ind, "PPG_Item_No"] = joint_ppg_item_no

    ret_ppg_id_new = ret_ppg_id_new.drop_duplicates(subset=["Retailer", "PPG", "Ret_idx", 
                                                            "PPG_idx", "PPG_Item_No"]).reset_index(drop=True)
    ret_ppg_id_new.loc[ret_ppg_id_new["Promo_Cat"]=="all_brand", "PPG"] \
        = ret_ppg_id_new.loc[ret_ppg_id_new["Promo_Cat"]=="all_brand", "PPG"] + ",all_brand"
    ret_ppg_id_new.loc[ret_ppg_id_new["Promo_Cat"]=="all_brand", "PPG_Item_No"] \
        = ret_ppg_id_new.loc[ret_ppg_id_new["Promo_Cat"]=="all_brand", "PPG_Item_No"] + ",all_brand"
    summary_df_new = summary_df.merge(ret_ppg_id_new[['Ret_idx','PPG_idx']],on=['Ret_idx','PPG_idx'],
                                how='left')
    summary_df_new_1 = summary_df_new.merge(ret_ppg_id_new[['Ret_idx','PPG_idx','PPG_Item_No']],
                                        on=['Ret_idx','PPG_idx'],
                                        how='left')

    ### To get avg tpr values in df_new2

    item_map1 = item_map.copy()
    item_map1.rename(columns={'PPG':'PPG_Own'},inplace=True)
    coeff_mapping = coeff_mapping.merge(item_map1[['Retailer'\
                    ,'PPG_Own']],left_on=['Retailer','PPG'],
                        right_on=['Retailer','PPG_Own'],how='left')
    coeff_mapping.drop(['PPG_Own'],axis=1,inplace=True)
    ret_ppg_to_slct = model_data_all[["Retailer", "PPG", "Segment"]].drop_duplicates()
    ## Removing this beacuse TPR 0 
    df_tpr_0 = model_data_all.groupby(['Retailer','PPG','Segment']).agg({'TPR':'sum'}).reset_index()
    df_tpr_0 = df_tpr_0[df_tpr_0['TPR']==0].reset_index(drop=True)
    df_tpr_0 = df_tpr_0[['Retailer','PPG','Segment']]
    df_tpr_0['flag_rem'] = 1
    
    ret_ppg_df = ret_ppg_to_slct.copy()
    ret_ppg_df = ret_ppg_df.reset_index(drop=True)
    ret_ppg_df = ret_ppg_df.merge(df_tpr_0,on=['Retailer','PPG','Segment'],how='left')
   
    ret_ppg_df = ret_ppg_df[ret_ppg_df['flag_rem'].isnull()].reset_index(drop=True)
    ret_ppg_df = ret_ppg_df[['Retailer','PPG','Segment']]
    ret_ppg_df1 = ret_ppg_df.copy()
    ret_ppg_df1['flag_keep'] = 1

    final_pred_data_all = pd.DataFrame()
    baseline_data_all = pd.DataFrame()
    model_coeff_all = pd.DataFrame()
    median_acv_df = model_data_all.groupby(["Retailer", "PPG"], as_index=False).agg(ACV_median=("acv_selling", np.median))
    for i in range(ret_ppg_df.shape[0]):
        
        slct_retailer = ret_ppg_df['Retailer'][i]
        slct_ppg = ret_ppg_df['PPG'][i]
        slct_segment = ret_ppg_df['Segment'][i]
        coeff_mapping_temp = coeff_mapping.loc[(coeff_mapping['Retailer']==slct_retailer) & 
                                            (coeff_mapping['PPG']==slct_ppg)]
        col_dict = dict(zip(coeff_mapping_temp['Coefficient_new'], coeff_mapping_temp['Coefficient']))
        col_dict_2 = dict(zip(coeff_mapping_temp['Coefficient'], coeff_mapping_temp['Coefficient_new']))
        if "Intercept" in col_dict_2.keys():
            col_dict_2.pop('Intercept')
        if "Intercept" in col_dict.keys():
            col_dict.pop('Intercept')
        # getting idvs present for the retailer, ppg
 
        idvs = [x.lower() for x in coeff_mapping_temp['Coefficient_new'].tolist()]
        if "intercept" in idvs:
            idvs.remove('intercept')
        if "tpr_discount_byppg" in idvs:
            index = idvs.index('tpr_discount_byppg')
            idvs[index] = 'TPR'
        model_data = model_data_all.loc[(model_data_all['Retailer']==slct_retailer) & 
                                        (model_data_all['PPG']==slct_ppg)].reset_index(drop=True)
        
        ### Replacing Baseline TPRs with AVG tpr value
        # avg_tpr_replace = round(model_data[model_data['Flag_promotype_Leaflet']!=0]['TPR'].mean())
        # model_data['TPR'] = np.where(model_data['Flag_promotype_Leaflet']>0, avg_tpr_replace, model_data['TPR'])
        no_leaflet_flag = False
        num_promo_weeks = model_data[model_data['promo_present']!=0].shape[0]
        is_leaflet_present = len([1 for x in coeff_mapping_temp['Coefficient_new'].unique() if 'promo_present' in x])

        if (num_promo_weeks == 0) or (is_leaflet_present == 0):
            avg_tpr_replace = 0
            if "promo_present" not in coeff_mapping_temp["Coefficient_new"].to_list():
                no_leaflet_flag = True
        else:
            avg_tpr_replace = round(model_data[model_data['promo_present']!=0]['TPR'].mean()) ## Leaflet promo change
 
        model_data['TPR'] = np.where(model_data['promo_present']>0, avg_tpr_replace, model_data['TPR'])
        model_data = model_data.merge(median_acv_df, on=["Retailer", "PPG"])
        model_data["ACV_Selling"] = model_data["ACV_median"]

        # Replace Median_Base_Price_log with 4 weeks rolling mean
        # model_data["Median_Base_Price_log"] = model_data["MBP_log_rolling_mean"]

        # model_data["TPR_10_above"] = np.where(model_data["Flag_promotype_Leaflet"]>0, model_data["TPR"], 0)
        # model_data["TPR_5_10"] = 0
        # model_data["tpr_discount_byppg_2019"] = 0    
        model_data = model_data[['Date','Retailer'] +  [ i for i in idvs if i in model_data.columns]]
        model_data.rename(columns=col_dict,inplace=True)
        model_data.rename(columns={"tpr_discount_byppg": "TPR"}, inplace=True)
        # getting model coefficients values with original names and format 
        model_coeff = coeff_mapping_temp[['Coefficient','Value','PPG','Retailer'\
                    ,'Coefficient_new']]
        model_coeff.rename(columns={'Value':'model_coefficients',
                                    'Coefficient_new':'names'},inplace=True)
        promo_list_ppg = roi_data[(roi_data\
                    ['Retailer'] == slct_retailer) & 
                    (roi_data['PPG'] == slct_ppg)].reset_index(drop=True)
        period_data=promo_list_ppg[['Date','GSV_Per_Unit_Future',
                                    'NSV_Per_Unit_Future','COGS_Per_Unit_Future']] ## change added 0902

        
        model_coeff_list_keep=list(model_coeff['names'])
        if "Intercept" in model_coeff_list_keep:
            model_coeff_list_keep.remove('Intercept')
        model_coeff.loc[model_coeff["names"]=="tpr_discount_byppg", "names"] = "TPR"

        period_data.loc[:, 'Date']=pd.to_datetime(period_data['Date'], format='%Y-%m-%d')

        model_data.loc[:, 'Date']=pd.to_datetime(model_data['Date'], format='%Y-%m-%d')

        final_pred_data=pd.merge(period_data,model_data,how="left",on="Date")
        
        final_pred_data['wk_base_price_perunit_byppg'] = np.exp(final_pred_data['wk_sold_median_base_price_byppg_log'])-1
    
        final_pred_data['Promo'] = np.where(final_pred_data['TPR'] == 0\
                ,final_pred_data['wk_base_price_perunit_byppg'],
                final_pred_data['wk_base_price_perunit_byppg'] \
                * (1-(final_pred_data['TPR']/100)))

        final_pred_data['wk_sold_avg_price_byppg'] = final_pred_data['Promo'] ## needed change?
    
        if 'TPR_lag1' in model_coeff_list_keep:
            final_pred_data['TPR_lag1']= final_pred_data['TPR'].shift(1).fillna(0) ## change added 0902
        if 'TPR_lag2' in model_coeff_list_keep:
            final_pred_data['TPR_lag2']= final_pred_data['TPR'].shift(2).fillna(0) ## change added 0902

        final_pred_data['Baseline_Prediction']=opt_generic.predict_sales(model_coeff,final_pred_data)
        final_pred_data['Baseline_Sales']=final_pred_data['Baseline_Prediction'] \
                                          *final_pred_data['Promo']
        final_pred_data["Baseline_GSV"] = final_pred_data['Baseline_Prediction']\
                                         * final_pred_data['GSV_Per_Unit_Future']
        final_pred_data["Baseline_NSV"] = final_pred_data['Baseline_Prediction']\
                                        * final_pred_data['NSV_Per_Unit_Future']
        ## Only in promo weeks
        final_pred_data["Baseline_Trade_Expense"] = final_pred_data["Baseline_GSV"] \
                                                    - final_pred_data["Baseline_NSV"]
        # Tactic_Medium_HZ
        if no_leaflet_flag:
            final_pred_data["Baseline_Trade_Expense_onPromo"] = 0
        else:
            final_pred_data["Baseline_Trade_Expense_onPromo"] \
                = np.where(final_pred_data['Tactic_Medium_HZ']>0, 
                final_pred_data["Baseline_GSV"] - final_pred_data["Baseline_NSV"],
                0) ## Leaflet promo change

        final_pred_data["Baseline_MAC"] = final_pred_data["Baseline_NSV"]\
                -(final_pred_data['Baseline_Prediction'] * final_pred_data['COGS_Per_Unit_Future'])
        final_pred_data["Baseline_RP"] = final_pred_data['Baseline_Sales']\
                -final_pred_data["Baseline_NSV"]
        final_pred_data['Retailer'] = slct_retailer
        final_pred_data['PPG'] = slct_ppg
        final_pred_data['Segment'] = slct_segment
        final_pred_data['Mechanic'] = ''
        baseline_data = final_pred_data.copy()
        baseline_data_all=baseline_data_all.append(baseline_data)
        final_pred_data_all=final_pred_data_all.append(final_pred_data)
        model_coeff_all = model_coeff_all.append(model_coeff)

    config_default_3 = {
        "Retailer":'Default',"PPG":'Default','Segment':'Default','TE_threshold':1,
        'Objective_metric': "MAC", "Objective":"Maximize",
        'config_constrain':{
            'MAC': False,'RP': False,'Trade_Expense': False,'Units': False, "NSV": False\
            , "GSV": False,
            "Sales":False,'MAC_Perc':False,
            "RP_Perc":False,'min_consecutive_promo':True,'max_consecutive_promo':True,
            'promo_gap':True,'tot_promo_min':False,'tot_promo_max':False,
            'promo_price':False,'automation':False,'52Weeks':True
        },
        'constrain_params': {
            'MAC':1,'RP':1,'Trade_Expense':1,'Units':1,'NSV':1,
            'GSV':1,'Sales':1,'MAC_Perc':1,'RP_Perc':1,
            'min_consecutive_promo':1, 'max_consecutive_promo':1,
            'promo_gap':2, 'tot_promo_min':2, 'tot_promo_max':20, 'compul_no_promo_weeks':[],
            'compul_promo_weeks' :[],'promo_price':0
        }
    }

    df_only_normal_new = ret_ppg_id_new.copy()
    df_only_normal_new = df_only_normal_new[["Retailer", "PPG", "Ret_idx"\
                        , "PPG_idx", "PPG_Item_No",'Promo_Cat']]

    df_only_normal_new["num_ppgs"] = df_only_normal_new["PPG_Item_No"]\
                                    .map(lambda x: len(x.split(",")))
    df_only_normal_new = df_only_normal_new.loc[df_only_normal_new["num_ppgs"] > 1]\
                        .reset_index(drop=True)
    df_only_normal_new["TPR_list"] = -1

    #all brand
    df_only_normal_new_all_brand = df_only_normal_new.loc[(df_only_normal_new\
                                    ["Promo_Cat"]=="all_brand")].reset_index(drop=True)
    temp_all_brand_ppg_item_list = df_only_normal_new_all_brand["PPG_Item_No"].to_list()
    temp_all_brand_ppg_item_list = [",".join(x.split(",")[0:-1]) for x in temp_all_brand_ppg_item_list]
    all_brand_ppg_list = []
    for ppg in temp_all_brand_ppg_item_list:
        temp_ppg_list = ppg.split(",") # change
        for temp_ppg in temp_ppg_list:
            if temp_ppg not in all_brand_ppg_list:
                all_brand_ppg_list.append(temp_ppg)

    temp_model_coeff_all = model_coeff_all.copy()
    temp_model_coeff_all["PPG"] = temp_model_coeff_all["PPG"]

    temp_all_brand_ppg_list = df_only_normal_new_all_brand["PPG"].to_list()
    temp_all_brand_ppg_list = [",".join(x.split(",")[0:-1]) for x in temp_all_brand_ppg_list]
    all_brand_ppgs = [y for x in temp_all_brand_ppg_list for y in x.split(",")]
    ppgs_dict = {k: [] for k in all_brand_ppgs}
    ppgs_dict['joint_ppg'] = ''
    temp_baseline_data_all = baseline_data_all.copy()
    
    config_df = pd.DataFrame(constraints.get('input_data')['data'])
    config_default_3 = config_default_3
    opt_summary_filtered_combined = pd.DataFrame()
    df_promo_weeks_all_brand = pd.DataFrame(columns=['Date', 'Retailer', 'PPG', 'PPG_No', 'Units', 'TPR', 'status',
                                                 'Leaflet_flag', 'Newsletter_flag', 'Advertising_without_price_flag',
                                                 'Bonus_flag', 'Pas_flag', 'Coupon_flag', 'EDLP_flag', 
                                                 'Multibuy_flag'])
    # @parallel_task_executioner3
    # def get_all_brand_calendar(**kwargs):
    if df_only_normal_new_all_brand.shape[0] > 0:
        for i in range(df_only_normal_new_all_brand.shape[0]):# Handle all brand promotion

            slct_retailer = df_only_normal_new_all_brand['Retailer'][i]
            joint_ppg = df_only_normal_new_all_brand["PPG"][i]
            ppg_names = joint_ppg.split(",")
            ppg_names = ppg_names[0:-1]
            break_out = False
            keep_ppgs = ret_ppg_df.loc[(ret_ppg_df["Retailer"]==slct_retailer), "PPG"].unique()
            zero_tpr_leaflet_ppgs = [x for x in ppg_names if (x not in keep_ppgs) and (x != "all_brand")]
            num_zero_tpr_leaflet_ppgs = len(zero_tpr_leaflet_ppgs)
            if num_zero_tpr_leaflet_ppgs > 0:
                break_out = True
                continue
            filtered_input_data = list(filter(partial(opt_generic.handle_joint_and_all_brand_ppg\
                                                    ,acc_name=slct_retailer\
                                                    ,ppg=joint_ppg,top='all_brand',_in=True)\
                            ,constraints.get('input_data')['data']))
            
            for _ik,input_data in enumerate(filtered_input_data):
                if len(filtered_input_data)>1 and _ik>0:
                    opt_summary_filtered_combined_copy = opt_summary_filtered_combined.loc[opt_summary_filtered_combined['PPG']==opt_generic.format_all_brand(opt_generic.format_ppg(joint_ppg))]
                    non_duplicated_opt_summary_filtered_combined = opt_summary_filtered_combined_copy.loc[~opt_summary_filtered_combined_copy.duplicated(['Week'])]
                    non_duplicated_opt_summary_filtered_combined['Brand']=input_data.get('brand')
                    non_duplicated_opt_summary_filtered_combined['PPG_MAIN']=input_data.get('ppg_belongs_to',None)
                    opt_summary_filtered_combined=opt_summary_filtered_combined.append(non_duplicated_opt_summary_filtered_combined)
                    continue
                config_default_3 = opt_generic.update_params(config_default_3,
                                                input_data,
                                                constraints.get('input_data')['no_of_leaflet'],
                                                constraints.get('input_data')['min_length_gap'],
                                                constraints.get('input_data')['no_of_promo'],
                                                )
                tpr_list = config_default_3['MARS_TPRS']
                tpr_mech = config_default_3['TPR_Mech']
                tpr_list = ",".join([str(x) for x in tpr_list])
                tpr_mech = ",".join([str(x) for x in tpr_mech])
                required_base_combined = pd.DataFrame()
                baseline_data_combined = pd.DataFrame()
                opt_summary_filtered_final = pd.DataFrame()

                df_only_normal_new_all_brand.loc[(df_only_normal_new_all_brand["Retailer"]==slct_retailer) & 
                                (df_only_normal_new_all_brand["PPG"]==joint_ppg) , "TPR_list"] = tpr_list
                df_only_normal_new_all_brand.loc[(df_only_normal_new_all_brand["Retailer"]==slct_retailer) & 
                                (df_only_normal_new_all_brand["PPG"]==joint_ppg) , "TPR_Mech"] = tpr_mech
                joint_ppg_item = df_only_normal_new_all_brand["PPG_Item_No"][i]
                
                _summary_df = summary_df_new_1[(summary_df_new_1['Retailer'] == slct_retailer) & 
                                        (summary_df_new_1['PPG_Item_No'] == joint_ppg_item)]\
                                        .reset_index(drop=True)
                
                tpr_list = df_only_normal_new_all_brand.loc[i, "TPR_list"]
                tpr_mech = df_only_normal_new_all_brand.loc[i, "TPR_Mech"]
                _summary_df['Mechanic']=''
                if tpr_list and tpr_list != -1:
                    tpr_list = tpr_list.split(",")
                    tpr_mech = tpr_mech.split(",")
                    tpr_list = [int(x) for x in tpr_list]
                    promo_ind = _summary_df.loc[_summary_df["Promo_Type"]==2].index[-1]
                    num_rows_before = _summary_df.shape[0]
                    for tpr_ind, tpr_val in enumerate(tpr_list):
                        new_ind = num_rows_before + tpr_ind
                        tpr_mech_val = tpr_mech[tpr_ind]
                        _summary_df.loc[new_ind, :] = _summary_df.loc[promo_ind, :]
                        _summary_df.loc[new_ind, "Promo_Type"] = _summary_df.loc[(num_rows_before-1)\
                                                                , "Promo_Type"] + tpr_ind + 1        
                        _summary_df.loc[new_ind, "TPR"] = tpr_val
                        _summary_df.loc[new_ind, "Mechanic"] = tpr_mech_val

                flag_cols = [x for x in _summary_df.columns if "_flag" in x]
                flag_cols = flag_cols + ["Promo_Type"] +['Mechanic']+ ["TPR"]
                temp_df_new1 = _summary_df[flag_cols].reset_index(drop=True)
                compul_week_all_brand = config_df.loc[config_df['promo_type']=='all_brand']
                compul_week_joint = config_df.loc[config_df['promo_type']=='joint']
                compul_week_single = config_df.loc[config_df['promo_type']=='single']
                compul_weeks = []
                _joint_ppg =  opt_generic.format_ppg7(' - '.join(ppg_names))
                compul_week_all_brand = compul_week_all_brand.loc[(compul_week_all_brand["product_group"]==_joint_ppg)]
                compul_week_joint = compul_week_joint.loc[(compul_week_joint["product_group"]==_joint_ppg)]
                joint_cmp_weeks = compul_week_joint['param_compulsory_promo_weeks'].tolist()
                joint_cmp_weeks = list(set(reduce(lambda x,y: x+y, joint_cmp_weeks))) if joint_cmp_weeks else joint_cmp_weeks
                ab_cmp_weeks = compul_week_all_brand['param_compulsory_promo_weeks'].tolist()
                ab_cmp_weeks = list(set(reduce(lambda x,y: x+y, ab_cmp_weeks))) if ab_cmp_weeks else ab_cmp_weeks
                compul_week_joint = compul_week_joint.loc[~compul_week_joint.duplicated(['product_group'])]
                compul_week_all_brand = compul_week_all_brand.loc[~compul_week_all_brand.duplicated(['product_group'])]
                compul_week_joint=compul_week_joint.assign(param_compulsory_promo_weeks\
                                                            =compul_week_joint['param_compulsory_promo_weeks']\
                                                            .apply(lambda row: joint_cmp_weeks))
                compul_week_all_brand=compul_week_all_brand.assign(param_compulsory_promo_weeks\
                                                            =compul_week_all_brand['param_compulsory_promo_weeks']\
                                                            .apply(lambda row: ab_cmp_weeks))

                joint_exclude_dates = []
                single_exclude_dates = []
                exclude_dates_final = []
                exclude_dates = []
                for ppg_ind, ppg in enumerate(ppg_names):
                    
                    if 'Edeka' in slct_retailer:
                        temp_compul_weeks = compul_week_all_brand['param_compulsory_promo_weeks']         
                        compul_weeks.append(temp_compul_weeks)
                                
                        # If a ppg is common b/w all_brand and joint, then joint's compulsory week should not be used for all_brand slots
                        joint_exclude_dates = []
                        single_exclude_dates = []
                        exclude_dates_final = []
                        exclude_dates = []
                        for jp in compul_week_joint["product_group"].unique():
                            jp_list = jp.split(" - ")
                            for jp_val in jp_list:
                                _jp_val = opt_generic.format_ppg2(jp_val)
                                if _jp_val in ppg_names:
                                    temp_jp_compul_weeks = compul_week_joint.loc[(compul_week_joint["product_group"]==jp), "param_compulsory_promo_weeks"].tolist()
                                    joint_exclude_dates.extend(temp_jp_compul_weeks)

                        # If a ppg is common b/w all_brand and single, then single's compulsory week should not be used for all_brand slots
                        for sp_val in compul_week_single["product_group"].unique():
                            _sp_val = opt_generic.format_ppg2(sp_val)
                            if _sp_val in ppg_names:
                                temp_sp_compul_weeks = compul_week_single.loc[(compul_week_single["product_group"]==sp_val), "param_compulsory_promo_weeks"].tolist()
                                single_exclude_dates.extend(temp_sp_compul_weeks)
    
                        total_exclude_dates = single_exclude_dates + joint_exclude_dates
                        total_exclude_dates = [y-1 for x in total_exclude_dates for y in x]
                        total_exclude_dates = list(np.unique(total_exclude_dates))
                        total_exclude_dates += ppgs_dict[ppg]
                        total_exclude_dates = list(np.unique(total_exclude_dates))
                        promo_gap_val = config_default_3["constrain_params"]["promo_gap"]
                        if promo_gap_val < 0:
                            promo_gap_val = 1            
                        for exclude_dates_val in total_exclude_dates:
                            temp_dates = []
                            for val in range(1, promo_gap_val+1):
                                temp_prev_dates = exclude_dates_val - val
                                temp_next_dates = exclude_dates_val + val
                                temp_dates.append([temp_prev_dates, temp_next_dates])
                            temp_dates = [y for x in temp_dates for y in x]
                            temp_dates.append(exclude_dates_val)
                            temp_dates.sort()
                            exclude_dates.append(temp_dates)
                        
                        exclude_dates = np.unique([y for x in exclude_dates for y in x])# if y!=52])
                        exclude_dates = [x for x in exclude_dates if (x>=0) and (x<52)]

                    baseline_data = temp_baseline_data_all[(temp_baseline_data_all['Retailer'] \
                                    == slct_retailer) & (temp_baseline_data_all['PPG'] == ppg)]        
                    model_coeff = temp_model_coeff_all[(temp_model_coeff_all['Retailer'] \
                        == slct_retailer) & (temp_model_coeff_all['PPG'] == ppg)]    
                    
                    
                    ## Passing Optimal weeks & Optimal TE from stage 1 output
                    ppg_item_num = joint_ppg_item.split(",")[ppg_ind]
                    required_base = get_required_base_3(baseline_data, 
                                                        model_coeff, 
                                                        temp_df_new1, 
                                                        _summary_df, 
                                                        ppg_item_num) 
                    req_base_cols = ["Prod", "Iteration", "WK_ID", "MAC", "RP", "TPR"\
                                    , "OptimizedWeeksPromo",'Mechanic']
                    temp_required_base = required_base[req_base_cols]        
                    required_base_combined = required_base_combined.append(temp_required_base)        
                    
                    base_cols = ['Date', 'Baseline_Prediction','Baseline_Sales',"Baseline_GSV", 
                                "Baseline_Trade_Expense", "Baseline_Trade_Expense_onPromo"\
                                , "Baseline_NSV", "Baseline_MAC", "Baseline_RP"]
                    temp_baseline_data = baseline_data[base_cols]
                    baseline_data_combined = baseline_data_combined.append(temp_baseline_data)
                    
                    opt_summary_filtered, _opt_summary_all = optimal_summary_fun_4(baseline_data, 
                                                                                    model_coeff)        
                    opt_summary_filtered_final = opt_summary_filtered_final.append(opt_summary_filtered)
                    opt_summary_filtered_final = opt_summary_filtered_final.reset_index(drop=True)       
                    
                required_base_combined = required_base_combined.groupby(["Iteration", "WK_ID"]).agg({"MAC": "sum", "RP": "sum",
                                                                                                    "TPR": "mean", 
                                                                                                    "OptimizedWeeksPromo": "mean"})    
                required_base_combined = required_base_combined.reset_index()
                required_base_combined["Prod"] = joint_ppg_item
                stg1_tactics_df = _summary_df[flag_cols[:-1]]
                stg1_tactics_df = stg1_tactics_df.rename(columns={"Promo_Type": "Iteration"}) 
                
                required_base_combined = required_base_combined.merge(right=stg1_tactics_df, on=["Iteration"])
                required_base_combined["wk_num"] = required_base_combined["WK_ID"].map(lambda x: int(x.split("_")[1]))
                required_base_combined = required_base_combined.sort_values(by="wk_num").reset_index(drop=True)
                
                baseline_data_combined = baseline_data_combined.groupby(["Date"]).agg(["sum"]).reset_index()
                baseline_data_combined.columns = baseline_data_combined.columns.droplevel(level=1)
                optimal_calendar_1, prob = master_func_4(baseline_data_combined, 
                                                        required_base_combined, 
                                                        config_default_3,
                                                        exclude_dates)
                
                if LpStatus[prob.status]!="Optimal":
                    temp_model_data = model_data_all.loc[(model_data_all["Retailer"]==slct_retailer) &
                                            (model_data_all["PPG"]==ppg_names[0])].reset_index(drop=True)
                    col_new_old_dict = {"Flag_promotype_Leaflet": "Leaflet_flag", 
                                        "Flag_promotype_newsletter": "Newsletter_flag",
                                        "Flag_promotype_advertising_without_price": "Advertising_without_price_flag",                        
                                        "Flag_promotype_bonus": "Bonus_flag", "Flag_promotype_pas": "Pas_flag",
                                        "Flag_promotype_coupon": "Coupon_flag",
                                        "Flag_promotype_edlp": "EDLP_flag",
                                        "Flag_promotype_multibuy": "Multibuy_flag"}

                    req_cols = ["Date", "TPR"] + list(col_new_old_dict.keys())
                    temp_df = temp_model_data[req_cols]
                    temp_df = temp_df.rename(columns=col_new_old_dict)

                    # Works only for ppgs having one joint promotion at a time - cannot recognize if it is an all_brand promotion
                    temp_model_coeff = model_coeff_all[(model_coeff_all['Retailer'] == slct_retailer) & 
                                                (model_coeff_all['PPG'] == ppg_names[0])].reset_index(drop=True)
                    temp_baseline_data = temp_baseline_data_all[(temp_baseline_data_all['Retailer'] == slct_retailer) & 
                                                    (temp_baseline_data_all['PPG'] == ppg_names[0])].reset_index(drop=True)
                    jp_ppg_names = [x for x in temp_model_coeff["Coefficient_new"].values if "Tactic_JP_" in x]
                    
                    jp_ppg_names = [x.replace("Tactic_JP_", "") for x in jp_ppg_names]
                    break_flag = False
                    for jp_ppg in jp_ppg_names:
                        for ppg_name_val in ppg_names[1:]:
                            if jp_ppg == ppg_name_val:
                                model_coeff_jp_name = "Tactic_JP_" + jp_ppg
                                flag_jp_val = temp_model_coeff.loc[temp_model_coeff["Coefficient_new"]==model_coeff_jp_name , 
                                                                "names"].values[0]
                                opt_summary_filtered_1, opt_summary_all_1 = optimal_summary_fun_4(temp_baseline_data, 
                                                                                                temp_model_coeff)
                                opt_summary_filtered_1 = opt_summary_filtered_1.reset_index(drop=True)
                                opt_summary_all_1 = opt_summary_all_1.reset_index(drop=True)
                                dates = temp_model_data.loc[temp_model_data[flag_jp_val]==1, "Date"].values
                                opt_summary_all_1[req_cols[1:]] = 0
                                opt_summary_all_1 = opt_summary_all_1.rename(columns=col_new_old_dict)
                                req_cols_new = ["TPR"] + list(col_new_old_dict.values())
                                opt_summary_all_1.loc[opt_summary_all_1["Date"].isin(dates)\
                                    , req_cols_new] = (temp_df.
                                                        loc[temp_df["Date"].
                                                        isin(dates),
                                                        req_cols_new])
                                opt_summary_filtered_1 = pd.concat([opt_summary_filtered_1, 
                                                            opt_summary_all_1[req_cols_new]], axis=1)
                                opt_summary_filtered_1.loc[:, "PPG_No"] = joint_ppg_item
                                opt_summary_filtered_1.loc[:, "Retailer"] = slct_retailer
                                opt_summary_filtered_1.loc[:, "PPG"] = opt_generic.format_all_brand(opt_generic.format_ppg(joint_ppg))
                                opt_summary_filtered_1.loc[:, "Brand"] = input_data.get('brand')
                                opt_summary_filtered_1.loc[:, "Week"] = opt_summary_filtered_1.groupby(["Retailer",'PPG']).cumcount()+1
                                opt_summary_filtered_1.loc[:, "IS_ALL_BRAND"] = input_data.get('is_all_brand',False)
                                opt_summary_filtered_1.loc[:, "TYPE_OF_PROMO"] = 'all_brand'
                                opt_summary_filtered_1.loc[:, "PPG_MAIN"] = input_data.get('ppg_belongs_to',None)
                                opt_summary_filtered_1.loc[:, "status"] = LpStatus[prob.status]
                                opt_summary_filtered_combined = opt_summary_filtered_combined\
                                                                .append(opt_summary_filtered_1)
                                break_flag = True
                            if break_flag:
                                break
                        if break_flag:
                            break    
                    if not break_flag: # Indicating Infeasible solution for all_brand
                        opt_summary_filtered_1, opt_summary_all_1 = optimal_summary_fun_4(temp_baseline_data,
                                                                                        temp_model_coeff)
                        opt_summary_filtered_1 = opt_summary_filtered_1.reset_index(drop=True)
                        opt_summary_all_1 = opt_summary_all_1.reset_index(drop=True)            
                        opt_summary_filtered_1.loc[:, "TPR"] = temp_baseline_data["TPR"]
                        opt_summary_filtered_1.loc[:, "Mechanic"] = temp_baseline_data["Mechanic"]
                        baseline_tactic_cols = [x for x in baseline_data.columns if (("Tactic_" in x) and 
                                                                            ("Tactic_JP_" not in x) and 
                                                                            ("Tactic_WHI" not in x))]

                        col_dict = {"Tactic_Medium_HZ": "Leaflet_flag", "Tactic_Medium_TZ": "Newsletter_flag", 
                                    "Tactic_Advertising_without_price": "Advertising_without_price_flag", 
                                    "Tactic_Bonus": "Bonus_flag", "Tactic_Coupon": "Coupon_flag", "Tactic_EDLP": "EDLP_flag",
                                    "Tactic_PAS": "Pas_flag", "Tactic_Multibuy": "Multibuy_flag"}
                        tactic_cols = list(col_dict.keys())
                        for col in tactic_cols:
                            if col in baseline_tactic_cols:
                                opt_summary_filtered_1[col] = baseline_data[col]
                            else:
                                opt_summary_filtered_1[col] = 0
                        opt_summary_filtered_1 = opt_summary_filtered_1.rename(columns=col_dict)
                        opt_summary_filtered_1.loc[:, "PPG_No"] = joint_ppg_item
                        opt_summary_filtered_1.loc[:, "Retailer"] = slct_retailer
                        opt_summary_filtered_1.loc[:, "PPG"] = opt_generic.format_all_brand(opt_generic.format_ppg(joint_ppg))
                        opt_summary_filtered_1.loc[:, "Brand"] = input_data.get('brand')
                        opt_summary_filtered_1.loc[:, "Week"] = opt_summary_filtered_1.groupby(["Retailer",'PPG']).cumcount()+1
                        opt_summary_filtered_1.loc[:, "IS_ALL_BRAND"] = input_data.get('is_all_brand',False)
                        opt_summary_filtered_1.loc[:, "TYPE_OF_PROMO"] = 'all_brand'
                        opt_summary_filtered_1.loc[:, "PPG_MAIN"] = input_data.get('ppg_belongs_to',None)
                        opt_summary_filtered_1.loc[:, "status"] = LpStatus[prob.status]
                        opt_summary_filtered_combined = opt_summary_filtered_combined.append(opt_summary_filtered_1)
                else:
                    promo_weeks = optimal_calendar_1.loc[(optimal_calendar_1["TPR"]!=0), "Week_no"].unique()
                    promo_weeks = [(x-1) for x in promo_weeks]
                    # Add promo weeks to ppg-exclude-week-dictionary
                    for ppg_val in ppg_names:
                        ppgs_dict[ppg_val] += promo_weeks
                    opt_summary_filtered_final = opt_summary_filtered_final.groupby("Date").agg(["sum"])\
                                                .reset_index()
                    opt_summary_filtered_final.columns = opt_summary_filtered_final.columns.droplevel(level=1)    
                    opt_summary_filtered_final.loc[:, "TPR"] = optimal_calendar_1["TPR"]
                    opt_summary_filtered_final.loc[:, "Mechanic"] = optimal_calendar_1["Mechanic"]
                    opt_calendar_tactics = (optimal_calendar_1.merge(right=stg1_tactics_df, 
                                                        on=["Iteration"]).sort_values(by="Week_no")\
                                                .reset_index(drop=True))
                    for col in flag_cols[:-3]:
                        opt_summary_filtered_final[col] = opt_calendar_tactics[col]
            
                    opt_summary_filtered_final.loc[:, "PPG_No"] = joint_ppg_item
                    opt_summary_filtered_final.loc[:, "Retailer"] = slct_retailer
                    opt_summary_filtered_final.loc[:, "PPG"] = opt_generic.format_all_brand(opt_generic.format_ppg(joint_ppg))
                    opt_summary_filtered_final.loc[:, "Brand"] = input_data.get('brand')
                    opt_summary_filtered_final.loc[:, "Week"] = opt_summary_filtered_final.groupby(["Retailer",'PPG']).cumcount()+1
                    opt_summary_filtered_final.loc[:, "IS_ALL_BRAND"] = input_data.get('is_all_brand',False)
                    opt_summary_filtered_final.loc[:, "TYPE_OF_PROMO"] = 'all_brand'
                    opt_summary_filtered_final.loc[:, "PPG_MAIN"] = input_data.get('ppg_belongs_to',None)
                    opt_summary_filtered_final.loc[:, "status"] = optimal_calendar_1["Solution"]
                    opt_summary_filtered_combined = opt_summary_filtered_combined\
                                                    .append(opt_summary_filtered_final)
        if not break_out:
            opt_summary_filtered_combined = opt_summary_filtered_combined.reset_index(drop=True)
            df_promo_weeks_all_brand = opt_summary_filtered_combined[['Date','Retailer','PPG','PPG_No'\
                ,'TPR','PPG_MAIN','IS_ALL_BRAND','Mechanic','status','TYPE_OF_PROMO',"Week","Brand"]\
                            + flag_cols[:-3]]
            df_promo_weeks_all_brand.loc[:, ["PPG"]] = df_promo_weeks_all_brand["PPG"].map(lambda x: x.split(",all_brand")[0])
            df_promo_weeks_all_brand.loc[:, ["PPG_No"]] = df_promo_weeks_all_brand["PPG_No"].map(lambda x: x.split(",all_brand")[0])
            df_promo_weeks_all_brand["Promo_Cat"] = "all_brand"
    else:
        df_promo_weeks_all_brand = pd.DataFrame(columns=['Date', 'Retailer', 'PPG', 'PPG_No', 'Units', 'TPR', 'status',
                                                        'Leaflet_flag', 'Newsletter_flag', 'Advertising_without_price_flag',
                                                        'Bonus_flag', 'Pas_flag', 'Coupon_flag', 'EDLP_flag', 
                                                        'Multibuy_flag'])
  
    df_only_normal_new_joint = df_only_normal_new.loc[(df_only_normal_new["Promo_Cat"]!="all_brand")].reset_index(drop=True)
    temp_joint_ppg_item_list = df_only_normal_new_joint["PPG_Item_No"].to_list()
    joint_ppg_list = []
    for ppg in temp_joint_ppg_item_list:
        temp_ppg_list = ppg.split(",") # change
        for temp_ppg in temp_ppg_list:
            if temp_ppg not in joint_ppg_list:
                joint_ppg_list.append(temp_ppg)
    temp_model_coeff_all = model_coeff_all.copy()
    temp_model_coeff_all["PPG"] = temp_model_coeff_all["PPG"]

    temp_joint_ppg_list = df_only_normal_new_joint["PPG"].to_list()
    joint_ppgs = [y for x in temp_joint_ppg_list for y in x.split(",")]

    ppgs_dict = {k: [] for k in joint_ppgs}
    ppgs_dict['joint_ppg'] = ''

    temp_baseline_data_all = baseline_data_all.copy()

    opt_summary_filtered_combined = pd.DataFrame()
    
    # @parallel_task_executioner3
    # def get_joint_calendar(**kwargs):
    if df_only_normal_new_joint.shape[0]>0:
        for i in range(df_only_normal_new_joint.shape[0]):# Handle joint promotion
            slct_retailer = df_only_normal_new_joint['Retailer'][i]
            joint_ppg = df_only_normal_new_joint["PPG"][i]
            ppg_names = joint_ppg.split(",")
            filtered_input_data = list(filter(partial(opt_generic.handle_joint_and_all_brand_ppg\
                                                        ,acc_name=slct_retailer\
                                                            ,ppg=joint_ppg\
                                                            ,top='joint')\
                                ,constraints.get('input_data')['data']))
            
            for _ik,input_data in enumerate(filtered_input_data):
                if len(filtered_input_data)>1 and _ik>0:
                    opt_summary_filtered_combined_copy = opt_summary_filtered_combined.loc[opt_summary_filtered_combined['PPG']==opt_generic.format_ppg(joint_ppg)]
                    non_duplicated_opt_summary_filtered_combined = opt_summary_filtered_combined_copy.loc[~opt_summary_filtered_combined_copy.duplicated(['Week'])]
                    non_duplicated_opt_summary_filtered_combined['Brand']=input_data.get('brand')
                    non_duplicated_opt_summary_filtered_combined['PPG_MAIN']=input_data.get('ppg_belongs_to',None)
                    opt_summary_filtered_combined=opt_summary_filtered_combined.append(non_duplicated_opt_summary_filtered_combined)
                    continue
                config_default_3 = opt_generic.update_params(config_default_3,
                                                input_data,
                                                constraints.get('input_data')['no_of_leaflet'],
                                                constraints.get('input_data')['min_length_gap'],
                                                constraints.get('input_data')['no_of_promo'],
                                                )
                tpr_list = config_default_3['MARS_TPRS']
                tpr_mech = config_default_3['TPR_Mech']
                tpr_list = ",".join([str(x) for x in tpr_list])
                tpr_mech = ",".join([str(x) for x in tpr_mech])
                required_base_combined = pd.DataFrame()
                baseline_data_combined = pd.DataFrame()
                opt_summary_filtered_final = pd.DataFrame()

                df_only_normal_new_joint.loc[(df_only_normal_new_joint["Retailer"]==slct_retailer) & 
                                (df_only_normal_new_joint["PPG"]==joint_ppg) , "TPR_list"] = tpr_list
                df_only_normal_new_joint.loc[(df_only_normal_new_joint["Retailer"]==slct_retailer) & 
                                (df_only_normal_new_joint["PPG"]==joint_ppg) , "TPR_Mech"] = tpr_mech
                joint_ppg_item = df_only_normal_new_joint["PPG_Item_No"][i]
                
                _summary_df = summary_df_new_1[(summary_df_new_1['Retailer'] == slct_retailer) & 
                                        (summary_df_new_1['PPG_Item_No'] == joint_ppg_item)]\
                                        .reset_index(drop=True)
                
                tpr_list = df_only_normal_new_joint.loc[i, "TPR_list"]
                tpr_mech = df_only_normal_new_joint.loc[i, "TPR_Mech"]
                _summary_df['Mechanic']=''
                if tpr_list and tpr_list != -1:
                    tpr_list = tpr_list.split(",")
                    tpr_mech = tpr_mech.split(",")
                    tpr_list = [int(x) for x in tpr_list]
                    promo_ind = _summary_df.loc[_summary_df["Promo_Type"]==2].index[-1]
                    num_rows_before = _summary_df.shape[0]
                    for tpr_ind, tpr_val in enumerate(tpr_list):
                        new_ind = num_rows_before + tpr_ind
                        tpr_mech_val = tpr_mech[tpr_ind]
                        _summary_df.loc[new_ind, :] = _summary_df.loc[promo_ind, :]
                        _summary_df.loc[new_ind, "Promo_Type"] = _summary_df.loc[(num_rows_before-1)\
                                                                , "Promo_Type"] + tpr_ind + 1        
                        _summary_df.loc[new_ind, "TPR"] = tpr_val
                        _summary_df.loc[new_ind, "Mechanic"] = tpr_mech_val

                flag_cols = [x for x in _summary_df.columns if "_flag" in x]
                flag_cols = flag_cols + ["Promo_Type"] +['Mechanic']+ ["TPR"]
                temp_df_new1 = _summary_df[flag_cols].reset_index(drop=True)
                exclude_dates_all_brand = []
                single_exclude_dates = []
                exclude_dates_final = []
                for ppg_ind, ppg in enumerate(ppg_names): 
                        
                    baseline_data = temp_baseline_data_all[(temp_baseline_data_all['Retailer'] \
                                    == slct_retailer) & (temp_baseline_data_all['PPG'] == ppg)]        
                    model_coeff = temp_model_coeff_all[(temp_model_coeff_all['Retailer'] \
                        == slct_retailer) & (temp_model_coeff_all['PPG'] == ppg)]    
                    
                    ## Passing Optimal weeks & Optimal TE from stage 1 output
                    ppg_item_num = joint_ppg_item.split(",")[ppg_ind]
                    required_base = get_required_base_3(baseline_data, 
                                                        model_coeff, 
                                                        temp_df_new1, 
                                                        _summary_df, 
                                                        ppg_item_num) 
                    req_base_cols = ["Prod", "Iteration", "WK_ID", "MAC", "RP", "TPR"\
                                    , "OptimizedWeeksPromo",'Mechanic']
                    temp_required_base = required_base[req_base_cols]        
                    required_base_combined = required_base_combined.append(temp_required_base)        
                    
                    base_cols = ['Date', 'Baseline_Prediction','Baseline_Sales',"Baseline_GSV", 
                                "Baseline_Trade_Expense", "Baseline_Trade_Expense_onPromo"\
                                , "Baseline_NSV", "Baseline_MAC", "Baseline_RP"]
                    temp_baseline_data = baseline_data[base_cols]
                    baseline_data_combined = baseline_data_combined.append(temp_baseline_data)
                    
                    opt_summary_filtered, _opt_summary_all = optimal_summary_fun_4(baseline_data, 
                                                                                    model_coeff)        
                    opt_summary_filtered_final = opt_summary_filtered_final.append(opt_summary_filtered)
                    opt_summary_filtered_final = opt_summary_filtered_final.reset_index(drop=True)
                
                    all_brand_ppg_week_nums = []
                    exclude_dates = []
                    # Check all_brand promo dates and exclude it from joint_promo dates
                    if ppg_item_num in all_brand_ppg_list:      
                        for all_brand_ppg_item_val in temp_all_brand_ppg_item_list: # 10th Jan 2023 change
                            if ppg_item_num in all_brand_ppg_item_val:
                                temp_df_promo_weeks = df_promo_weeks_all_brand.loc[df_promo_weeks_all_brand["PPG_No"]\
                                                        ==all_brand_ppg_item_val].reset_index()
                                temp_df_promo_weeks = temp_df_promo_weeks.rename(columns={"index": "week_num"})                    
                                temp_df_promo_weeks["promo_mask"] = False
                                promo_mask = temp_df_promo_weeks["promo_mask"]
                                for promo_week_col in temp_df_promo_weeks.columns:
                                    if ("_flag" in promo_week_col) or ("TPR" == promo_week_col):
                                        promo_mask |= (temp_df_promo_weeks[promo_week_col] > 0)
                                temp_all_brand_ppg_week_nums = temp_df_promo_weeks.loc[promo_mask, "week_num"].unique()
                                temp_all_brand_ppg_week_nums = [(x%52) for x in temp_all_brand_ppg_week_nums] # *change*
                                all_brand_ppg_week_nums.append(temp_all_brand_ppg_week_nums)

                        all_brand_ppg_week_nums = [y for x in all_brand_ppg_week_nums for y in x]
                        all_brand_ppg_week_nums = list(np.unique(all_brand_ppg_week_nums))
            
                        promo_gap_val = config_default_3["constrain_params"]["promo_gap"]
                        if promo_gap_val < 0:
                            promo_gap_val = 1            
                        for all_brand_ppg_week_num_val in all_brand_ppg_week_nums:
                            temp_dates = []
                            for val in range(1, promo_gap_val+1):
                                temp_prev_dates = all_brand_ppg_week_num_val - val
                                temp_next_dates = all_brand_ppg_week_num_val + val
                                temp_dates.append([temp_prev_dates, temp_next_dates])
                            temp_dates = [y for x in temp_dates for y in x]
                            temp_dates.append(all_brand_ppg_week_num_val)
                            temp_dates.sort()
                            exclude_dates.append(temp_dates)

                        exclude_dates = np.unique([y for x in exclude_dates for y in x])# if y!=52])
                        exclude_dates = [x for x in exclude_dates if (x>=0) and (x<52)]
                        exclude_dates_all_brand.append(exclude_dates)
                
                exclude_dates_all_brand = list(np.unique(exclude_dates_all_brand))
                if (len(exclude_dates_all_brand)>0):
                    if (type(exclude_dates_all_brand[0]) == type([])):
                        exclude_dates_all_brand = list(np.unique([y for x in exclude_dates_all_brand for y in x]))
                required_base_combined = required_base_combined.groupby(["Iteration", "WK_ID"])\
                                        .agg({"MAC": "sum", "RP": "sum",
                                            "TPR": "mean","OptimizedWeeksPromo": "mean"})

                required_base_combined = required_base_combined.reset_index()
                required_base_combined["Prod"] = joint_ppg_item
                # required_base_combined['Mechanic']
                stg1_tactics_df = _summary_df[flag_cols[:-1]]
                stg1_tactics_df = stg1_tactics_df.rename(columns={"Promo_Type": "Iteration"})
                required_base_combined = required_base_combined.merge(right=stg1_tactics_df\
                                        , on=["Iteration"])
                
                required_base_combined["wk_num"] = required_base_combined["WK_ID"]\
                                                .map(lambda x: int(x.split("_")[1]))
                required_base_combined = required_base_combined.sort_values(by="wk_num")\
                                        .reset_index(drop=True)
                
                baseline_data_combined = baseline_data_combined.groupby(["Date"]).agg(["sum"])\
                                        .reset_index()
                
                baseline_data_combined.columns = baseline_data_combined.columns.droplevel(level=1)

                if 'Edeka' in slct_retailer:
                    
                    compul_week_all_brand = config_df.loc[config_df['promo_type']=='all_brand']
                    compul_week_joint = config_df.loc[config_df['promo_type']=='joint']
                    compul_week_single = config_df.loc[config_df['promo_type']=='single']
                    _joint_ppg =  opt_generic.format_ppg7(' - '.join(ppg_names))
                    compul_week_all_brand = compul_week_all_brand.loc[(compul_week_all_brand["product_group"]==_joint_ppg)]
                    compul_week_joint = compul_week_joint.loc[(compul_week_joint["product_group"]==_joint_ppg)]
                    
                    joint_cmp_weeks = compul_week_joint['param_compulsory_promo_weeks'].tolist()
                    
                    joint_cmp_weeks = list(set(reduce(lambda x,y: x+y, joint_cmp_weeks))) if joint_cmp_weeks else joint_cmp_weeks
                    ab_cmp_weeks = compul_week_all_brand['param_compulsory_promo_weeks'].tolist()
                    ab_cmp_weeks = list(set(reduce(lambda x,y: x+y, ab_cmp_weeks))) if ab_cmp_weeks else ab_cmp_weeks
                    compul_week_joint = compul_week_joint.loc[~compul_week_joint.duplicated(['product_group'])]
                    compul_week_all_brand = compul_week_all_brand.loc[~compul_week_all_brand.duplicated(['product_group'])]
                    compul_week_joint=compul_week_joint.assign(param_compulsory_promo_weeks\
                                                                =compul_week_joint['param_compulsory_promo_weeks']\
                                                                .apply(lambda row: joint_cmp_weeks))
                    compul_week_all_brand=compul_week_all_brand.assign(param_compulsory_promo_weeks\
                                                                =compul_week_all_brand['param_compulsory_promo_weeks']\
                                                                .apply(lambda row: ab_cmp_weeks))
                    compul_week_single_ppgs = compul_week_single["product_group"].unique()
                    for sp_val in compul_week_single_ppgs:
                        _sp_val = opt_generic.format_ppg2(sp_val)
                        if _sp_val in ppg_names:
                            temp_sp_compul_weeks = compul_week_single.loc[(compul_week_single["product_group"]==sp_val), "param_compulsory_promo_weeks"].tolist()
                            single_exclude_dates.extend(temp_sp_compul_weeks)

                single_exclude_dates = [y-1 for x in single_exclude_dates for y in x]
                # if joint_ppg not in ppgs_dict.keys():
                for ppg_value in ppg_names:         
                    single_exclude_dates += ppgs_dict[ppg_value]
                single_exclude_dates = list(np.unique(single_exclude_dates))
                promo_gap_val = config_default_3["constrain_params"]["promo_gap"]
                if promo_gap_val < 0:
                    promo_gap_val = 1
                for single_exclude_dates_val in single_exclude_dates:
                    temp_dates = []
                    for val in range(1, promo_gap_val+1):
                        temp_prev_dates = single_exclude_dates_val - val
                        temp_next_dates = single_exclude_dates_val + val
                        temp_dates.append([temp_prev_dates, temp_next_dates])
                    temp_dates = [y for x in temp_dates for y in x]
                    temp_dates.append(single_exclude_dates_val)
                    temp_dates.sort()
                    exclude_dates_final.append(temp_dates)

                exclude_dates_final = np.unique([y for x in exclude_dates_final for y in x])# if y!=52])
                exclude_dates_final = [x for x in exclude_dates_final if (x>=0) and (x<52)]
                exclude_dates_all_brand = exclude_dates_all_brand + exclude_dates_final
                exclude_dates_all_brand = list(np.unique(exclude_dates_all_brand))
                optimal_calendar_1, prob = master_func_4(baseline_data_combined, 
                                                        required_base_combined, 
                                                        config_default_3,
                                                        exclude_dates_all_brand)
                if LpStatus[prob.status]!="Optimal":
                    temp_model_data = model_data_all.loc[(model_data_all["Retailer"]==slct_retailer) &
                                            (model_data_all["PPG"]==ppg_names[0])].reset_index(drop=True)
                    col_new_old_dict = {"Flag_promotype_Leaflet": "Leaflet_flag", 
                                        "Flag_promotype_newsletter": "Newsletter_flag",
                                        "Flag_promotype_advertising_without_price": "Advertising_without_price_flag",                        
                                        "Flag_promotype_bonus": "Bonus_flag", "Flag_promotype_pas": "Pas_flag",
                                        "Flag_promotype_coupon": "Coupon_flag",
                                        "Flag_promotype_edlp": "EDLP_flag",
                                        "Flag_promotype_multibuy": "Multibuy_flag"}

                    req_cols = ["Date", "TPR"] + list(col_new_old_dict.keys())
                    temp_df = temp_model_data[req_cols]
                    temp_df = temp_df.rename(columns=col_new_old_dict)

                    # Works only for ppgs having one joint promotion at a time - cannot recognize if it is an all_brand promotion
                    temp_model_coeff = model_coeff_all[(model_coeff_all['Retailer'] == slct_retailer) & 
                                                (model_coeff_all['PPG'] == ppg_names[0])].reset_index(drop=True)
                    temp_baseline_data = temp_baseline_data_all[(temp_baseline_data_all['Retailer'] == slct_retailer) & 
                                                    (temp_baseline_data_all['PPG'] == ppg_names[0])].reset_index(drop=True)
                    jp_ppg_names = [x for x in temp_model_coeff["Coefficient_new"].values if "Tactic_JP_" in x]
                    
                    jp_ppg_names = [x.replace("Tactic_JP_", "") for x in jp_ppg_names]
                    break_flag = False
                    for jp_ppg in jp_ppg_names:
                        for ppg_name_val in ppg_names[1:]:
                            if jp_ppg == ppg_name_val:
                                model_coeff_jp_name = "Tactic_JP_" + jp_ppg
                                flag_jp_val = temp_model_coeff.loc[temp_model_coeff["Coefficient_new"]==model_coeff_jp_name , 
                                                                "names"].values[0]
                                opt_summary_filtered_1, opt_summary_all_1 = optimal_summary_fun_4(temp_baseline_data, 
                                                                                                temp_model_coeff)
                                opt_summary_filtered_1 = opt_summary_filtered_1.reset_index(drop=True)
                                opt_summary_all_1 = opt_summary_all_1.reset_index(drop=True)
                                dates = temp_model_data.loc[temp_model_data[flag_jp_val]==1, "Date"].values
                                opt_summary_all_1[req_cols[1:]] = 0
                                opt_summary_all_1 = opt_summary_all_1.rename(columns=col_new_old_dict)
                                req_cols_new = ["TPR"] + list(col_new_old_dict.values())
                                opt_summary_all_1.loc[opt_summary_all_1["Date"].isin(dates)\
                                    , req_cols_new] = (temp_df.
                                                        loc[temp_df["Date"].
                                                        isin(dates),
                                                        req_cols_new])
                                opt_summary_filtered_1 = pd.concat([opt_summary_filtered_1, 
                                                            opt_summary_all_1[req_cols_new]], axis=1)
                                opt_summary_filtered_1.loc[:, "PPG_No"] = joint_ppg_item
                                opt_summary_filtered_1.loc[:, "Retailer"] = slct_retailer
                                opt_summary_filtered_1.loc[:, "PPG"] = opt_generic.format_ppg(joint_ppg)
                                opt_summary_filtered_1.loc[:, "Brand"] = input_data.get('brand')
                                opt_summary_filtered_1.loc[:, "Week"] = opt_summary_filtered_1.groupby(["Retailer",'PPG']).cumcount()+1
                                opt_summary_filtered_1.loc[:, "IS_ALL_BRAND"] = input_data.get('is_all_brand',False)
                                opt_summary_filtered_1.loc[:, "TYPE_OF_PROMO"] = 'joint'
                                opt_summary_filtered_1.loc[:, "PPG_MAIN"] = input_data.get('ppg_belongs_to',None)
                                opt_summary_filtered_1.loc[:, "status"] = LpStatus[prob.status]
                                opt_summary_filtered_combined = opt_summary_filtered_combined\
                                                                .append(opt_summary_filtered_1)
                                break_flag = True
                            if break_flag:
                                break
                        if break_flag:
                            break    
                    if not break_flag: # Indicating Infeasible solution for all_brand
                        opt_summary_filtered_1, opt_summary_all_1 = optimal_summary_fun_4(temp_baseline_data,
                                                                                        temp_model_coeff)
                        opt_summary_filtered_1 = opt_summary_filtered_1.reset_index(drop=True)
                        opt_summary_all_1 = opt_summary_all_1.reset_index(drop=True)            
                        opt_summary_filtered_1.loc[:, "TPR"] = temp_baseline_data["TPR"]
                        opt_summary_filtered_1.loc[:, "Mechanic"] = temp_baseline_data["Mechanic"]
                        baseline_tactic_cols = [x for x in baseline_data.columns if (("Tactic_" in x) and 
                                                                            ("Tactic_JP_" not in x) and 
                                                                            ("Tactic_WHI" not in x))]

                        col_dict = {"Tactic_Medium_HZ": "Leaflet_flag", "Tactic_Medium_TZ": "Newsletter_flag", 
                                    "Tactic_Advertising_without_price": "Advertising_without_price_flag", 
                                    "Tactic_Bonus": "Bonus_flag", "Tactic_Coupon": "Coupon_flag", "Tactic_EDLP": "EDLP_flag",
                                    "Tactic_PAS": "Pas_flag", "Tactic_Multibuy": "Multibuy_flag"}
                        tactic_cols = list(col_dict.keys())
                        for col in tactic_cols:
                            if col in baseline_tactic_cols:
                                opt_summary_filtered_1[col] = baseline_data[col]
                            else:
                                opt_summary_filtered_1[col] = 0
                        opt_summary_filtered_1 = opt_summary_filtered_1.rename(columns=col_dict)
                        opt_summary_filtered_1.loc[:, "PPG_No"] = joint_ppg_item
                        opt_summary_filtered_1.loc[:, "Retailer"] = slct_retailer
                        opt_summary_filtered_1.loc[:, "PPG"] = opt_generic.format_ppg(joint_ppg)
                        opt_summary_filtered_1.loc[:, "Brand"] = input_data.get('brand')
                        opt_summary_filtered_1.loc[:, "Week"] = opt_summary_filtered_1.groupby(["Retailer",'PPG']).cumcount()+1
                        opt_summary_filtered_1.loc[:, "IS_ALL_BRAND"] = input_data.get('is_all_brand',False)
                        opt_summary_filtered_1.loc[:, "TYPE_OF_PROMO"] = 'joint'
                        opt_summary_filtered_1.loc[:, "PPG_MAIN"] = input_data.get('ppg_belongs_to',None)
                        opt_summary_filtered_1.loc[:, "status"] = LpStatus[prob.status]
                        opt_summary_filtered_combined = opt_summary_filtered_combined.append(opt_summary_filtered_1)
                else:
                    promo_weeks = optimal_calendar_1.loc[(optimal_calendar_1["TPR"]!=0), "Week_no"].unique()
                    promo_weeks = [(x-1) for x in promo_weeks]
                    # Add promo weeks to ppg-exclude-week-dictionary
                    for ppg_val in ppg_names:
                        ppgs_dict[ppg_val] += promo_weeks                

                    opt_summary_filtered_final = opt_summary_filtered_final.groupby("Date").agg(["sum"])\
                                                .reset_index()
                    opt_summary_filtered_final.columns = opt_summary_filtered_final.columns.droplevel(level=1)    
                    opt_summary_filtered_final.loc[:, "TPR"] = optimal_calendar_1["TPR"]
                    opt_summary_filtered_final.loc[:, "Mechanic"] = optimal_calendar_1["Mechanic"]
                    opt_calendar_tactics = (optimal_calendar_1.merge(right=stg1_tactics_df, 
                                                        on=["Iteration"]).sort_values(by="Week_no")\
                                                .reset_index(drop=True))
                    for col in flag_cols[:-3]:
                        opt_summary_filtered_final[col] = opt_calendar_tactics[col]
            
                    opt_summary_filtered_final.loc[:, "PPG_No"] = joint_ppg_item
                    opt_summary_filtered_final.loc[:, "Retailer"] = slct_retailer
                    opt_summary_filtered_final.loc[:, "PPG"] = opt_generic.format_ppg(joint_ppg)
                    opt_summary_filtered_final.loc[:, "Brand"] = input_data.get('brand')
                    opt_summary_filtered_final.loc[:, "Week"] = opt_summary_filtered_final.groupby(["Retailer",'PPG']).cumcount()+1
                    opt_summary_filtered_final.loc[:, "IS_ALL_BRAND"] = input_data.get('is_all_brand',False)
                    opt_summary_filtered_final.loc[:, "TYPE_OF_PROMO"] = 'joint'
                    opt_summary_filtered_final.loc[:, "PPG_MAIN"] = input_data.get('ppg_belongs_to',None)
                    opt_summary_filtered_final.loc[:, "status"] = optimal_calendar_1["Solution"]
                    opt_summary_filtered_combined = opt_summary_filtered_combined\
                                                    .append(opt_summary_filtered_final)
        opt_summary_filtered_combined = opt_summary_filtered_combined.reset_index(drop=True)
        df_promo_weeks_joint = opt_summary_filtered_combined[['Date','Retailer','PPG','PPG_No'\
            ,'TPR','PPG_MAIN','IS_ALL_BRAND','Mechanic','status','TYPE_OF_PROMO',"Week","Brand"]\
                        + flag_cols[:-3]]
        df_promo_weeks_joint.loc[:,"Promo_Cat"] = "joint"
    else:
        df_promo_weeks_joint = pd.DataFrame(columns=['Date', 'Retailer', 'PPG', 'PPG_No', 'Units', 'TPR', 'status',
                                                     'Leaflet_flag', 'Newsletter_flag', 'Advertising_without_price_flag',
                                                     'Bonus_flag', 'Pas_flag', 'Coupon_flag', 'EDLP_flag', 
                                                     'Multibuy_flag']) 

    df_promo_weeks = pd.concat([df_promo_weeks_all_brand, df_promo_weeks_joint], axis=0, ignore_index=True)

    single_df = process_single_promo(
        {**constraints,"df_promo_weeks" : df_promo_weeks},
    )

    result_df = pd.concat([single_df,df_promo_weeks]).reset_index()
    # calculate finanical metrics
    resp_list=[]
    result_df = result_df.fillna(0)
    result_df.Week = result_df.Week.astype('int64')
    result_df['joint_flag'] = ''
    
    #handle Brand wise Infeasible
    if summary_df['status'].unique()[0]=='Infeasible':
        result_df['status']='Infeasible'
    df_group = result_df.groupby(["Retailer",'Brand'])
    
    for key,_item in df_group:
        brand_df = df_group.get_group(key).reset_index(drop=True)
        infeasible_brand_df = brand_df.loc[(brand_df['TYPE_OF_PROMO']=='single')\
                            & (brand_df['status']=='Infeasible')]
        if infeasible_brand_df.shape[0]:
            result_df.loc[(result_df['Retailer']==brand_df['Retailer'].values[0]) \
            & (result_df['Brand']==brand_df['Brand'].values[0]) \
            & (result_df['TYPE_OF_PROMO']=='joint'),'status']='Infeasible'
            
    result_df['is_one_shot_ppg'] = False
    df_copy_for_one_shot_ppg_group_df  = result_df.groupby(["Retailer",'PPG','PPG_MAIN'])
    cpy_oneshot_ppg_group = None
    for ckey,_ in df_copy_for_one_shot_ppg_group_df:
        cpy_oneshot_ppg_group = df_copy_for_one_shot_ppg_group_df.get_group(ckey).reset_index(drop=True)
        break
    
    new_one_dhot_ppg_df = pd.DataFrame()
    
    for _osppg in exclude_ppgs_list:
        osppgs_list = _osppg.split('_-_')  
        ospppg_df = one_shot_ppg_df.loc[one_shot_ppg_df['PPG'] ==_osppg]
        for osp in osppgs_list:
            
            cpy_oneshot_ppg_group['PPG'] = opt_generic.format_all_brand(opt_generic.format_ppg(_osppg))
            cpy_oneshot_ppg_group['PPG_MAIN'] = opt_generic.format_ppg(osp)
            cpy_oneshot_ppg_group['TYPE_OF_PROMO'] = ospppg_df['Type_of_Promo'].values[0]
            cpy_oneshot_ppg_group['is_one_shot_ppg'] = True
            cpy_oneshot_ppg_group['status'] = 'Optimal'
            new_one_dhot_ppg_df = new_one_dhot_ppg_df.append(cpy_oneshot_ppg_group)
 
    result_df = pd.concat([result_df,new_one_dhot_ppg_df])
    result_df['IS_OPTIMIZED'] = False
    result_df.loc[(result_df['TPR']>0),'IS_OPTIMIZED'] = True
    grouped_df = result_df.groupby(["Retailer",'PPG_MAIN'])
 
    @parallel_task_executioner
    def get_financial_results(key=None,grouped_df=None):    
    # for key,_item in grouped_df:
        
        a_group = grouped_df.get_group(key).reset_index(drop=True)
        one_shot_ppg_df = a_group.loc[a_group['is_one_shot_ppg']==True]
        non_one_shot_ppg = pd.DataFrame()
        if (a_group.shape[0]<=52 and one_shot_ppg_df.shape[0]) or a_group.shape[0]==one_shot_ppg_df.shape[0]:
            a_group['status']='Infeasible'
        else:
            #non one shot ppg
            non_one_shot_ppg_df = a_group.loc[a_group['is_one_shot_ppg']==False]
            if non_one_shot_ppg_df.shape[0]:
                a_group = non_one_shot_ppg_df
                non_one_shot_ppg = non_one_shot_ppg.append(a_group)
     
        a_group_infeasible = a_group.loc[a_group['status']=='Infeasible']
        rest_df = result_df.loc[result_df['PPG'].isin(a_group['PPG'].unique())]
        inf_rest_df = rest_df.loc[rest_df['status']=='Infeasible']
        if inf_rest_df.shape[0]:
            a_group.loc[a_group['PPG'].isin(inf_rest_df['PPG'].unique()),'status'] = 'Infeasible'
        if a_group_infeasible.shape[0]:
            sub_group = a_group_infeasible.groupby(["Retailer",'PPG'])
            first_group = sub_group.get_group((list(sub_group.groups)[0]))
            a_group = first_group

        single_ppg_df = a_group.loc[a_group['TYPE_OF_PROMO']=='single']
        joint_ppg_df = a_group.loc[a_group['TYPE_OF_PROMO']=='joint']
        all_brand_ppg_df= a_group.loc[a_group['TYPE_OF_PROMO']=='all_brand']
        
        #handle joint ppg
        inner_joint_grouped_df = joint_ppg_df.groupby(["Retailer",'PPG'])
        for key1,_item1 in inner_joint_grouped_df:
            a_joint_group = inner_joint_grouped_df.get_group(key1).reset_index(drop=True)
            if single_ppg_df.shape[0]:
                single_ppg_df[single_ppg_df['Week'].isin(a_joint_group.loc[a_joint_group['TPR']>0]\
                                ['Week'])]=a_joint_group.loc[a_joint_group['TPR']>0].values   
            else:
                single_ppg_df = a_joint_group

        #handle all brand ppg
        inner_all_brand_grouped_df = all_brand_ppg_df.groupby(["Retailer",'PPG'])
        for key2,_item2 in inner_all_brand_grouped_df:
            a_all_brand_group = inner_all_brand_grouped_df.get_group(key2).reset_index(drop=True)
            if single_ppg_df.shape[0]:
                single_ppg_df.loc[single_ppg_df['Week'].isin(a_all_brand_group.loc[a_all_brand_group['TPR']>0]\
                                    ['Week'])]=a_all_brand_group.loc[a_all_brand_group['TPR']>0].values
            else:
                single_ppg_df = a_all_brand_group
       
        single_ppg_df.loc[(single_ppg_df['TPR']==0),'TYPE_OF_PROMO'] = ''
        single_ppg_df = single_ppg_df.fillna(0)
        single_ppg_df.Week = single_ppg_df.Week.astype('int64')
        _ppg = a_group['PPG_MAIN'].unique()[0]
        ppg_main = opt_generic.format_ppg2(_ppg)
    
        account_name = a_group['Retailer'].unique()[0]
        model_coeff_query = list(filter(lambda x:x[2] == ppg_main,constraints.get('model_coeff_query')))
        
        mdl_query = list(filter(lambda x:x[2] == ppg_main,constraints.get('mdl_query')))
        roi_query = list(filter(lambda x:x[2] == ppg_main,constraints.get('roi_query')))
        filtered_df = model_data_all.loc[(model_data_all['PPG']==ppg_main)&(model_data_all['Retailer']==account_name)]
        single_ppg_df.rename(columns={'PPG':'product_group'\
                                      ,'TYPE_OF_PROMO':'type_of_promo'\
                                    ,'Retailer':'account_name'},inplace=True)
        _grouped_ppg_df = single_ppg_df.groupby(["account_name",'product_group','type_of_promo'])
        
        for _pk,_item1 in _grouped_ppg_df:
     
            if _pk[2]:
                if filtered_df.loc[(filtered_df['product_group_new']==_pk[1]) & (filtered_df['account_name_new']==_pk[0]) & (filtered_df['type_of_promo']==_pk[2]),'joint_flag'].shape[0]:
                    single_ppg_df.loc[(single_ppg_df['product_group']==_pk[1]) & (single_ppg_df['account_name']==_pk[0]) & (single_ppg_df['type_of_promo']==_pk[2]),'joint_flag'] = filtered_df.loc[(filtered_df['product_group_new']==_pk[1]) & (filtered_df['account_name_new']==_pk[0]) & (filtered_df['type_of_promo']==_pk[2]),'joint_flag'].values[0]
                if filtered_df.loc[(filtered_df['product_group_new']==_pk[1]) & (filtered_df['account_name_new']==_pk[0]) & (filtered_df['type_of_promo']==_pk[2]),'mechanic'].shape[0]:
                    single_ppg_df.loc[(single_ppg_df['product_group']==_pk[1]) & (single_ppg_df['account_name']==_pk[0]) & (single_ppg_df['type_of_promo']==_pk[2]),'mechanic'] = filtered_df.loc[(filtered_df['product_group_new']==_pk[1]) & (filtered_df['account_name_new']==_pk[0])  & (filtered_df['type_of_promo']==_pk[2]),'mechanic'].values[0]
        
        _grouped_one_shot_ppg_df = one_shot_ppg_df.groupby(['Retailer','PPG','TYPE_OF_PROMO'])
        single_ppg_df['is_one_shot_ppg'] = False
        if non_one_shot_ppg.shape[0] and one_shot_ppg_df.shape[0]:
            for opg,_ in _grouped_one_shot_ppg_df:
                    based_df = filtered_df.loc[(filtered_df['type_of_promo']==opg[2])&(filtered_df['product_group_new']==opg[1])]
                    if based_df.shape[0]:
                        not_optimized_single_ppg_df = single_ppg_df.loc[(single_ppg_df['IS_OPTIMIZED']!=True)]
                        not_optimized_weeks = not_optimized_single_ppg_df.loc[not_optimized_single_ppg_df['Week'].isin(based_df.loc[based_df['TPR']>0]['Week']),'Week'].tolist()
                        not_optimized_single_weekly_ppg_df = not_optimized_single_ppg_df.loc[not_optimized_single_ppg_df['Week'].isin(not_optimized_weeks)]
                        not_optimized_single_weekly_ppg_df[['Leaflet_flag','Newsletter_flag','Advertising_without_price_flag','Bonus_flag', 'Pas_flag','Coupon_flag', 'EDLP_flag', 'Multibuy_flag','joint_flag','mechanic','product_group','TPR']]=based_df.loc[based_df['Week'].isin(not_optimized_weeks),['Flag_promotype_Leaflet','Flag_promotype_newsletter','Flag_promotype_advertising_without_price','Flag_promotype_bonus','Flag_promotype_pas','Flag_promotype_coupon','Flag_promotype_edlp','Flag_promotype_multibuy','joint_flag','mechanic','product_group_new','TPR']].values
                        not_optimized_single_weekly_ppg_df['is_one_shot_ppg'] = True
                        single_ppg_df.loc[single_ppg_df['Week'].isin(not_optimized_weeks)] = not_optimized_single_weekly_ppg_df.values
        # add base promo        
        single_ppg_df['is_standalone'] = filtered_df['is_standalone'].values
     
        status = single_ppg_df['status'].unique()[0]
        is_one_shot_ppg = single_ppg_df['is_one_shot_ppg'].unique()[0]
        empty_jnt_df= single_ppg_df[single_ppg_df['joint_flag']=='']
        # breakpoint()
        single_ppg_df.loc[single_ppg_df['joint_flag']=='','joint_flag'] = filtered_df.loc[filtered_df['Week'].isin(empty_jnt_df['Week']),'joint_flag'].values

        if single_ppg_df.loc[single_ppg_df['TPR']==0].shape[0] == single_ppg_df.shape[0]:
            single_ppg_df['Leaflet_flag'] = 0
        single_ppg_df = single_ppg_df.fillna(0)
        # continue
        resp = opt_mixin.calculate_finacial_metrics_for_optimizer(
            single_ppg_df,
            model_coeff_query,
            mdl_query,
            roi_query,
            constraints.get('input_data')['scenario_name'],
            status=status,
            is_one_shot_ppg=is_one_shot_ppg,
            extra_columns=list(filtered_df.columns[-17:])
        )
  
        return resp
    
    resp_list = get_financial_results(grouped_df=grouped_df)

    total_no_of_slots_with_duplicates,total_no_of_slots_wo_duplicates = cmn_utils.get_no_of_slots(resp_list)
    return {"no_of_leaflet":total_no_of_slots_wo_duplicates,
            'total_no_of_slots':total_no_of_slots_with_duplicates,
            'objective_func':opt_utils.get_objective_func().get(config_default_3['Objective_metric']),
            'data':resp_list}
    