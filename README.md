# SRM Germany Pet application

## Requirements (Prerequisites)

Python 3.8 and up Install

## Setup

The first thing to do is to clone the repository:

```sh
$ git clone https://<EMAIL>/marsanalytics/ADV%20ANALYTICS%20GERMANY/_git/DE-PET-TPO-BACKEND
$ cd DE-PET-TPO-BACKEND
```

```sh
$ python venv de-pet-env
$ source env/bin/activate
```

Then install the dependencies:

```sh
(env)$ pip install -r requirements.txt
```

Once `pip` has finished downloading the dependencies:
```sh
(env)$ cd project
(env)$ python manage.py runserver
```
And navigate to `http://127.0.0.1:8000`.

## About Tool

### Scenario Planner :

    1. User can simulate various Discount types across different Weeks and see how the sales got affected.
    2. Unlike Pricing Tool User can Vary Discounts at a week level and see the affect at Weekly level.

### Optimizer :

    1. Optimizer helps to re-arrange the Promotions across weeks in such a way that it satisfies our Objective function.
    2. Here Objective fuction could be something like increasing our Mac or increasing Retailer Margin etc.,

## Linting
```sh
(env)$ pylint ./DE-PET-TPO-BACKEND
```

## Tests

To run the tests, `cd` into the directory where `manage.py` is:
```sh
(env)$ export DATABASE_ENV=testing_env # for linux container
(env)$ set DATABASE_ENV=testing_env # for windows container
(env)$ pytest -rP
(env)$ pytest --excelreport=test_report.xlsx --collect-only
(env)$ pytest --html=test_report.html --collect-only
```