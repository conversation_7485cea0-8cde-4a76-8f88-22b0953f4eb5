""" Scenario Planner Services"""
from __future__ import annotations
from decimal import Decimal

import logging
import re
from contextlib import closing

from django.db import connection, transaction
from apps.pricing.pricing_target.target_generics import agg_pricing_target
import ast

import utils
from apps.common import serializers as cmn_ser
from apps.common import services as cmn_serv
from apps.common import utils as cmn_utils
from apps.promo.promo_optimizer import generic as opt_generic
from apps.promo.promo_optimizer import process
from apps.promo.promo_optimizer import utils as opt_utils
from apps.pricing.pricing_common import calculations as calc
from apps.pricing.pricing_common import constants as cmn_const
from apps.pricing.pricing_common import queries,services as pcmn_serv,utils as pc_utils
from core.generics import constants
from core.generics import constants as CONST
from core.generics import exceptions, resp_utils
from core.generics import unit_of_work as _uow  # pylint: disable=E0401
from tasks.parallel_task import parallel_task_executioner2
from utils import _regex

from . import constants as CONST
from . import queries as set_target_queries

logger = logging.getLogger(__name__)

def get_scenario(uow: _uow.AbstractUnitOfWork\
                 ,scenario_id: int=None\
                ,user=None\
                ,scenario_view=''\
                ,scenario_type='simulator'\
                ,module_type='set_pricing'):
    """Return the Baseline data based on with or without scenario id input.

    Parameters
    ----------
    scenario id(Optional)
        Saved Scenario Id.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    list
        list of dicts if successful, empty list otherwise.

    """
    
    with uow as unit_of_work:
        if scenario_id:
            return unit_of_work.repo_obj.filter(scenario_id)
        return unit_of_work.repo_obj.get_by_scenario_type(scenario_type,module_type,user)

@resp_utils.handle_json
def save_promo_scenaro(**data):
    serialized_data = cmn_ser.ScenarioPromotionSerializer(
        data=data.get('request_data'))
    if serialized_data.is_valid():
        serialized_data.save(saved_scenario=data.get('instance'),id=data.get('instance').id)
        return {'saved_id':data.get('instance').id}
    
    raise exceptions.MissingRequestParamsError("status", serialized_data.errors)

@resp_utils.handle_json
def update_promo_scenaro(**data):
    
    serialized_data = cmn_ser.ScenarioPromotionSerializer(
        data.get('instance'),data=data.get('request_data'),partial=True)
    if serialized_data.is_valid():
        serialized_data.save()
        return {'updated_id':data.get('instance').id}
    
    raise exceptions.MissingRequestParamsError("status", serialized_data.errors)


def post_scenario(request_data: dict, uow: _uow.AbstractUnitOfWork,user):
    """Save Scenrio

    Parameters
    ----------
    data
        To insert the data into the database.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    str
        Success or Failure in inserting the data

    """
    data = request_data.copy()
    promotion_data = {
        'input_constraints':data.pop('input_constraints'),
        "data":data.pop('data')
    }
    data['created_by'] = user
    data['user'] = user
    with uow as unit_of_work:
        query = unit_of_work.repo_obj.filter_by_scenario_name_and_scenario_type(data.get('name'),data.get('scenario_type'))
        if  query.exists():
            raise exceptions.AlreadyExists(data.get('name'))
        scenario_instance = unit_of_work.repo_obj.add(data)
        unit_of_work.commit()

    return save_promo_scenaro(request_data=promotion_data,instance=scenario_instance)
  
    
def put_scenario(scenario_instance: dict, request_data: dict, uow: _uow.AbstractUnitOfWork,user):
    """Return the Baseline data based on with or without scenario id input.

    Parameters
    ----------
    scenario id(Optional)
        Saved Scenario Id.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    list
        list of dicts if successful, empty list otherwise.
    """

    if not scenario_instance:
        raise exceptions.MissingRequestParamsError("id", scenario_instance)
    
    data = request_data.copy()
    promotion_data = {
        'input_constraints':data.pop('input_constraints'),
        "data":data.pop('data')
    }
    data['modified_by'] = user
    with uow as unit_of_work:
        unit_of_work.repo_obj.update(scenario_instance.id,data)
        unit_of_work.commit()
        
    return update_promo_scenaro(request_data=promotion_data,
                                instance=scenario_instance)

def delete_scenario(scenario_id: int,user,uow: _uow.AbstractUnitOfWork):
    """Return the Baseline data based on with or without scenario id input.

    Parameters
    ----------
    scenario id(Optional)
        Saved Scenario Id.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    list
        list of dicts if successful, empty list otherwise.
    """

    if not scenario_id:
        raise exceptions.MissingRequestParamsError("id", scenario_id)
    with uow as unit_of_work:
        unit_of_work.repo_obj.delete(scenario_id,user)
        unit_of_work.commit()
    return {'status':'success'}

def compare_scenario(uow: _uow.AbstractUnitOfWork,saved_ids:list=None):
    """Return the Saved data based on with scenario id input.

    Parameters
    ----------
    scenario id(Optional)
        Saved Scenario Id.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    list
        list of dicts if successful, empty list otherwise.
    """

    with uow as unit_of_work:
        query_set = unit_of_work.repo_obj.filter_by_multiple_id(saved_ids)

    if not query_set.exists():
        raise  exceptions.NoDataError(saved_ids)
    return query_set

def search_scenario(uow: _uow.AbstractUnitOfWork,query_params:str=''):
    """Returns searched data based on given query.

    Parameters
    ----------
    query_params:
        search query.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    Queryset
        raw query set.
    """

    with uow as unit_of_work:
        query_params_values = list(query_params.values())
        if 'viewall' in query_params_values:
            query_params_values.remove('viewall')
        query_params_values[0] = '%' + query_params_values[0] + '%'
        query = {}
        if len(query_params_values) == 2:
            query = queries.search_pricing_query() 
        else: 
            query = queries.search_pricing_global_scenario_query() 
        _data = unit_of_work.search(query,query_params_values)
    if not _data:
        raise  exceptions.NoDataError(query_params)
    return _data

@pc_utils.validate_scenario
def get_pricing_target_data(uow:_uow.AbstractUnitOfWork\
                            ,suow:_uow.AbstractUnitOfWork=None\
                            ,scenario_name=''\
                            ,scenario_type=''\
                            ,user:dict=None):
    """Return the  Baseline data based on meta id.

    Parameters
    ----------
    meta_id : model meta id to get baseline meta data. 
    allowed_groups(Optionl): Contains list of groups that assigned to user.
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    QuerySet
        queryset.
    """

    with uow as unit_of_work:
        return unit_of_work.repo_obj.get_all()

def changed_scenario_batch_insert(
                     models,
                     n_records,
                     instance_id,
                    is_row_column=False):
    values = models['MODEL_VALUES'].value.copy()
    columns = models['MODEL_COLUMN'].value.copy() if not is_row_column else []
    new_columns = models['REQUIRED_COLUMN'].value+models['EXTRA_COLUMNS'].value
    if new_columns:
        for index,(name,_mask_name) in enumerate(new_columns):
            cmn_utils.insert_extra_var(values,index,name)
            if not is_row_column:
                cmn_utils.insert_extra_var(columns,index,_mask_name)
    basedata_query = set_target_queries.changed_scenario_batch_insert_query(utils.convert_list_to_string_list(values),len(n_records))                 
    basedata_query = re.sub("[\"\']", "", basedata_query)
    params = []
    for i in n_records:
        params.append([instance_id\
                        ,i['changed_nn_change_percent']\
                        ,i['status_flag']\
                        ,i['customer']\
                        ,i['nsv_sum']\
                        ,i['nn_change_percent']\
                        ,i['nn_change_percent_new']\
                    ])  
    with closing(connection.cursor()) as cursor:
        cursor.fast_executemany=True
        cursor.execute(queries.delete_changed_scenario_query(instance_id))
        cursor.executemany(basedata_query, params)


def save_changed_promo_scenaro(**data):
    changed_scenario_batch_insert(CONST.ChangedPricingScenario\
                     ,data.get('request_data').get('changed_records')\
                    ,data.get('request_data').get('pricing_saved_scenario_id')
                     )

def save_scenario(request_data: dict\
                  , uow: _uow.AbstractUnitOfWork\
                    ,user\
                    ,is_commit=True):
    """Save Scenrio

    Parameters
    ----------
    data
        To insert the data into the database.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    str
        Success or Failure in inserting the data

    """
    data = request_data.copy()
    non_committed_id = data.pop('non_committed_id',0)
    changed_records = {
        'changed_records':data.pop('pricing_payload'),
    }
    
    scenario_instance_id = non_committed_id
    with uow as unit_of_work:
        if non_committed_id:
            data['modified_by'] = user
            data['user'] = user
            data['is_committed'] = True
            unit_of_work.repo_obj.update(scenario_instance_id,ast.literal_eval(str(data)))
            unit_of_work.commit()
        else:
            data['created_by'] = user
            data['user'] = user
            data['modified_by'] = user
            data['is_committed'] = is_commit
            # breakpoint()
            scenario_instance = unit_of_work.repo_obj.add(ast.literal_eval(str(data)))
            scenario_instance_id = scenario_instance.id
            unit_of_work.commit()
    changed_records.update({
        'pricing_saved_scenario_id':scenario_instance_id
    })
    save_changed_promo_scenaro(request_data=changed_records)
    return {'saved_id':scenario_instance_id}

def publish_scenario(request_data: dict\
                  , uow: _uow.AbstractUnitOfWork\
                    ,user\
                    ,is_commit=True):
    """Save Scenrio

    Parameters
    ----------
    data
        To insert the data into the database.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    str
        Success or Failure in inserting the data

    """
    data = request_data.copy()
    non_committed_id = data.pop('non_committed_id',0)
    changed_records = {
        'changed_records':data.pop('pricing_payload'),
    }
    
    scenario_instance_id = non_committed_id
    with uow as unit_of_work:
        if non_committed_id:
            data['modified_by'] = user
            data['user'] = user
            data['is_committed'] = True
            unit_of_work.repo_obj.update(scenario_instance_id,ast.literal_eval(str(data))) #for some reason de-serialization is not happening, using this library we are able to do that.
            unit_of_work.commit()
        else:
            data['created_by'] = user
            data['user'] = user
            data['modified_by'] = user
            data['is_committed'] = is_commit
            scenario_instance = unit_of_work.repo_obj.add(ast.literal_eval(str(data))) #for some reason de-serialization is not happening, using this library we are able to do that.
            scenario_instance_id = scenario_instance.id
            unit_of_work.commit()
    changed_records.update({
        'pricing_saved_scenario_id':scenario_instance_id
    })

    save_changed_promo_scenaro(request_data=changed_records)
    with uow as unit_of_work:
       unit_of_work.repo_obj.update(scenario_instance_id,{'is_published':True})
       unit_of_work.repo_obj.update(scenario_instance_id,{'is_committed':False})
       unit_of_work.commit()
 
    if not scenario_instance_id:
        return  {'saved_id not provided'}
    publisheddata_query, params = queries.update_published_flag(scenario_instance_id,module_type='set_target')

    with closing(connection.cursor()) as cursor:
        cursor.execute(publisheddata_query,params)
    return {'data':'published successfully'}


def load_published_scenario(uow: _uow.AbstractUnitOfWork,suow,scenario_saved_id):
    with suow as unit_of_work:
        saved_scenario = unit_of_work.repo_obj.get(_id=scenario_saved_id)
    
    if not saved_scenario:
        raise  exceptions.NoDataError(scenario_saved_id)
    
    return list(get_raw_dict_from_query(uow,[CONST.ChangedPricingScenario]\
                                   ,scenario_id=scenario_saved_id\
                                    ,mode=saved_scenario.mode))[0]


def get_raw_dict_from_query(uow:_uow.AbstractUnitOfWork,
                              models_list:list,
                              scenario_id:int=0,
                              mode:str='all',
                              start_index:int=0,
                              is_row_column=False,
                              serach_query=None
                              ):
    """Return the  Baseline data data frame based on account name and product group.
    Parameters
    ----------
    models_list : list of baseline models 
    account name: retailer name
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    DataFrame
        dataframe.
    """

    for models in models_list: 
        with uow as unit_of_work:

            values = models['MODEL_VALUES'].value.copy()
            columns = models['MODEL_COLUMN'].value.copy() if not is_row_column else []
            new_columns = models['REQUIRED_COLUMN'].value+models['EXTRA_COLUMNS'].value
            if new_columns:
                for index,(name,_mask_name) in enumerate(new_columns):
                    cmn_utils.insert_extra_var(values,index,name)
                    if not is_row_column:
                        cmn_utils.insert_extra_var(columns,index,_mask_name)
            if mode == 'all':
                if serach_query:
                    basedata_query = set_target_queries.search_all_agg_changed_inputs_query(scenario_id,serach_query)
                else:
                    basedata_query = set_target_queries.all_agg_changed_inputs_query(scenario_id)
            else:
                if serach_query:
                    basedata_query = set_target_queries.search_agg_changed_inputs_query(scenario_id,serach_query)
                else:
                    basedata_query = set_target_queries.all_agg_changed_inputs_query(scenario_id)
          
            base_data_df = unit_of_work.get_raw_query_data(
                basedata_query,
                start_index=start_index
                )      
      
        yield base_data_df

def saved_input_load_scenario(uow: _uow.AbstractUnitOfWork,suow,scenario_saved_id):
    with suow as unit_of_work:
        saved_scenario = unit_of_work.repo_obj.get(_id=scenario_saved_id)
    
    if not saved_scenario:
        raise  exceptions.NoDataError(scenario_saved_id)
    
    return {'scenario_name':saved_scenario.name,'data':list(get_raw_dict_from_query(uow,[CONST.ChangedPricingScenario]\
                                   ,scenario_id=scenario_saved_id\
                                    ,mode=saved_scenario.mode))[0]}

def get_customer_targets(tuow: _uow.AbstractUnitOfWork,uow: _uow.AbstractUnitOfWork,suow: _uow.AbstractUnitOfWork,customers,scenario_saved_id):

    if scenario_saved_id:
        with suow as unit_of_work:
            saved_scenario = unit_of_work.repo_obj.get(_id=scenario_saved_id)
        if not saved_scenario:
            raise  exceptions.NoDataError(scenario_saved_id)
        customer_lvl_scenario_data = list(pcmn_serv.get_raw_dict_from_query(uow,[cmn_const.PricingScenario]\
                                   ,scenario_id=scenario_saved_id\
                                    ,mode=saved_scenario.mode\
                                    ,level='customer'
                                    ))[0]
        customers = list(map(lambda x:x['customer'],customer_lvl_scenario_data))
    with tuow as unit_of_work:
        customer_targets = unit_of_work.repo_obj.get_customer_target(customers)        
    return customer_targets
