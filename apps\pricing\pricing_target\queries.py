
""" Scenario Planner Query"""
from config import settings
from config.db_handler import db_table_format_in_sql_query_str


def search_pricing_query():
    """ Search Optimizer Scenario"""
    query_string = f"""
    SELECT * FROM {db_table_format_in_sql_query_str('saved_scenario')} s
    WHERE 
    s.name LIKE  %s
    AND
    s.scenario_type = 'pricing'
    AND 
    s.status_type = %s
    AND
    s.is_delete = 0
    ORDER BY
    s.id DESC
    """
    
    return query_string

def search_pricing_global_scenario_query():
    """ Search Global Scenario Planner"""
    query_string = f"""
    SELECT * FROM {db_table_format_in_sql_query_str('saved_scenario')} s
    WHERE 
    s.name LIKE  %s
    AND
    s.scenario_type = 'pricing'
    AND
    s.is_delete = 0
    ORDER BY
    s.id DESC
    """
    return query_string

def changed_scenario_batch_insert_query(columns,n_records=1):

    sql_query = f""" INSERT INTO {db_table_format_in_sql_query_str('changed_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA)} 
                ({columns}) VALUES {', '.join(['(%s, %s, %s,%s,%s,%s,%s)'])} """
    return sql_query

def delete_changed_scenario_query(pricing_saved_scenario_id:int=None):
    query = f""" DELETE FROM {db_table_format_in_sql_query_str('changed_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA)} 
                WHERE pricing_saved_scenario_id={pricing_saved_scenario_id}
            """
    return query

def published_scenario_batch_update_query(columns,n_records=1):
    sql_query = f""" INSERT INTO {db_table_format_in_sql_query_str('published_pricing_target_scenario',schema=settings.PRICING_DATABASE_SCHEMA)} 
                ({columns}) VALUES {', '.join(['(%s, %s, %s,%s,%s,%s,%s,%s)'] * n_records)} """
    return sql_query

def delete_published_scenario_query(pricing_saved_scenario_id:int=None):
    query = f""" DELETE FROM {db_table_format_in_sql_query_str('published_view',schema=settings.PRICING_DATABASE_SCHEMA)} 
                WHERE pricing_saved_scenario_id={pricing_saved_scenario_id}
            """
    return query

# def all_agg_changed_inputs_query(scenario_id):
#     query_params  = {
#             "db_table":db_table_format_in_sql_query_str('pricing_target_baseline_scenario_view',schema=settings.PRICING_DATABASE_SCHEMA),
#             "pricing_saved_scenario_id":scenario_id
#         }
#     query = f'''
#         SELECT
#             ROW_NUMBER() OVER (ORDER BY customer) AS id,
#             AVG(changed_nn_change_percent) AS changed_nn_change_percent,
#             AVG(nn_change_percent) AS nn_change_percent,
#             AVG(nn_change_percent_new) AS nn_change_percent_new,
#             nsv_sum,
#             customer,
#             status_flag
#         FROM
#             {query_params["db_table"]} AS spbcs
#         WHERE
#             pricing_saved_scenario_id = {query_params["pricing_saved_scenario_id"]} OR status_flag = 'not modified'
#         GROUP BY
#             customer,
#             status_flag
#     '''
#     return query



def all_agg_changed_inputs_query(scenario_id):
    
    query_params  = {
            "db_table":db_table_format_in_sql_query_str('pricing_target_view',schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table2":db_table_format_in_sql_query_str('changed_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "saved_scenario_id":scenario_id
        }
    
    query = "select ROW_NUMBER() OVER( ORDER BY spplv.customer ) AS  id,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.changed_nn_change_percent ELSE spplv.changed_nn_change_percent END AS changed_nn_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.nn_change_percent ELSE 100 END AS nn_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.nn_change_percent_new ELSE spplv.nn_change_percent_new END AS nn_change_percent_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.nsv_sum ELSE spplv.nsv_sum END AS nsv_sum,"\
            "spplv.customer,cps.status_flag"\
            " from {data[db_table]} as spplv"\
            " left join (select * from {data[db_table2]} as cps "\
            "where pricing_saved_scenario_id = {data[saved_scenario_id]}) cps on cps.customer = spplv.customer".format(data=query_params)

    return query