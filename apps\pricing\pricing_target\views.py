""" Common API Views."""
import ast

import structlog
from django.db import transaction
from django.http import (HttpResponse, HttpResponseBadRequest,
                         HttpResponseNotAllowed)
from drf_spectacular.utils import (OpenApiExample, OpenApiParameter,
                                   extend_schema)
from rest_framework import viewsets
from rest_framework.decorators import api_view
from rest_framework.pagination import PageNumberPagination

from apps.common import services as cmn_serv
from apps.pricing.pricing_common import serializers as cmn_serializer
from apps.pricing.pricing_common import services as cmn_serv
from apps.pricing.pricing_common import unit_of_work as cmn_uow
from core.generics import api_handler, excel, exceptions, oauth_token_validate
from core.generics import resp_utils as resp_util  # pylint: disable=E0401
from core.generics import search

from . import serializers
from . import serializers as ser
from . import services, unit_of_work

logger = structlog.get_logger(__name__)

class PricingTargetView(viewsets.GenericViewSet):
    serializer_class = serializers.SetPriceTargetSerializer

    @extend_schema(
        summary="Getpricing scenario data.",
        description="API end point that serves thepricing scenario data.",
    )
   
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    def get_pricing_target_data(self,request):
        """Get data from db

        Args:
            request (dict): HttpRequest object

        Returns:
            list: list of serialized data
        """
      
        logger.bind(method_name="get_targer_scenario_data", app_name="Set Target")
        scenario_name = request.query_params.get('scenario_name')
        scenario_type = request.query_params.get('scenario_type')
        response = services.get_pricing_target_data(
            unit_of_work.SetPricingTargetUnitOfWork(transaction),
            suow=cmn_uow.PricingPublishedSavedScenarioUnitOfWork(transaction),
            scenario_name=scenario_name,
            scenario_type=scenario_type,
            user=request._user
        )
        serializer = self.serializer_class(
            response, many=True)
        return serializer.data
    
    @extend_schema(
    summary="Simulate Scenario",
    description="API end point that serves the Simulate Scenario.",
    # request=ser.ScenarioPlannerRequestSerializer,
    # responses=ser.ScenarioPlannerRequestAndResponseSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @api_handler.API_REQUEST_QUERY_HANDLER
    @resp_util.validate_input_parameters
    @resp_util.handle_response

    def post_simulate_scenario(self,request):
        logger.bind(method_name="post_simulate_scenario", app_name="Pricing Common")
        response = services.simulate_pricing_scenario(cmn_uow.PricingScenarioUnitOfWork(transaction),
                                                      cmn_uow.PricingPublishedSavedScenarioUnitOfWork(transaction),
                                                    request.data,
                                                    request._user
                                                    )
        
        return response


class SetTargetSavedScenarioView(viewsets.GenericViewSet):
    serializer_class = cmn_serializer.PricingScenarioSerializer
    filter_backends = [search.CustomSearchFilter]
    search_fields  = ['name','status_type','scenario_type','promo_type']
    pagination_class = PageNumberPagination
    page_size_query_param = 'page_size'
    page_query_param = 'page'
    lookup_field = 'id'
    lookup_url_kwarg = 'saved_id'

    def get_queryset(self):
        return services.get_scenario(
                        cmn_uow.PricingScenarioUnitOfWork(transaction),
                        user=self.request._user
                    ).order_by('-id')
    
    @extend_schema(
        summary="Save scenario Based on saved id",
        description="API end point that serves saving scenario.",
        # request=ser.ScenarioPlannerSaveRequestSerializer,
        # responses=ser.ScenarioPlannerSavedResponseSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @api_handler.API_REQUEST_QUERY_HANDLER
    @resp_util.validate_input_parameters
    @resp_util.handle_response
   
    def save_scenario(self,request):
        logger.bind(method_name="save_scenario", app_name="Pricing Common")
        response = services.save_scenario(
            request.data, 
            cmn_uow.PricingPublishedSavedScenarioUnitOfWork(transaction),
                request._user,
                is_commit=True
        )
        return response
    


    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @api_handler.API_REQUEST_QUERY_HANDLER
    @resp_util.validate_input_parameters
    @resp_util.handle_response
   
    def publish_scenario(self,request):
        logger.bind(method_name="publish_scenario", app_name="Pricing Common")
        response = services.publish_scenario(
            request.data, 
            cmn_uow.PricingPublishedSavedScenarioUnitOfWork(transaction),
                request._user,
                is_commit=True
        )
        return response
    

    @extend_schema(
    summary="Load Saved scenario Based on saved id",
    description="API end point that serves the Loading Saved scenario Based on saved id.",
    parameters=[
        OpenApiParameter(
            name="saved_id",
            description="Filter by saved_id",
            required=True,
            type=str,
            examples=[
                OpenApiExample(
                    "Example 1",
                    summary="Filter by saved_id",
                    description="saved_id shpuld be type integer.",
                    value=1,
                )
            ],
        )
    ],
    # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    def load_published_scenario(self,request):
        response = services.load_published_scenario(
            cmn_uow.PricingCommonScenarioUnitOfWork(transaction),
            cmn_uow.PricingPublishedSavedScenarioUnitOfWork(transaction),
            request.parser_context['kwargs'].get('saved_id',None)
        )
        return response
    
    @extend_schema(
        summary="Load Saved scenario Based on saved id",
        description="API end point that serves the Loading Saved scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
  
    def load_scenario(self,request):
        response = services.saved_input_load_scenario(
            cmn_uow.PricingCommonScenarioUnitOfWork(transaction),
            cmn_uow.PricingPublishedSavedScenarioUnitOfWork(transaction),
            request.parser_context['kwargs'].get('saved_id',None)
        )

        return response
    
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @api_handler.API_REQUEST_QUERY_HANDLER
    @resp_util.validate_input_parameters
    @resp_util.handle_response
  
    def search(self,request,*args,**kwargs): 
        param = request.query_params.get('q','')
        if not param:
            raise exceptions.MissingRequestParamsError("q", param)

        logger.bind(method_name="search", app_name="Pricing Common")
        
        response = services.search_scenario(
            unit_of_work.SetTargetsSavedScenarioUnitOfWork(transaction),
            query_params=request.query_params
        )
        
        serializer = self.serializer_class(
            response, many=True)
        return serializer.data
    
    @extend_schema(
        summary="Download optimizer  output",
        description="Download optimizer  output",
        # request=ser.ScenarioPlannerRequestAndResponseSerializer,
        responses="Pricing Common output excel."       
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    
    def download(self,request,*_args,**_kwargs):
        logger.bind(method_name="download", app_name="Optimizer Scenario")

        filename = 'promo_optimizer.xlsx'
        response = HttpResponse(
            excel.download_excel_promo(request.data['data']),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename=%s' % filename
        return response
    
    @extend_schema(
        summary="Download optimizer  output",
        description="Download optimizer  output",
        # request=ser.ScenarioPlannerRequestAndResponseSerializer,
        responses="Pricing Common output excel."       
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    
    def get_customer_targets(self,request,*_args,**_kwargs):
        logger.bind(method_name="download", app_name="Optimizer Scenario")
        customers = request.data.get('customers')
        scenario_id = request.data.get('scenario_id',None)
        response = services.get_customer_targets(
            unit_of_work.SetPricingTargetUnitOfWork(transaction),
            cmn_uow.PricingScenarioCustomerLevelUnitOfWork(transaction),
            cmn_uow.PricingPublishedSavedScenarioUnitOfWork(transaction),
            customers,
            scenario_id
            
        )
        return response

