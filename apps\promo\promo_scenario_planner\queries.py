
""" Scenario Planner Query"""
from config.db_handler import db_table_format_in_sql_query_str

def search_scenario_planner_query():
    """ Search Optimizer Scenario"""
    query_string = f"""
    SELECT * FROM {db_table_format_in_sql_query_str('saved_scenario')} s
    WHERE 
    s.name LIKE  %s
    AND
    s.scenario_type = 'promo'
    AND 
    s.status_type = %s
    AND
    s.is_delete = 0
    ORDER BY
    s.id DESC
    """
    
    return query_string

def search_scenario_planner_global_scenario_query():
    """ Search Global Scenario Planner"""
    query_string = f"""
    SELECT * FROM {db_table_format_in_sql_query_str('saved_scenario')} s
    WHERE 
    s.name LIKE  %s
    AND
    s.scenario_type = 'promo'
    AND
    s.is_delete = 0
    ORDER BY
    s.id DESC
    """
    return query_string
