""" Common App Serializers"""
from rest_framework import serializers
from django.db import transaction
from apps.pricing.pricing_common import models as db_model
from apps.promo.promo_optimizer.generic import format_ppg #pylint: disable=E0401
from . import constants as cmn_sonst,services,unit_of_work as uow

class ChangedSavedPricingScenarioSerializer(serializers.ModelSerializer): # pylint: disable=W0223
    """ Scenario Promotion Serializer."""
    scenario_name = serializers.ReadOnlyField(source='pricing_saved_scenario.name')
    status_type = serializers.ReadOnlyField(source='pricing_saved_scenario.status_type')
    scenario_type = serializers.ReadOnlyField(source='pricing_saved_scenario.scenario_type')
    module_type = serializers.ReadOnlyField(source='pricing_saved_scenario.module_type')
    type_of_price_inc = serializers.ReadOnlyField(source='pricing_saved_scenario.type_of_price_inc')
    status_flag = serializers.ReadOnlyField(source='pricing_saved_scenario.status_flag')
    promo_type = serializers.ReadOnlyField(source='pricing_saved_scenario.promo_type')
    
    class Meta:
        """ Class Meta for Scenario Promotion Serializer."""
        model = db_model.ChangedPricingScenario
        fields = '__all__'
        read_only_fields = ('id',)
        ref_name = None

class PricingScenarioSerializer(serializers.ModelSerializer): # pylint: disable=W0223
    """ Model Meta Serializer."""
    class Meta:
        """ Class Meta for Model Meta Serializer."""
        model = db_model.PricingCommonScenarioView
        fields = '__all__'
        read_only_fields = ('id',)
        ref_name = None
        
class PricingScenarioCustomerLevelSerializer(serializers.ModelSerializer): # pylint: disable=W0223
    """ Model Meta Serializer."""
    class Meta:
        """ Class Meta for Model Meta Serializer."""
        model = db_model.PricingScenarioCustomerLevelView
        fields = '__all__'
        read_only_fields = ('id',)
        ref_name = None
    
class SelectedFieldsPricingScenarioSerializer(serializers.ModelSerializer): # pylint: disable=W0223
    """ Model Meta Serializer."""
    class Meta:
        """ Class Meta for Model Meta Serializer."""
        model = db_model.PricingCommonScenarioView
        fields = ('product_group',)
        read_only_fields = ('id',)
        ref_name = None
    
class CustomerPPGPricingScenarioSerializer(serializers.ModelSerializer): # pylint: disable=W0223
    """ Model Meta Serializer."""
    class Meta:
        """ Class Meta for Model Meta Serializer."""
        model = db_model.PricingCommonScenarioView
        fields = ('product_group',)
        read_only_fields = ('id',)
        ref_name = None

class PricingMetaScenarioSerializer(serializers.ModelSerializer): # pylint: disable=W0223
    """ Model Meta Serializer."""

    class Meta:
        """ Class Meta for Model Meta Serializer."""
        model = db_model.PricingCommonScenarioView
        fields = '__all__'
        read_only_fields = ('id',)
        ref_name = None

class PricingPermissionScenarioSerializer(serializers.ModelSerializer): # pylint: disable=W0223
    """ Model Meta Serializer."""
    permissions = serializers.SerializerMethodField('get_permissions')
 
    
    class Meta:
        """ Class Meta for Model Meta Serializer."""
        model = db_model.PricingPublishedSavedScenario
        fields = ('permissions',)
        read_only_fields = ('id',)
        ref_name = None
        
    def get_permissions(self,obj):
        user = self.context["request"]._user
        user_permissions_list =  obj.shared_user.get('user_permission',[])
        permissions=list(filter(lambda x:user['email'] == x['mail'],user_permissions_list))
        return permissions[0] if permissions else {}

class PricingPublishedSavedScenarioSerializer(serializers.ModelSerializer): # pylint: disable=W0223
    """ Model Meta Serializer."""
    meta = serializers.SerializerMethodField('get_meta')
    approval_status = serializers.CharField(source='get_approval_status_display')
    class Meta:
        """ Class Meta for Model Meta Serializer."""
        model = db_model.PricingPublishedSavedScenario
        fields = '__all__'
        read_only_fields = ('id',)
        ref_name = None
    
    def get_meta(self,obj):
        """ Return Meta data"""
        obj  = services.get_scenarios(
                        uow.ChangedPricingScenarioUnitOfWork(transaction),
                        obj.id
                    )

        return obj
    
class PricingSummeryScenarioSerializer(serializers.ModelSerializer): # pylint: disable=W0223
    """ Model Meta Serializer."""

    class Meta:
        """ Class Meta for Model Meta Serializer."""
        model = db_model.PricingScenarioSummary
        fields = ('pricing_saved_scenario_id','base','simulated','optimized_nn_target','target','actual_invest','invest_budget','calculated_nn_percent','nsv')
        read_only_fields = ('id',)
        ref_name = None

class PricingOverallSummeryScenarioSerializer(serializers.ModelSerializer): # pylint: disable=W0223
    """ Model Meta Serializer."""

    class Meta:
        """ Class Meta for Model Meta Serializer."""
        model = db_model.PricingScenarioOverallSummary
        fields = ('pricing_saved_scenario_id','base','simulated')
        read_only_fields = ('id',)
        ref_name = None

class BaseAndSimulatedPricingScenarioSerializer(serializers.ModelSerializer): # pylint: disable=W0223
    """ Model Meta Serializer."""

    class Meta:
        """ Class Meta for Model Meta Serializer."""
        model = db_model.BaseAndSimulatedPricingScenario
        fields = '__all__'
        # read_only_fields = ('id',)
        ref_name = None

class ComparePricingOverallSummeryScenarioSerializer(serializers.ModelSerializer): # pylint: disable=W0223
    """ Model Meta Serializer."""
    scenario_name = serializers.ReadOnlyField(source='pricing_saved_scenario.name')
    status_type = serializers.ReadOnlyField(source='pricing_saved_scenario.status_type')
    scenario_type = serializers.ReadOnlyField(source='pricing_saved_scenario.scenario_type')
    module_type = serializers.ReadOnlyField(source='pricing_saved_scenario.module_type')
    type_of_price_inc = serializers.ReadOnlyField(source='pricing_saved_scenario.type_of_price_inc')
    status_flag = serializers.ReadOnlyField(source='pricing_saved_scenario.status_flag')
    promo_type = serializers.ReadOnlyField(source='pricing_saved_scenario.promo_type')
    class Meta:
        """ Class Meta for Model Meta Serializer."""
        model = db_model.PricingScenarioOverallSummary
        fields = ('__all__')
        read_only_fields = ('id',)
        ref_name = None


class UploadedFileSerializer(serializers.ModelSerializer):
    class Meta:
        model = db_model.UploadedFile
        fields = '__all__'

class UploadedFileplannerSerializer(serializers.ModelSerializer):
    class Meta:
        model = db_model.uploadfile
        fields = '__all__'
