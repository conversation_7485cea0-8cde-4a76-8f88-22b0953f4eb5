import decimal
import logging
from django.conf import settings
from django.http import HttpResponse
import functools
import json
import numpy as np
import structlog
from datetime import date
from django.shortcuts import HttpResponse
from core.generics.exceptions import (AlreadyExists, NoDataError, 
                                    MissingRequestParamsError, 
                                    EmptyObjectError,
                                    MissingRequestParamsandPayloadError
                                    )
from rest_framework import serializers

logger = structlog.get_logger(__name__)

logger = logging.getLogger(__name__)



class AccountNameNotPassedException(Exception):
    """when you dont pass account_name column"""

def log_activity_info(module, page_name, action):
    def actual_decorator(func):
        @functools.wraps(func)
        def wrapper(self):
            result = func(self)
            logger.info(self.request, module, page_name, action)
            return result

        return wrapper

    return actual_decorator

def handle_json(func):
    def wrapper(*args, **kwargs):
        if settings.DATABASE_ENV=='TESTING':
            kwargs['request_data']['id'] = kwargs.get('instance').id
            kwargs['request_data']['data']=json.dumps(kwargs.get('request_data').get('data'))
            kwargs['request_data']['input_constraints']=json.dumps(kwargs.get('request_data')\
                                                        .get('input_constraints'))
        return func(*args, **kwargs)
    return wrapper

def handle_input_constraints(data):
    if settings.DATABASE_ENV=='TESTING':
        data=json.loads(data)
    return data

def validate_input_parameters(func):
    def wrapper(*args, **kwargs):
        print('validate func')

        try:
            
            index = kwargs.get('index',0)
            query_params_format = kwargs.get('query_params_format',None)
            if kwargs.get('method','GET') == 'POST':
                qp = args[index].data 
            elif kwargs.get('method','GET') == 'PUT':
                params  = args[index].parser_context['kwargs']
                if not params:
                    params = args[index].query_params.keys()
                qp = {**args[index].data,**params}
            else:
                qp = args[index].parser_context['kwargs']
                if not qp:
                    qp = args[index].query_params.keys()

            dd = list(filter(lambda x:x not in qp,query_params_format))
            if dd:
                for pm in dd:
                    if pm in kwargs.get('optional',[]):
                        print('skip')
                        continue
                    raise MissingRequestParamsandPayloadError(pm)
            return func(*args, **kwargs)
  
        except MissingRequestParamsandPayloadError as error:
            logger.exception(str(error))
            response = {
                "data": error.__str__(),
                "status": "ERROR IN PARAMS",
                "http_code": 406,
            }
        return HttpResponse(
        json.dumps(response),
        content_type="Application/json",
        status=int(response["http_code"]),
        )

    return wrapper

def handle_response(func):
    def wrapper(*args, **kwargs):
        print('handle func')
        response = {"data": "", "status": "OK", "http_code": 200}
        try:
            result = func(*args)
            response["data"] = result
        except MissingRequestParamsError as error:
            logger.exception(str(error))
            response = {
                "data": error.__str__(),
                "status": "ERROR IN PARAMS",
                "http_code": 406,
            }
        except NoDataError as error:
            logger.exception(str(error))
            response = {
                "data": error.__str__(),
                "status": "NO DATA FOUND",
                "http_code": 404,
            }
        except AlreadyExists as error:
            logger.exception(str(error))
            response = {
                "data": error.__str__(),
                "status": "Already Exists",
                "http_code": 409,
            }
        except EmptyObjectError as error:
            logger.exception(str(error))
            response = {
                "data": [],
                "status": "NO DATA FOUND",
                "http_code": 404,
            }
        except serializers.ValidationError as error:
            logger.exception(str(error.default_detail))
            response = {
                "data": str(error.default_detail),
                "status": "Data not accepted",
                "http_code": error.status_code,
            }
        except AccountNameNotPassedException as error:
            logger.exception(str(error))
            response = {
                "data": "account_name parameter not passed",
                "status": "ERROR",
                "http_code": 500,
            }
        except Exception as error:
            logger.exception(str(error))
            response = {
                "error":str(error),
                "data": "INTERNAL SERVER ERROR",
                "status": "ERROR",
                "http_code": 500,
            }
            if 'given query' in str(error):
                response = {
                "data": error.__str__(),
                "status": "NO DATA FOUND",
                "http_code": 404,
            }
        
 
        if 'data' in response:
            if response['data']:
                if 'Content-Type' in response['data']:
                    if 'spreadsheet' in response['data']['Content-Type']:
                        return response['data']
 
        return HttpResponse(
            json.dumps(response,cls=Encoder),
            content_type="Application/json",
            status=int(response["http_code"],
            
            ),
        )

    return wrapper

class Encoder(json.JSONEncoder):
    def default(self, obj):
        # 👇️ if passed in object is instance of Decimal
        # convert it to a string
        if isinstance(obj, decimal.Decimal):
            return float(obj)
        # 👇️ if passed in object is instance of Decimal
        # convert it to a string
        if isinstance(obj, date):
            return str(obj)
        # 👇️ otherwise use the default behavior
        if isinstance(obj, np.bool_):
            return bool(obj)
        
        if isinstance(obj, (np.int32,np.int64)):
            return int(obj)
        return json.JSONEncoder.default(self, obj)

def get_type_of_promo(val):
    if 'all_brand' in val:
        return 'all_brand'
    if '_' in val:
        return 'joint'
    return 'single'