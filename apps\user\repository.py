from rest_framework.serializers import ValidationError
from core.generics.respository import ORMModelRepository
from . import models

class RegionUserGroupRepository(ORMModelRepository):
    def __init__(self):
        super().__init__(models.RegionGroup)

    def get(self, uid):
        return self._model.filter(id=uid).first()

    def filter_by_id(self, uid):
        return self._model.filter(id=uid)

    def update(self, uid, entity):
        if self._model.filter(id=uid, is_delete=False).first():
            self._model.filter(id=uid).update(**entity)
        else:
            raise ValidationError("cannot update inactive records")

    def delete(self, uid):
        if self._model.filter(id=uid, is_delete=False).first():
            self._model.filter(id=uid).update(is_delete=True)
            raise ValidationError("cannot delete inactive records")

class RegionUserGroupMappingRepository(ORMModelRepository):
    def __init__(self):
        super().__init__(models.RegionUserGroupMapping)

    def get(self, uid):
        return self._model.filter(id=uid).first()

    def filter_by_id(self, uid):
        return self._model.filter(id=uid)

    def filter_by_allowed_group(self,user_id):
        return self._model.filter(user_id=user_id).values_list('group__group_name',flat=True)
    
    def filter_by_region(self,user_id):
        return self._model.filter(user_id=user_id).values_list('group__region',flat=True)
    
    def update(self, uid, entity):
        if self._model.filter(id=uid, is_delete=False).first():
            self._model.filter(id=uid).update(**entity)
        else:
            raise ValidationError("cannot update inactive records")

    def delete(self, uid):
        if self._model.filter(id=uid, is_delete=False).first():
            self._model.filter(id=uid).update(is_delete=True)
            raise ValidationError("cannot delete inactive records")
        
class UserGroupRetailerRepository(ORMModelRepository):
    def __init__(self):
        super().__init__(models.UserGroupRetailer)

    def get(self, _id):
        return self._model.filter(id=_id).first()
    
    def get_all(self):
        return self._model.all()

    def filter_by_id(self, uid):
        return self._model.filter(id=uid)

    def filter_by_region(self,groups):
        return self._model.filter(group_name__in=groups).values_list('region',flat=True)
    
    def filter_by_group(self,groups):
        return self._model.filter(group_name__in=groups)
    
    def filter_promo_all_and_self_group(self,groups):
        return self._model.filter(group_name__in=groups,is_promo_all=True)
    
    def filter_promo_all_group(self):
        return self._model.filter(is_promo=True)
    
    def filter_high_priority_group(self):
        return self._model.filter(limited_access=False)
    
    def update(self, uid, entity):
        if self._model.filter(id=uid, is_delete=False).first():
            self._model.filter(id=uid).update(**entity)
        else:
            raise ValidationError("cannot update inactive records")

    def delete(self, uid):
        if self._model.filter(id=uid, is_delete=False).first():
            self._model.filter(id=uid).update(is_delete=True)
            raise ValidationError("cannot delete inactive records")
        