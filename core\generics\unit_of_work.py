from __future__ import annotations
import abc

class AbstractUnitOfWork(abc.ABC):
    def __init__(self, conn, trans) -> AbstractUnitOfWork:
        print('inside absr unit')
        self.connection = conn
        self.transaction = trans

    def __enter__(self) -> AbstractUnitOfWork:
        return self

    def __exit__(self, *args):
        self.rollback()

    @abc.abstractmethod
    def commit(self):
        raise NotImplementedError

    @abc.abstractmethod
    def rollback(self):
        raise NotImplementedError


class ORMModelUnitOfWork(AbstractUnitOfWork):
    def __init__(self, transaction, repository_object):
        print('inside ORM')
        self.repo_obj = repository_object()
        self.transaction = transaction

    def __enter__(self):
        self.auto_commit_orig = self.transaction.get_autocommit()
        self.transaction.set_autocommit(False)
        return super().__enter__()

    def __exit__(self, *args):
        super().__exit__(*args)
        print('exit',self.auto_commit_orig)
        self.transaction.set_autocommit(self.auto_commit_orig)

    def commit(self):
        print('commit')
        self.transaction.commit()

    def rollback(self):
        print('roll back')
        self.transaction.rollback()
