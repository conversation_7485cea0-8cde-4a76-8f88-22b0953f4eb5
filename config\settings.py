"""
Django settings for discount_tool project.

Generated by 'django-admin startproject' using Django 3.2.12.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.2/ref/settings/
"""

from pathlib import Path
import structlog
import os
from dotenv import load_dotenv
from .db import get_database
load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent
PROJECT_DIR = BASE_DIR.parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure--89b@bgwyo(f57_g4rjy=3z6=e!ud2swzt*_qt0s^6@#e&(v#v"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = [os.getenv('DJANGO_HOST'),'bepnsrmmixandtpuksdevas.azurewebsites.net']
if DEBUG:
    ALLOWED_HOSTS += ["localhost",'127.0.0.1']


# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "rest_framework",
    'django_elasticsearch_dsl',
    'rest_framework.authtoken',
    'corsheaders',
    "drf_spectacular",
    "apps.promo.promo_scenario_planner",
    "apps.promo.promo_optimizer",
    "apps.user",
    "apps.common",
    "apps.pricing.pricing_common",
    "apps.pricing.set_pricing",
    "apps.pricing.pricing_target",
    "apps.pricing.pricing_scenario_planner"
    
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    'whitenoise.middleware.WhiteNoiseMiddleware',
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "django_structlog.middlewares.RequestMiddleware",
]

ROOT_URLCONF = "urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "config.wsgi.application"


# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases

DATABASES,DATABASE_SCHEMA,PRICING_DATABASE_SCHEMA= get_database()
DATABASE_ENV = os.getenv('DATABASE_ENV')
# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

REST_FRAMEWORK = {
    "DEFAULT_SCHEMA_CLASS": "drf_spectacular.openapi.AutoSchema",
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.LimitOffsetPagination',
    'PAGE_SIZE': 8,
    'COERCE_DECIMAL_TO_STRING': False
}
# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_L10N = True

USE_TZ = True

OPTIMIZER_MAXITER=os.getenv('OPTIMIZER_MAXITER',100)
NATIONAL_REGION=os.getenv('NATIONAL_REGION','')

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

STATIC_URL = "/static/"

STATIC_ROOT = BASE_DIR / 'staticfiles'

# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

GROUPIDS=os.getenv('GROUPIDS','[1,2]')

CORS_ORIGIN_ALLOW_ALL = True

TOKEN_EXPIRED_AFTER_SECONDS = 18000
DATA_UPLOAD_MAX_NUMBER_FIELDS = None
DATA_UPLOAD_MAX_MEMORY_SIZE = None
REDIS_CACHE_PASSWORD = os.getenv('REDIS_CACHE_PASSWORD')
REDIS_CACHE_DNS_NAME = os.getenv('REDIS_CACHE_DNS_NAME')
CACHE_LOCATION = f'redis://:{REDIS_CACHE_PASSWORD}@{REDIS_CACHE_DNS_NAME}:6379/6'
# CACHES = {
#     "default": {
#         "BACKEND": "django_redis.cache.RedisCache",
#         "LOCATION": CACHE_LOCATION,
#         "OPTIONS": {
#             "CLIENT_CLASS": "django_redis.client.DefaultClient",
#         }
#     }
# }

# LOG_PATH = "{0}/logs".format(BASE_DIR)

# Define LOG_PATH
LOG_PATH = os.path.join(BASE_DIR, 'logs')

# Ensure the LOG_PATH directory exists
os.makedirs(LOG_PATH, exist_ok=True)
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "json_formatter": {
            "()": structlog.stdlib.ProcessorFormatter,
            "processor": structlog.processors.JSONRenderer(),
        },
        "plain_console": {
            "()": structlog.stdlib.ProcessorFormatter,
            "processor": structlog.dev.ConsoleRenderer(),
        },
        "key_value": {
            "()": structlog.stdlib.ProcessorFormatter,
            "processor": structlog.processors.KeyValueRenderer(
                key_order=["timestamp", "level", "event", "logger"]
            ),
        },
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "plain_console",
        },
        "json_file": {
            "class": "logging.handlers.WatchedFileHandler",
            "filename": "{}/json.log".format(LOG_PATH),
            "formatter": "json_formatter",
        },
        "flat_line_file": {
            "class": "logging.handlers.WatchedFileHandler",
            "filename": "{}/flat_line.log".format(LOG_PATH),
            "formatter": "key_value",
        },
    },
    "loggers": {
        "django_structlog": {
            "handlers": ["console", "flat_line_file", "json_file"],
            "level": "INFO",
        },
        # Make sure to replace the following logger's name for yours
        "apps.user": {
            "handlers": ["console", "flat_line_file", "json_file"],
            "level": "INFO",
        },
        "apps.promo.promo_optimizer": {
            "handlers": ["console", "flat_line_file", "json_file"],
            "level": "INFO",
            
        },
        "apps.promo.promo_scenario_planner": {
            "handlers": ["console", "flat_line_file", "json_file"],
            "level": "INFO",
        },
    },
}
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.processors.TimeStamper(fmt="%Y-%m-%d %H:%M:%S", utc=False),
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.ExceptionPrettyPrinter(),
        structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
    ],
    context_class=structlog.threadlocal.wrap_dict(dict),
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

API_DESCRIPTION = "" \
    "<h3>Using Swagger UI for Testing.</h3>" \
    "<ol>" \
    "<li>Click on <strong>Django Login</strong></li>" \
    "<li>Enter valid user credentials in the login form and submit.</li>" \
    "<li>Copy the displayed token.</li>" \
    "<li>Go back to swagger UI and click on the <strong>Authorize</strong> button.</li>" \
    "<li>Enter <strong>Token</strong> {token copied in third step} and click on Authorize.\
    <i>Token prefix (with space) is important.</i></li>" \
    "<li>Click on the <strong>Close</strong> button.</li>" \
    "</ol>" \
    "Now you can use swagger UI to test the Pricing,Promo and Optimizer API."

SPECTACULAR_SETTINGS = {
    'TITLE': 'Promo and Optimizer API',
    'DESCRIPTION': API_DESCRIPTION,
    'VERSION': '1.0.0',
    'SERVE_INCLUDE_SCHEMA': False,
    # OTHER SETTINGS
    "SWAGGER_UI_SETTINGS": {
        "deepLinking": True,
        "persistAuthorization": True,
        "displayOperationId": True,
        # ...
    }
}

ELASTICSEARCH_DSL={
    'default': {
        'hosts': 'localhost:9200'
    },
}

#USER MODEL
AUTH_USER_MODEL = 'user.User'
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-language',
    'content-type',
    'content-disposition',
    'origin',
    'authorization',
    'x-csrf-token',
    'x-requested-with',
]