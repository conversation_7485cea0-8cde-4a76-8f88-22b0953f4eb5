from enum import Enum

class PricingPayload(Enum):
    promo_data:list=[]
    
    @classmethod
    def get_method(self):
        return 'POST'

class PricingSave(Enum):
    scenario_type =  "promo"
    promo_type="single"
    status_type="completed"
    name = "optimizer save"
    comments = ""
    input_constraints={}
    data=''

    @classmethod
    def get_method(self):
        return 'POST'
    
    @classmethod
    def optional_param(self):
      return ['id','comments','data','input_constraints']

class PricingInputConstraints(Enum):
    meta_ids:list=[]