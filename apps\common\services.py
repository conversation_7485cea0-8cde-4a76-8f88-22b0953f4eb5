""" Common Services"""
import ast
import os
import copy
import re
import json
import numpy as np
from urllib.parse import urlparse
import requests
import structlog
from django.template.loader import get_template
import pandas as pd
from django.db import transaction
from core.generics import exceptions
import utils
from core.constants.generic_constants import constants_decider as gen_const_decider
from apps.promo.promo_optimizer import generic as opt_generic,queries as OPT_query,utils as opt_utils,services as opt_serv,process
from apps.promo.promo_scenario_planner import task
from core.generics.exceptions import NoDataError
from core.generics import unit_of_work as cruow,constants as CONST
from . import utils as cmn_utis,queries as cmn_queries
from ..user import services as user_service,unit_of_work as user_uow
from tasks import parallel_task as p_task
from core.generics.resp_utils import AccountNameNotPassedException

logger = structlog.get_logger(__name__)

def get_scenarios(uow:cruow.AbstractUnitOfWork,scenario_id: dict=None):
    with uow as unit_of_work:
        qryset =  unit_of_work.repo_obj.filter(scenario_id)
     
    scenario_inputs = {}
    if qryset.exists():
        scenario_inputs = list(map(lambda x:{'account_name':x['account_name'],
                                            'product_group':x['product_group'],
                                            'brand':x.get('brand',''),
                                            'brand_tech':x.get('brand_tech',''),
                                            'product_type':x.get('product_type',''),
                                            'corporate_segment':x.get('corporate_segment',''),
                                            'promo_type':x.get('type_of_promo','')}\
                                            ,qryset[0].input_constraints['data']))
    
    return scenario_inputs

def get_list_value_from_query(uow:cruow.AbstractUnitOfWork,
                              models_list:list,
                              ac_name:str,
                              ppg:str,
                              start_index:int=0
                              ):
    """Return the RAW Baseline data based on account name and product group.
    Parameters
    ----------
    models_list: list of baseline models 
    account name: retailer name
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    Queryset
        raw query set.
    """
    for models in models_list: 
        with uow as unit_of_work:
            values = models['MODEL_VALUES'].value.copy()
            new_columns = models['REQUIRED_COLUMN'].value+models['EXTRA_COLUMNS'].value
            if new_columns:
                for index,(name,_mask_name) in enumerate(new_columns):
                    cmn_utis.insert_extra_var(values,index,name)
            ppg = [ppg] if isinstance(ppg,str) else ppg
            func = cmn_queries.get_list_value_by_yearwise if models['YEAR_WISE'].value else  cmn_queries.get_list_value
            basedata_query = func(utils.convert_list_to_string_list(values),
                                            models['MODEL_NAME'].value,
                                            models['FK_MODEL'].value,
                                            models['FK_NAME'].value,
                                            ppg
                                            )
            basedata_query = re.sub("[\"\']", "", basedata_query)
            
            base_raw_data = unit_of_work.get_raw_query_list(
                basedata_query,
                ([ac_name] + list(ppg)),
                start_index=start_index
            )            
            yield base_raw_data

def get_list_dict_value_from_query(uow:cruow.AbstractUnitOfWork,
                              models_list:list,
                              ac_name:str,
                              ppg:str,
                              start_index:int=0
                              ):
    """Return the RAW Baseline data based on account name and product group.
    Parameters
    ----------
    models_list: list of baseline models 
    account name: retailer name
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    Queryset
        raw query set.
    """
    for models in models_list: 
        with uow as unit_of_work:
            values = models['MODEL_VALUES'].value.copy()
            new_columns = models['REQUIRED_COLUMN'].value+models['EXTRA_COLUMNS'].value
            if new_columns:
                for index,(name,_mask_name) in enumerate(new_columns):
                    cmn_utis.insert_extra_var(values,index,name)
            ppg = [ppg] if isinstance(ppg,str) else ppg
            func = cmn_queries.get_list_value_by_yearwise if models['YEAR_WISE'].value else  cmn_queries.get_list_value
            basedata_query = func(utils.convert_list_to_string_list(values),
                                            models['MODEL_NAME'].value,
                                            models['FK_MODEL'].value,
                                            models['FK_NAME'].value,
                                            ppg

                                            )
            basedata_query = re.sub("[\"\']", "", basedata_query)
            
            base_raw_data = unit_of_work.get_raw_query_data(
                basedata_query,
                ([ac_name] + list(ppg)),
                start_index=start_index
            )          
            yield base_raw_data

def get_list_dict_value_from_base_query(uow:cruow.AbstractUnitOfWork,
                              models_list:list,
                              ac_name:str,
                              ppg:str,
                              start_index:int=0
                              ):
    """Return the RAW Baseline data based on account name and product group.
    Parameters
    ----------
    models_list: list of baseline models 
    account name: retailer name
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    Queryset
        raw query set.
    """
    for models in models_list: 
        with uow as unit_of_work:
            values = models['MODEL_VALUES'].value.copy()
            new_columns = models['REQUIRED_COLUMN'].value+models['EXTRA_COLUMNS'].value
            if new_columns:
                for index,(name,_mask_name) in enumerate(new_columns):
                    cmn_utis.insert_extra_var(values,index,name)
            ppg = [ppg] if isinstance(ppg,str) else ppg
            basedata_query = cmn_queries.get_list_value_from_base_model(utils.convert_list_to_string_list(values),
                                            models['MODEL_NAME'].value,
                                            ppg
                                            )
            basedata_query = re.sub("[\"\']", "", basedata_query)
            
            base_raw_data = unit_of_work.get_raw_query_data(
                basedata_query,
                ([ac_name] + list(ppg)),
                start_index=start_index
            )          
        yield base_raw_data

def get_df_from_query(uow:cruow.AbstractUnitOfWork,
                              models_list:list,
                              ac_name:str,
                              ppg:list,
                              start_index:int=0,
                              is_row_column=False
                              ):
    """Return the  Baseline data data frame based on account name and product group.
    Parameters
    ----------
    models_list : list of baseline models 
    account name: retailer name
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    DataFrame
        dataframe.
    """
    for models in models_list: 
        with uow as unit_of_work:
            values = models['MODEL_VALUES'].value.copy()
            columns = models['MODEL_COLUMN'].value.copy() if not is_row_column else []
            new_columns = models['REQUIRED_COLUMN'].value+models['EXTRA_COLUMNS'].value
            if new_columns:
                for index,(name,_mask_name) in enumerate(new_columns):
                    cmn_utis.insert_extra_var(values,index,name)
                    if not is_row_column:
                        cmn_utis.insert_extra_var(columns,index,_mask_name)
            ppg = [ppg] if isinstance(ppg,str) else ppg
            func = cmn_queries.get_list_value_by_yearwise if models['YEAR_WISE'].value else  cmn_queries.get_list_value
            basedata_query = func(utils.convert_list_to_string_list(values),
                                            models['MODEL_NAME'].value,
                                            models['FK_MODEL'].value,
                                            models['FK_NAME'].value,
                                            ppg)                           
            basedata_query = re.sub("[\"\']", "", basedata_query)
            base_data_df = unit_of_work.get_data_df(
                basedata_query,
                ([ac_name] + list(ppg)),
                named_columns=columns,
                start_index=start_index
                )            
        yield base_data_df

def get_df_from_query_by_brand(uow:cruow.AbstractUnitOfWork,
                              models_list:list,
                              ac_name:str,
                              brand:list,
                              start_index:int=0,
                              is_row_column=False
                              ):
    """Return the  Baseline data data frame based on account name and product group.
    Parameters
    ----------
    models_list : list of baseline models 
    account name: retailer name
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    DataFrame
        dataframe.
    """
    for models in models_list: 
        with uow as unit_of_work:

            values = models['MODEL_VALUES'].value.copy()
            columns = models['MODEL_COLUMN'].value.copy() if not is_row_column else []
            new_columns = models['REQUIRED_COLUMN'].value+models['EXTRA_COLUMNS'].value
            if new_columns:
                for index,(name,_mask_name) in enumerate(new_columns):
                    cmn_utis.insert_extra_var(values,index,name)
                    if not is_row_column:
                        cmn_utis.insert_extra_var(columns,index,_mask_name)
            brand = [brand] if isinstance(brand,str) else brand
            func = cmn_queries.get_brandwise_list_value_by_yearwise if models['YEAR_WISE'].value \
                    else  cmn_queries.get_brandwise_list_value
            basedata_query = func(utils.convert_list_to_string_list(values),
                                            models['MODEL_NAME'].value,
                                            models['FK_MODEL'].value,
                                            models['FK_NAME'].value,
                                            brand)                         
            basedata_query = re.sub("[\"\']", "", basedata_query)
            base_data_df = unit_of_work.get_data_df(
                basedata_query,
                ([ac_name] + list(brand)),
                named_columns=columns,
                start_index=start_index
                )    
        yield base_data_df
    

def get_df_from_query_by_retailer(uow:cruow.AbstractUnitOfWork,
                              models_list:list,
                              ac_name:str,
                              start_index:int=0,
                              is_row_column=False
                              ):
    """Return the  Baseline data data frame based on account name and product group.
    Parameters
    ----------
    models_list : list of baseline models 
    account name: retailer name
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    DataFrame
        dataframe.
    """
    for models in models_list: 
        with uow as unit_of_work:

            values = models['MODEL_VALUES'].value.copy()
            columns = models['MODEL_COLUMN'].value.copy() if not is_row_column else []
            new_columns = models['REQUIRED_COLUMN'].value+models['EXTRA_COLUMNS'].value
            if new_columns:
                for index,(name,_mask_name) in enumerate(new_columns):
                    cmn_utis.insert_extra_var(values,index,name)
                    if not is_row_column:
                        cmn_utis.insert_extra_var(columns,index,_mask_name)
           
            basedata_query = cmn_queries.get_list_value_by_retailer(utils.convert_list_to_string_list(values),
                                            models['MODEL_NAME'].value,
                                            models['FK_MODEL'].value,
                                            models['FK_NAME'].value,
                                            )                           
            basedata_query = re.sub("[\"\']", "", basedata_query)
            base_data_df = unit_of_work.get_data_df(
                basedata_query,
                ([ac_name]),
                named_columns=columns,
                start_index=start_index
                )            
        yield base_data_df


def get_list_dict_value_from_query_by_retailer(uow:cruow.AbstractUnitOfWork,
                              models_list:list,
                              ac_name:str,
                              start_index:int=0
                              ):
    """Return the RAW Baseline data based on account name and product group.
    Parameters
    ----------
    models_list: list of baseline models 
    account name: retailer name
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    Queryset
        raw query set.
    """
    for models in models_list: 
        with uow as unit_of_work:
            values = models['MODEL_VALUES'].value.copy()
            new_columns = models['REQUIRED_COLUMN'].value+models['EXTRA_COLUMNS'].value
            if new_columns:
                for index,(name,_mask_name) in enumerate(new_columns):
                    cmn_utis.insert_extra_var(values,index,name)
            basedata_query = cmn_queries.get_list_value_by_retailer(utils.convert_list_to_string_list(values),
                                            models['MODEL_NAME'].value,
                                            models['FK_MODEL'].value,
                                            models['FK_NAME'].value,
                                            )
            basedata_query = re.sub("[\"\']", "", basedata_query)
            
            base_raw_data = unit_of_work.get_raw_query_data(
                basedata_query,
                ([ac_name]),
                start_index=start_index
            )          
            yield base_raw_data



def get_meta_data(uow:cruow.AbstractUnitOfWork,request_user_obj,request_query_obj):
    """Return the  Baseline data based on meta id.
    The meta_data API being used here is made totally configurable as it has to return meta_data
      information of columns that might change from region to region. We are using constants in core.

    Parameters
    ----------
    meta_id : model meta id to get baseline meta data. 
    allowed_groups(Optionl): Contains list of groups that assigned to user.
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    QuerySet
        queryset.
    """
    user = request_user_obj
    regions = []
    if user:
        regions = user_service.get_region(user_uow.UserGroupRetailerUnitOfWork(transaction),user['allowed_groups'])
    cols_based_on_region = gen_const_decider.META_DATA_API_VALUES
    meta_data_response_columns = gen_const_decider.META_DATA_API_VALUES[1:]
    query = cmn_queries.get_meta_data_query(cols_based_on_region,'selected_baseline_data_view')
    meta_data_df=uow.get_data_df(query,[],[])
    account_names = meta_data_df[meta_data_response_columns[0]].unique()
    """
    If user is belonging to a particular region, then he/she has to be displayed account_names that she is 
    authorized to be viewed so. So, based on their group information we are filtering the account hames.
    """
    if regions:
        region_list=[]
        for i in range(len(regions)):
            region_list.extend(regions[i].split(','))
        if region_list and 'all' not in region_list:
            account_names = [item for item in account_names if any(parent_item in item for parent_item in region_list)]
    account_names_flag=0
    secondary_params_list=[]
    """
    This API accepts (multiple) query params seperated by comma, taking those parameters we query corresponding 
    view from database and, the query params we get and the response columns we return are same (exluding primary 
    key column)
    if no query params are passed at all, then we return all response columns with their corresponding data
    """
    if len(request_query_obj) == 0:
        response_data_df = meta_data_df[meta_data_df[meta_data_response_columns[0]].isin(account_names)]
    else:
        """
        If query params are passed at all, then account_name will be passed first and that forms the where clause and the subsequent 
        query params are passed in else case to concatenate to the existing query using and keyword.
        """
        request_query_obj=request_query_obj.copy()
        for ind,val in enumerate(meta_data_response_columns):
            if ind == 0:
                if request_query_obj.get(val,None):
                    account_names_flag=1
                    query = cmn_queries.query_enhancer_with_where_column(query,val,cmn_utis.convert_to_comma_seperated_strings(request_query_obj.get(val)),'first')
            else:
                if request_query_obj.get(val,None):
                    if val=='product_group':
                        request_query_obj[val]=request_query_obj.get(val).replace(' ','_')
                    secondary_params_list.append(request_query_obj.get(val))
                    query = cmn_queries.query_enhancer_with_where_column(query,val,cmn_utis.convert_to_comma_seperated_strings(request_query_obj.get(val)),'consecutive')
        if account_names_flag == 0 and any(secondary_params_list):
            raise AccountNameNotPassedException
        response_data_df=uow.get_data_df(query,[],[])

    response_data_df = response_data_df[meta_data_response_columns]
    response = response_data_df.to_dict('list')
    response_after_duplicate_removal = {}
    for k,v in response.items():
        response_after_duplicate_removal[k] = list(set(v))
    response_after_duplicate_removal['product_group']  = [product_group.replace('_',' ') for product_group in response_after_duplicate_removal['product_group']]
    return response_after_duplicate_removal

    

def get_model_data(uow:cruow.AbstractUnitOfWork,meta_id:int=None,retailer=None):
    """Return the  Baseline data based on meta id.

    Parameters
    ----------
    meta_id : model meta id to get baseline model data.
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    QuerySet
        queryset.
    """
    with uow as unit_of_work:
        if meta_id:
            return unit_of_work.repo_obj.filter_by_meta_id(meta_id)
        if retailer:
            return unit_of_work.repo_obj.filter_by_account_name(retailer)
        return unit_of_work.repo_obj.get_all()


def bulk_update(uow:cruow.AbstractUnitOfWork):
    """Update given constrains.
    Parameters
    ----------
    uow:Unit of work to get the data from the DB.
    """
    with uow as unit_of_work:
        unit_of_work.repo_obj.bulk_update()
        unit_of_work.commit()


def bulk_delete(uow:cruow.AbstractUnitOfWork,ids):
    """delete given constrains.
    Parameters
    ----------
    uow:Unit of work to get the data from the DB.
    """
    with uow as unit_of_work:
        unit_of_work.repo_obj.bulk_delete(ids)
        unit_of_work.commit()

def bulk_inactive(uow:cruow.AbstractUnitOfWork,ids):
    """delete given constrains.
    Parameters
    ----------
    uow:Unit of work to get the data from the DB.
    """
    with uow as unit_of_work:
        unit_of_work.repo_obj.bulk_inactive(ids)
        unit_of_work.commit()

def get_scenrio_data(uow:cruow.AbstractUnitOfWork,scenario_id: int=None):
    """Return  Saved Scenario data based on scenario_id.
    Parameters
    ----------
    scenario_id(Optional): to get scenario data 
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    QuerySet
        queryset.
    """
    with uow as unit_of_work:
        if scenario_id:
            return unit_of_work.repo_obj.filter(scenario_id)
        return unit_of_work.repo_obj.get_all()
def get_model_meta_data(uow:cruow.AbstractUnitOfWork,meta_id: int=None,user:dict=None,account_name=''):
    """Return the  Baseline data based on meta id.

    Parameters
    ----------
    meta_id : model meta id to get baseline meta data. 
    allowed_groups(Optionl): Contains list of groups that assigned to user.
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    QuerySet
        queryset.
    """

    regions = []
    if user:
        regions = user_service.get_region(
            user_uow.UserGroupRetailerUnitOfWork(transaction),
            user['allowed_groups']
        )
    cols_based_on_region = gen_const_decider.META_DATA_API_VALUES
    meta_data_response_columns = gen_const_decider.META_DATA_API_VALUES[1:]
    query = cmn_queries.get_meta_data_query(cols_based_on_region,'model_meta')
    meta_data_df=uow.get_data_df(query,[],[])
    return meta_data_df

def update_base_and_national_promo(
                            uow:cruow.AbstractUnitOfWork,
                           puow:cruow.AbstractUnitOfWork,
                           rpmuow:cruow.AbstractUnitOfWork,
                           bnpuow: cruow.AbstractUnitOfWork,                    
                            _user:dict=None
                            ):
    """
        Returns Week level base constraints

    Args:
        uow (_uowcruow.AbstractUnitOfWork): unit_of_work
        meta_ids (list, optional): meta_ids. Defaults to [].

    Returns:
        list: final data
    """

    meta_data = get_model_meta_data(uow)
    _meta_df=meta_data[['account_name','product_group']]
    delete_base_and_national_promo(bnpuow)
    _meta_df = pd.DataFrame(_meta_df,columns=['account_name','product_group'])
    # _meta_df = _meta_df.loc[_meta_df['account_name']=='Netto']
    # _meta_df = _meta_df.loc[_meta_df['product_group']=='PEDIGREE_C_T_RANCHOS_70G']
    _grouped_df = _meta_df.groupby(["account_name"])
#    
    for kk,_item in _grouped_df:
        
        a_group = _grouped_df.get_group(kk).reset_index(drop=True)
        product_group = list(a_group['product_group'].unique())
        account_name = kk
        region = account_name.split(' ',maxsplit=1)[0]
        promotion_level_data = opt_serv.get_promotion_levels_data(uow,puow,region=region)

        acc_name_ppg_tuple_list = list(map(lambda x:(account_name,opt_generic.format_ppg2(x))\
                                        ,product_group))
                
        rpm_data = list(get_list_dict_value_from_rpm_data_query(
                                        rpmuow,
                                        acc_name_ppg_tuple_list,
                                        start_index=1
                                        ))[0]
        reatiler_ppgs_promp_types = list(map(lambda x:(x.get('account_name')\
                                        ,opt_generic.format_ppg2(x.get('product_group'))\
                                        ,x.get('type_of_promo').lower(),opt_generic.format_ppg2(x.get('ppg_item'))),rpm_data))

        ppgs_list = list(map(lambda x:x.get('product_group').replace(' ','_').replace(',', '_').replace('&', '_'),rpm_data))
        # ppgs_list = list(map(lambda x:x.get('ppg_item'),rpm_data))
        single_ppgs = list(filter(lambda x:len(x.split('_-_'))==1 and 'all_brand' not in x,ppgs_list))
        basedata_df,roi_data_df = list(get_df_from_query(
                                            uow,
                                            [CONST.ModelData,CONST.ModelROI],                                   
                                            account_name,
                                            product_group,
                                            start_index=2,
                                            is_row_column=True
                                            )
                                            )
        if basedata_df.shape[0] == 0:
            raise NoDataError(ppgs_list)
        
        if account_name in CONST.RETAILER_2021:
            basedata_df,roi_data_df = cmn_utis.get_2021_data(basedata_df,roi_data_df)
        else:
            basedata_df,roi_data_df = cmn_utis.get_last_52weeks_data(basedata_df,roi_data_df)
        basedata_df.loc[basedata_df['promo_present']==0,'tpr_discount_byppg'] = 0
        basedata_df.loc[basedata_df['promo_present']==0,'flag_promotype_all_brand'] = 0
        # basedata_df["flag_promotype_all_brand"] = np.where(
        #                         (basedata_df["promo_present"]==1) &
        #                         (basedata_df["flag_promotype_all_brand"]==1),
        #                         0, basedata_df["flag_promotype_all_brand"])
        basedata_df['acv_selling'] = basedata_df['acv'].astype(float)
        median_acv_df = basedata_df.groupby(["account_name", "product_group"], as_index=False).agg(acv_median=("acv_selling", np.median))
        inclusive_basedata_df = basedata_df.loc[basedata_df['product_group'].isin(single_ppgs)]
        grouped_df = inclusive_basedata_df.groupby(["account_name", "product_group"])
        result_dict = dict.fromkeys(CONST.DATA_VALUES)
        for key, value in zip(result_dict.keys(), CONST.DATA_HEADER):
            result_dict[key] = value
        _result = task.get_optimizer_planner_input_constraints(grouped_df=grouped_df\
                                                ,result_dict=result_dict\
                                                ,promotion_level_data=promotion_level_data\
                                                ,reatiler_ppgs_promp_types=reatiler_ppgs_promp_types\
                                                ,rpmuow=rpmuow\
                                                ,account_name=account_name\
                                                ,user=_user\
                                                ,bnpuow=bnpuow,
                                                roi_data_df=roi_data_df,
                                                basedata_df=basedata_df,
                                                median_acv_df=median_acv_df,
                                                acc=kk
                                                )   

def delete_base_and_national_promo(uow:cruow.AbstractUnitOfWork):
    with uow as unit_of_work:
        unit_of_work.repo_obj.delete()
        unit_of_work.commit()

def post_base_and_national_promo(uow:cruow.AbstractUnitOfWork,data):
    """Return the  Baseline data based on meta id.

    Parameters
    ----------
    meta_id : model meta id to get baseline meta data. 
    allowed_groups(Optionl): Contains list of groups that assigned to user.
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    QuerySet
        queryset.
    """

    with uow as unit_of_work:
        bnp_data = unit_of_work.repo_obj.filter(data.get('account_name'),[data.get('product_group')])
        if bnp_data.exists():         
            unit_of_work.repo_obj.update(bnp_data[0].id,data)
            unit_of_work.commit()
            return
        unit_of_work.repo_obj.add(data)
        unit_of_work.commit()
        

    return "added successfully"

def get_base_and_national_promo(uow:cruow.AbstractUnitOfWork,data):
    """Return the  Baseline data based on meta id.

    Parameters
    ----------
    meta_id : model meta id to get baseline meta data. 
    allowed_groups(Optionl): Contains list of groups that assigned to user.
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    QuerySet
        queryset.
    """

    with uow as unit_of_work:
        bnp_data = unit_of_work.repo_obj.filter(data.get('account_name'),data.get('product_group'))
    return bnp_data

def get_list_dict_value_from_rpm_data_query(uow:cruow.AbstractUnitOfWork,
                              acc_ppg_tuple_list:list,
                              start_index:int=0
                              ):
    """Return the RAW Baseline data based on account name and product group.
    Parameters
    ----------
    models_list: list of baseline models 
    account name: retailer name
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    Queryset
        raw query set.
    """

    with uow as unit_of_work:
        rpm_data =  unit_of_work.repo_obj.filter_by_retailer_ppg(acc_ppg_tuple_list)
        ppgs_list = list(set(rpm_data.values_list('product_group',flat=True)))
        values = CONST.ModelRetailerPPGMap['MODEL_VALUES'].value.copy()
        new_columns = CONST.ModelRetailerPPGMap['REQUIRED_COLUMN'].value\
                    +CONST.ModelRetailerPPGMap['EXTRA_COLUMNS'].value
        if new_columns:
            for index,(name,_mask_name) in enumerate(new_columns):
                cmn_utis.insert_extra_var(values,index,name)
        basedata_query = cmn_queries.get_list_value_from_base_model(utils.convert_list_to_string_list(values),
                            CONST.ModelRetailerPPGMap['MODEL_NAME'].value,
                            ppgs_list
                            )
        basedata_query = re.sub("[\"\']", "", basedata_query)
        base_raw_data = unit_of_work.get_raw_query_data(
            basedata_query,
            ([acc_ppg_tuple_list[0][0]] + list(ppgs_list)),
            start_index=start_index
        )          
    yield base_raw_data


def get_list_dict_value_from_rpm_data_query_with_top(uow:cruow.AbstractUnitOfWork,
                              acc_ppg_top_tuple_list:list,
                              start_index:int=0
                              ):
    """Return the RAW Baseline data based on account name and product group.
    Parameters
    ----------
    models_list: list of baseline models 
    account name: retailer name
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    Queryset
        raw query set.
    """

    with uow as unit_of_work:
        
        rpm_data =  unit_of_work.repo_obj.filter_by_retailer_ppg_and_promo_type(acc_ppg_top_tuple_list)
        ppgs_list = list(rpm_data.values_list('product_group',flat=True))
        values = CONST.ModelRetailerPPGMap['MODEL_VALUES'].value.copy()
        new_columns = CONST.ModelRetailerPPGMap['REQUIRED_COLUMN'].value\
                    +CONST.ModelRetailerPPGMap['EXTRA_COLUMNS'].value
        if new_columns:
            for index,(name,_mask_name) in enumerate(new_columns):
                cmn_utis.insert_extra_var(values,index,name)
        basedata_query = cmn_queries.get_list_value_from_base_model(utils.convert_list_to_string_list(values),
                            CONST.ModelRetailerPPGMap['MODEL_NAME'].value,
                            ppgs_list
                            )
        basedata_query = re.sub("[\"\']", "", basedata_query)
        base_raw_data = unit_of_work.get_raw_query_data(
            basedata_query,
            ([acc_ppg_top_tuple_list[0][0]] + list(ppgs_list)),
            start_index=start_index
        )          
    yield base_raw_data

def get_list_dict_value_from_rpm_data_query_by_retailer(uow:cruow.AbstractUnitOfWork,
                              acc_name:str,
                              start_index:int=0
                              ):
    """Return the RAW Baseline data based on account name and product group.
    Parameters
    ----------
    models_list: list of baseline models 
    account name: retailer name
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    Queryset
        raw query set.
    """

    with uow as unit_of_work:
        values = CONST.ModelRetailerPPGMap['MODEL_VALUES'].value.copy()
        new_columns = CONST.ModelRetailerPPGMap['REQUIRED_COLUMN'].value\
                    +CONST.ModelRetailerPPGMap['EXTRA_COLUMNS'].value
        if new_columns:
            for index,(name,_mask_name) in enumerate(new_columns):
                cmn_utis.insert_extra_var(values,index,name)
        basedata_query = cmn_queries.get_list_value_from_base_model_by_retailer(utils.convert_list_to_string_list(values),
                            CONST.ModelRetailerPPGMap['MODEL_NAME'].value
                            )
        basedata_query = re.sub("[\"\']", "", basedata_query)
        base_raw_data = unit_of_work.get_raw_query_data(
            basedata_query,
            ([acc_name]),
            start_index=start_index
        )          
    yield base_raw_data

def get_opt_tactic(uow:cruow.AbstractUnitOfWork,r_p_pt:dict=None)->pd.DataFrame:
    """get_optimizer_summary

    Args:
        uow (_uowcruow.AbstractUnitOfWork): unit_of_work
        data (dict, optional): dict. Defaults to {}.

    Returns:
        pd.DataFrame: summary_df
    """
    
    with uow as unit_of_work:
        rpm_data =  unit_of_work.repo_obj.filter_by_retailer_ppg2(r_p_pt)
        values = CONST.ModelTactic['MODEL_VALUES'].value.copy()
        columns = CONST.ModelTactic['MODEL_COLUMN'].value.copy()
        new_columns = CONST.ModelTactic['REQUIRED_COLUMN'].value\
            +CONST.ModelTactic['EXTRA_COLUMNS'].value
        if new_columns:
            for index,(name,_mask_name) in enumerate(new_columns):
                cmn_utis.insert_extra_var(values,index,name)
                cmn_utis.insert_extra_var(columns,index,_mask_name)
        basedata_query = OPT_query.get_tactic_data(utils.convert_list_to_string_list(values),
                        ','.join(map(str, list(rpm_data.values_list('id',flat=True))))
        )
        basedata_query = re.sub("[\"\']", "", basedata_query)
        basedata_df = unit_of_work.get_data_df(
            basedata_query,
            named_columns=columns,
            start_index=1
        )

    return basedata_df
      

def get_opt_tactic2(uow:cruow.AbstractUnitOfWork,r_p_pt:dict=None)->pd.DataFrame:
    """get_optimizer_summary

    Args:
        uow (_uowcruow.AbstractUnitOfWork): unit_of_work
        data (dict, optional): dict. Defaults to {}.

    Returns:
        pd.DataFrame: summary_df
    """
    
    with uow as unit_of_work:
        rpm_data =  unit_of_work.repo_obj.filter_by_retailer_ppg_and_promo_type(r_p_pt)
        values = CONST.ModelTactic['MODEL_VALUES'].value.copy()
        columns = CONST.ModelTactic['MODEL_COLUMN'].value.copy()
        new_columns = CONST.ModelTactic['REQUIRED_COLUMN'].value\
            +CONST.ModelTactic['EXTRA_COLUMNS'].value
        if new_columns:
            for index,(name,_mask_name) in enumerate(new_columns):
                cmn_utis.insert_extra_var(values,index,name)
                cmn_utis.insert_extra_var(columns,index,_mask_name)
        basedata_query = OPT_query.get_tactic_data(utils.convert_list_to_string_list(values),
                        ','.join(map(str, list(rpm_data.values_list('id',flat=True))))
        )
        basedata_query = re.sub("[\"\']", "", basedata_query)
        basedata_df = unit_of_work.get_data_df(
            basedata_query,
            named_columns=columns,
            start_index=1
        )

    return basedata_df

def get_retailer_ppg_weekly(uow:cruow.AbstractUnitOfWork,retailer:str=None)->pd.DataFrame:
    """get_optimizer_summary

    Args:
        uow (_uowcruow.AbstractUnitOfWork): unit_of_work
        data (dict, optional): dict. Defaults to {}.

    Returns:
        pd.DataFrame: summary_df
    """
    
    with uow as unit_of_work:
        values = CONST.ModelRETAILER_PPG_MAPPING_WEEKLY['MODEL_VALUES'].value.copy()
        columns = CONST.ModelRETAILER_PPG_MAPPING_WEEKLY['MODEL_COLUMN'].value.copy()
        new_columns = CONST.ModelRETAILER_PPG_MAPPING_WEEKLY['REQUIRED_COLUMN'].value\
            +CONST.ModelRETAILER_PPG_MAPPING_WEEKLY['EXTRA_COLUMNS'].value
        if new_columns:
            for index,(name,_mask_name) in enumerate(new_columns):
                cmn_utis.insert_extra_var(values,index,name)
                cmn_utis.insert_extra_var(columns,index,_mask_name)
        basedata_query = OPT_query.get_rpm_weekly_data_all(utils.convert_list_to_string_list(values))
        basedata_query = re.sub("[\"\']", "", basedata_query)
        basedata_df = unit_of_work.get_data_df(
            basedata_query,
            named_columns=columns,
            start_index=1,
            params=[retailer]
        )

    return basedata_df

def load_config(config_path):
    with open(config_path, 'r') as config_file:
        config_data = json.load(config_file)
    return config_data

config = load_config('./config.json')

def share_scenario(data: dict, suow: cruow.AbstractUnitOfWork,suow2: cruow.AbstractUnitOfWork=None,user=None, scenario_type='optimizer'):
    _s = requests.session()
    _s.headers = {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }

    url = re.search("(?P<url>https?://[^\s]+)", data['content']).group("url")
    data['generic_mail'] = os.getenv('GENERIC_MAIL_ADDRESS', '')
    parsed_url = urlparse(url)
    scenario_id = int(cmn_utis.decrypt(parsed_url.query[3:]))
    
    if isinstance(data['to'], list):
        to = list(map(lambda x: x.get('mail'), data['to']))
        to_str = ','.join(to)
        shared_user_payload = {'shared_user': {'from': user, 'to': to_str, 'user_permission': data['to']}}
        data['to'] = to_str
    else:
        shared_user_payload = {'shared_user': {'from': user, 'to': data['to']}}

    is_review = data.get('is_review', False)

    with suow as unit_of_work:
        scenario_obj = unit_of_work.repo_obj.get(scenario_id)
        shared_user_payload['is_review'] = is_review
        shared_user_payload['status_type'] = 'completed'
        # breakpoint()
        unit_of_work.repo_obj.update(scenario_id, ast.literal_eval(str(shared_user_payload)))
        unit_of_work.commit()
    
    # # I assume below code is to send intimation mail to reviewer about scenario being shared.
    _data = []
    if suow2:
        _data = get_scenarios(suow2, scenario_id)
    
    if is_review:
        sender_template_path = config['templates']['review']
        receiver_template_path = config['templates']['action_required']
        data['subject'] = 'Action Required: Review Request | MARS SRM POLAND PET -Advanced Analytics Tool Kit'
    else:
        sender_template_path = None
        receiver_template_path = config['templates']['default']  
        data['subject'] = 'Optimized Result Shared with You | MARS SRM POLAND PET-Advanced Analytics Tool Kit'

    receiver_message = get_template(receiver_template_path).render({
        'scenario': {
            'link':url,
            'name': f'{scenario_type} scenario',
            'tool_link': os.getenv('TOOL_LINK'),
            'retailer': _data[0]['account_name'] if _data else None,
            'scenario_name': scenario_obj.name,
            'created_by': scenario_obj.created_by['username'],
            'sender_name': user['email']
        }
    })
    # data['to'] = scenario_obj.created_by['email']
    data['content'] = receiver_message
    response = _s.post(os.environ['LOGIC_APP_URL'], json=data)

    if is_review and sender_template_path:
        data['subject'] = 'Scenario Sent for Review Confirmation | MARS SRM POLAND PET - Advanced Analytics Tool Kit'
        sender_message = get_template(sender_template_path).render({
            'scenario': {
                'link':url,
                'name': f'{scenario_type} scenario',
                'tool_link': os.getenv('TOOL_LINK'),
                'retailer': _data[0]['account_name'] if _data else None,
                'scenario_name': scenario_obj.name,
                'created_by': scenario_obj.created_by['username'],
                'sender_name': user['email']
            }
        })
        data['to'] = user['email']
        data['content'] = sender_message
        response = _s.post(os.environ['LOGIC_APP_URL'], json=data)

    return {'message': response.content.decode("utf-8"), 'status': response.status_code}
    # return {'message':'sent successfully'}

def scenario_approval(
                    data:dict\
                   ,uow: cruow.AbstractUnitOfWork\
                    ,saved_id=None\
                    ,user=None\
                    ,scenario_type='optimizer'):
    
    _s = requests.session()
    _s.headers = {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }

    if data.get('approval_status'):
        approval_status = data.get('approval_status')

    with uow as unit_of_work:
        if saved_id:
            scenario_obj = unit_of_work.repo_obj.get(saved_id)
            unit_of_work.repo_obj.update(saved_id,data)
            unit_of_work.commit()
    
    if not saved_id:
        raise  exceptions.NoDataError(saved_id)
    data['generic_mail'] = os.getenv('GENERIC_MAIL_ADDRESS','')
    _data = []
    if approval_status =='accepted':
        sender_template_path = config['templates']['approved']
        data['subject']='Scenario Approved | MARS SRM POLAND PET - Advanced Analytics Tool Kit'
    else:
        sender_template_path = config['templates']['rejected']
        data['subject']='Scenario Rejected | MARS SRM POLAND PET - Advanced Analytics Tool Kit'


    sender_message = get_template(sender_template_path).render({
        'scenario': {
            'name': f'{scenario_type} scenario',
            'tool_link':os.getenv('TOOL_LINK'),
            'retailer': _data[0]['account_name'] if _data else None,
            'scenario_name': scenario_obj.name,
            'created_by': scenario_obj.created_by['username'],
            'sender_name': user['email']
        }
    })

    data['content'] = sender_message
    data['to'] = scenario_obj.created_by['email']

    response = _s.post(os.environ['LOGIC_APP_URL'], json = data)
  
    return {'message':response.content.decode("utf-8"),'status':response.status_code}


@opt_generic.validate_scenario
def get_weekly_constraints(data\
                           ,uow:cruow.AbstractUnitOfWork\
                            ,puow:cruow.AbstractUnitOfWork\
                            ,_sopuow1:cruow.AbstractUnitOfWork=None\
                            ,_sopuow2:cruow.AbstractUnitOfWork=None\
                            ,_user=None\
                            ,is_optimizer_or_planner=None,
                            is_optional=False
                            ):
    
    account_name = data.get('account_name')
    product_group = data.get('product_group')
    region = account_name.split(' ',maxsplit=1)[0]
    promotion_level_data = opt_serv.get_promotion_levels_data(uow,puow,user=_user,region=region)
    bnp_data = get_base_and_national_promo(uow,{'account_name':account_name,'product_group':product_group})
    final_res = {'data':[]}
    input_data_dict_list = list(map(lambda x:json.loads(x.base_and_national_promo['promotion_dict']),bnp_data))
    weekly_data,raw_headers = zip(*list(map(lambda x:(json.loads(x.base_and_national_promo['weekly_data'])\
                                                              ,x.raw_headers),bnp_data)))
    base_national_promo_df = pd.DataFrame(sum(weekly_data,[]),columns=raw_headers[0])
    roi_data = list(map(lambda x:json.loads(x.base_and_national_promo['roi_data']),bnp_data))
    roi_df = pd.DataFrame(sum(roi_data,[]),columns=CONST.ROI_HEADER)

    grouped_df = base_national_promo_df.groupby(["account_name", "product_group"])
    
    @p_task.parallel_task_executioner
    def update_national_promo(**kwargs):
        # for key,_item in kwargs.get('grouped_df'):
            key = kwargs.get('key')
            a_group = kwargs.get('grouped_df').get_group(key).reset_index()
            roi_df1 = roi_df.loc[roi_df['PPG']==opt_generic.format_ppg2(key[1])]
            promoted_ppg_comb = a_group['product_group_new'].copy()
            a_group['product_group_new'] = a_group['product_group']
            a_group['product_group'] = promoted_ppg_comb
            
            df_dict = a_group.to_dict('records')
            ppg = opt_generic.format_ppg(df_dict[0]['product_group_new'])
            input_data_dict = list(filter(lambda x:x.get(ppg,''),kwargs.get('input_data_dict_list')))[0]
            input_data_dict[ppg]['data'] = {**input_data_dict[ppg]['data'],
                                                            'weekly':df_dict
                                                            }
            input_data_dict[ppg]['roi_data'] = roi_df1.to_dict('records')
            if kwargs.get('promotion_level_data'):
                filtered_promotion_level_data = list(filter(lambda x:(sorted(x['product_group'])\
                                                ==sorted(ppg)),kwargs.get('promotion_level_data')))
    
                if filtered_promotion_level_data:
                    
                    input_data_dict = cmn_utis.post_update_natioal_promotion_mechanics(df_dict\
                                                                    ,filtered_promotion_level_data[0]['weekly']
                                                                    ,input_data_dict,
                                                                    ppg_main=ppg,
                                                                    rpt=kwargs.get('reatiler_ppgs_promp_types')
                                                                )
        
            return input_data_dict
        
    result = update_national_promo(grouped_df=grouped_df,input_data_dict_list=input_data_dict_list,promotion_level_data=promotion_level_data)
    result = list(filter(lambda x:x,result))
    total_no_of_slots_with_duplicates=total_no_of_slots_wo_duplicates = 0

    if result:
        _final_list = list(map(lambda x:{**list(x.values())[0]['data'],'joint_ppg_data':list(x.values())[0]\
                                ['joint_ppg_data'],'all_brand_ppg_data':list(x.values())[0]\
                                ['all_brand_ppg_data']},result))
        
        if  is_optimizer_or_planner:
            weekly_data = list(map(lambda x:x['weekly'],_final_list))
            
            roi_weekly_data = list(map(lambda x:list(x.values())[0]['roi_data'],result))
            base_national_promo_df = pd.DataFrame(sum(weekly_data,[]))
            roi_weekly_data_df = pd.DataFrame(sum(roi_weekly_data,[]))
            base_national_promo_df = cmn_utis.format_headers(CONST.DATA_VALUES,CONST.DATA_HEADER,base_national_promo_df)
            ppgs = base_national_promo_df['PPG'].copy()
            base_national_promo_df['PPG'] = base_national_promo_df['product_group_new']
            base_national_promo_df['product_group_new'] = ppgs
            base_national_promo_df=base_national_promo_df.drop(columns=['index'])
            return base_national_promo_df,roi_weekly_data_df
  
        total_no_of_slots_with_duplicates,total_no_of_slots_wo_duplicates = cmn_utis.get_no_of_slots(_final_list)
    
    final_res = {}
    final_res.update(
        {
            'data':_final_list,
            'no_of_leaflet':total_no_of_slots_wo_duplicates,
            'min_length_gap':3,
            'no_of_promo': 12,
            'overall_no_of_leaflet':total_no_of_slots_with_duplicates,
            'scenario_name':data.get('scenario_name'),
            'objective_func':opt_utils.get_objective_func()     
        }
    )

    return final_res
