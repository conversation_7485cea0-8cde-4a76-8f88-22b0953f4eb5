from django.urls import path
from .views import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,ScenarioList,ScenarioPlannerSearchAPI

urlpatterns = [
    path('simulate/', ScenarioPlanner\
        .as_view({'post':'post_simulate_scenario'})),
    path('save/', <PERSON>enarioPlanner\
        .as_view({'post':'save_scenario'})),
    path('update/<int:saved_id>', ScenarioPlanner\
        .as_view({'put':'update_scenario'})),
    path('delete/<int:saved_id>', ScenarioPlanner\
        .as_view({'delete':'delete_scenario'})),
    path('load/<int:saved_id>/', ScenarioPlanner\
        .as_view({'get':'load_scenario'})),
    path('compare/', ScenarioPlanner\
        .as_view({'get':'compare_scenario'})),
    path('saved_scenario/<int:saved_id>/', ScenarioList\
        .as_view({'get':'get_saved_scenario'})),
    path('saved_scenario/', <PERSON>enarioL<PERSON>\
        .as_view({'get':'get_saved_scenario'})),
    path("search/", ScenarioPlannerSearchAPI.as_view({'get':'search'})),
    path("download/", ScenarioPlanner.as_view({'post':'download'})),
    path("share_planner_scenario/", ScenarioPlanner.as_view({'post':'share_planner_scenario'}))
]
