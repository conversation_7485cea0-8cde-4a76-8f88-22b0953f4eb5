import requests
import structlog
from django.db import transaction
from rest_framework.decorators import api_view
from drf_spectacular.utils import (
    extend_schema,
)
from apps.common.models import CONSTANTS
from apps.user.serializers import CreateUserSerializer
from core.generics import resp_utils
from  core.generics.oauth_token_validate import requires_auth
from . import services as serv,unit_of_work as uow
from apps.common import constants as cmc
from apps.promo.promo_scenario_planner import constants as spc
from apps.promo.promo_optimizer import constants as opc
from apps.common.tests import factories as cfac
from apps.promo.promo_scenario_planner.tests import factories as sfac
from apps.promo.promo_optimizer.tests import factories as ofac
logger = structlog.get_logger(__name__)
from django.core.cache import cache as base_data

@requires_auth
@extend_schema(
        summary="Login",
        description="Enter email and password to login.",
        request=CreateUserSerializer
    )
@api_view(["POST"])
@resp_utils.handle_response
def login(request,*args, **kwargs):     
    user_id = request._user['user_id']
    logger.info(message=f"Successfully logged In with  user ID :  {user_id}")
    return {**request._user}

@requires_auth
@extend_schema(
        summary="User",
        description="get user from beloved groups.",
        # request=CreateUserSerializer
    )
@api_view(["GET"])
@resp_utils.handle_response
def get_user(request,*args, **kwargs): 
    token  = request.META['HTTP_AUTHORIZATION']
    is_promo = request.query_params.get('is_promo',False)

    user_groups = serv.get_user_groups(
        uow.UserGroupRetailerUnitOfWork(transaction),
        request._user['allowed_groups'],
        is_all=request._user['has_all_promo_access'],
        is_promo=is_promo
    )

    user_groups_ids = user_groups.values_list('group_id',flat=True)
    members_profile_list = []
    headers = {
        "Authorization": f"{token}",
        "Content-Type": "application/json",
    }
    for gpid in user_groups_ids:
        response = requests.request("GET", f'https://graph.microsoft.com/v1.0/groups/{gpid}/members/',headers=headers)
        members_profile_list.extend(list(set(list(map(lambda x:x['mail'],response.json()['value'])))))
    logger.info(message=f"Successfully fetched users")
    return {'users':members_profile_list}

@requires_auth
@extend_schema(
        summary="User",
        description="get user from beloved groups.",
        # request=CreateUserSerializer
    )
@api_view(["GET"])
@resp_utils.handle_response
def get_higher_priority_user(request,*args, **kwargs): 
    token  = request.META['HTTP_AUTHORIZATION']
    user_groups = serv.get_user_groups(
        uow.UserGroupRetailerUnitOfWork(transaction),
        request._user['allowed_groups'],
        is_high=True
    )
    user_groups_ids = user_groups.filter(limited_access=False).values_list('group_id',flat=True)
    members_profile_list = []
    headers = {
        "Authorization": f"{token}",
        "Content-Type": "application/json",
    }
    for gpid in user_groups_ids:
        response = requests.request("GET", f'https://graph.microsoft.com/v1.0/groups/{gpid}/members/',headers=headers)
        members_profile_list.extend(list(map(lambda x:x['mail'],response.json()['value'])))
    logger.info(message=f"Successfully fetched users")
    return {'users':members_profile_list}

