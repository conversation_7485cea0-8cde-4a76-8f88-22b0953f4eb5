import os
market = os.environ.get('REGION_NAME')

from core.constants.generic_constants.common import *
if market == "AU":
    from core.constants.generic_constants.constants_au import *

elif market == "PO":
    from core.constants.generic_constants.constants_po import *

elif market == "NZ":
    from core.constants.generic_constants.constants_nz import *
elif market == "UK":
    from core.constants.generic_constants.constants_uk import *
else:
    from core.constants.generic_constants.constants_de import *