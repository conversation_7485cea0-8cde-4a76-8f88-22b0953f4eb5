''' Generic Calculation'''
import math
from apps.common import utils as cmn_utils
from apps.promo.promo_optimizer import utils as opt_utils
from . import unit_models as model
from .constants import (
    DATA_VALUES as data_values, ROI_VALUES as roi_values
)
from utils import average,_divide

def get_holiday_information(data):
    '''Method to get the ho;iday info'''
    holidays = ['holiday_flag1', 'holiday_flag2']
    for i in holidays:
        if data[data_values.index(i)]:
            return i
    return None


def yearly_level_value(roi):
    total_cogs = float(sum(list(map(lambda x:(x[roi_values.index('cogs')]),roi))))
    total_units  = float(sum(list(map(lambda x:(x[roi_values.index('units')]),roi))))
    total_promo_total_units = float(sum(list(map(lambda x:x[roi_values.index('units')]\
                            ,list(filter(lambda x:(x[roi_values.index('tactic_medium_hz')]),roi))))))
    total_non_promo_total_units = float(sum(list(map(lambda x:x[roi_values.index('units')]\
                                ,list(filter(lambda x:(not x[roi_values.index('tactic_medium_hz')]),roi))))))
    total_promo_nsv = float(sum(list(map(lambda x:x[roi_values.index('nsv')]\
                            ,list(filter(lambda x:(x[roi_values.index('tactic_medium_hz')]),roi))))))
    total_non_promo_nsv = float(sum(list(map(lambda x:x[roi_values.index('nsv')]\
                        ,list(filter(lambda x:not  x[roi_values.index('tactic_medium_hz')],roi))))))
    total_cogs_per_unit = _divide(total_cogs,total_units)
    return total_cogs_per_unit,total_promo_total_units,total_non_promo_total_units,total_promo_nsv,total_non_promo_nsv

def calculate_financial_mertrics(data_list:list,
                                 roi_list:list, 
                                 unit_info:list, 
                                 flag:str,
                                 d_columns:list=None,
                                 is_loaded_from_opt=False
                                 )->dict:  # pylint: disable=R0913
    """To calculate financial metrics for each week as well as total

    Args:
        data_list (list): model data
        roi_list (list): roi data
        unit_info (list): unit data
        flag (_type_): base/simulated flag
        is_optimiser (bool, optional): check if it is come from optimizer. Defaults to False.

    Raises:
        _e: _description_

    Returns:
        dict: _description_
    """
    
    weekly_units = []
    total_units = model.TotalUnit()
    total_cogs_per_unit,total_promo_total_units,total_non_promo_total_units\
        ,total_promo_nsv,total_non_promo_nsv = yearly_level_value(roi_list)
    
    for i,_val in enumerate(data_list): 
       
        roi = roi_list[i]   
        unit = unit_info[i]
        data = data_list[i]
        joint_flags = [ data[d_columns.index(j)] for j in d_columns if 'joint_promo' in j]
        mechanic,_type_of_promo = cmn_utils.get_scenario_planner_promo_mechanic(
                                    data[d_columns.index('flag_promotype_multibuy_tpr')],
                                    data[d_columns.index('flag_promotype_multibuy')],
                                    data[d_columns.index('flag_promotype_lottery')],
                                    data[d_columns.index('flag_promotype_unpublished')],
                                    data[d_columns.index('flag_promotype_tpr')],
                                    data[d_columns.index('flag_promotype_miscellaneous')],
                                    data[d_columns.index('promo_present')],
                                    joint_flags,
                                    data[d_columns.index('flag_promotype_all_brand')],
                                    data[d_columns.index('mechanic')],
                                    flag=flag,
                                    index=i
                                    )
        try:
            _ob = model.UnitModel(
                date=data[d_columns.index('date')],
                week=int(data[d_columns.index('week')]),
                year=int(data[d_columns.index('year')]),
                quarter=(roi[roi_values.index('quarter')]),
                month=(data[d_columns.index('month')]),
                period=(roi[roi_values.index('period')]),
                # si=data[d_columns.index('si_week')],
                predicted_units=unit['Predicted_sales'],
                promo_depth=data[d_columns.index('tpr_discount_byppg')],
                median_base_price_log=(math.exp(data[d_columns.index('wk_sold_median_base_price_byppg_log')])-1),
                incremental_unit=unit['Incremental_Units_Corrected'],
                base_unit=unit['Base_Units_Corrected'],
                incremental_unit_old=unit['Incremental'],
                base_unit_old=unit['Base'],
                total_cogs_per_unit=total_cogs_per_unit,
                cogs_per_unit_future=roi[roi_values.index('cogs_per_unit_future')],
                nsv_per_unit_future=roi[roi_values.index('nsv_per_unit_future')],
                gsv_per_unit_future=roi[roi_values.index('gsv_per_unit_future')],
                total_sold_volume=roi[roi_values.index('total_sold_volume')],
                total_sold_unit=roi[roi_values.index('total_sold_unit')],
                mechanic=mechanic,
                list_price=(roi[roi_values.index('list_price')]),
                total_trade_investment=(roi[roi_values.index('total_trade_investment')]),
                leaflet_flag=data[d_columns.index('promo_present')],
                type_of_promo=_type_of_promo,
                flag=flag,
                ppg=data[d_columns.index('product_group_new')],
                # national_promo=data[d_columns.index('promotion_levels')],
                # flag_promotype_joint_promo_1=data[d_columns.index('flag_promotype_joint_promo_1')],
                # flag_promotype_joint_promo_2=data[d_columns.index('flag_promotype_joint_promo_2')],
                # flag_promotype_joint_promo_3=data[d_columns.index('flag_promotype_joint_promo_3')],
                # flag_promotype_joint_promo_4=data[d_columns.index('flag_promotype_joint_promo_4')],
                # flag_promotype_joint_promo_5=data[d_columns.index('flag_promotype_joint_promo_5')],
                # flag_promotype_joint_promo_6=data[d_columns.index('flag_promotype_joint_promo_6')],
                # flag_promotype_joint_promo_7=data[d_columns.index('flag_promotype_joint_promo_7')],
                # flag_promotype_joint_promo_8=data[d_columns.index('flag_promotype_joint_promo_8')],
                # flag_promotype_joint_promo_9=data[d_columns.index('flag_promotype_joint_promo_9')],
                # flag_promotype_joint_promo_10=data[d_columns.index('flag_promotype_joint_promo_10')],
                # flag_promotype_joint_promo_11=data[d_columns.index('flag_promotype_joint_promo_11')],
                # flag_promotype_joint_promo_12=data[d_columns.index('flag_promotype_joint_promo_12')],
                # flag_promotype_joint_promo_13=data[d_columns.index('flag_promotype_joint_promo_13')],
                # flag_promotype_joint_promo_14=data[d_columns.index('flag_promotype_joint_promo_14')],
                # flag_promotype_joint_promo_15=data[d_columns.index('flag_promotype_joint_promo_15')],
                flag_promotype_all_brand=data[d_columns.index('flag_promotype_all_brand')],
                flag_promotype_multibuy_tpr=data[d_columns.index('flag_promotype_multibuy_tpr')],
                flag_promotype_lottery=data[d_columns.index('flag_promotype_lottery')],
                flag_promotype_multibuy=data[d_columns.index('flag_promotype_multibuy')],
                flag_promotype_unpublished=data[d_columns.index('flag_promotype_unpublished')],
                flag_promotype_tpr=data[d_columns.index('flag_promotype_tpr')],
                flag_promotype_miscellaneous=data[d_columns.index('flag_promotype_miscellaneous')],
                # flag_promotype_advertising_without_price=data[d_columns.index('flag_promotype_advertising_without_price')],
                is_loaded_from_opt=is_loaded_from_opt,
                is_standalone=data[d_columns.index('is_standalone')],
                total_promo_units=total_promo_total_units,
                total_non_promo_total_units=total_non_promo_total_units,
                total_promo_nsv=total_promo_nsv,
                total_non_promo_nsv=total_non_promo_nsv 
            )
            update_total(total_units, _ob)
            ob_dict = _ob.__dict__
            ob_dict['holiday'] = get_holiday_information(data)
            weekly_units.append(ob_dict)
        except Exception as _e:
            raise _e
        
    aggregate_total(total_units)   
    global_slot_dict = opt_utils.get_joint_and_all_brand_promo_for_output(
        weekly_units,
        data[d_columns.index('product_group')]
    )
    return {
        flag: {
            'total':  total_units.__dict__,
            'weekly': weekly_units,
            'slots_dict':global_slot_dict
        }
    }

def update_total(total_unit: model.TotalUnit, unit_model: model.UnitModel):
    '''Updating total'''

    total_unit.no_of_leaflet_count = total_unit.no_of_leaflet_count + float(unit_model.leaflet_flag)  
    total_unit.total_rsv_w_o_vat = total_unit.total_rsv_w_o_vat + unit_model.total_rsv_w_o_vat
    total_unit.cogs = total_unit.cogs + unit_model.total_cogs
    total_unit.units = total_unit.units + unit_model.predicted_units
    total_unit.te = total_unit.te + unit_model.trade_expense
    total_unit.lsv = total_unit.lsv + unit_model.total_lsv
    total_unit.incremental_rsv = total_unit.incremental_rsv + unit_model.incremental_rsv
    total_unit.incremental_lsv = total_unit.incremental_lsv + unit_model.incremental_lsv
    total_unit.incremental_nsv = total_unit.incremental_nsv + unit_model.incremental_nsv
    total_unit.incremental_rp = total_unit.incremental_rp + unit_model.incremental_rp
    total_unit.nsv = total_unit.nsv + unit_model.total_nsv
    total_unit.mac = total_unit.mac + unit_model.mars_mac
    total_unit.asp = total_unit.asp + unit_model.asp
    total_unit.incremental_gmac = total_unit.incremental_gmac + unit_model.incremental_gmac
    total_unit.incremental_mac = total_unit.incremental_mac + unit_model.incremental_mac
    total_unit.incremental_te = total_unit.incremental_te + unit_model.incremental_te
    total_unit.avg_promo_selling_price = average(total_unit.avg_promo_selling_price,
                                                          unit_model.promo_asp)
    total_unit.base_units = total_unit.base_units + unit_model.base_unit
    total_unit.increment_units = total_unit.increment_units + unit_model.incremental_unit
    total_unit.total_sold_volume = total_unit.total_sold_volume + unit_model.total_sold_volume
    total_unit.total_sold_unit = total_unit.total_sold_unit + unit_model.total_sold_unit
    total_unit.vol_on_deal = total_unit.vol_on_deal + unit_model.vol_on_deal
    total_unit.gsv_per_unit_future = total_unit.gsv_per_unit_future + unit_model.gsv_per_unit_future

    if unit_model.leaflet_flag:
        total_unit.promo_mac = total_unit.promo_mac + unit_model.mars_mac
        total_unit.promo_te = total_unit.promo_te + unit_model.trade_expense
        total_unit.promo_lsv = total_unit.promo_lsv + unit_model.total_lsv
        total_unit.promo_incremental_mac = total_unit.promo_incremental_mac + unit_model.incremental_mac
        total_unit.promo_gsv_per_unit_future = total_unit.promo_gsv_per_unit_future + unit_model.gsv_per_unit_future
        total_unit.promo_incremental_gmac = total_unit.promo_incremental_gmac + unit_model.incremental_gmac
        total_unit.promo_incremental_te = total_unit.promo_incremental_te + unit_model.incremental_te
        total_unit.promo_base_units = total_unit.promo_base_units + unit_model.base_unit
        total_unit.promo_increment_units = total_unit.promo_increment_units + unit_model.incremental_unit
        total_unit.promo_incremental_rsv = total_unit.promo_incremental_rsv + unit_model.incremental_rsv
        total_unit.promo_incremental_lsv = total_unit.promo_incremental_lsv + unit_model.incremental_lsv
        total_unit.promo_incremental_nsv = total_unit.promo_incremental_nsv + unit_model.incremental_nsv
        total_unit.promo_incremental_rp = total_unit.promo_incremental_rp + unit_model.incremental_rp
    total_unit.rp = total_unit.rp + unit_model.retailer_margin
    
    # if unit_model.promotion_levels:
    #     total_unit.national_mac = total_unit.national_mac + unit_model.mars_mac

    if not unit_model.leaflet_flag:
        total_unit.non_promo_mac = total_unit.non_promo_mac + unit_model.mars_mac

    
def aggregate_total(total_unit: model.TotalUnit):
    '''Method for aggregate total'''
    total_unit.rp_percent = _divide(total_unit.rp, total_unit.total_rsv_w_o_vat) * 100
    total_unit.te_percent_of_lsv = _divide(total_unit.promo_te, total_unit.promo_lsv) * 100
    total_unit.mac_percent = _divide(total_unit.mac, total_unit.nsv) * 100
    total_unit.te_per_unit = _divide(total_unit.te, total_unit.units)
    total_unit.lift = _divide(total_unit.promo_increment_units, total_unit.promo_base_units)
    total_unit.lift_percent = total_unit.lift * 100
    total_unit.asp = _divide(total_unit.asp, 52)
    total_unit.vol_on_deal = _divide((total_unit.promo_base_units+total_unit.promo_increment_units)\
                                     ,(total_unit.base_units+total_unit.increment_units))
    total_unit.vol_on_deal_percent = total_unit.vol_on_deal*100
    total_unit.roi = _divide(total_unit.promo_incremental_mac,total_unit.promo_incremental_te)+1
    total_unit.rp = total_unit.rp
