from enum import Enum

class ScenarioPlannerPayload(Enum):
    promo_data:list=[]
    
    @classmethod
    def get_method(self):
        return 'POST'

class ScenarioPlannerSave(Enum):
    scenario_type =  "promo"
    promo_type="single"
    status_type="completed"
    name = "optimizer save"
    comments = ""
    input_constraints={}
    data=''

    @classmethod
    def get_method(self):
        return 'POST'
    
    @classmethod
    def optional_param(self):
      return ['id','comments','data','input_constraints']

class ScenarioPlannerInputConstraints(Enum):
    meta_ids:list=[]