from enum import Enum







PRICING_CHANGED_SCENARIO_COLUMNS=[

    'pricing_saved_scenario_id',

    'level_param',

    "ogsm_param",

    'changed_non_promo_price',

    'changed_lp_percent',

    'changed_pack_weight_kg_percent',

    'changed_cogs_t_percent',

    'status_flag',

    'type_of_price_inc',

    'competitor_follows',

    'list_price_change_percent',

    'list_price_change_percent_kg',

    'nsv_price_impact_direct',

    'nsv_price_impact_indirect',

    'nsv_price_impact_base',

    'new_lp_after_base_price',

    'type_of_base_inc_after_change',

    'list_price_new',

    'pack_weight_kg_new',

    'dead_net_new',

    'net_net_new',

    'non_promo_price_new',

    'promo_price_new',

    'changed_net_net_change_percent',

    'changed_dead_net_change_percent',

    'cogs_t_new'  ,

    # 'net_net_multiplication_factor',

    # 'lp_multiplication_factor'

]