
from enum import Enum

PRICING_HEADER=[

    'Retailer',
    'Segment',
    'PPG',
    'OGSM Type',
    'Sell In Volume [t]',
    'BWW / GSV [€]',
    'NSV [€]',
    'NSV/t [€]',
    'TT%',
    'COGS / t [€]', 	 
    'GMAC abs [€]',
    'GMAC %',
    'LP [€]',
    'Pack Weight [kg]',
    'Tax',
    'Net Net [€]',	
    'Promo NN (=Dead Net) [€]',
    'Non Promo Price per Unit [€]',
    'Promo Price per Unit [€]',
    'Non Promo Price per kg [€]',
    'Promo Price Elasticity',
    'Promo Price per kg [€]'
    '% Promo Share',
    'TPR %',
    'Sell Out Volume Sales [t]',
    'Unit Sales (000)',
    'Value Sales = RSV [€]',
    'Non Promo Price Elasticity',
    'Competitor Coefficient'
]


PRICING_CHANGED_SCENARIO_COLUMNS=[
    'pricing_saved_scenario_id',
    'changed_nn_change_percent',
    'status_flag',
    'customer',
    'nsv_sum',
    'nn_change_percent',
    'nn_change_percent_new',
]



publish_PRICING_CHANGED_SCENARIO_COLUMNS=[
    'pricing_saved_scenario_id',
    'changed_nn_change_percent',
    'status_flag',
    'customer',
    'nsv_sum',
    'nn_change_percent',
    'nn_change_percent_new',
    'publish_flag'
]


class PublishedPricingScenario(Enum):
    MODEL_NAME = 'published_pricing_scenario'
    MODEL_COLUMN = publish_PRICING_CHANGED_SCENARIO_COLUMNS
    MODEL_VALUES = publish_PRICING_CHANGED_SCENARIO_COLUMNS
    FK_MODEL = 'pricing_saved_scenario'
    FK_NAME = 'pricing_saved_scenario'
    YEAR_WISE = False
    FIN_YEAR_WISE = False
    REQUIRED_COLUMN = []
    EXTRA_COLUMNS = []

class ChangedPricingScenario(Enum):
    MODEL_NAME = 'changed_pricing_scenario'
    MODEL_COLUMN = PRICING_CHANGED_SCENARIO_COLUMNS
    MODEL_VALUES = PRICING_CHANGED_SCENARIO_COLUMNS
    FK_MODEL = 'pricing_saved_scenario'
    FK_NAME = 'pricing_saved_scenario'
    YEAR_WISE = False
    FIN_YEAR_WISE = False
    REQUIRED_COLUMN = []
    EXTRA_COLUMNS = []