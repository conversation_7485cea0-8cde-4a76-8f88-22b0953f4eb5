""" Params Pattern"""
from enum import Enum

class OptimizerSave(Enum):
    """OptimizerSave Enum
    """
    ID=1
    SCENARIO_TYPE =  "optimizer"
    STATUS_TYPE="completed"
    NAME = "optimizer save"
    COMMENTS = ""
    INPUT_CONSTRAINTS={}
    DATA = []
    
    @classmethod
    def get_method(cls):
        """ Get Request Method"""
        return 'POST'
    
    @classmethod
    def optional_param(cls):
        """ Get Optional Parameters"""
        return ['id','comments']

class OptimizerUpdate(Enum):
    """OptimizerSave Enum
    """
    ID=1
    SAVED_ID = 2
    SCENARIO_TYPE =  "optimizer"
    STATUS_TYPE="completed"
    NAME = "optimizer save"
    COMMENTS = ""
    INPUT_CONSTRAINTS={}
    DATA = []
    
    @classmethod
    def get_method(cls):
        """ Get Request Method"""
        return 'PUT'
    
    @classmethod
    def optional_param(cls):
        """ Get Optional Parameters"""
        return ['id','comments']

class OptimizerWeeklyConstraints(Enum):
    ACCOUNT_NAME = "test 1"
    PRODUCT_GROUP = ["test product_group"]
    SCENARIO_NAME = 'test name'
    SCENARIO_TYPE =  "optimizer"
    @classmethod
    def get_method(cls):
        """ Get Request Method"""
        return 'POST'

    @classmethod
    def optional_param(cls):
        """ Get Optional Parameters"""
        return ['scenario_type']

class OptimizerInputConstraints(Enum):
    ACCOUNT_NAME = "test 1"
    PRODUCT_GROUP = ["test product_group"]
    SCENARIO_NAME = 'test name'
    @classmethod
    def get_method(cls):
        """ Get Request Method"""
        return 'POST'
        
class Optimize(Enum):
    OBJECTIVE_FUN = 'MAC'
    PARAM_MAC=0
    PARAM_NSV=0
    PARAM_UNITS=0
    PARAM_RP=0
    NO_OF_LEAFLET=0
    MIN_LENGTH_GAP=0
    NO_OF_PROMO=0
    DATA=[]

    @classmethod
    def get_method(cls):
        """ Get Request Method"""
        return 'POST'
