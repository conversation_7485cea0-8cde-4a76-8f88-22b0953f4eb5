### parallel task ###
import structlog
import threading
from joblib import parallel_backend,Parallel,delayed
logger = structlog.get_logger(__name__)

def parallel_task_executioner(func):
    def wrapper(*args, **kwargs):
        with parallel_backend(backend="threading",  n_jobs=-1):
            result = None
            try:
                logger.info(message="Running Task Parallely")
                parallel = Parallel(verbose=5)
                result = parallel([delayed(func)(key=key,*args,**kwargs) for key,item in kwargs['grouped_df']])
            except Exception as e:
                print("Task Timeout : ", e)
                
                logger.info(message="Re-Running Task Parallely")
                parallel = Parallel(verbose=5)
                result = parallel([delayed(func)(key=key,*args,**kwargs) for key,item in kwargs['grouped_df']])
                
        return result
    return wrapper

def parallel_task_executioner2(func):
    def wrapper(*args, **kwargs):
        with parallel_backend(backend="threading",  n_jobs=-1):
            result = None
            try:
                logger.info(message="Running Task Parallely")
                parallel = Parallel(verbose=5)
                result = parallel([delayed(func)(_pd=item,*args,**kwargs) for item in kwargs['iterable']])
            except Exception as e:
                print("Task Timeout : ", e)
                logger.info(message="Re-Running Task Parallely")
                parallel = Parallel(verbose=5)
                result = parallel([delayed(func)(_pd=item,*args,**kwargs) for item in kwargs['iterable']])
        return result
    return wrapper

def parallel_task_executioner3(func):
    def wrapper(*args, **kwargs):
        with parallel_backend(backend="threading",  n_jobs=-1):
            result = None
            try:
                logger.info(message="Running Task Parallely")
                parallel = Parallel(verbose=5)
                result = parallel([delayed(func)(indx=item,*args,**kwargs) for item in range(kwargs['iterable'].shape[0])])
            except Exception as e:
                print("Task Timeout : ", e)
                logger.info(message="Re-Running Task Parallely")
                parallel = Parallel(verbose=5)
                result = parallel([delayed(func)(indx=item,*args,**kwargs) for item in range(kwargs['iterable'].shape[0])])
        return result
    return wrapper

def sync_to_async(func):
    def wrapper(*args, **kwargs):
        task = threading.Thread(target=func,args=args, kwargs=kwargs,daemon=True)
        task.start()
    return wrapper
