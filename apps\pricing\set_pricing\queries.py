
""" Scenario Planner Query"""
from django.conf import settings

from config.db_handler import db_table_format_in_sql_query_str


def search_pricing_query():
    """ Search Optimizer Scenario"""
    query_string = f"""
    SELECT * FROM {db_table_format_in_sql_query_str('saved_scenario')} s
    WHERE 
    s.name LIKE  %s
    AND
    s.scenario_type = 'set_pricing'
    AND 
    s.status_type = %s
    AND
    s.is_delete = 0
    ORDER BY
    s.id DESC
    """
    
    return query_string


def search_pricing_query_by_ppg():
    """ Search Optimizer Scenario"""
    query_string = f"""
    SELECT * FROM {db_table_format_in_sql_query_str('set_pricing_ppg_level_view')} s
    WHERE 
    s.product_group LIKE %s
    ORDER BY
    s.id DESC
    """
    
    return query_string

def search_pricing_global_scenario_query():
    """ Search Global Scenario Planner"""
    query_string = f"""
    SELECT * FROM {db_table_format_in_sql_query_str('saved_scenario')} s
    WHERE 
    s.name LIKE  %s
    AND
    s.scenario_type = 'set_pricing'
    AND
    s.is_delete = 0
    ORDER BY
    s.id DESC
    """
    return query_string


def search_pricing_global_scenario_query():
    """ Search Global Scenario Planner"""
    query_string = f"""
    SELECT * FROM {db_table_format_in_sql_query_str('pricing_scenario')} s
    
    ORDER BY
    s.id DESC
    """
    return query_string

def get_pricing_scenario_by_id(columns,model_name1,scenario_id):
    """ Query for getting data."""
    query_string = f"""
                        SELECT {columns} FROM {db_table_format_in_sql_query_str(model_name1,schema=settings.PRICING_DATABASE_SCHEMA)} a 
                        WHERE
                        a.pricing_saved_scenario_id = {scenario_id}
                      
                    """
    return query_string

def get_pricing_scenario_by_mode(columns,model_name1,scenario_id):
    """ Query for getting data."""
    query_string = f"""
                        SELECT {columns} FROM {db_table_format_in_sql_query_str(model_name1,schema=settings.PRICING_DATABASE_SCHEMA)} a 
                        WHERE
                        a.pricing_saved_scenario_id = {scenario_id}
                        or
                        a.status_flag = 'not modified'
                      
                    """
    return query_string

def changed_scenario_batch_insert_query(columns,n_records=1):

    sql_query = f""" INSERT INTO {db_table_format_in_sql_query_str('changed_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA)} 
                ({columns}) VALUES {', '.join(['(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s, %s, %s,%s, %s, %s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)'])} """
    return sql_query


def all_agg_optimized_output_query(scenario_id):
    query_params  = {
                "db_table":db_table_format_in_sql_query_str('set_pricing_ppg_level_view',schema=settings.PRICING_DATABASE_SCHEMA),
                "db_table2":db_table_format_in_sql_query_str('base_and_simulated_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
                "db_table3":db_table_format_in_sql_query_str('pricing_saved_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
                "saved_scenario_id":scenario_id
            }
    inner_query=(
        "select saved_scenario_id={data[saved_scenario_id]},product_group,AVG(CAST(non_promo_price_new as float)) non_promo_price_new,AVG(CAST(promo_price_new as float)) promo_price_new,AVG(CAST(list_price_new as float)) list_price_new,"\
        "AVG(CAST(changed_cogs_t_percent as float)) changed_cogs_t_percent,AVG(CAST(changed_pack_weight_kg_percent as float)) changed_pack_weight_kg_percent,"\
        "MIN(type_of_price_inc) type_of_price_inc,MIN(status_flag) status_flag,MIN(competitor_follows) competitor_follows,AVG(CAST(floor_price as float)) floor_price,"\
        "AVG(CAST(nsv_pricing_impact_direct as float)) nsv_price_impact_direct,AVG(CAST(nsv_pricing_impact_indirect as float)) nsv_price_impact_indirect,AVG(CAST(nsv_pricing_impact_base as float)) nsv_price_impact_base,"\
        "AVG(CAST(changed_promo_vol_change_percent as float)) changed_promo_vol_change_percent,AVG(CAST(pack_weight_kg_new as float)) pack_weight_kg_new,SUM(CAST(nsv_new as float)) nsv_sum from {data[db_table2]} where pricing_saved_scenario_id = {data[saved_scenario_id]} and is_base =0 group by product_group".format(data=query_params)
    )
    query_params['inner_query'] = inner_query
    
    query = "select ROW_NUMBER() OVER( ORDER BY spplv.nsv_sum desc) AS  id,"\
                "ISNULL(pss.net_net_multiplication_factor,1)*COALESCE((bsps.non_promo_price_new-non_promo_price_per_unit)/NULLIF(non_promo_price_per_unit,0),0)*100 changed_net_net_change_percent,"\
                "ISNULL(pss.net_net_multiplication_factor,1)*COALESCE((bsps.non_promo_price_new-non_promo_price_per_unit)/NULLIF(non_promo_price_per_unit,0),0)*100 changed_dead_net_change_percent,"\
                "net_net*(1+ISNULL(pss.net_net_multiplication_factor,1)*COALESCE((bsps.non_promo_price_new-non_promo_price_per_unit)/NULLIF(non_promo_price_per_unit,0),0)) net_net_new,"\
                "dead_net*(1+ISNULL(pss.net_net_multiplication_factor,1)*COALESCE((bsps.non_promo_price_new-non_promo_price_per_unit)/NULLIF(non_promo_price_per_unit,0),0))  dead_net_new,"\
                "COALESCE((bsps.non_promo_price_new-non_promo_price_per_unit)/NULLIF(non_promo_price_per_unit,0),0)*100 changed_non_promo_price,"\
                "COALESCE((bsps.promo_price_new-promo_price_per_unit)/NULLIF(promo_price_per_unit,0),0)*100 changed_promo_price,"\
                "COALESCE((bsps.list_price_new-list_price)/NULLIF(list_price,0),0)*100 changed_lp_percent,"\
                "cogs_t*(1+bsps.changed_cogs_t_percent/100) cogs_t_new,"\
                "case when bsps.competitor_follows = 'Yes' THEN non_promo_price_elasticity + competitor_coefficient else non_promo_price_elasticity end as  net_non_promo_price_elasticity,"\
                "(bsps.changed_cogs_t_percent)*100 changed_cogs_t_percent,"\
                "bsps.changed_pack_weight_kg_percent,"\
                "bsps.changed_promo_vol_change_percent,"\
                "bsps.status_flag,"\
                "ROUND(bsps.list_price_new,2) list_price_new,"\
                "bsps.pack_weight_kg_new,"\
                "bsps.changed_promo_vol_change_percent as promo_vol_change_new,"\
                "bsps.non_promo_price_new non_promo_price_new,"\
                "bsps.promo_price_new promo_price_new,"\
                "bsps.competitor_follows,"\
                "bsps.type_of_price_inc,"\
                "promo_sold_volume,promo_sold_volume_new,non_promo_sold_volume,non_promo_sold_volume_new,"\
                "net_non_promo_price_elasticity,"\
                "spplv.nsv_sum,cogs_t,list_price,pack_weight_kg,"\
                "net_net,dead_net,promo_price_per_unit,"\
                "promo_price_elasticity,"\
                "non_promo_price_elasticity,promo_vol_change,ogsm_type,"\
                "non_promo_price_per_unit,competitor_coefficient,"\
                "COALESCE((bsps.list_price_new-list_price)/NULLIF(list_price,0),0)*100 as list_price_change_percent,COALESCE((bsps.list_price_new-list_price)/NULLIF(list_price,0),0)*100 as list_price_change_percent_kg,bsps.nsv_price_impact_direct,bsps.nsv_price_impact_indirect,"\
                "bsps.nsv_price_impact_base,bsps.list_price_new new_lp_after_base_price,bsps.type_of_price_inc as type_of_base_inc_after_change,"\
                "bsps.product_group,ogsm_type,technology,brand from  {data[db_table]} as spplv inner join ({data[inner_query]}) bsps on spplv.product_group=bsps.product_group "\
                "inner join {data[db_table3]} pss on pss.id=bsps.saved_scenario_id".format(data=query_params)
    return query
     
def all_agg_changed_inputs_query(scenario_id,db_table):
    query_params  = {
            "db_table":db_table_format_in_sql_query_str(db_table,schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table2":db_table_format_in_sql_query_str('changed_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table3":db_table_format_in_sql_query_str('pricing_saved_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "saved_scenario_id":scenario_id
        }
    
    query = "select ROW_NUMBER() OVER( ORDER BY spplv.product_group ) AS  id,"\
        "case when cps.status_flag = 'modified' then cps.net_net_multiplication_factor else spplv.net_net_multiplication_factor end as net_net_multiplication_factor,"\
        "case when cps.status_flag = 'modified' then cps.lp_multiplication_factor else spplv.lp_multiplication_factor end as lp_multiplication_factor,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_cogs_t_percent,0) ELSE spplv.changed_cogs_t_percent END AS changed_cogs_t_percent,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_net_net_change_percent,0) ELSE spplv.changed_net_net_change_percent END AS changed_net_net_change_percent,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_dead_net_change_percent,0) ELSE spplv.changed_dead_net_change_percent END AS changed_dead_net_change_percent,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_lp_percent,0) ELSE spplv.changed_lp_percent END AS changed_lp_percent,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_non_promo_price,0) else spplv.changed_non_promo_price end as  changed_non_promo_price,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_promo_price,0) else spplv.changed_promo_price end as  changed_promo_price,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_pack_weight_kg_percent,0) ELSE spplv.changed_pack_weight_kg_percent END AS changed_pack_weight_kg_percent,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_shelf_price_percent,0) ELSE spplv.changed_shelf_price_percent END AS changed_shelf_price_percent,"\
        "CASE WHEN cps.status_flag = 'modified' THEN cps.status_flag else spplv.status_flag end as  status_flag,"\
        "CASE WHEN cps.status_flag = 'modified' THEN cps.competitor_follows else spplv.competitor_follows end as  competitor_follows,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.list_price_new,0) else spplv.list_price_new end as  list_price_new,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.pack_weight_kg_new,0) else spplv.pack_weight_kg_new end as  pack_weight_kg_new,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.dead_net_new,0) else spplv.dead_net_new end as  dead_net_new,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.net_net_new,0) else spplv.net_net_new end as  net_net_new,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.non_promo_price_new,0) else spplv.non_promo_price_new end as  non_promo_price_new,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.promo_price_new,0) else spplv.promo_price_new end as  promo_price_new,"\
        "CASE WHEN cps.competitor_follows = 'Yes' THEN non_promo_price_elasticity + competitor_coefficient else spplv.net_non_promo_price_elasticity end as  net_non_promo_price_elasticity,"\
        "CASE WHEN cps.status_flag = 'modified' THEN cps.type_of_price_inc else spplv.type_of_price_inc end as  type_of_price_inc,"\
        "spplv.nsv_sum,list_price,pack_weight_kg,cogs_t,spplv.ogsm_type,technology,brand,shelf_price,non_promo_price_per_unit,promo_price_per_unit,"\
        "cps.list_price_change_percent,cps.list_price_change_percent_kg,cps.nsv_price_impact_direct,cps.nsv_price_impact_indirect,competitor_coefficient,"\
        "cps.nsv_price_impact_base,cps.new_lp_after_base_price,cps.type_of_base_inc_after_change,cps.shelf_price_new,cps.promo_sold_volume_new, cps.promo_sold_volume, cps.non_promo_sold_volume, cps.non_promo_sold_volume_new,cps.cogs_t_new, cps.promo_sold_volume_percent, cps.promo_sold_volume_percent_new,"\
        "dead_net,net_net,non_promo_price_elasticity,promo_price_elasticity,spplv.product_group,spplv.min_shelf_price,spplv.floor_price "\
        "from {data[db_table]} as spplv left join (select pss.net_net_multiplication_factor,pss.lp_multiplication_factor,cps.* from {data[db_table2]} as cps inner join {data[db_table3]} as pss on cps.pricing_saved_scenario_id=pss.id "\
        "where pricing_saved_scenario_id = {data[saved_scenario_id]}) cps on cps.level_param = spplv.product_group order by spplv.nsv_sum".format(data=query_params)

    return query

def agg_changed_inputs_query(scenario_id,db_table):
    
    query_params  = {
            "db_table":db_table_format_in_sql_query_str(db_table,schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table2":db_table_format_in_sql_query_str('changed_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table3":db_table_format_in_sql_query_str('pricing_saved_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "saved_scenario_id":scenario_id
        }
    
    query = "select ROW_NUMBER() OVER( ORDER BY spplv.product_group ) AS  id,"\
        "case when cps.status_flag = 'modified' then cps.net_net_multiplication_factor else spplv.net_net_multiplication_factor end as net_net_multiplication_factor,"\
        "case when cps.status_flag = 'modified' then cps.lp_multiplication_factor else spplv.lp_multiplication_factor end as lp_multiplication_factor,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_cogs_t_percent,0) ELSE spplv.changed_cogs_t_percent END AS changed_cogs_t_percent,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_net_net_change_percent,0) ELSE spplv.changed_net_net_change_percent END AS changed_net_net_change_percent,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_dead_net_change_percent,0) ELSE spplv.changed_dead_net_change_percent END AS changed_dead_net_change_percent,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_lp_percent,0) ELSE spplv.changed_lp_percent END AS changed_lp_percent,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_pack_weight_kg_percent,0) ELSE spplv.changed_pack_weight_kg_percent END AS changed_pack_weight_kg_percent,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_shelf_price_percent,0) ELSE spplv.changed_shelf_price_percent END AS changed_shelf_price_percent,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_non_promo_price,0) else spplv.changed_non_promo_price end as  changed_non_promo_price,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_promo_price,0) else spplv.changed_promo_price end as  changed_promo_price,"\
        "CASE WHEN cps.competitor_follows = 'Yes' THEN non_promo_price_elasticity + competitor_coefficient else spplv.net_non_promo_price_elasticity end as  net_non_promo_price_elasticity,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.list_price_new,0) else spplv.list_price_new end as  list_price_new,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.pack_weight_kg_new,0) else spplv.pack_weight_kg_new end as  pack_weight_kg_new,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.dead_net_new,0) else spplv.dead_net_new end as  dead_net_new,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.net_net_new,0) else spplv.net_net_new end as  net_net_new,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.non_promo_price_new,0) else spplv.non_promo_price_new end as  non_promo_price_new,"\
        "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.promo_price_new,0) else spplv.promo_price_new end as  promo_price_new,"\
        "CASE WHEN cps.status_flag = 'modified' THEN cps.status_flag else spplv.status_flag end as  status_flag,"\
        "CASE WHEN cps.status_flag = 'modified' THEN cps.competitor_follows else spplv.competitor_follows end as  competitor_follows,"\
        "CASE WHEN cps.status_flag = 'modified' THEN cps.type_of_price_inc else spplv.type_of_price_inc end as  type_of_price_inc,"\
        "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_sold_volume_new else 0 end as  promo_sold_volume_new,"\
        "CASE WHEN cps.status_flag = 'modified' THEN cps.non_promo_sold_volume_new else 0 end as  non_promo_sold_volume_new,"\
        "cps.promo_sold_volume,cps.non_promo_sold_volume,"\
        "spplv.nsv_sum,list_price,pack_weight_kg,cogs_t,spplv.ogsm_type,technology,brand,shelf_price,non_promo_price_per_unit,promo_price_per_unit,"\
        "cps.list_price_change_percent,cps.list_price_change_percent_kg,cps.nsv_price_impact_direct,cps.nsv_price_impact_indirect,"\
        "cps.nsv_price_impact_base,cps.new_lp_after_base_price,cps.type_of_base_inc_after_change,cps.shelf_price_new,competitor_coefficient,"\
        "dead_net,net_net,promo_price_elasticity,non_promo_price_elasticity,spplv.product_group,spplv.min_shelf_price  "\
        "from {data[db_table]} as spplv inner join (select pss.net_net_multiplication_factor,pss.lp_multiplication_factor,cps.* from {data[db_table2]} as cps inner join {data[db_table3]} as pss on cps.pricing_saved_scenario_id=pss.id "\
        "where pricing_saved_scenario_id = {data[saved_scenario_id]}) cps on cps.level_param = spplv.product_group order by spplv.nsv_sum".format(data=query_params)
    
    return query

def search_all_agg_changed_inputs_query(scenario_id,ppg):
    query_params  = {
            "db_table":db_table_format_in_sql_query_str('pricing_baseline_changed_scenario_view',schema=settings.PRICING_DATABASE_SCHEMA),
            "saved_scenario_id":scenario_id,
            'product_group':ppg
        }
    query = "select ROW_NUMBER() OVER( ORDER BY product_group ) AS  id,"\
            "avg(changed_lp_percent) as changed_lp_percent,avg(changed_pack_weight_kg_percent) as changed_pack_weight_kg_percent,avg(changed_cogs_t_percent) as changed_cogs_t_percent,"\
            "product_group,ogsm_param as ogsm_type,technology,brand,competitor_follows,type_of_price_inc,status_flag "\
            "from {data[db_table]} as spbcs "\
            "where pricing_saved_scenario_id = {data[saved_scenario_id]} or status_flag = 'not modified' and product_group LIKE {data[ppg]} "\
            "group by product_group,ogsm_param,technology,brand,competitor_follows,type_of_price_inc,status_flag".format(data=query_params)
    return query

def search_agg_changed_inputs_query(scenario_id,ppg):
    
    query_params  = {
            "db_table":db_table_format_in_sql_query_str('pricing_baseline_changed_scenario_view',schema=settings.PRICING_DATABASE_SCHEMA),
            "saved_scenario_id":scenario_id,
            'product_group':ppg
        }
    query = "select ROW_NUMBER() OVER( ORDER BY product_group ) AS  id,"\
            "avg(changed_lp_percent) as changed_lp_percent,avg(changed_pack_weight_kg_percent) as changed_pack_weight_kg_percent,avg(changed_cogs_t_percent) as changed_cogs_t_percent,"\
            "product_group,ogsm_param as ogsm_type,technology,brand,competitor_follows,type_of_price_inc,status_flag "\
            "from {data[db_table]} as spbcs "\
            "where pricing_saved_scenario_id = {data[saved_scenario_id]} and product_group LIKE {data[ppg]}"\
            "group by product_group,ogsm_param,technology,brand,competitor_follows,type_of_price_inc,status_flag".format(data=query_params)
    return query
