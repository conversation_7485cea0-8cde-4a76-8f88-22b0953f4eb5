import math
import ast
import json
import re
from statistics import mean
import pandas as pd
import logging
from django.core.paginator import Paginator
from numerize import numerize

logger = logging.getLogger(__name__)

def convert_str_to_json(val):
    try:
        val = ast.literal_eval(val)
    except Exception as e:
        val = json.loads(val)
    return val

def round_half_up(n, decimals=0):
    multiplier = 10 ** decimals
    return int(math.floor(n*multiplier + 0.5) / multiplier)

def format_promotions(mechanic, promo_depth, co_inv,hide_percent=False):
    promo_string =''

    if promo_depth:
        if hide_percent:
            promo_string+=mechanic
        else:
            promo_string+=mechanic + "-" + str(round(promo_depth,2)) + "%"

    if co_inv:
        promo_string+= " (Co-"+str(round(co_inv,2))+"%)"

    if promo_string:
        return promo_string

    return '-'

def _limit(val , min , max):
    if val < min or val > max:
        return False
    return True

def is_zero_to_hundred(val):
    return _limit(val , 0 ,100)

def generate_slug_string(s1,s2,s3):
    return "{}-{}-{}".format(remove_duplicate_spaces(s1)\
                    ,remove_duplicate_spaces(s2)\
                    ,remove_duplicate_spaces(s3))

def remove_duplicate_spaces(s):
    # import datetime
    if not isinstance(s, int):
        return "".join(s.split()).lower()
    else:
        return str(s)
        
def remove_special_charater_from_string(string:str):
    return re.sub('[^a-zA-Z0-9 \n\.]', '_', string)

def _divide(n1 , n2):
    if not n1 or not n2:
        return 0
    return n1/n2

def _divide_df(n1 , n2):
    if not n1.shape[0] or not n2.shape[0]:
        return 0
    return n1/n2

def percentile(n1 , n2):
    if not n1 or not n2:
        return 0
    return (n1/n2)*100

def _multiply(n1,n2):
    return n1*n2

def average(n1,n2):
    if not n1:
        return n2
    return (n1 + n2)/2

def _regex(pattern,string):
    return re.compile(pattern).search(string)

def convert_string_of_list_to_list(str):
    return str.strip('][').split(',')

def convert_list_to_string_list(_list:list):
    return str(_list).strip('[]')


def iterateModel(iterable:list):
    _iter = iter(iterable)

    try:
        while True:
            d = next(_iter)
            yield d.value.strip('[]')
    except StopIteration:
        pass

def convert_to_specific_dtype(df:pd.DataFrame,_iterable:list,dtype=int):
    try:
        _iter = iter(_iterable)
        while True:
            d = next(_iter)
            df[d] = df[d].astype(dtype)
            yield df
    except StopIteration:
        pass

def convert_to_int(val):
    if val:
        val = int(val)
    return val

def iterateEnum(iterable:list):
    for i in iterable:
        print(i)
    return list(i.name.lower() for i in iterable)

def convert_to_df(data_list:list=[],columns=[]):
    for index,data in enumerate(data_list):
        df = pd.DataFrame(data, columns=columns[index])
        yield df

def swapPositions(list, pos1, pos2):
     
    list[pos1], list[pos2] = list[pos2], list[pos1]
    return list


class CustomPaginator():

    def __init__(self,page_number):
        self.page_numer = page_number
        self.page_size = 3
        
    def get_paginated_data(self,data):
        self.page_size = len(data)
        _paginated_data =  Paginator(data,self.page_size)

        _pagewize_data = _paginated_data.page(self.page_numer)

        return _pagewize_data


def _average(val):
    filtered_val = list(filter(lambda x:x,val))
    if len(filtered_val)>1:
        return mean(filtered_val)
    if filtered_val:
        return filtered_val[0]
    return 0

def numerize_numbers(numbers_list,decimals=0):
    formated_number = []
    for num in numbers_list:
        formated_number.append(numerize.numerize(num,decimals))
    return formated_number

def currency_format(numbers_list,currency,decimals=0):
    formated_number = []
    for num in numbers_list:
        formated_number.append(f'{numerize.numerize(num,decimals)}{currency}')
    return formated_number

def convert_to_perc(fraction_list):
    formated_frac = []
    for fc in fraction_list:
        per = "{:.2%}".format(fc)
        
        formated_frac.append(per)
    return formated_frac

def format_decimals(decimals):
    fromated_dec = []
    for dc in decimals:
        fromated_dec.append(str(round(dc,2)))
    return fromated_dec


def day_month(date):
    return ' '.join(date.strftime('%d-%b-%Y').split('-')[::-1][1:])


def remove_duplicates(_list:list):
    unique_val = list(set(_list))
    return unique_val

def handle_divison_by_zero(col1,col2,col3,df):
    mask = (df[col1] !=0) | (df[col2] != 0)
    df.loc[mask, col3] = df.loc[mask, col1] / df.loc[mask, col2]
    return df

def convert_list_to_tuple_str(iterable):
    return f'''({','.join([f"'{x}'" for x in iterable])})''' if iterable else "('0')"

def convert_to_str(val):
    return  f'''('{val}')'''
