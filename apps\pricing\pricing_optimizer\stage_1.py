from django.conf import settings
import numpy as np
import pandas as pd
import scipy
from scipy import optimize
import functools
pd.set_option('mode.chained_assignment',None)
import pulp as lp
from datetime import datetime

# COMMAND ----------

# MAGIC %md # CONFIG 

# COMMAND ----------

# objective,min_change_overall_netnet,max_change_overall_netnet,cogs_unit_new

# COMMAND ----------

def get_config():
    config = {
    # 'objective':'NSV', # NSV or MAC or RSV
    'method':'SLSQP', # SLSQP or COBYLA
    'eq_constraint' : False,
    'min_max_constraint' : True,
    'hier_constraint' : True,
    'netnet_constraint' : True,
    # 'nsv_constraint' : True,
    'mac_constraint' : True,
    'tm_constraint' : True,
    'price_per_item_constraint':True,
    'exclude_few_retailers' : True,
    'save_intermediate_files':True,
    'round_fn' : 'price_round_up' #price_round_off or price_round_up , price_round_off is new
    }
    return config



# COMMAND ----------

# It rounds up to either 5 or 9 in cents [1 euro = 100 cents]
def price_round_up(x):
  """
  x is price in Euro
  It rounds up to either 5 or 9 in cents [1 euro = 100 cents]
  """
  price_in_cents = x * 100
  rounded_price_in_cents = np.ceil(price_in_cents/5)*5
  rounded_price_in_cents_with_proper_ending = np.where(np.mod(rounded_price_in_cents,10)==0,rounded_price_in_cents-1,rounded_price_in_cents)
  rounded_price_in_euro = rounded_price_in_cents_with_proper_ending/100
  rounded_price_in_euro = np.where(x<=rounded_price_in_euro,rounded_price_in_euro,rounded_price_in_euro+0.06)
  return rounded_price_in_euro

# COMMAND ----------

# It rounds off to either 5 or 9 in cents [1 euro = 100 cents]
def price_round_off(x):
  """
  x is price in Euro
  It rounds off to either 5 or 9 in cents [1 euro = 100 cents]
  """
  price_in_cents = x * 100

  # For multiple of 5
  rounded_price_in_cents = np.round(price_in_cents/5,0)*5
  # rounded_price_in_cents = ((price_in_cents*10)//2)*5
  # For making to end with 9 instead of 0
  rounded_price_in_cents_with_proper_ending = np.where(np.mod(rounded_price_in_cents,10)==0,rounded_price_in_cents-1,rounded_price_in_cents)
  rounded_price_in_euro = rounded_price_in_cents_with_proper_ending/100

  return rounded_price_in_euro

# COMMAND ----------

# Helper Function to append multiple matrices
def numpy_append(list_of_arrays,axis=None):
     return functools.reduce(lambda arr, values: np.append(arr,values,axis=axis), list_of_arrays)

# COMMAND ----------

def get_tpr_elasticity(coef):
    tpr_elasticity = coef[coef['Parameters']=='tpr_discount_byppg'][['customer_PPG','Value']]
    tpr_elasticity['customer_PPG'] = tpr_elasticity['customer_PPG'].apply(lambda x: "_".join(x.split('|'))).str.lower()
    tpr_elasticity.rename(columns = {'customer_PPG':'Customer_PPG'}, inplace = True)
    return tpr_elasticity

# COMMAND ----------

def get_avg_and_max_shelf_price(data):
    avg_shelf_price_df = (data
                      .groupby(['PPG'])
                      .agg(shelf_price=('avg_shelf_price', 'mean'),
                           max_shelf_price=('max_shelf_price', 'max'),
                      )
                      .reset_index()
                     )
    
    return avg_shelf_price_df.sort_values(by='PPG')

# COMMAND ----------

def n_customers_each_ppg_func(data):

    n_cust = (data[['PPG','Customer']]
        .drop_duplicates()
        .groupby(['PPG']).count()
        .reset_index()
        .rename(columns={'Customer':'n_customer'})
        ).sort_values(by = ['PPG'])
    return n_cust['n_customer'].to_numpy()

# COMMAND ----------

def get_elasticities(data):
    data["competitor_follows"] = np.where(data["competitor_follows"]=="No",0,1)
    elasticities = data["Non Promo Price Elasticity"] + (data["competitor_follows"] * data["Competitor Coefficient"])
    return np.diag(elasticities)

# COMMAND ----------

# tpr% change
def tpr_per_change(shelf_promo_price,
        current_shelf_price,
        shelf_promo_price_ppg,
        shelf_non_promo_price_ppg,
        tpr_coef):

    tpr_base = (1 - shelf_promo_price / current_shelf_price)*100
    tpr_new = (1 - shelf_promo_price_ppg / shelf_non_promo_price_ppg)*100
    return np.exp(tpr_coef * (tpr_new - tpr_base))

# COMMAND ----------

def tpr_per_change_rounded(x, x_promo,shelf_promo_price,
        current_shelf_price,tpr_coef):
    tpr_base = (1 - shelf_promo_price / current_shelf_price)*100
    tpr_new = (1-x_promo/x)*100
    return np.exp(tpr_coef * (tpr_new - tpr_base))

# COMMAND ----------

## Getting constants for units
def get_price_cotrib(elasticities,shelf_price):
    # print(shelf_price)
    price_contrib = np.exp(<EMAIL>(shelf_price))
    return price_contrib

def get_constants_units(ps,
                        units, #shape(n_ppg_customer,) Unit or NSV
                elasticities, #shape(n_ppg_customer,n_ppg_customer)
                shelf_promo_price,
                current_shelf_price,
                shelf_promo_price_ppg,
                shelf_non_promo_price_ppg,
                tpr_coef, rounded_flag = False,non_promo_new = None, promo_new = None
                ):
    price_contrib = get_price_cotrib(elasticities,current_shelf_price)

    # tpr% change

    if rounded_flag:
        tpr_change = tpr_per_change_rounded(non_promo_new, promo_new,shelf_promo_price,
            current_shelf_price,tpr_coef)
    else:
        tpr_change = tpr_per_change(shelf_promo_price,
                current_shelf_price,
                shelf_promo_price_ppg,
                shelf_non_promo_price_ppg,
                tpr_coef)
        
    constants1 =  units/price_contrib
    # weighted avg term
    constants2 = (1-ps) + (ps * tpr_change)


    constants = constants1 * constants2
    return constants


# COMMAND ----------

def get_constants_nsv(ps,
                      units, #shape(n_ppg_customer,) Unit or NSV
                elasticities, #shape(n_ppg_customer,n_ppg_customer)
                shelf_promo_price,
                current_shelf_price,
                shelf_promo_price_ppg,
                shelf_non_promo_price_ppg,
                tpr_coef,
                netnet,
                deadnet,rounded_flag = False,non_promo_new = None, promo_new = None
                  ):
    price_contrib = get_price_cotrib(elasticities,current_shelf_price)

    # tpr% change

    if rounded_flag:
        tpr_change = tpr_per_change_rounded(non_promo_new, promo_new,shelf_promo_price,
            current_shelf_price,tpr_coef)
    else:
        tpr_change = tpr_per_change(shelf_promo_price,
                current_shelf_price,
                shelf_promo_price_ppg,
                shelf_non_promo_price_ppg,
                tpr_coef)
        
    constants1 =  units/price_contrib
    # weighted avg term
    constants2 = netnet*(1-ps) + (deadnet * ps * tpr_change)
    
    constants = constants1 * constants2

    return constants

# COMMAND ----------

def get_constants_rsv(ps,
                      units, #shape(n_ppg_customer,) Unit or NSV
                elasticities, #shape(n_ppg_customer,n_ppg_customer)
                shelf_promo_price,
                current_shelf_price,
                shelf_promo_price_ppg,
                shelf_non_promo_price_ppg,
                tpr_coef, rounded_flag = False,non_promo_new = None, promo_new = None
                  ):
    price_contrib = get_price_cotrib(elasticities,current_shelf_price)

    # tpr% change
    if rounded_flag:
        tpr_change = tpr_per_change_rounded(non_promo_new, promo_new,shelf_promo_price,
            current_shelf_price,tpr_coef)
    else:
        tpr_change = tpr_per_change(shelf_promo_price,
                current_shelf_price,
                shelf_promo_price_ppg,
                shelf_non_promo_price_ppg,
                tpr_coef)

    constants1 =  units/price_contrib
    # weighted avg term
    constants2 = (1-ps) + (ps * tpr_change* shelf_promo_price/current_shelf_price)

    constants = constants1 * constants2

    return constants

# COMMAND ----------

## Unit
def unit_ppg_customer(x, #shape(n_ppg_customer,)
                        elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                        constants, #shape(n_ppg_customer,)
                        rounded_flag = False
                        ):

    shelf_price = x[:]

    # Base Price Contribution
    price_contrib = np.exp(<EMAIL>(shelf_price))
    sales_vector = constants*price_contrib

    return sales_vector, None

def unit_overall(x, #shape(n_ppg_customer,)
                        elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                        constants, #shape(n_ppg_customer,)
                         ):
    sales_vector_total, sales_deriv = unit_ppg_customer(x, #shape(n_ppg_customer,)
                        elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                        constants, #shape(n_ppg_customer,)
                        )
    
    return sales_vector_total.sum(), None
    
def unit_objective(x, #shape(n_ppg_customer,)
                        elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                        constants, #shape(n_ppg_customer,)
                        n_customers_each_ppg, eq_constraint,
                units, equal_price_ppg = None, avg_shelf_price_df = None):
    if eq_constraint:
        x = equal_ppgs_ext(x, equal_price_ppg, avg_shelf_price_df)

    shelf_price = np.repeat(x, n_customers_each_ppg)

    sales, sales_deriv = unit_overall(shelf_price, #shape(n_ppg_customer,)
                        elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                        constants, #shape(n_ppg_customer,)
                        )

    return -sales*10/units.sum()

# COMMAND ----------

# MAGIC %md
# MAGIC ### Volume:

# COMMAND ----------

def volume_overall(x, #shape(n_ppg_customer,)
                        elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                        constants, #shape(n_ppg_customer,)
                        pack_weight_in_kg,
                         ):
    units_vector_total, sales_deriv = unit_ppg_customer(x, #shape(n_ppg_customer,)
                        elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                        constants, #shape(n_ppg_customer,)
                        )
    
    vol_ppg_cust = units_vector_total * pack_weight_in_kg

    return vol_ppg_cust.sum(), None
    
def volume_objective(x, #shape(n_ppg_customer,)
                        elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                        constants, #shape(n_ppg_customer,)
                        n_customers_each_ppg, eq_constraint,
                base_vol, pack_weight_in_kg, equal_price_ppg = None, avg_shelf_price_df = None):
    if eq_constraint:
        x = equal_ppgs_ext(x, equal_price_ppg, avg_shelf_price_df)

    shelf_price = np.repeat(x, n_customers_each_ppg)

    sales, sales_deriv = volume_overall(shelf_price, #shape(n_ppg_customer,)
                        elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                        constants, #shape(n_ppg_customer,)
                        pack_weight_in_kg,
                        )

    return -sales*10/base_vol.sum()

# COMMAND ----------

# MAGIC %md
# MAGIC ## RSV Calc

# COMMAND ----------

## RSV
def rsv_ppg_customer(x, #shape(n_ppg_customer,)
                        ps,
                        shelf_promo_price,
                        shelf_promo_price_ppg,
                        shelf_non_promo_price_ppg,
                        tpr_coef,
                        current_shelf_price,
                        elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                        constants, #shape(n_ppg_customer,)
                        rounded_flag = False, promo_new = None
                        ):

    shelf_price = x[:]

    if rounded_flag:
        tpr_change = tpr_per_change_rounded(x, promo_new,shelf_promo_price,
            current_shelf_price,tpr_coef)
    else:
        tpr_change = tpr_per_change(shelf_promo_price,
                current_shelf_price,
                shelf_promo_price_ppg,
                shelf_non_promo_price_ppg,
                tpr_coef)
    
    # Base Price Contribution
    price_contrib = np.exp(<EMAIL>(shelf_price))
    

    sales_vector = constants*price_contrib*shelf_price

    return sales_vector, None


def rsv_overall(x, #shape(n_ppg_customer,)
                ps,
                shelf_promo_price,
                shelf_promo_price_ppg,
                shelf_non_promo_price_ppg,
                tpr_coef,
                current_shelf_price,
                elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                constants, #shape(n_ppg_customer,)
                ):
    
    sales_vector_total,_ = rsv_ppg_customer(x, #shape(n_ppg_customer,)
                        ps,
                        shelf_promo_price,
                        shelf_promo_price_ppg,
                        shelf_non_promo_price_ppg,
                        tpr_coef,
                        current_shelf_price,
                        elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                        constants, #shape(n_ppg_customer,)
                        )
  
    return sales_vector_total.sum(),None

def rsv_objective(x, #shape(n_ppg_customer,)
                ps,
                shelf_promo_price,
                shelf_promo_price_ppg,
                shelf_non_promo_price_ppg,
                tpr_coef,
                current_shelf_price,
                elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                constants, n_customers_each_ppg, eq_constraint,
                rsv, equal_price_ppg = None, avg_shelf_price_df = None):
    if eq_constraint:
        x = equal_ppgs_ext(x, equal_price_ppg, avg_shelf_price_df)

    shelf_price = np.repeat(x, n_customers_each_ppg)

    sales, sales_deriv = rsv_overall(shelf_price, #shape(n_ppg_customer,)
                ps,
                shelf_promo_price,
                shelf_promo_price_ppg,
                shelf_non_promo_price_ppg,
                tpr_coef,
                current_shelf_price,
                elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                constants, #shape(n_ppg_customer,)
                )

    return -sales*10/rsv.sum()

# COMMAND ----------

# MAGIC %md
# MAGIC ## NSV Calc

# COMMAND ----------

def equal_price_ppgs_fn(data, equal_ppgs):
    data['Value Sales = RSV'] = data['Value Sales = RSV'].astype('float')
    ppg_rsv = data.groupby('PPG')['Value Sales = RSV'].sum().reset_index()

    equal_ppgs = equal_ppgs.merge(ppg_rsv, on = 'PPG')

    # Find the index of maximum 'rsv' within each group
    max_rsv_indices = equal_ppgs.groupby('group')['Value Sales = RSV'].idxmax()

    # Get the corresponding 'ppg' values using the indices
    result = equal_ppgs.loc[max_rsv_indices, ['group', 'PPG']]

    # Calculate left over ppg in each group
    equal_ppg = (
    equal_ppgs.groupby('group').agg(equal_ppg = ('PPG','unique')).reset_index()
    )

    result = equal_ppgs.loc[max_rsv_indices, ['group', 'PPG']]

    result = result.merge(equal_ppg, on = 'group')
    equal_price_ppgs = result.copy()

    equal_price_ppgs = equal_price_ppgs.explode('equal_ppg').reset_index(drop=True)

    all_ppgs = pd.DataFrame()
    all_ppgs['PPG'] = data['PPG'].drop_duplicates().values
    equal_price_ppgs.rename(columns = {'PPG':'high_sales_ppg'}, inplace = True)

    equal_price_ppgs = all_ppgs.merge(equal_price_ppgs[['high_sales_ppg','equal_ppg','group']], left_on = 'PPG', right_on = 'equal_ppg', how = 'left')
    equal_price_ppgs['equal_ppg'] = equal_price_ppgs['equal_ppg'].fillna(all_ppgs['PPG'])
    equal_price_ppgs['high_sales_ppg'] = equal_price_ppgs['high_sales_ppg'].fillna(all_ppgs['PPG'])
    equal_price_ppgs['group'] = equal_price_ppgs['group'].fillna('not_equal')

    return equal_price_ppgs

# COMMAND ----------


def equal_ppgs_ext(opt_x, equal_price_ppg, avg_shelf_price_df):
    avg_shelf_price_df.sort_values(by='PPG',inplace = True)
    avg_shelf_price_df['opt_x']=opt_x
    equal_price_ppg = equal_price_ppg.merge(avg_shelf_price_df[['PPG','opt_x']], left_on = 'high_sales_ppg', right_on = 'PPG')
    equal_price_ppg.sort_values(by = 'equal_ppg', inplace = True)
    return equal_price_ppg['opt_x'].values

# COMMAND ----------

def nsv_ppg_customer(x, elasticities, constants, current_shelf_price, adjust_percentage,rounded_flag=False):
    shelf_price = x[:]

    # Price Contribution
    price_contrib = np.exp(elasticities @ np.log(shelf_price))

    ratio_term = (1 - adjust_percentage) + adjust_percentage * shelf_price / current_shelf_price

    sales_vector = constants * price_contrib * ratio_term

    return sales_vector, None

def nsv_overall(x, elasticities, constants, current_shelf_price, adjust_percentage, nsv):

    sales_vector_total, sales_deriv = nsv_ppg_customer(x, elasticities, constants, current_shelf_price, adjust_percentage)

    return sales_vector_total.sum(), None

def nsv_objective(x, elasticities, constants, current_shelf_price, adjust_percentage,
                   n_customers_each_ppg, eq_constraint,
                   nsv, equal_price_ppg = None, avg_shelf_price_df = None):
    if eq_constraint:
        x = equal_ppgs_ext(x, equal_price_ppg, avg_shelf_price_df)

    shelf_price = np.repeat(x, n_customers_each_ppg)

    sales, sales_deriv = nsv_overall(shelf_price, elasticities, constants, current_shelf_price, adjust_percentage, nsv)

    return -sales*10/nsv.sum()


# COMMAND ----------

# MAGIC %md
# MAGIC ## MAC Calc

# COMMAND ----------

## MAC
def mac_ppg_customer(x, #shape(n_ppg_customer,)
                        elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                        constants_for_nsv,
                        constants_for_sell_in_unit, #shape(n_ppg_customer,)
                        current_shelf_price, #shape(n_ppg_customer,)
                        adjust_percentage, #either a number or shape(n_ppg_customer,)
                        cogs_per_unit, #shape(n_ppg,)
                        rounded_flag = False
                        ):
    nsv,_ = nsv_ppg_customer(x, #shape(n_ppg_customer,)
                        elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                        constants_for_nsv, #shape(n_ppg_customer,)
                        current_shelf_price,
                        adjust_percentage,
                        rounded_flag )
    unit,_ = unit_ppg_customer(x, #shape(n_ppg_customer,)
                        elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                        constants_for_sell_in_unit, #shape(n_ppg_customer,)
                        rounded_flag
                        )
    mac = nsv - cogs_per_unit * unit

    return mac,None


def mac_overall(x, #shape(n_ppg_customer,)
                        elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                        constants_for_nsv,
                        constants_for_sell_in_unit, #shape(n_ppg_customer,)
                        current_shelf_price, #shape(n_ppg_customer,)
                        adjust_percentage, #either a number or shape(n_ppg_customer,)
                        cogs_per_unit, #shape(n_ppg,)
                        mac,
                        ):
    mac_vector_total,_ = mac_ppg_customer(x, #shape(n_ppg_customer,)
                        elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                        constants_for_nsv,
                        constants_for_sell_in_unit, #shape(n_ppg_customer,)
                        current_shelf_price, #shape(n_ppg_customer,)
                        adjust_percentage, #either a number or shape(n_ppg_customer,)
                        cogs_per_unit, #shape(n_ppg,)
                        )

    return mac_vector_total.sum(),None


def mac_objective(x, #shape(n_ppg_customer,)
                        elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                        constants_for_nsv,
                        constants_for_sell_in_unit, #shape(n_ppg_customer,)
                        current_shelf_price, #shape(n_ppg_customer,)
                        adjust_percentage, #either a number or shape(n_ppg_customer,)
                        cogs_per_unit, #shape(n_ppg,)
                        n_customers_each_ppg,
                        eq_constraint,mac,
                        equal_price_ppg = None, avg_shelf_price_df = None
                        ):
    if eq_constraint:
        x = equal_ppgs_ext(x, equal_price_ppg, avg_shelf_price_df)

    shelf_price = np.repeat(x,n_customers_each_ppg)
    sales,_ = mac_overall(shelf_price, #shape(n_ppg_customer,)
                        elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                        constants_for_nsv,
                        constants_for_sell_in_unit, #shape(n_ppg_customer,)
                        current_shelf_price, #shape(n_ppg_customer,)
                        adjust_percentage, #either a number or shape(n_ppg_customer,)
                        cogs_per_unit, #shape(n_ppg,)
                        mac
                        )

    return -sales*10/mac.sum()

# COMMAND ----------

def trade_margin_objective(x,
                vat_tax,
                trade_margin_base,
                adjust_percentage,
                ps,
                shelf_promo_price,
                shelf_promo_price_ppg,
                shelf_non_promo_price_ppg,
                tpr_coef,
                current_shelf_price,
                elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                constants_for_rsv,
                constants_for_nsv,
                n_customers_each_ppg,
                eq_constraint,
                equal_price_ppg = None, avg_shelf_price_df = None):
    if eq_constraint:
        x = equal_ppgs_ext(x, equal_price_ppg, avg_shelf_price_df)
    shelf_price = np.repeat(x,n_customers_each_ppg)
    # rsv/(1+vat) - nsv
    rsv_new, rsv_deriv = rsv_ppg_customer(shelf_price, #shape(n_ppg_customer,)
                        ps,
                        shelf_promo_price,
                        shelf_promo_price_ppg,
                        shelf_non_promo_price_ppg,
                        tpr_coef,
                        current_shelf_price,
                        elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                        constants_for_rsv, #shape(n_ppg_customer,)
                        )
    nsv_new, nsv_deriv = nsv_ppg_customer(shelf_price, elasticities, constants_for_nsv, current_shelf_price, adjust_percentage)
    tm = rsv_new/(1 + vat_tax) - nsv_new
    return -tm.sum()*10/trade_margin_base.sum()

# COMMAND ----------

def get_min_max_constraints(min_shelf_price,max_shelf_price):
  n = min_shelf_price.shape[0]
  mat = np.eye(n)
  lb = min_shelf_price
  ub = max_shelf_price
  return mat, lb, ub

# COMMAND ----------

def get_overall_netnet_change_cons(x,
                                n_customers_each_ppg, #shape(n_ppg,)
                                current_shelf_price, #shape(n_ppg_customer,)
                                adjust_percentage, #float
                                nsv,
                                eq_constraint,
                                equal_price_ppg = None, 
                                avg_shelf_price_df = None,
                                ):

    if eq_constraint:
        x = equal_ppgs_ext(x, equal_price_ppg, avg_shelf_price_df)

    shelf_price = np.repeat(x,n_customers_each_ppg)
    new_netnet_perc = ((shelf_price*1/current_shelf_price) - 1) * adjust_percentage
    new_netnet_perc_change = (new_netnet_perc*nsv).sum()/nsv.sum()
    return new_netnet_perc_change*100
    

# COMMAND ----------



# COMMAND ----------

def measures(opt_x, data,unit_args, rsv_args, nsv_args, mac_args,args_constant_unit, args_constant_nsv, args_constant_rsv, round_up_fn, n_customers_each_ppg = None):

    if n_customers_each_ppg is not None:
        opt_x_rounded = eval(round_up_fn)(opt_x)
        

        opt_x_ext = np.repeat(opt_x,n_customers_each_ppg)
        opt_x_rounded_ext = np.repeat(opt_x_rounded,n_customers_each_ppg)

    else:
        opt_x_ext = opt_x
        opt_x_rounded_ext = eval(round_up_fn)(opt_x)
    
    data.sort_values(by=['PPG','Customer'], inplace = True)
    data_with_optimum = data.copy()
    
    data_with_optimum['new_promo_price'] = data_with_optimum['promo_shelf_price_ppg'] * opt_x_ext/data_with_optimum['shelf_price_ppg']
    data_with_optimum['new_promo_price_rounded'] = data_with_optimum['new_promo_price'].apply(eval(round_up_fn))

    promo_new = data_with_optimum['new_promo_price_rounded']
    data_with_optimum['opt_shelf_price'] = opt_x_ext
    data_with_optimum['opt_shelf_price_rounded'] = opt_x_rounded_ext


    measures = {
        'OptUnit': (unit_ppg_customer,unit_args),
        'OptRSV': (rsv_ppg_customer,rsv_args),
        'OptNSV': (nsv_ppg_customer,nsv_args),
        'OptMAC': (mac_ppg_customer,mac_args)
    }

    

    for k, v in measures.items():
        f, args = v
        opt,_ = f(opt_x_ext,
                    *args,
                    )
        data_with_optimum[k] = opt


    #for rounded
    args_constant_unit = args_constant_unit+ (True,opt_x_rounded_ext,promo_new)
    args_constant_nsv = args_constant_nsv+ (True,opt_x_rounded_ext,promo_new)
    args_constant_rsv = args_constant_rsv+ (True,opt_x_rounded_ext,promo_new)
   
    unit_args_list = list(unit_args)
    nsv_args_list = list(nsv_args)
    rsv_args_list = list(rsv_args)
    mac_args_list = list(mac_args)

    unit_args_list[1] = get_constants_units(*args_constant_unit)
    nsv_args_list[1] = get_constants_nsv(*args_constant_nsv)
    rsv_args_list[-1] = get_constants_rsv(*args_constant_rsv)
    mac_args_list[1] = get_constants_nsv(*args_constant_nsv)
    mac_args_list[2] =get_constants_units(*args_constant_unit)


    unit_args = tuple(unit_args_list)
    nsv_args = tuple(nsv_args_list)
    rsv_args = tuple(rsv_args_list)
    mac_args = tuple(mac_args_list)

    measures = {
        'OptUnit': (unit_ppg_customer,unit_args),
        'OptRSV': (rsv_ppg_customer,rsv_args),
        'OptNSV': (nsv_ppg_customer,nsv_args),
        'OptMAC': (mac_ppg_customer,mac_args)
    }

    for k, v in measures.items():  
        #for rounded
        f, args = v
        args = args+ (True,)
        if k=='OptRSV':
            args = args + (promo_new,)
        opt_rounded,_ = f(opt_x_rounded_ext,
                    *args,
                    )
        data_with_optimum[k+'_rounded'] = opt_rounded
 

    data_with_optimum['OptNetNet'] = data_with_optimum['NetNet_received_file']*((1-data_with_optimum['adjust_percentage']) + (data_with_optimum['adjust_percentage'] * data_with_optimum['opt_shelf_price']/data_with_optimum['avg_shelf_price']))

    data_with_optimum['OptDeadNet'] = data_with_optimum['DeadNet_received_file']*((1-data_with_optimum['adjust_percentage']) + (data_with_optimum['adjust_percentage'] * data_with_optimum['opt_shelf_price']/data_with_optimum['avg_shelf_price']))

    data_with_optimum['OptNetNet_rounded'] = data_with_optimum['NetNet_received_file']*((1-data_with_optimum['adjust_percentage']) + (data_with_optimum['adjust_percentage'] * data_with_optimum['opt_shelf_price_rounded']/data_with_optimum['avg_shelf_price']))

    data_with_optimum['OptDeadNet_rounded'] = data_with_optimum['DeadNet_received_file']*((1-data_with_optimum['adjust_percentage']) + (data_with_optimum['adjust_percentage'] * data_with_optimum['opt_shelf_price_rounded']/data_with_optimum['avg_shelf_price']))   
    

    #  replace the values in the "opt netnet" column with the corresponding values from the "floorprice" column wherever the "opt netnet" values are less than the "floorprice" values
    data_with_optimum.loc[data_with_optimum['OptNetNet'] < data_with_optimum['Floorprice'], 'OptNetNet'] = data_with_optimum['Floorprice']
    data_with_optimum.loc[data_with_optimum['OptDeadNet'] < data_with_optimum['Floorprice'], 'OptDeadNet'] = data_with_optimum['Floorprice']

    data_with_optimum['OptNetNet % Change'] = ((data_with_optimum['opt_shelf_price']/data_with_optimum['avg_shelf_price'])
                        - 1) * data_with_optimum['adjust_percentage']

    data_with_optimum['OptTradeMargin%'] = 1 - (data_with_optimum['OptNetNet']*(1+data_with_optimum['Tax'])/data_with_optimum['opt_shelf_price'])

    # data_with_optimum['OptListPrice'] = data_with_optimum['lp'] * (1 + ((data_with_optimum['opt_shelf_price']/data_with_optimum['shelf_price_ppg'])- 1)*data_with_optimum['adjust_percentage']*data_with_optimum['lp_multiplication_factor'])
    # data_with_optimum['OptListPrice_rounded'] = data_with_optimum['lp'] * (1 + ((data_with_optimum['opt_shelf_price_rounded']/data_with_optimum['shelf_price_ppg'])- 1)*data_with_optimum['adjust_percentage']*data_with_optimum['lp_multiplication_factor'])

    
    data_with_optimum['TPR new recalculated'] = (1 - data_with_optimum['new_promo_price_rounded'] / data_with_optimum['opt_shelf_price_rounded'])*100
    data_with_optimum['TPR new'] = (1 - data_with_optimum['promo_shelf_price_ppg'] / data_with_optimum['shelf_price_ppg'])*100
    data_with_optimum['TPR base'] = (1 - data_with_optimum['avg_shelf_promo_price'] / data_with_optimum['avg_shelf_price'])*100
    
     
    return data_with_optimum

# COMMAND ----------

def modify_min_price_cal(min_price_cal):
    min_price_cal = min_price_cal[['PPG','Customer','opt_shelf_price_rounded','OptUnit','OptUnit_rounded','OptRSV','OptRSV_rounded','OptNSV','OptNSV_rounded','OptMAC','OptMAC_rounded', 'OptTradeMargin%', 'OptNetNet', 'OptNetNet % Change']]

    min_price_cal = min_price_cal.rename(columns={
        'opt_shelf_price_rounded': 'min_shelf_price_rounded',
        'OptUnit': 'minUnit',
        'OptUnit_rounded': 'minUnit_rounded',
        'OptRSV': 'minRSV',
        'OptRSV_rounded': 'minRSV_rounded',
        'OptNSV': 'minNSV',
        'OptNSV_rounded': 'minNSV_rounded',
        'OptMAC': 'minMAC',
        'OptMAC_rounded': 'minMAC_rounded',
        'OptTradeMargin%': 'minTradeMargin%',
        'OptNetNet': 'minNetNet',
        'OptNetNet % Change': 'minNetNet % Change',
    })
    return min_price_cal

# COMMAND ----------

def opt_summary(level, optmised_data):


    # Calculate the sum of KPIs
    sum_kpis = optmised_data.groupby(level)[['sell_in_units', 'Value Sales = RSV', 'NSV', 'MAC', 'OptUnit', 'OptRSV', 'OptNSV', 'OptMAC', 'minUnit', 'minRSV','minNSV','minMAC']].sum().reset_index()

    sum_kpis.columns = [level, 'Total Unit Sales', 'Total RSV', 'Total NSV', 'Total MAC', 'Total OptUnit', 'Total OptRSV', 'Total OptNSV', 'Total OptMAC', 'Total minUnit', 'Total minRSV', 'Total minNSV', 'Total minMAC']


    # Calculate the percentage change
    sum_kpis['Unit Sales % Change'] = ((sum_kpis['Total OptUnit'] / sum_kpis['Total Unit Sales'])-1)
    sum_kpis['RSV % Change'] = ((sum_kpis['Total OptRSV'] / sum_kpis['Total RSV'])-1)
    sum_kpis['NSV % Change'] = ((sum_kpis['Total OptNSV'] / sum_kpis['Total NSV'])-1)
    sum_kpis['MAC % Change'] = ((sum_kpis['Total OptMAC'] / sum_kpis['Total MAC'])-1)

    # Calculate the percentage change using "min" values
    sum_kpis['(Min) Unit Sales % Change'] = ((sum_kpis['Total minUnit'] / sum_kpis['Total Unit Sales']) - 1)
    sum_kpis['(Min) RSV % Change'] = ((sum_kpis['Total minRSV'] / sum_kpis['Total RSV']) - 1)
    sum_kpis['(Min) NSV % Change'] = ((sum_kpis['Total minNSV'] / sum_kpis['Total NSV']) - 1)
    sum_kpis['(Min) MAC % Change'] = ((sum_kpis['Total minMAC'] / sum_kpis['Total MAC']) - 1)


    # overall netnet cal at level
    nn_opt = optmised_data.groupby(level).\
        apply(lambda df : (df['OptNetNet % Change']*df['NSV']).sum()/df['NSV'].sum()).reset_index().rename({0:'opt netnet % change'},axis=1)

    nn_min = optmised_data.groupby(level).apply(lambda df : (df['minNetNet % Change']*df['NSV']).sum()/df['NSV'].sum()).reset_index().rename({0:'min netnet % change'},axis=1)


    nn = nn_opt.merge(nn_min, on=level)

    # merging final
    sum_kpis = sum_kpis.merge(nn, on = level)



    total_sum = sum_kpis[['Total Unit Sales', 'Total RSV', 'Total NSV', 'Total MAC', 'Total OptUnit', 'Total OptRSV',
                           'Total OptNSV', 'Total OptMAC', 'Total minUnit', 'Total minRSV', 'Total minNSV', 'Total minMAC']].sum()


    total_sum['Unit Sales % Change'] = total_sum['Total OptUnit'] / total_sum['Total Unit Sales'] - 1
    total_sum['RSV % Change'] = total_sum['Total OptRSV'] / total_sum['Total RSV'] - 1
    total_sum['NSV % Change'] = total_sum['Total OptNSV'] / total_sum['Total NSV'] - 1
    total_sum['MAC % Change'] = total_sum['Total OptMAC'] / total_sum['Total MAC'] - 1

    total_sum['(Min) Unit Sales % Change'] = total_sum['Total minUnit'] / total_sum['Total Unit Sales'] - 1
    total_sum['(Min) RSV % Change'] = total_sum['Total minRSV'] / total_sum['Total RSV'] - 1
    total_sum['(Min) NSV % Change'] = total_sum['Total minNSV'] / total_sum['Total NSV'] - 1
    total_sum['(Min) MAC % Change'] = total_sum['Total minMAC'] / total_sum['Total MAC'] - 1



    total_sum['opt netnet % change'] = (optmised_data['OptNetNet % Change']*optmised_data['NSV']).sum()/optmised_data['NSV'].sum()
    total_sum['min netnet % change'] = (optmised_data['minNetNet % Change']*optmised_data['NSV']).sum()/optmised_data['NSV'].sum()
 


    # Step 3: Create the new row with calculated sums and % changes
    new_row = {level: 'Total',
        'Total Unit Sales': total_sum['Total Unit Sales'],
        'Total RSV': total_sum['Total RSV'],
        'Total NSV': total_sum['Total NSV'],
        'Total MAC': total_sum['Total MAC'],
        'Total OptUnit': total_sum['Total OptUnit'],
        'Total OptRSV': total_sum['Total OptRSV'],
        'Total OptNSV': total_sum['Total OptNSV'],
        'Total OptMAC': total_sum['Total OptMAC'],
        'Total minUnit': total_sum['Total minUnit'],
        'Total minRSV': total_sum['Total minRSV'],
        'Total minNSV': total_sum['Total minNSV'],
        'Total minMAC': total_sum['Total minMAC'],
        'Unit Sales % Change': total_sum['Unit Sales % Change'],
        'RSV % Change': total_sum['RSV % Change'],
        'NSV % Change': total_sum['NSV % Change'],
        'MAC % Change': total_sum['MAC % Change'],
        '(Min) Unit Sales % Change': total_sum['(Min) Unit Sales % Change'],
        '(Min) RSV % Change': total_sum['(Min) RSV % Change'],
        '(Min) NSV % Change': total_sum['(Min) NSV % Change'],
        '(Min) MAC % Change': total_sum['(Min) MAC % Change'],
        'opt netnet % change': total_sum['opt netnet % change'],
        'min netnet % change': total_sum['min netnet % change']}

    # Append the new row to the DataFrame
    sum_kpis = sum_kpis.append(new_row, ignore_index=True)


    return sum_kpis

# COMMAND ----------

def find_outliers(df):
    
    lower_bound = df['Value Sales = RSV'].quantile(0.1)
    df['90th percentile'] = lower_bound
    df['is_outlier'] = df['Value Sales = RSV'] < lower_bound
    df['min_shelf_price_all'] = df['min_shelf_price'].max()

    if df['is_outlier'].any():
        df['min_shelf_price_wo_outliers'] = df[df['is_outlier'] == False]['min_shelf_price'].max()
    else:
        df['min_shelf_price_wo_outliers'] = df['min_shelf_price_all']
    
    return df


# COMMAND ----------

def opt_result(data):
    return data[['Customer','PPG',
                 'NetNet_received_file',
                 'sell_in_units',
                 'OptUnit_rounded',
                 'OptTradeMargin%',
                 'opt_shelf_price',
                 'opt_shelf_price_rounded','new_promo_price', 'new_promo_price_rounded','OptNetNet','OptDeadNet','OptNetNet_rounded','OptDeadNet_rounded',
                 'OptNetNet % Change',
                 'OptNSV',
                 'OptNSV_rounded',
                 'OptRSV',
                 'OptRSV_rounded',
                 'OptMAC',
                 'OptMAC_rounded','nn_change_percent','Floorprice',]]


# COMMAND ----------

def args_fn_excluded_ppgs(data, 
            elasticities, 
            adjust_percentage):

    sell_in_units = data['sell_in_units'].to_numpy()
    netnet = data['NetNet_received_file'].to_numpy()
    deadnet = data['DeadNet_received_file'].to_numpy()
    base_vol = (data['sell_in_volume_t']*1000).to_numpy()
    ps = data['% Promo Share (Unit)'].to_numpy()

    pack_weight_in_kg = data['pack_weight'].to_numpy()

    cogs_per_kg = (data['COGS/t']/1000).to_numpy()
    cogs_per_unit = pack_weight_in_kg * cogs_per_kg

    current_shelf_price = data['avg_shelf_price'].to_numpy()

    vat_tax = data['Tax'].to_numpy()

    shelf_promo_price_ppg = data['promo_shelf_price_ppg'].to_numpy()
    shelf_non_promo_price_ppg = data['shelf_price_ppg'].to_numpy()

    shelf_promo_price = data['avg_shelf_promo_price'].to_numpy()


    tpr_coef = data['Promo Price Elasticity'].to_numpy()
    weighted_nn_dn = (1-ps)*netnet + ps*deadnet

    nsv = data['NSV']
    rsv = data['Value Sales = RSV']
    mac = data['GMAC abs']
    data['MAC'] = mac

    rsv_excl_vat = rsv/(1+vat_tax)
    trade_margin_base = rsv_excl_vat - nsv
    current_netnet_overall = (netnet * sell_in_units).sum()/sell_in_units.sum()

    cogs_change = 0.0 #Needs to be updated
    cogs_per_unit_new = cogs_per_unit * (1+cogs_change)

    args_constant_unit = (ps,sell_in_units, #shape(n_ppg_customer,) Unit or NSV
                    elasticities, #shape(n_ppg_customer,n_ppg_customer)
                    shelf_promo_price,
                    current_shelf_price,
                    shelf_promo_price_ppg,
                    shelf_non_promo_price_ppg,
                    tpr_coef)
    args_constant_nsv = (ps,sell_in_units, #shape(n_ppg_customer,) Unit or NSV
                elasticities, #shape(n_ppg_customer,n_ppg_customer)
                shelf_promo_price,
                current_shelf_price,
                shelf_promo_price_ppg,
                shelf_non_promo_price_ppg,
                tpr_coef,
                netnet,deadnet)
    args_constant_rsv = (ps,sell_in_units, #shape(n_ppg_customer,) Unit or NSV
                elasticities, #shape(n_ppg_customer,n_ppg_customer)
                shelf_promo_price,
                current_shelf_price,
                shelf_promo_price_ppg,
                shelf_non_promo_price_ppg,
                tpr_coef)

               
    constants_for_sell_in_unit = get_constants_units(*args_constant_unit)
    constants_for_nsv = get_constants_nsv(*args_constant_nsv)
    constants_for_rsv = get_constants_rsv(*args_constant_rsv)


    rsv_args = (ps,
                shelf_promo_price,
                shelf_promo_price_ppg,
                shelf_non_promo_price_ppg,
                tpr_coef,
                current_shelf_price,
                elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                constants_for_rsv, #shape(n_ppg_customer,)
                )

    unit_args = (elasticities,  #shape(n_ppg_customer,n_ppg_customer)
            constants_for_sell_in_unit, #shape(n_ppg_customer,)
            )

    nsv_args = (elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                        constants_for_nsv, #shape(n_ppg_customer,)
                        current_shelf_price,
                        adjust_percentage
                        )
    mac_args = (elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                constants_for_nsv,
                constants_for_sell_in_unit, #shape(n_ppg_customer,)
                current_shelf_price, #shape(n_ppg_customer,)
                adjust_percentage, #either a number or shape(n_ppg_customer,)
                cogs_per_unit, #shape(n_ppg,)
                )
    tm_args =   (vat_tax,
                trade_margin_base,
                adjust_percentage,
                ps,
                shelf_promo_price,
                shelf_promo_price_ppg,
                shelf_non_promo_price_ppg,
                tpr_coef,
                current_shelf_price,
                elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                constants_for_rsv,
                constants_for_nsv)

    return data, unit_args, rsv_args, nsv_args, mac_args, shelf_non_promo_price_ppg, args_constant_unit, args_constant_nsv, args_constant_rsv

# COMMAND ----------

def args_fn(data, 
            elasticities, 
            adjust_percentage,eq_constraint,n_customers_each_ppg,
            equal_price_ppg = None, avg_shelf_price_df = None):

    sell_in_units = data['sell_in_units'].to_numpy()
    netnet = data['NetNet_received_file'].to_numpy()
    deadnet = data['DeadNet_received_file'].to_numpy()
    base_vol = (data['sell_in_volume_t']*1000).to_numpy()
    ps = data['% Promo Share (Unit)'].to_numpy()

    pack_weight_in_kg = data['pack_weight'].to_numpy()

    cogs_per_kg = (data['COGS/t']/1000).to_numpy()
    cogs_per_unit = pack_weight_in_kg * cogs_per_kg

    current_shelf_price = data['avg_shelf_price'].to_numpy()

    vat_tax = data['Tax'].to_numpy()

    shelf_promo_price_ppg = data['promo_shelf_price_ppg'].to_numpy()
    shelf_non_promo_price_ppg = data['shelf_price_ppg'].to_numpy()

    shelf_promo_price = data['avg_shelf_promo_price'].to_numpy()


    tpr_coef = data['Promo Price Elasticity'].to_numpy()
    weighted_nn_dn = (1-ps)*netnet + ps*deadnet

    nsv = data['NSV']
    rsv = data['Value Sales = RSV']
    mac = data['GMAC abs']
    data['MAC'] = mac

    rsv_excl_vat = rsv/(1+vat_tax)
    trade_margin_base = rsv_excl_vat - nsv
    current_netnet_overall = (netnet * sell_in_units).sum()/sell_in_units.sum()

    cogs_change = 0.0 #Needs to be updated
    cogs_per_unit_new = cogs_per_unit * (1+cogs_change)



    args_constant_unit = (ps,sell_in_units, #shape(n_ppg_customer,) Unit or NSV
                    elasticities, #shape(n_ppg_customer,n_ppg_customer)
                    shelf_promo_price,
                    current_shelf_price,
                    shelf_promo_price_ppg,
                    shelf_non_promo_price_ppg,
                    tpr_coef)
    args_constant_nsv = (ps,sell_in_units, #shape(n_ppg_customer,) Unit or NSV
                elasticities, #shape(n_ppg_customer,n_ppg_customer)
                shelf_promo_price,
                current_shelf_price,
                shelf_promo_price_ppg,
                shelf_non_promo_price_ppg,
                tpr_coef,
                netnet,deadnet)
    args_constant_rsv = (ps,sell_in_units, #shape(n_ppg_customer,) Unit or NSV
                elasticities, #shape(n_ppg_customer,n_ppg_customer)
                shelf_promo_price,
                current_shelf_price,
                shelf_promo_price_ppg,
                shelf_non_promo_price_ppg,
                tpr_coef)

               
    constants_for_sell_in_unit = get_constants_units(*args_constant_unit)
    constants_for_nsv = get_constants_nsv(*args_constant_nsv)
    constants_for_rsv = get_constants_rsv(*args_constant_rsv)
    

    rsv_args = (ps,
                shelf_promo_price,
                shelf_promo_price_ppg,
                shelf_non_promo_price_ppg,
                tpr_coef,
                current_shelf_price,
                elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                constants_for_rsv, #shape(n_ppg_customer,)
                )

    unit_args = (elasticities,  #shape(n_ppg_customer,n_ppg_customer)
            constants_for_sell_in_unit, #shape(n_ppg_customer,)
            )


    nsv_args = (elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                        constants_for_nsv, #shape(n_ppg_customer,)
                        current_shelf_price,
                        adjust_percentage
                        )
    mac_args = (elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                constants_for_nsv,
                constants_for_sell_in_unit, #shape(n_ppg_customer,)
                current_shelf_price, #shape(n_ppg_customer,)
                adjust_percentage, #either a number or shape(n_ppg_customer,)
                cogs_per_unit, #shape(n_ppg,)
                )
    tm_args =   (vat_tax,
                trade_margin_base,
                adjust_percentage,
                ps,
                shelf_promo_price,
                shelf_promo_price_ppg,
                shelf_non_promo_price_ppg,
                tpr_coef,
                current_shelf_price,
                elasticities,  #shape(n_ppg_customer,n_ppg_customer)
                constants_for_rsv,
                constants_for_nsv)

    if eq_constraint:
        obj_nsv_args = nsv_args + (n_customers_each_ppg,
                            eq_constraint,nsv,
                            equal_price_ppg, avg_shelf_price_df)

        obj_mac_args = mac_args + (n_customers_each_ppg,
                            eq_constraint,mac,
                            equal_price_ppg, avg_shelf_price_df)
        obj_rsv_args = rsv_args + (n_customers_each_ppg,
                            eq_constraint,rsv,
                            equal_price_ppg, avg_shelf_price_df)
        obj_unit_args = unit_args + (n_customers_each_ppg,
                            eq_constraint,sell_in_units,
                            equal_price_ppg, avg_shelf_price_df)
        obj_volume_args = unit_args + (n_customers_each_ppg,
                            eq_constraint,base_vol, pack_weight_in_kg,
                            equal_price_ppg, avg_shelf_price_df)
        obj_tm_args = tm_args + (n_customers_each_ppg,
                            eq_constraint, equal_price_ppg, avg_shelf_price_df)
    else:
        obj_nsv_args = nsv_args + (n_customers_each_ppg,
                            eq_constraint,nsv,)

        obj_mac_args = mac_args + (n_customers_each_ppg,
                            eq_constraint,mac,)
        obj_rsv_args = rsv_args + (n_customers_each_ppg,
                            eq_constraint,rsv,)
        obj_unit_args = unit_args + (n_customers_each_ppg,
                            eq_constraint,sell_in_units,)
        obj_volume_args = unit_args + (n_customers_each_ppg,
                            eq_constraint,base_vol, pack_weight_in_kg,)
        obj_tm_args = tm_args + (n_customers_each_ppg,
                            eq_constraint)
    if eq_constraint:
        nn_args = (n_customers_each_ppg, #shape(n_ppg,)
                            current_shelf_price, #shape(n_ppg_customer,)
                            adjust_percentage, #float
                            nsv,
                            eq_constraint,
                            equal_price_ppg, 
                            avg_shelf_price_df
                            )
    else:
        nn_args = (n_customers_each_ppg, #shape(n_ppg,)
                            current_shelf_price, #shape(n_ppg_customer,)
                            adjust_percentage, #float
                            nsv,
                            eq_constraint,
                            )


    return data, unit_args, rsv_args, nsv_args, mac_args, obj_nsv_args, obj_mac_args, nn_args, obj_rsv_args, obj_unit_args, obj_volume_args, obj_tm_args, args_constant_unit, args_constant_nsv, args_constant_rsv


# COMMAND ----------

def hierarchy_analysis(optmised_merged_data, group_ppgs):
    # hierarchy analysis 
    hierarchy_analysis_ppg = optmised_merged_data[['PPG','avg_shelf_price_ppg','min_shelf_price','max_shelf_price','opt_shelf_price','initial_guess']].drop_duplicates()
    pack_weight_ppg = optmised_merged_data.groupby('PPG')['pack_weight'].mean().reset_index()
    hierarchy_analysis_ppg = group_ppgs.merge(hierarchy_analysis_ppg, on = 'PPG', how='left')
    hierarchy_analysis_ppg = hierarchy_analysis_ppg.merge(pack_weight_ppg, on = ['PPG'], how = 'left')
    hierarchy_analysis_ppg.dropna(inplace = True)
    hierarchy_analysis_ppg = hierarchy_analysis_ppg.groupby('group').agg(list).apply(lambda col: col.explode()).reset_index()

    hierarchy_analysis_ppg['avg/kg'] = hierarchy_analysis_ppg['avg_shelf_price_ppg']/hierarchy_analysis_ppg['pack_weight']
    hierarchy_analysis_ppg['min/kg'] = hierarchy_analysis_ppg['min_shelf_price']/hierarchy_analysis_ppg['pack_weight']
    hierarchy_analysis_ppg['max/kg'] = hierarchy_analysis_ppg['max_shelf_price']/hierarchy_analysis_ppg['pack_weight']
    hierarchy_analysis_ppg['opt/kg'] = hierarchy_analysis_ppg['opt_shelf_price']/hierarchy_analysis_ppg['pack_weight']
    hierarchy_analysis_ppg['initial/kg'] = hierarchy_analysis_ppg['initial_guess']/hierarchy_analysis_ppg['pack_weight']

    def create_boolean_column(df, column_to_compare, new_col, condition_func):
        df = df[['PPG','group']].merge(df.groupby('group').apply(
            lambda group: condition_func(group, column_to_compare, new_col)
        ).reset_index(), on = ['PPG','group'])
        df.loc[df.groupby('group').head(1).index, new_col] = np.nan
        return df
            
    def check_condition(group, column_to_compare, new_col):
        group[new_col] = group[column_to_compare] <= group[column_to_compare].shift(1)
        group.loc[group.index[0], new_col] = np.nan
        return group

    hierarchy_analysis_ppg_final = create_boolean_column(hierarchy_analysis_ppg, 'avg/kg', 'avg/kg satisfied', check_condition)
    hierarchy_analysis_ppg_final = hierarchy_analysis_ppg_final.merge(create_boolean_column(hierarchy_analysis_ppg, 'min/kg', 'min/kg satisfied', check_condition)[['PPG','group','min/kg satisfied']], on = ['PPG','group'])
    hierarchy_analysis_ppg_final = hierarchy_analysis_ppg_final.merge(create_boolean_column(hierarchy_analysis_ppg, 'max/kg', 'max/kg satisfied', check_condition)[['PPG','group','max/kg satisfied']], on = ['PPG','group'])
    hierarchy_analysis_ppg_final = hierarchy_analysis_ppg_final.merge(create_boolean_column(hierarchy_analysis_ppg, 'opt/kg', 'opt/kg satisfied', check_condition)[['PPG','group','opt/kg satisfied']], on = ['PPG','group'])
    hierarchy_analysis_ppg_final = hierarchy_analysis_ppg_final.merge(create_boolean_column(hierarchy_analysis_ppg, 'initial/kg', 'initial/kg satisfied', check_condition)[['PPG','group','initial/kg satisfied']], on = ['PPG','group'])
    
    return hierarchy_analysis_ppg_final

# COMMAND ----------

def get_indices(df, hierarchy_ppgs, ppg_column_name='PPG'):
    indices_list = []
    for sublist in hierarchy_ppgs:
        sublist_indices = []

        for ppg in sublist:
            if ppg in df[ppg_column_name].values:
                indices = df[df[ppg_column_name] == ppg].index.tolist()
                sublist_indices.extend(indices)

        indices_list.append(sublist_indices)

    return indices_list


# COMMAND ----------

# updating the max price according to hier
def update_max_hier(indices_list, factors_list, lb_min_max, ub_min_max,avg_shelf_price_df):
    max_copy = ub_min_max.copy()
    for index,(indices,factors) in enumerate(list(zip(indices_list,factors_list))):
        for i in range(len(indices)-1):
            if max_copy[indices[i]]*factors[i]<lb_min_max[indices[i+1]]*factors[i+1]:
                max_copy[indices[i]] = 100

    return max_copy

# COMMAND ----------

# updating the max price according to eq
def update_max_eq(indices_list, lb_min_max, ub_min_max):
    max_copy = ub_min_max.copy()
    for index,indices in enumerate(list(indices_list)):
        for i in range(len(indices)-1):
            if max_copy[indices[i]]<lb_min_max[indices[i]]:
                max_copy[indices[i]] = 100

    return max_copy

# COMMAND ----------

def save_dataframe_with_date(df, name, path, verbose = True):
    if verbose:
        current_date = datetime.now().strftime('%Y-%m-%d')
        file_name = f"{name}_{current_date}.csv"
        file_path = path+file_name
        df.to_csv(file_path, index=False)

# COMMAND ----------

def solver1(lb_min_max, ub_min_max, 
                indices_list, factors_list, 
                indices_list_ppi, factors_list_price_per_item, 
                nn_args, target,
                hier_flag = True, nn_flag = True):
    lp_problem = lp.LpProblem("My_LP_Problem", lp.LpMinimize)
    X = np.array([lp.LpVariable(f"x_{i:0>3}", lowBound=lb_min_max[i],upBound = ub_min_max[i]) for i in range(len(lb_min_max))])
    X_copy = X.copy()  

    # Add constraints
    # hier
    if hier_flag:
        for index,(indices,factors) in enumerate(list(zip(indices_list,factors_list))):
            for i in range(len(indices)-1):
                lp_problem += X[indices[i]]*factors[i]-X[indices[i+1]]*factors[i+1]>=0
        # ppi
        for index,(indices,factors) in enumerate(list(zip(indices_list_ppi,factors_list_price_per_item))):
            for i in range(len(indices)-1):
                lp_problem += X[indices[i]]*factors[i]-X[indices[i+1]]*factors[i+1]>=0
    if nn_flag:
        objective_function = get_overall_netnet_change_cons(X,*nn_args) - target
        lp_problem += objective_function, "Objective Function"
        lp_problem+=objective_function==0
    # lp_problem.writeMPS('/dbfs/mnt/gen2_FILES/GERMANY_PET_TPO/kv_netpricing_test/solver1_ds_mps')
    lp_problem.solve()
    return lp_problem, X

# COMMAND ----------

def report_hier(lb_min_max, ub_min_max, 
            indices_list, factors_list, 
            indices_list_ppi, factors_list_price_per_item, 
            nn_args, target, groups_hier_cons, avg_shelf_price_df):
    lp_problem_check, X_check = solver1(lb_min_max, ub_min_max, 
            indices_list, factors_list, 
            indices_list_ppi, factors_list_price_per_item, 
            nn_args, target,
            hier_flag = False, nn_flag = False)
    if lp.LpStatus[lp_problem_check.status]=='Optimal':
        print('min max feasible')
        # check if nn is feasible
        lp_problem_check, X_check = solver1(lb_min_max, ub_min_max, 
            indices_list, factors_list, 
            indices_list_ppi, factors_list_price_per_item, 
            nn_args, target,
            hier_flag = False, nn_flag = True)
        if lp.LpStatus[lp_problem_check.status]=='Optimal':
            print('nn feasible')
            cons_report_dict = {}
            # generate hierarchy report
            keys = ['group', 'constraint', 'positive_var_factor','positive_var_index','negative_var_factor', 'negative_var_index', 'pos_var_min',	'pos_var_max', 'neg_var_min', 'neg_var_max']
            for key in keys:
                cons_report_dict[key] = []
            for index,(indices,factors) in enumerate(list(zip(indices_list, factors_list))):
                for i in range(len(indices)-1):
                    cons_report_dict['group'].append(groups_hier_cons[index])
                    cons_report_dict['constraint'].append(index+1)
                    cons_report_dict['positive_var_factor'].append(factors[i])
                    cons_report_dict['positive_var_index'].append(indices[i])
                    cons_report_dict['negative_var_factor'].append(factors[i+1])
                    cons_report_dict['negative_var_index'].append(indices[i+1])
                    cons_report_dict['pos_var_min'].append(lb_min_max[indices[i]])
                    cons_report_dict['pos_var_max'].append(ub_min_max[indices[i]])
                    cons_report_dict['neg_var_min'].append(lb_min_max[indices[i+1]])
                    cons_report_dict['neg_var_max'].append(ub_min_max[indices[i+1]])
            report_solver1 = pd.DataFrame(cons_report_dict)
            report_dict = {}
            keys = ['group', 'constraint', 'index','max','min']
            for key in keys:
                report_dict[key] = []
            # positive index iteration
            for i in range(report_solver1.shape[0]):
                report_dict['group'].append(report_solver1.loc[i]['group'])
                report_dict['constraint'].append(report_solver1.loc[i]['constraint'])
                report_dict['index'].append(report_solver1.loc[i]['positive_var_index'])

                report_dict['min'].append(report_solver1.loc[i]['negative_var_factor']*report_solver1.loc[i]['neg_var_min']/report_solver1.loc[i]['positive_var_factor'])
                report_dict['max'].append(np.inf)
            # negative index iteration
            for i in range(report_solver1.shape[0]):
                report_dict['group'].append(report_solver1.loc[i]['group'])
                report_dict['constraint'].append(report_solver1.loc[i]['constraint'])
                report_dict['index'].append(report_solver1.loc[i]['negative_var_index'])

                report_dict['max'].append(report_solver1.loc[i]['positive_var_factor']*report_solver1.loc[i]['pos_var_max']/report_solver1.loc[i]['negative_var_factor'])
                report_dict['min'].append(-np.inf)

            final_report_solver1 = pd.DataFrame(report_dict)

            report_dict_pos = report_solver1[['positive_var_index','pos_var_min','pos_var_max']].drop_duplicates()
            report_dict_pos.rename(columns = {'positive_var_index':'index', 'pos_var_min':'min', 'pos_var_max':'max'},inplace = True)
            report_dict_pos['group'] = 'min-max'
            report_dict_pos['constraint'] = 0
            report_dict_pos = report_dict_pos[['group', 'constraint', 'index','max','min']]
            report_dict_neg = report_solver1[['negative_var_index','neg_var_min','neg_var_max']].drop_duplicates()
            report_dict_neg.rename(columns = {'negative_var_index':'index','neg_var_min':'min', 'neg_var_max':'max'},inplace = True)
            report_dict_neg['group'] = 'min-max'
            report_dict_neg['constraint'] = 0
            report_dict_neg = report_dict_neg[['group', 'constraint', 'index','max','min']]
            final_report_solver1 = pd.concat([final_report_solver1,report_dict_neg, report_dict_pos])
            final_report_solver1 = final_report_solver1.drop_duplicates()
            
            final_report_solver1['min_max'] = final_report_solver1.groupby('index')['max'].transform('min')
            final_report_solver1['max_min'] = final_report_solver1.groupby('index')['min'].transform('max')
            final_report_solver1['feasibility'] = final_report_solver1['max_min']  <=final_report_solver1['min_max']
            avg_shelf_price_df = avg_shelf_price_df.reset_index()
            final_report_solver1 = final_report_solver1.merge(avg_shelf_price_df[['PPG','index']], left_on = 'index', right_on = 'index')
            print('hierarchy constraint failing')
            return final_report_solver1 #save to adls
        else:
            print('nn infeasible')
    else:
        print('min max infeasible')
        return None

# COMMAND ----------

def optimizer(data,config,equal_ppgs,group_ppgs): 
    # breakpoint()
    data = data.rename(columns={"product_group":"PPG","customer":"Customer","non_promo_price_elasticity":"Non Promo Price Elasticity","competitor_coefficient":"Competitor Coefficient","sell_in_unit":"sell_in_units","net_net":"NetNet_received_file","dead_net":"DeadNet_received_file","promo_share":"% Promo Share (Unit)","pack_weight_kg":"pack_weight","tax":"Tax","promo_price_elasticity":"Promo Price Elasticity","brand":"Brand","technology":"Technology","non_promo_price_per_unit":"avg_shelf_price","promo_price_per_unit":"avg_shelf_promo_price","cogs_t_new":"COGS/t","promo_price_per_unit_ppg":"promo_shelf_price_ppg","non_promo_price_per_unit_ppg":"shelf_price_ppg","floor_price":"Floorprice","value_sales_rsv":'Value Sales = RSV','non_promo_price_new':'max_shelf_price', 'nsv':'NSV', 'gmac_abs': 'GMAC abs'})
    data['adjust_percentage'] = config['adjust_percentage']
    # data['lp_multiplication_factor'] = config['lp_multiplication_factor']
    data['Promo Price Elasticity'] = data['Promo Price Elasticity'].fillna(0)

    group_ppgs['len'] = group_ppgs.groupby('group')['PPG'].transform('count')
    group_ppgs['order'] = group_ppgs.groupby('group').cumcount()

    
    data = data.drop_duplicates(subset = ['PPG','Customer'])
    data.sort_values(by = ['PPG','Customer'],inplace = True)
    data = data.reset_index(drop= True)

    # replace '----' by column name
    # data.rename(columns = {'----': 'min_shelf_price'},inplace = True)
    data = data[~data['PPG'].isin(['SHEBA POUCH OVERFILL (12+3)X50G'])]

    min_max_price_df = data.groupby('PPG')[['max_shelf_price','min_shelf_price']].mean().reset_index()
    max_price = min_max_price_df['max_shelf_price'].values
    min_shelf_price = min_max_price_df['min_shelf_price'].values
    # breakpoint()
    # updating max_shelf_price for eq_ppgs constraint
    equal_ppgs = pd.DataFrame(columns=('PPG','group'))
    equal_ppgs_df = equal_ppgs.groupby(['group']).agg(PPG=('PPG', list))
    equal_ppgs_list = equal_ppgs_df['PPG'].to_list()
    indices_list_eq = get_indices(min_max_price_df, equal_ppgs_list, ppg_column_name='PPG')

    min_max_price_df['max_shelf_price'] = update_max_eq(indices_list_eq, min_shelf_price, max_price)
    data.drop('max_shelf_price', axis = 1, inplace = True)
    data = data.merge(min_max_price_df[['PPG','max_shelf_price']], on = 'PPG')

    # exluding edeka, netto and aldi north for optimizer
    data_all_retailers = data.copy()
    data = data.loc[(data['exclude_retailer']==False)].reset_index(drop=True)
    data_all_retailers.drop_duplicates(inplace = True)
    data_all_retailers.sort_values(by=['PPG','Customer'], inplace = True)
    data.drop_duplicates(inplace = True)
    data.sort_values(by=['PPG','Customer'], inplace = True)

    # local config
    local_config = get_config()

    eq_constraint = local_config['eq_constraint']
    min_max_constraint = local_config['min_max_constraint']
    hier_constraint = local_config['hier_constraint']
    # nsv_constraint = local_config['nsv_constraint']
    mac_constraint = local_config['mac_constraint']
    tm_constraint = local_config['tm_constraint']
    netnet_constraint = local_config['netnet_constraint']
    price_per_item_constraint = local_config['price_per_item_constraint']
    exclude_few_retailers = local_config['exclude_few_retailers']
    # target = config['national_nn_target'] -1
    target = config['national_nn_target']
    verbose = local_config['save_intermediate_files']
    round_up_fn = local_config['round_fn']
    # Get Elasticities Diagonal matrix
    elasticities = get_elasticities(data)
    # elasticities_excluded_ppgs = get_elasticities(data_excluded_ppgs)
    elasticities_all_retailers = get_elasticities(data_all_retailers)
    # Get number of customer sin each PPG
    n_customers_each_ppg = n_customers_each_ppg_func(data)
    n_customers_each_ppg_all_retailers = n_customers_each_ppg_func(data_all_retailers)

    if eq_constraint:
        equal_price_ppg = equal_price_ppgs_fn(data,equal_ppgs)
        data_sub = data[(data['PPG'].isin(equal_price_ppg['high_sales_ppg'].unique()))]
        data_sub.sort_values(by = ['PPG','Customer'], inplace = True)
        avg_shelf_price_df = get_avg_and_max_shelf_price(data_sub)

        # all retailers
        equal_price_ppg_all_retailers = equal_price_ppgs_fn(data_all_retailers,equal_ppgs)
        data_all_retailers_sub = data_all_retailers[
            (data_all_retailers['PPG'].isin(equal_price_ppg_all_retailers['high_sales_ppg'].unique()))]
        
        data_all_retailers_sub.sort_values(by = ['PPG','Customer'], inplace = True)
        avg_shelf_price_df_all_retailers = get_avg_and_max_shelf_price(data_all_retailers_sub)

        # min_shelf_price
        min_shelf_price_df = data_sub[['PPG', 'min_shelf_price']].drop_duplicates()
        min_shelf_price_df = min_shelf_price_df.sort_values('PPG')
        # min_shelf_price_df.rename(columns = {'min_shelf_price_wo_outliers': 'min_shelf_price'},inplace = True)
    else:
        avg_shelf_price_df = get_avg_and_max_shelf_price(data)
        
        avg_shelf_price_df_all_retailers = get_avg_and_max_shelf_price(data_all_retailers)

        # min_shelf_price
        min_shelf_price_df = data[['PPG', 'min_shelf_price']].drop_duplicates()
        min_shelf_price_df = min_shelf_price_df.sort_values('PPG')
        # min_shelf_price_df.rename(columns = {'min_shelf_price_wo_outliers': 'min_shelf_price'},inplace = True)

    adjust_percentage = config['adjust_percentage']
    if eq_constraint:
        data, unit_args, rsv_args, nsv_args, mac_args, obj_nsv_args, obj_mac_args, nn_args, obj_rsv_args, obj_unit_args, obj_volume_args, obj_tm_args, args_constant_unit, args_constant_nsv, args_constant_rsv= args_fn(
            data, elasticities, adjust_percentage,eq_constraint,n_customers_each_ppg,
            equal_price_ppg, avg_shelf_price_df
            )
        data_all_retailers, unit_args_all_retailers, rsv_args_all_retailers, nsv_args_all_retailers, mac_args_all_retailers, obj_nsv_args_all_retailers, obj_mac_args_all_retailers, nn_args_all_retailers, obj_rsv_args_all_retailers, obj_unit_args_all_retailers, obj_volume_args_all_retailers, obj_tm_args_all_retailers, args_constant_unit_all_retailers, args_constant_nsv_all_retailers, args_constant_rsv_all_retailers = args_fn(
            data_all_retailers, elasticities_all_retailers, adjust_percentage,eq_constraint,n_customers_each_ppg_all_retailers,
            equal_price_ppg, avg_shelf_price_df)
    else:
        data, unit_args, rsv_args, nsv_args, mac_args, obj_nsv_args, obj_mac_args, nn_args, obj_rsv_args, obj_unit_args, obj_volume_args, obj_tm_args, args_constant_unit, args_constant_nsv, args_constant_rsv = args_fn(
            data, elasticities, adjust_percentage,eq_constraint,n_customers_each_ppg,
            )
        data_all_retailers, unit_args_all_retailers, rsv_args_all_retailers, nsv_args_all_retailers, mac_args_all_retailers, obj_nsv_args_all_retailers, obj_mac_args_all_retailers, nn_args_all_retailers, obj_rsv_args_all_retailers, obj_unit_args_all_retailers, obj_volume_args_all_retailers, obj_tm_args_all_retailers, args_constant_unit_all_retailers, args_constant_nsv_all_retailers, args_constant_rsv_all_retailers = args_fn(
            data_all_retailers, elasticities_all_retailers, adjust_percentage,eq_constraint,n_customers_each_ppg_all_retailers,
            )       
    
    # exlcuded_ppgs
    # data_excluded_ppgs, unit_args_excluded_ppgs, rsv_args_excluded_ppgs, nsv_args_excluded_ppgs, mac_args_excluded_ppgs, base_price_excluded_ppgs, args_constant_unit_excluded_ppgs, args_constant_nsv_excluded_ppgs, args_constant_rsv_excluded_ppgs = args_fn_excluded_ppgs(data_excluded_ppgs, 
    #         elasticities_excluded_ppgs, 
    #         adjust_percentage)
    
    # Objective function Selection
    nsv_objective_wo_deriv = functools.partial(nsv_objective)
    mac_objective_wo_deriv = functools.partial(mac_objective)
    rsv_objective_wo_deriv = functools.partial(rsv_objective)
    unit_objective_wo_deriv = functools.partial(unit_objective)
    volume_objective_wo_deriv = functools.partial(volume_objective)
    trade_margin_objective_wo_deriv = functools.partial(trade_margin_objective)


    # suffix,options_dict,method =  get_suffix_and_options(config['method'])
    obj_suffix = config['objective'] + '_' + 'wo_deriv'
    objectives = {
    'NSV_wo_deriv': (nsv_objective_wo_deriv,obj_nsv_args),
    'MAC_wo_deriv': (mac_objective_wo_deriv,obj_mac_args),
    'RSV_wo_deriv': (rsv_objective_wo_deriv,obj_rsv_args),
    'Unit_wo_deriv': (unit_objective_wo_deriv,obj_unit_args),
    'Volume_wo_deriv': (volume_objective_wo_deriv,obj_volume_args),
    'Trade_Margin_wo_deriv': (trade_margin_objective_wo_deriv,obj_tm_args),}
    selected_objective_fn, selected_objective_args = objectives[obj_suffix]


    # defining inputs for optimizer
    avg_shelf_price_df.sort_values(by='PPG',inplace=True)
    x0 = avg_shelf_price_df.shelf_price.to_numpy()
    min_shelf_price = min_shelf_price_df.min_shelf_price.to_numpy()  #(EXCLUDED OUTLIERS HERE (RSV 90th))
    max_shelf_price_input = avg_shelf_price_df.max_shelf_price.to_numpy()
    max_shelf_price_input = np.where(max_shelf_price_input<min_shelf_price, np.maximum(min_shelf_price, x0)*1.5,max_shelf_price_input)
    avg_shelf_price = x0
    if eq_constraint:
        min_shelf_price_full = equal_ppgs_ext(min_shelf_price, equal_price_ppg, avg_shelf_price_df)   
        max_shelf_price_full = equal_ppgs_ext(max_shelf_price_input, equal_price_ppg, avg_shelf_price_df)
        avg_shelf_price_full = equal_ppgs_ext(avg_shelf_price, equal_price_ppg, avg_shelf_price_df)

    # price per kg and price per item constraint
    price_per_item_ppgs = group_ppgs[group_ppgs['group'].str.contains('price_per_item')]
    group_ppgs = group_ppgs[~group_ppgs['group'].str.contains('price_per_item')]

    if eq_constraint:
        group_ppgs = group_ppgs[group_ppgs['PPG'].isin(data_sub.PPG.unique())]
        ppg_weight = data_sub.groupby('PPG')['pack_weight'].mean().reset_index()
        ppg_weight = group_ppgs.merge(ppg_weight, on = 'PPG', how='left')
        price_per_item_ppgs = price_per_item_ppgs.drop_duplicates().merge(data_sub[['PPG']], on = 'PPG')
    else:
        group_ppgs = group_ppgs[group_ppgs['PPG'].isin(data.PPG.unique())]
        ppg_weight = data.groupby('PPG')['pack_weight'].mean().reset_index()
        ppg_weight = group_ppgs.merge(ppg_weight, on = 'PPG', how='left')
        price_per_item_ppgs = price_per_item_ppgs.drop_duplicates().merge(data[['PPG']], on = 'PPG')

    ppg_weight.sort_values(by = ['group','order'], inplace = True)
    hierarchy_df = (ppg_weight
    .assign(factors = lambda df_:1/df_['pack_weight'])
    .groupby(['group'])
    .agg(PPG=('PPG', list),
        factors=('factors', list),
        pack_weight = ('pack_weight','count'),
        avg_pack_weight = ('pack_weight','mean'))
    )

    hierarchy_ppgs = hierarchy_df[hierarchy_df['pack_weight']>1]['PPG'].to_list()
    factors_list = hierarchy_df[hierarchy_df['pack_weight']>1]['factors'].to_list()

    price_per_item_df = (price_per_item_ppgs
    .assign(factors = lambda df_:1)
    .groupby(['group'])
    .agg({'PPG':list,
        'factors':list})
    )
    price_per_item_hierarchy_ppgs = price_per_item_df['PPG'].to_list()
    factors_list_price_per_item = price_per_item_df['factors'].to_list()

    if eq_constraint:
        ppg_weight_all = data_sub.groupby('PPG')['pack_weight'].mean().reset_index()
    else:
        ppg_weight_all = data.groupby('PPG')['pack_weight'].mean().reset_index()

    indices_list = get_indices(ppg_weight_all, hierarchy_ppgs, ppg_column_name='PPG')
    indices_list_ppi = get_indices(ppg_weight_all, price_per_item_hierarchy_ppgs, ppg_column_name='PPG')
   

    # updating max
    max_shelf_price_input = update_max_hier(indices_list, factors_list, min_shelf_price, max_shelf_price_input,avg_shelf_price_df)
    mat_min_max, lb_min_max, ub_min_max = get_min_max_constraints(min_shelf_price,max_shelf_price_input)

    # nn_obj_fn to get initial guess
    lp_problem, X = solver1(lb_min_max, ub_min_max, 
                indices_list, factors_list, 
                indices_list_ppi, factors_list_price_per_item, 
                nn_args, target,
                hier_flag = True, nn_flag = True)
    
    print("Solver1 Status:", lp.LpStatus[lp_problem.status])
    # breakpoint()
     # checking feasibility of solver1
    
    if lp.LpStatus[lp_problem.status]!='Optimal':
        # report
        report_solver1 = report_hier(lb_min_max, ub_min_max, 
            indices_list, factors_list, 
            indices_list_ppi, factors_list_price_per_item,
            nn_args, target, groups_hier_cons, avg_shelf_price_df)
        # return report_solver1

    x = np.array([x.varValue for x in X])

    if target<0:
        min_netnet = 1.05*target
        max_netnet = 0.95*target
    else:
        min_netnet = 0.95*target
        max_netnet = 1.05*target

    cons_min_max_dicts = [
        {'type':'ineq', 'fun': lambda x: np.dot(mat_min_max,x) - lb_min_max},
        {'type':'ineq', 'fun': lambda x: ub_min_max - np.dot(mat_min_max,x)},
    ]


    def diff_price(x,indices, factors, i):
        return x[indices[i]]*factors[i]-x[indices[i+1]]*factors[i+1]
    
    cons_hier_dicts=[]
    for index,(indices,factors) in enumerate(list(zip(indices_list,factors_list))):
        for i in range(len(indices)-1):
            cons_hier_dicts.append({'type':'ineq', 'fun': functools.partial(diff_price,indices=indices,factors=factors,i=i)})

    cons_netnet_dicts = {'type':'eq', 'fun': lambda x: get_overall_netnet_change_cons(x,*nn_args)- target}
    # cons_nsv = {'type':'ineq', 'fun': lambda x: (nsv_objective(x,*obj_nsv_args)*-1)- 10}
    cons_mac = {'type':'ineq', 'fun': lambda x: (mac_objective(x,*obj_mac_args)*-1)- 10}
    cons_tm = {'type':'ineq', 'fun': lambda x: (trade_margin_objective(x,*obj_tm_args)*-1)- 10}
    cons_dicts = []
    if min_max_constraint:
        for cons in cons_min_max_dicts:
            cons_dicts.append(cons)
    if netnet_constraint:
        cons_dicts.append(cons_netnet_dicts)
    if hier_constraint:
        cons_dicts.extend(cons_hier_dicts)
    # if nsv_constraint:
    #     cons_dicts.append(cons_nsv)
    if mac_constraint:
        cons_dicts.append(cons_mac)
    if tm_constraint:
        cons_dicts.append(cons_tm)
    initial_guess = x
    res = optimize.minimize(fun=selected_objective_fn,
                                x0=initial_guess,
                                args=selected_objective_args,
                                method='SLSQP',
                                constraints = cons_dicts,
                                  options={'maxiter':1000,'disp':True})
    
    if res.message == 'Optimization terminated successfully':
        status = True
    else:
        status = False
    if lp.LpStatus[lp_problem.status]=='Optimal':
        if status ==False:
            # no tm constraint
            cons_dicts.remove(cons_tm)
            res_check = optimize.minimize(fun=selected_objective_fn,
                                x0=initial_guess,
                                args=selected_objective_args,
                                method='SLSQP',
                                constraints = cons_dicts,
                                  options={'maxiter':1000})
            if res_check.message == 'Optimization terminated successfully':
                print('optimal without trade margin constraint')
            else:
                cons_dicts.remove(cons_mac)
                cons_dicts.append(cons_tm)
                res_check = optimize.minimize(fun=selected_objective_fn,
                                x0=initial_guess,
                                args=selected_objective_args,
                                method='SLSQP',
                                constraints = cons_dicts,
                                options={'maxiter':1000})
                if res_check.message == 'Optimization terminated successfully':
                    print('optimal without mac constraint')

    if eq_constraint:
        # equal_price_ppg_all_retailers.to_csv('/dbfs/mnt/gen2_FILES/GERMANY_PET_TPO/Netpricing/equal_price_ppg_all_retailers.csv', index = False)
        opt_x = equal_ppgs_ext(res.x, equal_price_ppg, avg_shelf_price_df)
    else:
        opt_x = res.x
    opt_x_rounded = eval(round_up_fn)(opt_x)
    
    if exclude_few_retailers:
        if eq_constraint:
            optmised_data = measures(opt_x, data_all_retailers,unit_args_all_retailers, rsv_args_all_retailers, nsv_args_all_retailers, mac_args_all_retailers,args_constant_unit_all_retailers, args_constant_nsv_all_retailers, args_constant_rsv_all_retailers,round_up_fn,n_customers_each_ppg_all_retailers)
            min_price_cal = measures(min_shelf_price_full, data_all_retailers,unit_args_all_retailers, rsv_args_all_retailers, nsv_args_all_retailers, mac_args_all_retailers,args_constant_unit_all_retailers, args_constant_nsv_all_retailers, args_constant_rsv_all_retailers,round_up_fn,n_customers_each_ppg_all_retailers)
        else:
            optmised_data = measures(opt_x, data_all_retailers,unit_args_all_retailers, rsv_args_all_retailers, nsv_args_all_retailers, mac_args_all_retailers,args_constant_unit_all_retailers, args_constant_nsv_all_retailers, args_constant_rsv_all_retailers,round_up_fn,n_customers_each_ppg_all_retailers)
            min_price_cal = measures(min_shelf_price, data_all_retailers,unit_args_all_retailers, rsv_args_all_retailers, nsv_args_all_retailers, mac_args_all_retailers,args_constant_unit_all_retailers, args_constant_nsv_all_retailers, args_constant_rsv_all_retailers,round_up_fn,n_customers_each_ppg_all_retailers)
    else:
        if eq_constraint:
            optmised_data = measures(opt_x, data,unit_args, rsv_args, nsv_args, mac_args,args_constant_unit, args_constant_nsv, args_constant_rsv,round_up_fn,n_customers_each_ppg)
            min_price_cal = measures(min_shelf_price_full, data,unit_args, rsv_args, nsv_args, mac_args,args_constant_unit, args_constant_nsv, args_constant_rsv,round_up_fn,n_customers_each_ppg)
        else:
            optmised_data = measures(opt_x, data,unit_args, rsv_args, nsv_args, mac_args,args_constant_unit, args_constant_nsv, args_constant_rsv,round_up_fn,n_customers_each_ppg)
            min_price_cal = measures(min_shelf_price, data,unit_args, rsv_args, nsv_args, mac_args,args_constant_unit, args_constant_nsv, args_constant_rsv,round_up_fn,n_customers_each_ppg)
    

    
    min_price_cal = modify_min_price_cal(min_price_cal)
    optmised_merged_data = optmised_data.merge(min_price_cal, on = ['PPG', 'Customer'])
    opt_result_data = opt_result(optmised_merged_data)

    # # adding excluded ppgs data
    # exluded_ppgs_cal = measures(base_price_excluded_ppgs, data_excluded_ppgs, unit_args_excluded_ppgs, rsv_args_excluded_ppgs, nsv_args_excluded_ppgs, mac_args_excluded_ppgs,args_constant_unit_excluded_ppgs, args_constant_nsv_excluded_ppgs, args_constant_rsv_excluded_ppgs, round_up_fn)
    # exluded_ppgs_cal = opt_result(exluded_ppgs_cal)

    # opt_result_data = pd.concat([opt_result_data,exluded_ppgs_cal], ignore_index=True)


    if exclude_few_retailers:
        if eq_constraint:
            optmised_merged_data['max_shelf_price'] = np.repeat(max_shelf_price_full,n_customers_each_ppg_all_retailers)
            optmised_merged_data['min_shelf_price'] = np.repeat(min_shelf_price_full,n_customers_each_ppg_all_retailers)
            optmised_merged_data['avg_shelf_price_ppg'] = np.repeat(avg_shelf_price_full,n_customers_each_ppg_all_retailers)
            initial_guess_full = equal_ppgs_ext(initial_guess, equal_price_ppg, avg_shelf_price_df)
            optmised_merged_data['initial_guess'] = np.repeat(initial_guess_full,n_customers_each_ppg_all_retailers)
        else:
            optmised_merged_data['max_shelf_price'] = np.repeat(max_shelf_price_input,n_customers_each_ppg_all_retailers)
            optmised_merged_data['min_shelf_price'] = np.repeat(min_shelf_price,n_customers_each_ppg_all_retailers)
            optmised_merged_data['avg_shelf_price_ppg'] = np.repeat(avg_shelf_price,n_customers_each_ppg_all_retailers)
            optmised_merged_data['initial_guess'] = np.repeat(initial_guess,n_customers_each_ppg_all_retailers)      
    else:
        if eq_constraint:
            optmised_merged_data['max_shelf_price'] = np.repeat(max_shelf_price_full,n_customers_each_ppg)
            optmised_merged_data['min_shelf_price'] = np.repeat(min_shelf_price_full,n_customers_each_ppg)
            optmised_merged_data['avg_shelf_price_ppg'] = np.repeat(avg_shelf_price_full,n_customers_each_ppg)
            initial_guess_full = equal_ppgs_ext(initial_guess, equal_price_ppg, avg_shelf_price_df)
            optmised_merged_data['initial_guess'] = np.repeat(initial_guess_full,n_customers_each_ppg)
        else:
            optmised_merged_data['max_shelf_price'] = np.repeat(max_shelf_price_input,n_customers_each_ppg)
            optmised_merged_data['min_shelf_price'] = np.repeat(min_shelf_price,n_customers_each_ppg)      
            optmised_merged_data['avg_shelf_price_ppg'] = np.repeat(avg_shelf_price,n_customers_each_ppg)      
            optmised_merged_data['initial_guess'] = np.repeat(initial_guess,n_customers_each_ppg)      
    
    # hierarchy_analysis_ppg = hierarchy_analysis(optmised_merged_data, group_ppgs)
    # save_dataframe_with_date(hierarchy_analysis_ppg, 'hierarchy_analysis_ppg', '/dbfs/mnt/gen2_FILES/GERMANY_PET_TPO/Netpricing/OPTIMIZER/Output/',verbose)
    # save_dataframe_with_date(optmised_merged_data, 'optmised_merged_data', '/dbfs/mnt/gen2_FILES/GERMANY_PET_TPO/Netpricing/OPTIMIZER/Output/',verbose)
    # customer_analysis = opt_summary('Customer', optmised_merged_data)
    # save_dataframe_with_date(customer_analysis, 'customer_analysis', '/dbfs/mnt/gen2_FILES/GERMANY_PET_TPO/Netpricing/OPTIMIZER/Output/',verbose)
    # ppg_analysis = opt_summary('PPG', optmised_merged_data)
    # save_dataframe_with_date(ppg_analysis, 'ppg_analysis', '/dbfs/mnt/gen2_FILES/GERMANY_PET_TPO/Netpricing/OPTIMIZER/Output/',verbose)

    print('Solver2 result:')
    print("min:", all(eval(round_up_fn)(min_shelf_price) <= eval(round_up_fn)(res.x)))
    print("max:", all(eval(round_up_fn)(res.x) <= eval(round_up_fn)(max_shelf_price_input)))

    nsv_obj_res = res.x.copy()
    print('Solver2 result:')
    hier_satisfied = True
    for index,(indices,factors) in enumerate(list(zip(indices_list,factors_list))):
        for i in range(len(indices)-1):
            if not np.round(nsv_obj_res[indices[i]]*factors[i],2)>=np.round(nsv_obj_res[indices[i+1]]*factors[i+1],2):
                hier_satisfied = False
    if hier_satisfied:
        print("hier: True")
    else:
        print("hier: False")

    print("netnet min:",min_netnet, get_overall_netnet_change_cons(res.x,*nn_args)>= min_netnet)
    print("netnet max:",max_netnet, max_netnet>= get_overall_netnet_change_cons(res.x,*nn_args))           
    print('min nn% : ' , get_overall_netnet_change_cons(lb_min_max,*nn_args))
    print('max nn% : ' , get_overall_netnet_change_cons(ub_min_max,*nn_args))

    print('opt nn%: ' , get_overall_netnet_change_cons(res.x,*nn_args))
    print('opt nn% rounded: ' , get_overall_netnet_change_cons(eval(round_up_fn)(res.x),*nn_args))
    print('initial guess nn% :',get_overall_netnet_change_cons(initial_guess,*nn_args))

    print('base nsv:', data['NSV'].sum())
    print('opt nsv:', nsv_objective(res.x,*obj_nsv_args)*data['NSV'].sum()/10)
    print('initial guess nsv:', nsv_objective(initial_guess,*obj_nsv_args)*data['NSV'].sum()/10)
    print('base tm', obj_tm_args[1].sum())
    print('opt tm', trade_margin_objective(res.x,*obj_tm_args)*obj_tm_args[1].sum()/10)
    print('initial guess tm', trade_margin_objective(initial_guess,*obj_tm_args)*obj_tm_args[1].sum()/10)
    print('base mac', data['MAC'].sum())
    print('opt mac', mac_objective(res.x,*obj_mac_args)*data['MAC'].sum()/10)
    print('initial guess mac:',mac_objective(initial_guess,*obj_mac_args)*data['MAC'].sum()/10)
    # breakpoint()
    if lp.LpStatus[lp_problem.status]=='Optimal':
        return opt_result_data, status, None, lp_problem
    else:
        return opt_result_data, status, report_solver1, lp_problem