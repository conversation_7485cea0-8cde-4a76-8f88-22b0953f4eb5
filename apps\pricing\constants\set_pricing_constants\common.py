from enum import Enum

LP_MULTIPLICATION_FACTOR = 1.2
NET_NET_MULTIPLICATION_FACTOR = 1
NATIONA_NN_INDEX=100

ROUND_OFF_COLUMN=[
    'changed_lp_percent',
    'changed_cogs_t_percent',
    'changed_non_promo_price',
    'changed_promo_price',
    ''
]
PRICING_CHANGED_SCENARIO_COLUMNS=[
    'pricing_saved_scenario_id',
    'level_param',
    "ogsm_param",
    'changed_non_promo_price',
    'changed_promo_price',
    'changed_lp_percent',
    'changed_pack_weight_kg_percent',
    'changed_cogs_t_percent',
    'status_flag',
    'type_of_price_inc',
    'competitor_follows',
    'list_price_change_percent',
    'list_price_change_percent_kg',
    'nsv_price_impact_direct',
    'nsv_price_impact_indirect',
    'nsv_price_impact_base',
    'new_lp_after_base_price',
    'type_of_base_inc_after_change',
    'list_price_new',
    'pack_weight_kg_new',
    'dead_net_new',
    'net_net_new',
    'non_promo_price_new',
    'promo_price_new',
    'changed_net_net_change_percent',
    'changed_dead_net_change_percent',
    'cogs_t_new'  ,
    'promo_sold_volume',
    'promo_sold_volume_new',
    'non_promo_sold_volume',
    'non_promo_sold_volume_new',
    'promo_sold_volume_percent',
    'promo_sold_volume_percent_new'
    # 'net_net_multiplication_factor',
    # 'lp_multiplication_factor'
]
class ChangedPricingScenario(Enum):
    MODEL_NAME = 'changed_pricing_scenario'
    MODEL_COLUMN = PRICING_CHANGED_SCENARIO_COLUMNS
    MODEL_VALUES = PRICING_CHANGED_SCENARIO_COLUMNS
    FK_MODEL = 'pricing_saved_scenario'
    FK_NAME = 'pricing_saved_scenario'
    YEAR_WISE = False
    FIN_YEAR_WISE = False
    REQUIRED_COLUMN = []
    EXTRA_COLUMNS = []