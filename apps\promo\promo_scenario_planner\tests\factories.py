""" Test promo Factories"""
import factory
from pytest_factoryboy import register
from apps.common import models
from django.core.cache import cache as base_data
from core.generics.respository import AbstractRepository #pylint: disable=E0401
from core.generics.unit_of_work import AbstractUnitOfWork #pylint: disable=E0401

sp_instance = base_data.get('scenario_planner',{})
save_active_promo_scenario = sp_instance.get('save_active_promo_scenario')
save_completed_promo_scenario = sp_instance.get('save_completed_promo_scenario')
save_active_promo_scenario_partial_data = sp_instance.get('save_active_promo_scenario_partial_data')
save_completed_promo_scenario_with_data = sp_instance.get('save_completed_promo_scenario_with_data')
save_completed_promo_scenario_with_data_for_api = sp_instance.get('save_completed_promo_scenario_with_data_for_api')
save_active_promo_scenario_partial_data_for_api = sp_instance.get('save_active_promo_scenario_partial_data_for_api')
promo_scenario_constraints_payload = sp_instance.get('promo_scenario_constraints_payload')
scenario_planner_payload = sp_instance.get('scenario_planner_payload')
data = sp_instance.get('data')
input_constraints = sp_instance.get('input_constraints')
 
@register
class SaveActivePromoScenarioFactory(factory.Factory):
    id= 5
    scenario_type =  "promo"
    promo_type="single"
    status_type="active"
    name = "promo save"
    comments = ""

    class Meta:
        model = models.SavedScenario

@register
class SaveCompletedPromoScenarioFactory(factory.Factory):
    id= 6
    scenario_type =  "promo"
    promo_type="single"
    status_type="completed"
    name = "promo save"
    comments = ""

    class Meta:
        model = models.SavedScenario

@register
class SaveActivePromoScenarioWoDataFactory(factory.Factory):
    id= 5
    saved_scenario_id=5
    data=[]
    input_constraints={}

    class Meta:
        model = models.ScenarioPromotionSave


@register
class SaveCompletePromoScenarioWithDataFactory(factory.Factory):
    id= 6
    saved_scenario_id=6
    input_constraints =  input_constraints
    data= data
    class Meta:
        model = models.ScenarioPromotionSave

class FakeModel:
    def __init__(self, model):
        super().__init__([])
        self._model = dict(model)
        super().__init__([])

    def add(self, entity):
        self._model[entity[0]] = entity[1:]

    def delete(self, _id):
        self._model.pop(_id)

    def get(self, _id=0):
        return [id] + self._model.get(_id, [])

    def update(self, entity):
        self._model.update({entity[0]: entity[1:]})


class FakeRepository(AbstractRepository):
    def __init__(self, model):
        super().__init__([])
        self._model = dict(model)
        super().__init__([])

    def add(self, entity):
        self._model[entity[0]] = entity[1:]

    def delete(self, _id):
        self._model.pop(_id)

    def get(self, _id=0):
        return [_id] + self._model.get(_id, [])

    def get_all(self):
        return [0] + self._model.get(0, [])

    def get_by_scenario_type(self,scenario_type,fetch_save_scenario=False):
        return [scenario_type] + self._model.get(0, [])

    def update(self, entity):
        self._model.update({entity[0]: entity[1:]})


class FakeUnitOfWork(AbstractUnitOfWork):
    def __init__(self):
        self.model = FakeRepository([])
        self.repo_obj = FakeRepository([])
        self.committed = False

    def commit(self):
        self.committed = True

    def rollback(self):
        return True

    def add(self, entity):
        self.model.add(entity)

    def get_data_dict(self):
        return self.model.get()

    def search(self,_query_string,params):
        """_summary_

        Args:
            _query_string (str): sql query string
            params (list): parameters

        Returns:
            list: searched data from db
        """
        if params:
            return [
                [i[0]] + list(i[1])
                for i in self.model._model.items()
                if i[1][0] in params
            ]
        
        return list(self.model._model.items())
