from rest_framework.serializers import ValidationError
import operator
from functools import reduce
from django.db.models import Q,Sum,F,ExpressionWrapper,FloatField,Avg,Max
from core.generics.respository import ORMModelRepository
from apps.pricing.set_pricing import models as db_model 

class SetPricingCommonViewRepository(ORMModelRepository):
    def __init__(self):
        super().__init__(db_model.SetPricingCommonView)
        
    def get_min_shelf_price(self,factor:int=0,ppgs=None):
        min_shelf_price_qry = self._model.filter(product_group__in=ppgs,exclude_retailer=False,is_outlier=False) if ppgs else self._model.filter()
        min_shelf_price_gby = (min_shelf_price_qry.values('product_group').annotate(
            min_shelf_price = Max(((F('floor_price')-(1-factor)*(F('net_net')*(1-F('promo_share'))+F('dead_net')*F('promo_share')))*F('non_promo_price_per_unit'))/(factor*(F('net_net')*(1-F('promo_share'))+F('dead_net')*F('promo_share')))))
        ).values('product_group','min_shelf_price').order_by()
    
        return list(min_shelf_price_gby.values('product_group','min_shelf_price'))
        
class SetPricingOptimizerViewRepository(ORMModelRepository):
    def __init__(self):
        super().__init__(db_model.SetPricingOptimizerView)
        
    def get_min_shelf_price(self,factor:int=0,ppgs=None):
        min_shelf_price_qry = self._model.filter(product_group__in=ppgs,exclude_retailer=False,is_outlier=False) if ppgs else self._model.filter()
        min_shelf_price_gby = (min_shelf_price_qry.values('product_group').annotate(
            min_shelf_price = Max(((F('floor_price')-(1-factor)*(F('net_net')*(1-F('promo_share'))+F('dead_net')*F('promo_share')))*F('non_promo_price_per_unit'))/(factor*(F('net_net')*(1-F('promo_share'))+F('dead_net')*F('promo_share')))))
        ).values('product_group','min_shelf_price').order_by()

        return list(min_shelf_price_gby.values('product_group','min_shelf_price'))
        
        
class SetPricingIncreaseScenarioRepository(ORMModelRepository):
    def __init__(self):
        super().__init__(db_model.SetPriceIncreaseView)

    def filter_by_ppg(self,ppgs):
        return self._model.filter(product_group__in=ppgs).order_by('-nsv_sum')
            
    def get_all(self, **kwargs):
        return self._model.filter().order_by('-nsv_sum')
    
    def get(self, _id):
        """ Filter data based on id"""
        return self._model.filter(id=_id).first()

    def filter_by_id(self, _id):
        """ Filter data based on scenario id"""
        return self._model.filter(id=_id)
    
    def filter_by_multiple_id(self, _ids):
        """ Filter data based on list of scenario ids"""
        return self._model.filter(id__in=_ids)
    

    def update(self, _id, entity):
        """ Update data based on id"""
        if self._model.filter(id=_id, is_delete=False).first():
            self._model.filter(id=_id).update(**entity)
        else:
            raise ValidationError("cannot update inactive records")
        
    def filter_by_ppg(self,ppg):
        return self._model.filter(product_group__in=ppg)

    def delete(self, _id):
        """ Delete data based on id"""
        if self._model.filter(id=_id, is_delete=False).first():
            self._model.filter(id=_id).update(is_delete=True)
            raise ValidationError("cannot delete inactive records")

    def get_by_scenario_type(self,scenario_type,user):
        return self._model.select_related('saved_scenario')\
            .filter(Q(saved_scenario__created_by__user_id=user['user_id'])|
                    Q(saved_scenario__shared_user__to__icontains=user['email'])\
                    ,saved_scenario__scenario_type=scenario_type\
                    ,is_delete=False\
                    )
    
    def get_my_scenario_by_scenario_type(self,scenario_type,user):
        return self._model.select_related('saved_scenario')\
            .filter(saved_scenario__created_by__user_id=user['user_id']\
                    ,saved_scenario__scenario_type=scenario_type\
                    ,is_delete=False\
                    )
    
    def get_shared_scenario_by_scenario_type(self,scenario_type,user):
        return self._model.select_related('saved_scenario')\
            .filter(saved_scenario__shared_user__to__icontains=user['email']\
                    ,saved_scenario__scenario_type=scenario_type\
                    ,is_delete=False\
                    )

class SetPricingIncreaseOptimizerScenarioRepository(ORMModelRepository):
    def __init__(self):
        super().__init__(db_model.SetPricingOptimizerPPGLevelView)

    def filter_by_ppg(self,ppgs):
        return self._model.filter(product_group__in=ppgs).order_by('-nsv_sum')
            
    def get_all(self, **kwargs):
        return self._model.filter().order_by('-nsv_sum')
    
    def get(self, _id):
        """ Filter data based on id"""
        return self._model.filter(id=_id).first()

    def filter_by_id(self, _id):
        """ Filter data based on scenario id"""
        return self._model.filter(id=_id)
    
    def filter_by_multiple_id(self, _ids):
        """ Filter data based on list of scenario ids"""
        return self._model.filter(id__in=_ids)
    

    def update(self, _id, entity):
        """ Update data based on id"""
        if self._model.filter(id=_id, is_delete=False).first():
            self._model.filter(id=_id).update(**entity)
        else:
            raise ValidationError("cannot update inactive records")
        
    def filter_by_ppg(self,ppg):
        return self._model.filter(product_group__in=ppg)

    def delete(self, _id):
        """ Delete data based on id"""
        if self._model.filter(id=_id, is_delete=False).first():
            self._model.filter(id=_id).update(is_delete=True)
            raise ValidationError("cannot delete inactive records")

    def get_by_scenario_type(self,scenario_type,user):
        return self._model.select_related('saved_scenario')\
            .filter(Q(saved_scenario__created_by__user_id=user['user_id'])|
                    Q(saved_scenario__shared_user__to__icontains=user['email'])\
                    ,saved_scenario__scenario_type=scenario_type\
                    ,is_delete=False\
                    )
    
    def get_my_scenario_by_scenario_type(self,scenario_type,user):
        return self._model.select_related('saved_scenario')\
            .filter(saved_scenario__created_by__user_id=user['user_id']\
                    ,saved_scenario__scenario_type=scenario_type\
                    ,is_delete=False\
                    )
    
    def get_shared_scenario_by_scenario_type(self,scenario_type,user):
        return self._model.select_related('saved_scenario')\
            .filter(saved_scenario__shared_user__to__icontains=user['email']\
                    ,saved_scenario__scenario_type=scenario_type\
                    ,is_delete=False\
                    )
  
class SetPricingPricingSavedScenarioRepository(ORMModelRepository):
    def __init__(self):
        super().__init__(db_model.SetPriceIncreaseView)

    def filter_by_scenario_name(self, scenario_name):
        """ Filter data based on scenario name"""
        return self._model.filter(name=scenario_name,is_delete=False)
    
    def filter_by_scenario_name_and_scenario_type(self, scenario_name,scenario_type):
        """ Filter data based on scenario name"""
        return self._model.filter(name=scenario_name,scenario_type=scenario_type,is_delete=False)
    
    def get_by_scenario_type(self,scenario_type,user):
        return self._model.prefetch_related('saved_promotion_save')\
            .filter(Q(created_by__user_id=user['user_id'])| Q(shared_user__to__icontains=user['email'])\
                    ,scenario_type=scenario_type\
                    ,is_delete=False)
    
    def get_my_scenario_by_scenario_type(self,scenario_type,user):
        return self._model.prefetch_related('saved_promotion_save')\
        .filter(created_by__user_id=user['user_id']\
                ,scenario_type=scenario_type\
                ,is_delete=False\
                )

    def get_shared_scenario_by_scenario_type(self,scenario_type,user):
        return self._model.prefetch_related('saved_promotion_save')\
            .filter(shared_user__to__icontains=user['email']\
                    ,scenario_type=scenario_type\
                    ,is_delete=False\
                    )
            
class PricingScenarioCommonRepository(ORMModelRepository):
    """ Meta Repository"""
    def __init__(self):
        super().__init__(db_model.SetPricingCommonView)

    def get(self, _id):
        """ Filter data based on id"""
        return self._model.filter(id=_id).first()
    
    def get_customer_ppg_scenario_data(self,customer_ppg_data:list=None):
        query = reduce(operator.or_, (Q(customer = cp['customer']) \
                                      |Q(product_group__in = cp.get('product_group',[])) \
                                        for cp in customer_ppg_data))
        return self._model.filter(query)
    
    def get_customer_scenario_data(self,customers:list=None):
        return self._model.filter( customer__in=customers).order_by('-nsv')
    
    def get_meta_data_ppg_level(self):
        """ Filter pricing"""
        return self._model.values_list('product_group',flat=True).distinct().order_by('-nsv')
    
    def get_meta_data_customer_ppg_level(self):
        """ Filter pricing"""
        return self._model.values('customer','product_group').distinct().order_by('-nsv')
    
    def verify_and_filter_customers_with_inner_mode_all(self,saved_id,customer):
        """ Filter pricing"""
        saved_scenario_queryset = self._model.prefetch_related('changed_saved_pricing_scenario').filter(id=saved_id)
        customers_qryset = saved_scenario_queryset.filter(customer_level_param = customer)
        if not customers_qryset.exists():
            return 0
        return customer
    
    def get_meta_data_bulk_data(self,customer_ppg_data:list=None):
        query = reduce(operator.or_, (Q(customer = cp['customer']) \
                                      |Q(product_group__in = cp.get('product_group',[])) \
                                        for cp in customer_ppg_data))
        return self._model.filter(query).values('ogsm_type','customer'\
                                                ,'product_group','brand'\
                                                ,'technology','dead_net','net_net'\
                                                ,'competitor_follows'\
                                                ,'type_of_price_inc'\
                                                ,'status_flag').distinct().order_by('-nsv')
    
    def filter_by_pricing_scenario_id(self,pricing_scenario_id):
        """ Filter data based on account name"""
        return self._model.filter(id=pricing_scenario_id,is_delete=False,is_active=True)
    
    
    def filter_by_region(self,region):
        """ Filter data based on account name"""
        query = reduce(operator.or_, (Q(customer__icontains = item) \
                                      & Q(is_delete=False) \
                                        & Q(is_active=True) for item in region))
        return self._model.filter(query)

    def filter_by_retailer_and_ppg(self,acc_name,ppg):
        """ Filter data based on retailer and ppg"""
        return self._model.filter(customer=acc_name,product_name=ppg,is_delete=False,is_active=True)

    def get_all(self, **kwargs):
        return self._model.filter()
     