"""
Excel Read and Write
"""
import datetime
import io

import openpyxl
import xlsxwriter
from django.db import transaction
from openpyxl.utils import get_column_letter
from xlsxwriter.utility import xl_range

import utils
from apps.common import models as db_model  # pylint: disable=E0401
from apps.common import services, unit_of_work  # pylint: disable=E0401
from apps.promo.promo_optimizer.generic import preprcoess_mapping, preprocess
from core.generics.resp_utils import get_type_of_promo  # pylint: disable=E0401

from . import constants as const


def _get_format_value_currency(currency:str):
    """Get format value currency

    Args:
        currency (str): currency symbol

    Returns:
        dict: format dict
    """
    return {'border': 1,
            'align': 'center',
            'text_wrap': True,
            'valign': 'vcenter',
            'num_format': f'[<999950]0.0,"K {currency}";[<*********]0.0,,"M {currency}";0.0,,,"B {currency}"'
            }


def _get_format_ng(currency:str):
    """Get format ng

    Args:
        currency (str): currency symbol

    Returns:
        dict: format dict
    """
    return {'border': 1,
            'align': 'center',
            'text_wrap': True,
            'valign': 'vcenter',
            'num_format': f'[>-999950]0.0,"K {currency}";[>-*********]0.0,,"M {currency}";0.0,,,"B {currency}"'}


def _get_summary_value_currency(currency:str):
    """Get summary value currency

    Args:
        currency (str): currency symbol

    Returns:
        dict: format dict
    """
    return {
        'bold': 1,
        'border': 1,
        'align': 'center',
        'text_wrap': True,
        'valign': 'vcenter',
        'num_format': f'[<999950]0.0,"K {currency}";[<*********]0.0,,"M {currency}";0.0,,,"B {currency}"'
    }


def _get_sheet_value(sheet:openpyxl.worksheet.worksheet.Worksheet,
                     row:int,
                     column:int,
                     is_string:bool=False):
    """Get sheet value

    Args:
        sheet (openpyxl.worksheet.worksheet.Worksheet): worksheet
        row (int): sheet row
        column (int): sheet column
        is_string (bool, optional): true or false. Defaults to False.

    Returns:
        _type_: validated output
    """
    value = sheet.cell(row=row, column=column).value
    if value == 'inf':
        return 0.0
    if(isinstance(value, float)):
        value = str(round(value, 25))
    if not value:
        if is_string:
            return ''
        return 0.0
    return value


def excel(data:dict, output:io.BytesIO):
    """Excel

    Args:
        data (dict): Dict consists of pricing data
        output (io.BytesIO): BytesIO

    Returns:
        io.BytesIO:pricing data
    """
    row_const = 5
    col_const = 1
    keys = list(data[0].keys())
    values = []
    for _d in data:
        values.append(list(_d.values()))
    workbook = xlsxwriter.Workbook(output)
    worksheet = workbook.add_worksheet()
    worksheet.hide_gridlines(2)
    merge_format_date = workbook.add_format({
        'bold': 1,
        'border': 1,
        'align': 'center',
        'valign': 'vcenter',
        'fg_color': 'yellow'})

    merge_format_app = workbook.add_format({
        'bold': 1,

        'align': 'center',
        'valign': 'vcenter'
    })
    merge_format_app.set_font_size(20)
    worksheet.set_column('B:D', 12)
    format2 = workbook.add_format({'bold': 1,
                                   'border': 1,
                                   'align': 'center',
                                   'valign': 'vcenter',
                                   'fg_color': 'blue'})

    format1 = workbook.add_format({'bold': 0,
                                   'border': 1,
                                   'align': 'center',
                                   'valign': 'vcenter',
                                   'text_wrap': 1})
    row = row_const
    col = col_const

    worksheet.merge_range('B2:D2', 'Downloaded on ' + dateformat(), merge_format_date)
    worksheet.merge_range('B3:E3', 'Price Simulator Tool', merge_format_app)

    # Iterate over the data and write it out row by row.
    for header in keys:
        worksheet.write(row, col,     " ".join(header.split("_")), format2)
        worksheet.set_row(row, 25)
        worksheet.set_column(col, col, 20)
        col += 1
    row += 1
    col = col_const
    for val in values:
        col = col_const
        for _v in val:
            worksheet.write(row, col, " ".join(_v.split("_")), format1)
            worksheet.set_row(row, 45)
            worksheet.set_column(col, col, 20)
            col += 1
        row += 1

    workbook.close()


def excel_summary(data:dict, output:io.BytesIO):
    """Excel Summary

    Args:
        data (dict): Dict consists of pricing data
        output (io.BytesIO): pricing data
    Returns:
        io.BytesIO:pricing data
    """
    row_const = 6
    col_const = 4
    workbook = xlsxwriter.Workbook(output)
    merge_format_date = workbook.add_format({
        'bold': 1,
        'align': 'center',
        'valign': 'vcenter',
    })

    merge_format_app = workbook.add_format({
        'bold': 1,

        'align': 'center',
        'valign': 'vcenter'
    })
    merge_format_app.set_font_size(20)
    worksheet = workbook.add_worksheet()
    worksheet.hide_gridlines(2)
    format_header = workbook.add_format({'bold': 1,
                                         'border': 1,
                                         'align': 'center',
                                         'valign': 'vcenter'})
    format_header.set_font_size(14)
    format_name = workbook.add_format({'bold': 1,
                                       'border': 1,
                                       'align': 'center',
                                       'valign': 'vcenter'})
    format_name.set_font_size(20)
    format_value = workbook.add_format({
        'border': 1,
        'align': 'center',
        'valign': 'vcenter'})

    row = row_const
    col = col_const

    worksheet.merge_range('B2:D2', 'Downloaded on ' + dateformat(), merge_format_date)
    worksheet.merge_range('B3:D3', 'Price Simulator Tool', merge_format_app)
    worksheet.merge_range('B4:D4', 'Comparison Summary', merge_format_app)
    zip_list = []
    data_val = list(data.values())
    for _i in data_val:
        zip_list.append(zip(_i["header"], _i["current"], _i["simulated"], _i["Absolute change"], _i["percent change"]))
    for idx, _z in enumerate(zip_list):
        worksheet.merge_range(row+1, col-4, row+4, col-2, data_val[idx]['name'], format_name)
        _write_excel(worksheet, row+1, col-1, "Current Value", format_header)
        _write_excel(worksheet, row+2, col-1, "Simulated Value", format_header)
        _write_excel(worksheet, row+3, col-1, "Absolute Change", format_header)
        _write_excel(worksheet, row+4, col-1, "Percent Change", format_header)
        for header, current, simulate, absc, per in _z:
            _write_excel(worksheet, row, col, " ".join(header.split("_")).title(), format_header)
            _write_excel(worksheet, row+1, col, current, format_value)
            _write_excel(worksheet, row+2, col, simulate, format_value)
            _write_excel(worksheet, row+3, col, absc, format_value)
            _write_excel(worksheet, row+4, col, per, format_value)
            col += 1
        row += 6
        col = col_const

    workbook.close()

def _write_excel(worksheet, row, col, val, _format):
    worksheet.write(row, col, val, _format)
    worksheet.set_row(row, 50)
    worksheet.set_column(col, col, 45)
   

def dateformat():
    _x = datetime.datetime.now()
    return _x.strftime("%b %d %Y %H:%M:%S")

def read_model_files(file,
                        slug_memory,
                        is_coeff,
                        is_model,
                        is_coeff_map):
    """Read model files

    Args:
        file (binary):  data file
        slug_memory (dict): slug
        is_coeff (bool): true or false
        is_model (bool): true or false
        is_coeff_map (bool): true or false

    Returns:
        dict: slug_memory
    """
    if is_coeff:
        slug_memory = read_promo_coeff(file, slug_memory)
    if is_model:
        slug_memory = read_promo_data(file, slug_memory)
    if is_coeff_map:
        slug_memory = read_coeff_map(file, slug_memory)
    return slug_memory


def read_roi_data(file, slug_memory):
    """Read roi data

    Args:
        file (binary): roi data  file
        slug_memory (dict): slug
    """
    services.bulk_update(unit_of_work.ModelROIUnitOfWork(transaction))  # pylint: disable=no-member
    headers = const.ROI_HEADER
    book = openpyxl.load_workbook(file, data_only=True)
    sheet = book['ROI_DATA']
    rows = sheet.max_row
    row_ = 1
    black_listslugs = []
    bulk_obj = []

    for row in range(row_+1, rows+1):
        slg = utils.generate_slug_string(
            _get_sheet_value(sheet, row, headers.index('Retailer') + 1),
            _get_sheet_value(sheet, row, headers.index('Segment') + 1),
            preprocess(_get_sheet_value(sheet ,row ,  headers.index('PPG') + 1))
        )
        print(slg, "current slug")

        try:
            if slg not in black_listslugs:
                if slg in slug_memory:
                    meta = slug_memory[slg]
                else:
                    try:
                        print("hitting query")
                        meta = db_model.ModelMeta.objects.get(  # pylint: disable=no-member
                            slug=slg
                        )
                        meta.brand = _get_sheet_value(sheet, row, headers.index('Brand') + 1)
                        meta.brand_tech = _get_sheet_value(sheet, row, headers.index('Brand_Tech') + 1)
                        meta.product_type = _get_sheet_value(sheet, row, headers.index('Product_Type') + 1)

                        meta.save()
                        slug_memory[slg] = meta
                    except Exception as _e:
                        meta = db_model.ModelMeta(
                            account_name=_get_sheet_value(sheet, row, headers.index('Retailer') + 1),
                            corporate_segment=_get_sheet_value(sheet, row, headers.index('Segment') + 1),
                            product_group=preprocess(_get_sheet_value(sheet ,row ,  headers.index('PPG') + 1)),
                            brand=_get_sheet_value(sheet, row, headers.index('Brand') + 1),
                            brand_tech = _get_sheet_value(sheet, row, headers.index('Brand_Tech') + 1),
                            product_type = _get_sheet_value(sheet, row, headers.index('Product_Type') + 1),
                            slug=slg
                        )
                        meta.save()
                        slug_memory[slg] = meta
                bulk_obj.append(
                    db_model.ModelROI(
                        model_meta = meta,
                        date=_get_sheet_value(sheet ,row , headers.index('Date') + 1),
                        year = int(float(_get_sheet_value(sheet ,row ,  headers.index('Year') + 1))),
                        period = _get_sheet_value(sheet ,row ,  headers.index('Period') + 1),  
                        quarter = _get_sheet_value(sheet ,row ,  headers.index('Quarter') + 1),                      
                        week = int(float(_get_sheet_value(sheet ,row ,  headers.index('Week') + 1))),
                        list_price = int(float(_get_sheet_value(sheet ,row ,  headers.index('List Price') + 1))),
                        total_trade_investment = int(float(_get_sheet_value(sheet ,row ,  headers.index('Total Trade Investment') + 1))),
                        gsv = int(float(_get_sheet_value(sheet ,row ,  headers.index('GSV') + 1))),
                        nsv=_get_sheet_value(sheet ,row , headers.index('NSV') + 1),
                        volume=_get_sheet_value(sheet ,row , headers.index('Volume') + 1),
                        units=_get_sheet_value(sheet ,row , headers.index('Units') + 1),
                        nsv_per_unit_future=_get_sheet_value(sheet ,row , headers.index('NSV_Per_Unit_Future') + 1),
                        cogs_per_unit_future=_get_sheet_value(sheet ,row , headers.index('COGS_Per_Unit_Future') + 1),
                        gsv_per_unit_future=_get_sheet_value(sheet ,row , headers.index('GSV_Per_Unit_Future') + 1),
                        cogs=_get_sheet_value(sheet ,row , headers.index('COGS') + 1),
                        total_sold_unit=_get_sheet_value(sheet ,row , headers.index('total_sold_unit') + 1),
                        total_sold_volume=_get_sheet_value(sheet ,row , headers.index('total_sold_volume') + 1),
                        pack_weight =_get_sheet_value(sheet ,row , headers.index('Pack Weight') + 1),
                        promo_price = int(float(_get_sheet_value(sheet ,row ,  headers.index('Promo_Price_Per_Unit') + 1))),

                    )
                )
            # db_model.ModelROI.objects.bulk_create(bulk_obj)# pylint: disable=no-member
        except Exception as _e:
            import pdb
            pdb.set_trace()
            print(_e, "exception message")
            print("black listing slug", slg)
            black_listslugs.append(slg)
    
    db_model.ModelROI.objects.bulk_create(bulk_obj, batch_size=1000)  # pylint: disable=no-member
    meta_data = services.get_model_data(unit_of_work.CoeffUnitOfWork(transaction))  # pylint: disable=no-member
    meta_ids = meta_data.values_list('model_meta_id',flat=True)
    services.bulk_inactive(unit_of_work.MetaUnitOfWork(transaction),meta_ids)
    print(black_listslugs, "black listed slugs")
    print(" updated ")

    book.close()


def read_promo_coeff(file, slug_memory):
    """Read promo coefficient

    Args:
        file (binary): promo coefficient
        slug_memory (dict): slug

    Returns:
        dict: slug memory
    """
    services.bulk_update(unit_of_work.CoeffUnitOfWork(transaction))
    headers = const.COEFF_HEADER
    book = openpyxl.load_workbook(file, data_only=True)
    sheet = book['MODEL_COEFFICIENT']
    columns = sheet.max_column
    rows = sheet.max_row
    col_ = []
    row_ = 1
    header_found = False
    for row in range(1, rows+1):
        for col in range(1, columns+1):
            cell_obj = sheet.cell(row=row, column=col)
            if cell_obj.value in headers:
                header_found = True
                col_.append(col)
        if header_found:
            break

    bulk_obj = []

    for row in range(row_+1, rows+1):
        slg = utils.generate_slug_string(
            _get_sheet_value(sheet, row, headers.index('Retailer') + 1),
            _get_sheet_value(sheet, row, headers.index('Segment') + 1),
            preprocess(_get_sheet_value(sheet ,row ,  headers.index('PPG') + 1))
        )
        
        if slg in slug_memory:
            meta = slug_memory[slg]
        else:
            try:
                print("hitting query")
                
                meta = db_model.ModelMeta.objects.get(slug=slg)
                meta.is_delete = False
                meta.brand = _get_sheet_value(sheet, row, headers.index('Brand') + 1)
                meta.brand_tech = _get_sheet_value(sheet, row, headers.index('Brand_Tech') + 1)
                meta.product_type = _get_sheet_value(sheet, row, headers.index('Product_Type') + 1)
                meta.save()
                slug_memory[slg] = meta
            except Exception as _e:
                meta = db_model.ModelMeta(
                    account_name=_get_sheet_value(sheet, row, headers.index('Retailer') + 1),
                    corporate_segment=_get_sheet_value(sheet, row, headers.index('Segment') + 1),
                    product_group=preprocess(_get_sheet_value(sheet ,row ,  headers.index('PPG') + 1)),
                    brand=_get_sheet_value(sheet, row, headers.index('Brand') + 1),
                    brand_tech = _get_sheet_value(sheet, row, headers.index('Brand_Tech') + 1),
                    product_type = _get_sheet_value(sheet, row, headers.index('Product_Type') + 1),
                    slug=slg
                )
                meta.save()
                slug_memory[slg] = meta
        
        bulk_obj.append(db_model.ModelCoefficient(
            model_meta = meta,
            wmape = _get_sheet_value(sheet ,row ,headers.index('WMAPE') + 1),
            rsq= _get_sheet_value(sheet ,row ,headers.index('Rsq') + 1),
            acv_selling = _get_sheet_value(sheet ,row ,headers.index('ACV_Selling') + 1),
            c_1_promoted_discount = _get_sheet_value(sheet ,row ,headers.index('C_1_promoted_discount') +1),
            c_1_regular_price = _get_sheet_value(sheet ,row ,headers.index('C_1_regular_price') + 1),
            c_2_promoted_discount = _get_sheet_value(sheet ,row ,headers.index('C_2_promoted_discount') +1),
            c_2_regular_price = _get_sheet_value(sheet ,row ,headers.index('C_2_regular_price') +1),
            c_3_promoted_discount = _get_sheet_value(sheet ,row ,headers.index('C_3_promoted_discount') +1),
            c_3_regular_price = _get_sheet_value(sheet ,row ,headers.index('C_3_regular_price') +1),
            c_4_regular_price = _get_sheet_value(sheet ,row ,headers.index('C_4_regular_price') +1),
            down_t_lag = _get_sheet_value(sheet ,row ,headers.index('Down_t_lag') +1),
            flag_promotype_leaflet = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_Leaflet') +1),
            flag_promotype_advertising_without_price = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_advertising_without_price') +1),
            flag_promotype_all_brand = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_all_brand') +1),
            flag_promotype_back_page = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_back_page') +1),
            flag_promotype_bonus = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_bonus') +1),
            flag_promotype_coupon = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_coupon') +1),
            flag_promotype_edlp = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_edlp') +1),
            flag_promotype_front_page = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_front_page') +1),
            flag_promotype_joint_promo_1 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_1') +1),
            flag_promotype_joint_promo_10 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_10') +1),
            flag_promotype_joint_promo_11 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_11') +1),
            flag_promotype_joint_promo_12 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_12') +1),
            flag_promotype_joint_promo_13 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_13') +1),
            flag_promotype_joint_promo_14 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_14') +1),
            flag_promotype_joint_promo_15 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_15') +1),
            flag_promotype_joint_promo_2 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_2') +1),
            flag_promotype_joint_promo_3 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_3') +1),
            flag_promotype_joint_promo_4 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_4') +1),
            flag_promotype_joint_promo_5 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_5') +1),
            flag_promotype_joint_promo_6 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_6') +1),
            flag_promotype_joint_promo_7 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_7') +1),
            flag_promotype_joint_promo_8 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_8') +1),
            flag_promotype_joint_promo_9 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_9') +1),
            flag_promotype_logo = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_logo') +1),
            flag_promotype_multibuy = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_multibuy') +1),
            flag_promotype_newsletter = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_newsletter') +1),
            flag_promotype_pas = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_pas') +1),
            holiday_Flag_1 = _get_sheet_value(sheet ,row ,headers.index('Holiday_Flag1') +1),
            holiday_Flag_2 = _get_sheet_value(sheet ,row ,headers.index('Holiday_Flag2') +1),
            holiday_Flag_3 = _get_sheet_value(sheet ,row ,headers.index('Holiday_Flag3') +1),
            holiday_Flag_4 = _get_sheet_value(sheet ,row ,headers.index('Holiday_Flag4') +1),
            holiday_flag1 = _get_sheet_value(sheet ,row ,headers.index('Holiday_flag1') +1),
            holiday_flag2 = _get_sheet_value(sheet ,row ,headers.index('Holiday_flag2') +1),
            holiday_flag3 = _get_sheet_value(sheet ,row ,headers.index('Holiday_flag3') +1),
            holiday_flag4 = _get_sheet_value(sheet ,row ,headers.index('Holiday_flag4') +1),
            holiday_flag5 = _get_sheet_value(sheet ,row ,headers.index('Holiday_flag5') +1),
            holiday_flag6 = _get_sheet_value(sheet ,row ,headers.index('Holiday_flag6') +1),
            holiday_flag7 = _get_sheet_value(sheet ,row ,headers.index('Holiday_flag7') +1),
            median_base_price_log = _get_sheet_value(sheet ,row ,headers.index('Median_Base_Price_log') + 1),
            non_promo_flag_date_1 = _get_sheet_value(sheet ,row ,headers.index('NonPromo_flag_date_1') +1),
            non_promo_flag_date_2 = _get_sheet_value(sheet ,row ,headers.index('NonPromo_flag_date_2') +1),
            non_promo_flag_date_3 = _get_sheet_value(sheet ,row ,headers.index('NonPromo_flag_date_3') +1),
            non_promo_flag_date_4 = _get_sheet_value(sheet ,row ,headers.index('NonPromo_flag_date_4') +1),
            nov_2022_trend = _get_sheet_value(sheet ,row ,headers.index('Nov_2022_trend') +1),
            nov_trend = _get_sheet_value(sheet ,row ,headers.index('Nov_trend') +1),
            oct_2022_trend = _get_sheet_value(sheet ,row ,headers.index('Oct_2022_trend') +1),
            oct_trend = _get_sheet_value(sheet ,row ,headers.index('Oct_trend') +1),
            p09_2022 = _get_sheet_value(sheet ,row ,headers.index('P09_2022') +1),
            p10_11_2022 = _get_sheet_value(sheet ,row ,headers.index('P10_11_2022') +1),
            p10_2022 = _get_sheet_value(sheet ,row ,headers.index('P10_2022') +1),
            p10_2022_trend = _get_sheet_value(sheet ,row ,headers.index('P10_2022_trend') +1),
            p11_2022 = _get_sheet_value(sheet ,row ,headers.index('P11_2022') +1),
            p11_2022_trend = _get_sheet_value(sheet ,row ,headers.index('P11_2022_trend') +1),
            p9_2022 = _get_sheet_value(sheet ,row ,headers.index('P9_2022') +1),
            p9_2022_trend = _get_sheet_value(sheet ,row ,headers.index('P9_2022_trend') +1),
            promo_flag_1 = _get_sheet_value(sheet ,row ,headers.index('Promo_Flag_1') +1),
            promo_flag_2 = _get_sheet_value(sheet ,row ,headers.index('Promo_Flag_2') +1),
            promo_flag_3 = _get_sheet_value(sheet ,row ,headers.index('Promo_Flag_3') +1),
            promo_flag_date_1 = _get_sheet_value(sheet ,row ,headers.index('Promo_flag_date_1') +1),
            promo_flag_date_2 = _get_sheet_value(sheet ,row ,headers.index('Promo_flag_date_2') +1),
            promo_flag_date_3 = _get_sheet_value(sheet ,row ,headers.index('Promo_flag_date_3') +1),
            promo_flag_date_4 = _get_sheet_value(sheet ,row ,headers.index('Promo_flag_date_4') +1),
            promo_flag_date_correction_2020_08_29 = _get_sheet_value(sheet ,row ,headers.index('Promo_flag_date_correction_2020_08_29') +1),
            promo_flag_date_correction_2020_10_10 = _get_sheet_value(sheet ,row ,headers.index('Promo_flag_date_correction_2020_10_10') +1),
            promo_flag_date_correction_2022_04_23 = _get_sheet_value(sheet ,row ,headers.index('Promo_flag_date_correction_2022_04_23') +1),
            promo_flag_date_correction_2022_08_13 = _get_sheet_value(sheet ,row ,headers.index('Promo_flag_date_correction_2022_08_13') +1),
            quarter_trend_2021_1 = _get_sheet_value(sheet ,row ,headers.index('Quarter_Trend_2021') +1),
            si_week = _get_sheet_value(sheet ,row ,headers.index('SI') + 1),
            si_period = _get_sheet_value(sheet ,row ,headers.index('SI_Period') + 1),
            si_quarter = _get_sheet_value(sheet ,row ,headers.index('SI_quarterly') + 1),
            sept_2022_trend = _get_sheet_value(sheet ,row ,headers.index('Sept_2022_trend') + 1),
            tpr_discount_byppg = _get_sheet_value(sheet ,row ,headers.index('TPR') + 1),
            tpr_discount_byppg_10_above = _get_sheet_value(sheet ,row ,headers.index('TPR_10_above') + 1),
            tpr_discount_byppg_5_10 = _get_sheet_value(sheet ,row ,headers.index('TPR_5_10') + 1),
            tpr_discount_byppg_lag1 = _get_sheet_value(sheet ,row ,headers.index('TPR_lag_1') + 1),
            tpr_discount_byppg_lag2 = _get_sheet_value(sheet ,row ,headers.index('TPR_lag_2') + 1),
            y_2020 = _get_sheet_value(sheet ,row ,headers.index('Y_2020') + 1),
            y_2021 = _get_sheet_value(sheet ,row ,headers.index('Y_2021') + 1),
            y_2022 = _get_sheet_value(sheet ,row ,headers.index('Y_2022') + 1),
            year_trend = _get_sheet_value(sheet ,row ,headers.index('Year_Trend') + 1),
            year_trend_2020 = _get_sheet_value(sheet ,row ,headers.index('Year_Trend_2020') + 1),
            year_trend_2022 = _get_sheet_value(sheet ,row ,headers.index('Year_Trend_2022') + 1),
            down_promo_flag = _get_sheet_value(sheet ,row ,headers.index('down_promo_flag') + 1),
            mars_period = _get_sheet_value(sheet ,row ,headers.index('mars_period') + 1),
            mars_period_2021_first_half_trend = _get_sheet_value(sheet ,row ,headers.index('mars_period_2021_first_half_trend') + 1),
            mars_period_2021_second_half_trend = _get_sheet_value(sheet ,row ,headers.index('mars_period_2021_second_half_trend') + 1),
            mars_quarter = _get_sheet_value(sheet ,row ,headers.index('mars_quarter') + 1),
            quarter2_2022 = _get_sheet_value(sheet ,row ,headers.index('quarter2_2022') + 1),
            quarter_4_2022 = _get_sheet_value(sheet ,row ,headers.index('quarter_4_2022') + 1),
            quarter_trend = _get_sheet_value(sheet ,row ,headers.index('quarter_trend') + 1),
            quarter_trend_2019 = _get_sheet_value(sheet ,row ,headers.index('quarter_trend_2019') + 1),
            quarter_trend_2020 = _get_sheet_value(sheet ,row ,headers.index('quarter_trend_2020') + 1),
            quarter_trend_2021 = _get_sheet_value(sheet ,row ,headers.index('quarter_trend_2021') + 1),
            tpr_discount_byppg_2019 = _get_sheet_value(sheet ,row ,headers.index('tpr_discount_byppg_2019') + 1),
            year_trend_less_than_2022 = _get_sheet_value(sheet ,row ,headers.index('year_trend_less_than_2022') + 1),
            intercept = _get_sheet_value(sheet ,row ,headers.index('Intercept') + 1),
        )
        )

    db_model.ModelCoefficient.objects.bulk_create(bulk_obj)  # pylint: disable=no-member
    print(" updated ")
    book.close()
    return slug_memory


def read_promo_data(file, slug_memory):
    """Read promo data

    Args:
        file (binary): promo data
        slug_memory (dict): slug

    Returns:
        dict : slug memory
    """
    # services.bulk_update(unit_of_work.ModelDataUnitOfWork(transaction))  # pylint: disable=no-member
    headers = const.DATA_HEADER[:-1]
    book = openpyxl.load_workbook(file, data_only=True)
    sheet = book['MODEL_DATA']
    columns = sheet.max_column
    rows = sheet.max_row
    col_ = []
    row_ = 1
    header_found = False
    for row in range(1, rows+1):

        print(row, "row count")
        for col in range(1, columns+1):
            print(col, "column count")
            cell_obj = sheet.cell(row=row, column=col)
            if cell_obj.value in headers:
                header_found = True
                print(cell_obj.value, 'object value')
                col_.append(col)
        if header_found:
            break
    print(col_, "coldddd")

    bulk_obj = []
    for row in range(row_+1, rows+1):
        try:
            slg = utils.generate_slug_string(
                _get_sheet_value(sheet, row, headers.index('Retailer') + 1),
                _get_sheet_value(sheet, row, headers.index('Segment') + 1),
                preprocess(_get_sheet_value(sheet ,row ,  headers.index('PPG') + 1))
            )

            if slg in slug_memory:
                meta = slug_memory[slg]
            else:
                try:
                    print("hitting query")
                    meta = db_model.ModelMeta.objects.get(  # pylint: disable=no-member
                        slug=slg
                    )
                    meta.brand_tech = _get_sheet_value(sheet, row, headers.index('Brand_Tech') + 1)
                    meta.product_type = _get_sheet_value(sheet, row, headers.index('Product_Type') + 1)
                    meta.save()
                    slug_memory[slg] = meta
                except Exception as _e:
                    meta = db_model.ModelMeta(
                        account_name=_get_sheet_value(sheet, row, headers.index('Retailer') + 1),
                        corporate_segment=_get_sheet_value(sheet, row, headers.index('Segment') + 1),
                        product_group=preprocess(_get_sheet_value(sheet ,row ,  headers.index('PPG') + 1)),
                        brand=_get_sheet_value(sheet, row, headers.index('Brand') + 1),
                        brand_tech = _get_sheet_value(sheet, row, headers.index('Brand_Tech') + 1),
                        product_type = _get_sheet_value(sheet, row, headers.index('Product_Type') + 1),
                        slug=slg

                    )
                    meta.save()
                    slug_memory[slg] = meta

            print("creating bulkobject for slug" + slg)
            
            bulk_obj.append(db_model.ModelData(
                    model_meta = meta,
                    year = int(float(_get_sheet_value(sheet ,row , headers.index('Year') + 1))),
                    date= _get_sheet_value(sheet ,row ,  headers.index('Date') + 1),
                    month= _get_sheet_value(sheet ,row ,  headers.index('Month') + 1),
                    week= int(float(_get_sheet_value(sheet ,row ,  headers.index('Week') + 1))),
                    wk_sold_doll_byppg = _get_sheet_value(sheet ,row , headers.index('wk_sold_doll_byppg') + 1),
                    wk_sold_qty_byppg = _get_sheet_value(sheet ,row , headers.index('wk_sold_qty_byppg') + 1),
                    wk_sold_avg_price_byppg = _get_sheet_value(sheet ,row , headers.index('wk_sold_avg_price_byppg') + 1),
                    wk_sold_qty_byppg_log = _get_sheet_value(sheet ,row , headers.index('wk_sold_qty_byppg_log') + 1),
                    acv_selling = _get_sheet_value(sheet ,row ,headers.index('ACV_Selling') + 1),
                c_1_promoted_discount = _get_sheet_value(sheet ,row ,headers.index('C_1_promoted_discount') +1),
                c_1_regular_price = _get_sheet_value(sheet ,row ,headers.index('C_1_regular_price') + 1),
                c_2_promoted_discount = _get_sheet_value(sheet ,row ,headers.index('C_2_promoted_discount') +1),
                c_2_regular_price = _get_sheet_value(sheet ,row ,headers.index('C_2_regular_price') +1),
                c_3_promoted_discount = _get_sheet_value(sheet ,row ,headers.index('C_3_promoted_discount') +1),
                c_3_regular_price = _get_sheet_value(sheet ,row ,headers.index('C_3_regular_price') +1),
                c_4_regular_price = _get_sheet_value(sheet ,row ,headers.index('C_4_regular_price') +1),
                down_t_lag = _get_sheet_value(sheet ,row ,headers.index('Down_t_lag') +1),
                flag_promotype_leaflet = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_Leaflet') +1),
                flag_promotype_advertising_without_price = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_advertising_without_price') +1),
                flag_promotype_all_brand = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_all_brand') +1),
                flag_promotype_back_page = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_back_page') +1),
                flag_promotype_bonus = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_bonus') +1),
                flag_promotype_coupon = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_coupon') +1),
                flag_promotype_edlp = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_edlp') +1),
                flag_promotype_front_page = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_front_page') +1),
                flag_promotype_joint_promo_1 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_1') +1),
                flag_promotype_joint_promo_10 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_10') +1),
            flag_promotype_joint_promo_11 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_11') +1),
            flag_promotype_joint_promo_12 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_12') +1),
            flag_promotype_joint_promo_13 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_13') +1),
            flag_promotype_joint_promo_14 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_14') +1),
            flag_promotype_joint_promo_15 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_15') +1),
            flag_promotype_joint_promo_2 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_2') +1),
            flag_promotype_joint_promo_3 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_3') +1),
            flag_promotype_joint_promo_4 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_4') +1),
            flag_promotype_joint_promo_5 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_5') +1),
            flag_promotype_joint_promo_6 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_6') +1),
            flag_promotype_joint_promo_7 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_7') +1),
            flag_promotype_joint_promo_8 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_8') +1),
            flag_promotype_joint_promo_9 = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_joint_promo_9') +1),
            flag_promotype_logo = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_logo') +1),
            flag_promotype_multibuy = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_multibuy') +1),
            flag_promotype_newsletter = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_newsletter') +1),
            flag_promotype_pas = _get_sheet_value(sheet ,row ,headers.index('Flag_promotype_pas') +1),
            holiday_Flag_1 = _get_sheet_value(sheet ,row ,headers.index('Holiday_Flag1') +1),
            holiday_Flag_2 = _get_sheet_value(sheet ,row ,headers.index('Holiday_Flag2') +1),
            holiday_Flag_3 = _get_sheet_value(sheet ,row ,headers.index('Holiday_Flag3') +1),
            holiday_Flag_4 = _get_sheet_value(sheet ,row ,headers.index('Holiday_Flag4') +1),
            holiday_flag1 = _get_sheet_value(sheet ,row ,headers.index('Holiday_flag1') +1),
            holiday_flag2 = _get_sheet_value(sheet ,row ,headers.index('Holiday_flag2') +1),
            holiday_flag3 = _get_sheet_value(sheet ,row ,headers.index('Holiday_flag3') +1),
            holiday_flag4 = _get_sheet_value(sheet ,row ,headers.index('Holiday_flag4') +1),
            holiday_flag5 = _get_sheet_value(sheet ,row ,headers.index('Holiday_flag5') +1),
            holiday_flag6 = _get_sheet_value(sheet ,row ,headers.index('Holiday_flag6') +1),
            holiday_flag7 = _get_sheet_value(sheet ,row ,headers.index('Holiday_flag7') +1),
            median_base_price_log = _get_sheet_value(sheet ,row ,headers.index('Median_Base_Price_log') + 1),
            non_promo_flag_date_1 = _get_sheet_value(sheet ,row ,headers.index('NonPromo_flag_date_1') +1),
            non_promo_flag_date_2 = _get_sheet_value(sheet ,row ,headers.index('NonPromo_flag_date_2') +1),
            non_promo_flag_date_3 = _get_sheet_value(sheet ,row ,headers.index('NonPromo_flag_date_3') +1),
            non_promo_flag_date_4 = _get_sheet_value(sheet ,row ,headers.index('NonPromo_flag_date_4') +1),
            nov_2022_trend = _get_sheet_value(sheet ,row ,headers.index('Nov_2022_trend') +1),
            nov_trend = _get_sheet_value(sheet ,row ,headers.index('Nov_trend') +1),
            oct_2022_trend = _get_sheet_value(sheet ,row ,headers.index('Oct_2022_trend') +1),
            oct_trend = _get_sheet_value(sheet ,row ,headers.index('Oct_trend') +1),
            p09_2022 = _get_sheet_value(sheet ,row ,headers.index('P09_2022') +1),
            p10_11_2022 = _get_sheet_value(sheet ,row ,headers.index('P10_11_2022') +1),
            p10_2022 = _get_sheet_value(sheet ,row ,headers.index('P10_2022') +1),
            p10_2022_trend = _get_sheet_value(sheet ,row ,headers.index('P10_2022_trend') +1),
            p11_2022 = _get_sheet_value(sheet ,row ,headers.index('P11_2022') +1),
            p11_2022_trend = _get_sheet_value(sheet ,row ,headers.index('P11_2022_trend') +1),
            p9_2022 = _get_sheet_value(sheet ,row ,headers.index('P9_2022') +1),
            p9_2022_trend = _get_sheet_value(sheet ,row ,headers.index('P9_2022_trend') +1),
            promo_flag_1 = _get_sheet_value(sheet ,row ,headers.index('Promo_Flag_1') +1),
            promo_flag_2 = _get_sheet_value(sheet ,row ,headers.index('Promo_Flag_2') +1),
            promo_flag_3 = _get_sheet_value(sheet ,row ,headers.index('Promo_Flag_3') +1),
            promo_flag_date_1 = _get_sheet_value(sheet ,row ,headers.index('Promo_flag_date_1') +1),
            promo_flag_date_2 = _get_sheet_value(sheet ,row ,headers.index('Promo_flag_date_2') +1),
            promo_flag_date_3 = _get_sheet_value(sheet ,row ,headers.index('Promo_flag_date_3') +1),
            promo_flag_date_4 = _get_sheet_value(sheet ,row ,headers.index('Promo_flag_date_4') +1),
            promo_flag_date_correction_2020_08_29 = _get_sheet_value(sheet ,row ,headers.index('Promo_flag_date_correction_2020_08_29') +1),
            promo_flag_date_correction_2020_10_10 = _get_sheet_value(sheet ,row ,headers.index('Promo_flag_date_correction_2020_10_10') +1),
            promo_flag_date_correction_2022_04_23 = _get_sheet_value(sheet ,row ,headers.index('Promo_flag_date_correction_2022_04_23') +1),
            promo_flag_date_correction_2022_08_13 = _get_sheet_value(sheet ,row ,headers.index('Promo_flag_date_correction_2022_08_13') +1),
            quarter_trend_2021_1 = _get_sheet_value(sheet ,row ,headers.index('Quarter_Trend_2021') +1),
            si_week = _get_sheet_value(sheet ,row ,headers.index('SI') + 1),
            si_period = _get_sheet_value(sheet ,row ,headers.index('SI_Period') + 1),
            si_quarter = _get_sheet_value(sheet ,row ,headers.index('SI_quarterly') + 1),
            sept_2022_trend = _get_sheet_value(sheet ,row ,headers.index('Sept_2022_trend') + 1),
            tpr_discount_byppg = _get_sheet_value(sheet ,row ,headers.index('TPR') + 1),
            tpr_discount_byppg_10_above = _get_sheet_value(sheet ,row ,headers.index('TPR_10_above') + 1),
            tpr_discount_byppg_5_10 = _get_sheet_value(sheet ,row ,headers.index('TPR_5_10') + 1),
            tpr_discount_byppg_lag1 = _get_sheet_value(sheet ,row ,headers.index('TPR_lag_1') + 1),
            tpr_discount_byppg_lag2 = _get_sheet_value(sheet ,row ,headers.index('TPR_lag_2') + 1),
            y_2020 = _get_sheet_value(sheet ,row ,headers.index('Y_2020') + 1),
            y_2021 = _get_sheet_value(sheet ,row ,headers.index('Y_2021') + 1),
            y_2022 = _get_sheet_value(sheet ,row ,headers.index('Y_2022') + 1),
            year_trend = _get_sheet_value(sheet ,row ,headers.index('Year_Trend') + 1),
            year_trend_2020 = _get_sheet_value(sheet ,row ,headers.index('Year_Trend_2020') + 1),
            year_trend_2022 = _get_sheet_value(sheet ,row ,headers.index('Year_Trend_2022') + 1),
            down_promo_flag = _get_sheet_value(sheet ,row ,headers.index('down_promo_flag') + 1),
            mars_period = _get_sheet_value(sheet ,row ,headers.index('mars_period') + 1),
            mars_period_2021_first_half_trend = _get_sheet_value(sheet ,row ,headers.index('mars_period_2021_first_half_trend') + 1),
            mars_period_2021_second_half_trend = _get_sheet_value(sheet ,row ,headers.index('mars_period_2021_second_half_trend') + 1),
            mars_quarter = _get_sheet_value(sheet ,row ,headers.index('mars_quarter') + 1),
            quarter2_2022 = _get_sheet_value(sheet ,row ,headers.index('quarter2_2022') + 1),
            quarter_4_2022 = _get_sheet_value(sheet ,row ,headers.index('quarter_4_2022') + 1),
            quarter_trend = _get_sheet_value(sheet ,row ,headers.index('quarter_trend') + 1),
            quarter_trend_2019 = _get_sheet_value(sheet ,row ,headers.index('quarter_trend_2019') + 1),
            quarter_trend_2020 = _get_sheet_value(sheet ,row ,headers.index('quarter_trend_2020') + 1),
            quarter_trend_2021 = _get_sheet_value(sheet ,row ,headers.index('quarter_trend_2021') + 1),
            tpr_discount_byppg_2019 = _get_sheet_value(sheet ,row ,headers.index('tpr_discount_byppg_2019') + 1),
            year_trend_less_than_2022 = _get_sheet_value(sheet ,row ,headers.index('year_trend_less_than_2022') + 1)      
            )
            )
        except Exception as _e:
            print(_e)
            import pdb
            pdb.set_trace()
            print(_e,row)

    db_model.ModelData.objects.bulk_create(bulk_obj, batch_size=1000)  # pylint: disable=no-member

    print(" updated ")

    book.close()
    return slug_memory


def read_coeff_map(file, slug_memory):
    """Read Coefficient map

    Args:
        file (binary): coefficient map  file
        slug_memory (dict): slug

    Returns:
        dict: slug memory
    """
    # services.bulk_update(unit_of_work.CoeffMapUnitOfWork(transaction))  # pylint: disable=no-member
    headers = const.COEFF_MAP_HEADER
    book = openpyxl.load_workbook(file, data_only=True)
    sheet = book['COEFF_MAPPING']
    columns = sheet.max_column
    rows = sheet.max_row
    col_ = []
    row_ = 1
    header_found = False
    for row in range(1, rows+1):
        for col in range(1, columns+1):
            cell_obj = sheet.cell(row=row, column=col)
            if cell_obj.value in headers:
                header_found = True
                col_.append(col)
        if header_found:
            break
    bulk_obj = []
    for row in range(row_+1, rows+1):
        # slg = utils.generate_slug_string(
        #     _get_sheet_value(sheet, row, 1),
        #     _get_sheet_value(sheet, row, 2),
        #     preprocess(_get_sheet_value(sheet, row, 3))
        # )

    
        meta = db_model.ModelMeta.objects.get(id=_get_sheet_value(sheet, row, 1))
        print("creating bulkobject")
        bulk_obj.append(db_model.CoefficientMapping(
            model_meta=meta,
            coefficient_old=preprcoess_mapping(_get_sheet_value(sheet, row, 2)),
            coefficient_new=_get_sheet_value(sheet, row, 3),
            value=_get_sheet_value(sheet, row, 4),
            ppg_item_no=_get_sheet_value(sheet, row, 5)
        )
        )

    db_model.CoefficientMapping.objects.bulk_create(bulk_obj)  # pylint: disable=no-member

    print(" updated ")

    book.close()
    print("returned...")
    return slug_memory


def read_tactic_data(file, slug_memory):
    """Read Coefficient map

    Args:
        file (binary): coefficient map  file
        slug_memory (dict): slug

    Returns:
        dict: slug memory
    """
    services.bulk_update(unit_of_work.TacticUnitOfWork(transaction))  # pylint: disable=no-member
    headers = const.TACTIC_HEADER
    book = openpyxl.load_workbook(file, data_only=True)
    sheet = book['TACTIC']
    columns = sheet.max_column
    rows = sheet.max_row
    col_ = []
    row_ = 1
    header_found = False
    for row in range(1, rows+1):
        for col in range(1, columns+1):
            cell_obj = sheet.cell(row=row, column=col)
            if cell_obj.value in headers:
                header_found = True
                col_.append(col)
        if header_found:
            break
    bulk_obj = []
    try:
        for row in range(row_+1, rows+1):
            retailer_ppg_mapping = db_model.RetailerPPGMapping.objects.get(
            retailer_index= _get_sheet_value(sheet ,row ,  headers.index('Retailer') + 1),
            ppg_index= _get_sheet_value(sheet ,row ,  headers.index('PPG') + 1),
            is_delete=False
            )
            print("creating bulkobject",_get_sheet_value(sheet ,row ,  headers.index('PPG') + 1))
            try:
                bulk_obj.append(db_model.ModelTactic(
                    retailer_ppg_map = retailer_ppg_mapping,
                    promo_type= _get_sheet_value(sheet ,row ,  headers.index('Promo_Type') + 1),
                    avg_units_per_week= _get_sheet_value(sheet ,row ,  headers.index('Avg_units_per_Week') + 1),
                    tpr_discount_byppg= _get_sheet_value(sheet ,row ,  headers.index('TPR') + 1),
                    leaflet_flag= _get_sheet_value(sheet ,row ,  headers.index('Leaflet_flag') + 1),
                    newsletter_flag= _get_sheet_value(sheet ,row ,  headers.index('Newsletter_flag') + 1),
                    advertising_without_price_flag= _get_sheet_value(sheet ,row ,  headers.index('Advertising_without_price_flag') + 1),
                    bonus_flag= _get_sheet_value(sheet ,row ,  headers.index('Bonus_flag') + 1),
                    pas_flag= _get_sheet_value(sheet ,row ,  headers.index('Pas_flag') + 1),
                    coupon_flag= _get_sheet_value(sheet ,row ,  headers.index('Coupon_flag') + 1),
                    edlp_flag= _get_sheet_value(sheet ,row ,  headers.index('EDLP_flag') + 1),
                    multibuy_flag= _get_sheet_value(sheet ,row ,  headers.index('Multibuy_flag') + 1),
                    te_per_week= _get_sheet_value(sheet ,row ,  headers.index('TE_perweek') + 1),
                    list_price_per_week= _get_sheet_value(sheet ,row ,  headers.index('ListPrice_perweek') + 1),
                    cogs_per_week= _get_sheet_value(sheet ,row ,  headers.index('COGS_perweek') + 1),
                    max_week= _get_sheet_value(sheet ,row ,  headers.index('maxWeek') + 1),
                    min_week= _get_sheet_value(sheet ,row ,  headers.index('minWeek') + 1),
                    min_week_edlp= _get_sheet_value(sheet ,row ,  headers.index('minWeek_edlp') + 1),
                    max_week_edlp= _get_sheet_value(sheet ,row ,  headers.index('maxWeek_edlp') + 1),
                    promo_flag= _get_sheet_value(sheet ,row ,  headers.index('Promo_Flag') + 1),
                    gsv_per_week= _get_sheet_value(sheet ,row ,  headers.index('GSV_perweek') + 1),
                    is_all_brand= _get_sheet_value(sheet ,row ,  headers.index('is_all_brand') + 1)
                )
                )
            except Exception as e:
                print(e,'exc')
                import pdb

                pdb.set_trace()

        db_model.ModelTactic.objects.bulk_create(bulk_obj)  # pylint: disable=no-member
    except Exception as e:
        print(e,'ee')
        import pdb
        pdb.set_trace()
        print(e,'ee')

    print(" updated ")

    book.close()
    print("returned...")
    return slug_memory

def read_retailer_ppg_mapping_data(file, slug_memory):
    """Read Coefficient map

    Args:
        file (binary): coefficient map  file
        slug_memory (dict): slug

    Returns:
        dict: slug memory
    """
    services.bulk_update(unit_of_work.RetailerPPGMappingUnitOfWork(transaction))  # pylint: disable=no-member
    headers = const.RETAILER_PPG_MAPPING_HEADER
    book = openpyxl.load_workbook(file, data_only=True)
    sheet = book['RETAILER_PPG_MAPPING']
    columns = sheet.max_column
    rows = sheet.max_row
    col_ = []
    row_ = 1
    header_found = False
    for row in range(1, rows+1):
        for col in range(1, columns+1):
            cell_obj = sheet.cell(row=row, column=col)
            if cell_obj.value in headers:
                header_found = True
                col_.append(col)
        if header_found:
            break
    bulk_obj = []
    for row in range(row_+1, rows+1):
        print("creating bulkobject")
        bulk_obj.append(db_model.RetailerPPGMapping(
            account_name= _get_sheet_value(sheet ,row ,  headers.index('Retailer') + 1),
            product_group= preprocess(_get_sheet_value(sheet ,row ,  headers.index('PPG') + 1)),
            retailer_index= _get_sheet_value(sheet ,row ,  headers.index('Ret_idx') + 1),
            ppg_index= _get_sheet_value(sheet ,row ,  headers.index('PPG_idx') + 1),
            type_of_promo = get_type_of_promo(str(_get_sheet_value(sheet ,row ,  headers.index('PPG_idx') + 1))),
        )
        )

    db_model.RetailerPPGMapping.objects.bulk_create(bulk_obj)  # pylint: disable=no-member

    print(" updated ")

    book.close()
    print("returned...")
    return slug_memory

def read_item_mapping_data(file, slug_memory):
    """Read Coefficient map

    Args:
        file (binary): coefficient map  file
        slug_memory (dict): slug

    Returns:
        dict: slug memory
    """
    services.bulk_update(unit_of_work.ItemMapUnitOfWork(transaction))  # pylint: disable=no-member
    headers = const.ITEM_MAPPING_HEADER
    book = openpyxl.load_workbook(file, data_only=True)
    sheet = book['ITEM_MAP']
    columns = sheet.max_column
    rows = sheet.max_row
    col_ = []
    row_ = 1
    header_found = False
    for row in range(1, rows+1):
        for col in range(1, columns+1):
            cell_obj = sheet.cell(row=row, column=col)
            if cell_obj.value in headers:
                header_found = True
                col_.append(col)
        if header_found:
            break
    bulk_obj = []
    for row in range(row_+1, rows+1):
    
        print("creating bulkobject")
        
        bulk_obj.append(db_model.ItemMap(
            account_name= _get_sheet_value(sheet ,row ,  headers.index('Retailer') + 1),
            product_group= preprocess(_get_sheet_value(sheet ,row ,  headers.index('PPG') + 1)),
            ppg_item_no= _get_sheet_value(sheet ,row ,  headers.index('PPG_Item_No') + 1),
        )
        )

    db_model.ItemMap.objects.bulk_create(bulk_obj)  # pylint: disable=no-member

    print(" updated ")

    book.close()
    print("returned...")
    return slug_memory

def get_summary_data(**kwargs):

    _aggregrated_list = []
    for _i,k in enumerate(kwargs['total_header']):
        _obj = {}
        if k in kwargs['percent_header']:
            change = (kwargs['simulated_total'][k]/100)-(kwargs['base_total'][k]/100)
            _obj['base_scenario'] = kwargs['base_total'][k]/100
            _obj['recommended_scenario'] = kwargs['simulated_total'][k]/100
            _obj['delta'] = (change/kwargs['base_total'][k])*100 if change > 0 else 0
            _obj['change'] = change

            _obj['base_scenario_raw'] = kwargs['base_total'][k]/100
            _obj['recommended_scenario_raw'] = kwargs['simulated_total'][k]/100
            _obj['delta_raw'] = change/kwargs['base_total'][k] if change > 0 else 0
            _obj['change_raw'] = change

        elif k in kwargs['currency_header']:
            change = kwargs['simulated_total'][k]-kwargs['base_total'][k]
            _obj['base_scenario'] = kwargs['base_total'][k]
            _obj['recommended_scenario'] = kwargs['simulated_total'][k]

            _obj['base_scenario_raw'] = kwargs['base_total'][k]
            _obj['recommended_scenario_raw'] = kwargs['simulated_total'][k]
            if change < 0:
                _obj['change'] = change
                _obj['delta'] = (change/kwargs['base_total'][k]) * 100 if change > 0 else 0

                _obj['change_raw'] = change
                _obj['delta_raw'] = change/kwargs['base_total'][k] if change > 0 else 0

            else:
                _obj['change'] = change
                _obj['delta'] = (change/kwargs['base_total'][k]) * 100 if change > 0 else 0

                _obj['change_raw'] = change
                _obj['delta_raw'] = change/kwargs['base_total'][k] if change > 0 else 0

        elif k in kwargs['decimal_header']:

            change = kwargs['simulated_total'][k]-kwargs['base_total'][k]
            _obj['base_scenario'] = kwargs['base_total'][k]
            _obj['recommended_scenario'] = kwargs['simulated_total'][k]

            _obj['base_scenario_raw'] = kwargs['base_total'][k]
            _obj['recommended_scenario_raw'] = kwargs['simulated_total'][k]
            if change < 0:
                _obj['change']=change
                _obj['delta']= (change/kwargs['base_total'][k]) * 100 if change > 0 else 0

                _obj['change_raw']=change
                _obj['delta_raw']=change/kwargs['base_total'][k] if change > 0 else 0
            else:
                _obj['change']=change
                _obj['delta']= 0 if change == 0 else (change/kwargs['base_total'][k]) * 100 if change > 0 else 0

                _obj['change_raw']=change
                _obj['delta_raw']= 0 if change == 0 else change/kwargs['base_total'][k] if change > 0 else 0
        else:
            change = kwargs['simulated_total'][k]-kwargs['base_total'][k]
            _obj['base_scenario'] = kwargs['base_total'][k]
            _obj['recommended_scenario'] = kwargs['simulated_total'][k]

            _obj['base_scenario_raw'] = kwargs['base_total'][k]
            _obj['recommended_scenario_raw'] = kwargs['simulated_total'][k]
            if change < 0:
                _obj['change']=change
                _obj['delta']=(change/kwargs['base_total'][k]) * 100 if change > 0 else 0

                _obj['change_raw']=change
                _obj['delta_raw']=change/kwargs['base_total'][k] if change > 0 else 0
            else:
                _obj['change']=change
                _obj['delta']= 0 if change == 0 else (change/kwargs['base_total'][k]) * 100 if change > 0 else 0

                _obj['change_raw']=change
                _obj['delta_raw']= 0 if change == 0 else change/kwargs['base_total'][k] if change > 0 else 0
        _obj['header'] = k
        _obj['account_name'] = kwargs['account_name']
        _obj['product_group'] = kwargs['product_group']
        _aggregrated_list.append(_obj)
    return _aggregrated_list

class TotalSummary():
    def __init__(self):
        self.account_name = ''
        self.product_group = ''
        self.total_baseline = dict()
        self.total_baseline_raw = dict()
        self.total_recomanded = dict()
        self.total_recomanded_raw = dict()
        self.total_change = dict()
        self.total_change_raw = dict()
        self.total_delta = dict()
        self.total_delta_raw = dict()

class UnitSummary():
    def __init__(self):
        self.account_name = ''
        self.product_group = ''
        self.total_baseline = dict()
        self.total_baseline_raw = dict()
        self.total_recomanded = dict()
        self.total_recomanded_raw = dict()
        self.total_change = dict()
        self.total_change_raw = dict()
        self.total_delta = dict()
        self.total_delta_raw = dict()

def _total_summary(total_summary:TotalSummary,unit_summary:UnitSummary,key):

    if not key in total_summary.total_baseline:
        total_summary.total_baseline[key] = 0
        total_summary.total_recomanded[key] = 0
        total_summary.total_change[key] = 0
        total_summary.total_delta[key] = 0
        total_summary.total_baseline_raw[key] = 0
        total_summary.total_recomanded_raw[key] = 0
        total_summary.total_change_raw[key] = 0
        total_summary.total_delta_raw[key] = 0
    total_summary.account_name = unit_summary.account_name
    total_summary.product_group = unit_summary.product_group
    total_summary.total_baseline[key] = total_summary.total_baseline[key] + unit_summary.total_baseline[key]
    total_summary.total_recomanded[key] = total_summary.total_recomanded[key] + unit_summary.total_recomanded[key]
    total_summary.total_change[key] = total_summary.total_change[key] + unit_summary.total_change[key]
    total_summary.total_delta[key] = total_summary.total_delta[key] + unit_summary.total_delta[key]
    total_summary.total_baseline_raw[key] = total_summary.total_baseline_raw[key] + unit_summary.total_baseline_raw[key]
    total_summary.total_recomanded_raw[key] = total_summary.total_recomanded_raw[key] + unit_summary.total_recomanded_raw[key]
    total_summary.total_change_raw[key] = total_summary.total_change_raw[key] + unit_summary.total_change_raw[key]
    total_summary.total_delta_raw[key] = total_summary.total_delta_raw[key] + unit_summary.total_delta_raw[key]

def calculate_summary_data(**kwargs):
    total_summary = TotalSummary()
    unit_summary_data = []
    for _index,_d_list in  enumerate(kwargs['summary_data_list']):
        unit_summary = UnitSummary()
        
        for val_obj in _d_list:
            unit_summary.account_name = val_obj['account_name']
            unit_summary.product_group = val_obj['product_group']
            unit_summary.total_baseline[val_obj['header']] = val_obj['base_scenario']
            unit_summary.total_recomanded[val_obj['header']] = val_obj['recommended_scenario']
            unit_summary.total_delta[val_obj['header']] = val_obj['delta']
            unit_summary.total_change[val_obj['header']] = val_obj['change']
            unit_summary.total_baseline_raw[val_obj['header']] = val_obj['base_scenario_raw']
            unit_summary.total_recomanded_raw[val_obj['header']] = val_obj['recommended_scenario_raw']
            unit_summary.total_delta_raw[val_obj['header']] = val_obj['delta_raw']
            unit_summary.total_change_raw[val_obj['header']] = val_obj['change_raw']
            _total_summary(total_summary,unit_summary,val_obj['header'])
        unit_summary_data.append(unit_summary)
    return total_summary,unit_summary_data
    
def download_excel_optimizer(data):
    output = io.BytesIO()
    summary_all_list = []
    workbook = xlsxwriter.Workbook(output)
    percent_header = ['RP_Perc', 'Mac_Perc']
    decimal_header = ['roi']
    currency_header = ['total_rsv_w_o_vat', 'lsv', 'nsv','mac', 'te', 'rp']
    percent_header = ['lift_percent']
    total_header = ['units', 'lift_percent','roi','total_rsv_w_o_vat','rp','lsv','nsv','mac','te']
    header_key = ['Units', 'Lift%','ROI','RSV','Trade Margin','LSV','NSV','MAC','Trade Expense']

    optimized_cal = list(col for col in range(1,53))
    optimized_cal.insert(0,'Week')
    ROW_CONST = 6
    COL_CONST = 1
    _optimial_cal_list = []
    
    for _index, _d in enumerate(data['data']):
        
        simulated_weekly = _d['simulated']['weekly']

        merge_format_date = workbook.add_format({
            'bold': 1,
            'align': 'center',
            'valign': 'vcenter',
        })

        merge_format_app = workbook.add_format({
            'bold': 1,
            'align': 'center',
            'valign': 'vcenter'
        })

        merge_format_app.set_font_size(20)
        account_name = _d['account_name']
        product_group = _d['product_group']

        simulated_total = _d['simulated']['total']
        base_total = _d['base']['total']

        format_header = workbook.add_format({'bold': 1,
                                             'border': 1,
                                             'align': 'center',
                                             'valign': 'vcenter'})
        format_header.set_font_size(14)
        format_name = workbook.add_format({'bold': 1,
                                           'border': 1,
                                           'align': 'center',
                                           'valign': 'vcenter'})
        format_name.set_font_size(20)
        format_value = workbook.add_format({
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
            'text_wrap': True})
        format_value_raw_value = workbook.add_format({
            'border': 1,
            'bold': 1,
            'align': 'center',
            'valign': 'vcenter',
            'text_wrap': True})
        format_value_raw_value.set_font_size(14)

        row = ROW_CONST
        col = COL_CONST

        format_value_percentage = workbook.add_format(
            {'border': 1, 'align': 'center', 'text_wrap': True, 'valign': 'vcenter', 'num_format': '0.00 %'})

        format_value_currency = workbook.add_format({'border': 1, 'align': 'center', 'text_wrap': True,
                                                     'valign': 'vcenter',
                                                      'num_format': '[<999950]0.0,"K €";[<*********]0.0,,"M €";0.0,,,"B €"'})
        summary_value_percentage = workbook.add_format(
            {'bold': 1, 'border': 1, 'align': 'center', 'text_wrap': True, 'valign': 'vcenter', 'num_format': '0.00 %'})
        summary_value_percentage.set_font_size(14)

        summary_value_currency = workbook.add_format({'bold': 1, 'border': 1, 'align': 'center', 'text_wrap': True,
                                                      'valign': 'vcenter', 
                                                      'num_format': '[<999950]0.0,"K €";[<*********]0.0,,"M €";0.0,,,"B €"'})
        summary_value_currency.set_font_size(14)

        summary_value_number = workbook.add_format({'bold': 1, 'border': 1, 'align': 'center', 'text_wrap': True,
                                                    'valign': 'vcenter', 
                                                    'num_format': '[<999950]0.0,"K";[<*********]0.0,,"M";0.0,,,"B"'})
        summary_value_number.set_font_size(14)

        summary_data = get_summary_data(
            account_name = account_name,
            product_group = product_group,
            simulated_total=simulated_total,
            base_total=base_total,
            total_header=total_header,
            percent_header=percent_header,
            currency_header=currency_header,
            decimal_header=decimal_header,
        )
        summary_all_list.append(summary_data)
        _optimial_cal_list.append({'product_group':product_group,'weekly_data':simulated_weekly})
      
        if len(data['data']) == len(summary_all_list):
            #Optimal Calendar
            optimial_cal_worksheet = workbook.add_worksheet('Optimal_calendar')
            optimial_cal_worksheet.hide_gridlines(2)
            optimial_cal_worksheet.merge_range('B2:N2', 'Downloaded on {}'.format(
                dateformat()), merge_format_date)
            optimial_cal_worksheet.merge_range(
                'B3:N3', 'Promo Optimizer Tool', merge_format_app)
            optimial_cal_worksheet.set_column('B:N', 20)
            optimial_cal_worksheet.merge_range('B4:N4', "Account Name : {}".format(
                account_name), merge_format_app)
    
            row += 1

            for key in optimized_cal:
                _write_excel(optimial_cal_worksheet, row, col-1, key, format_header)
                col += 1
            row += 1
            col = COL_CONST

            for _indx,opt in enumerate(_optimial_cal_list):
                _write_excel(optimial_cal_worksheet, row+_indx, col-1, opt['product_group'], format_header)

            for key in optimized_cal[1:]:
                for _indx,opt in enumerate(_optimial_cal_list):
                    if key>len(opt['weekly_data']):
                        simulated_week={}
                        simulated_week['mechanic'] = ''
                        simulated_week['type_of_promo'] = ''
                    else:
                        simulated_week = opt['weekly_data'][key-1]

                    mech = simulated_week['mechanic']
                    promo_type = simulated_week['type_of_promo'].title()
                    _write_excel(optimial_cal_worksheet, row+_indx, col\
                        ,f'{promo_type} - {mech}' , format_value)
                    optimial_cal_worksheet.set_column(key, key, 20)
                col+=1

            # Summary
            row = ROW_CONST
            col = COL_CONST
            total_summary_data,unit_summary_data = calculate_summary_data(
                summary_data_list=summary_all_list)

            worksheet = workbook.add_worksheet(f'Summary')
            worksheet.hide_gridlines(2)

            worksheet.merge_range('B2:D2', 'Downloaded on {}'.format(
                dateformat()), merge_format_date)
            worksheet.merge_range(
                'B3:D3', 'Promo Optimizer Tool', merge_format_app)
            worksheet.set_column('B:D', 20)
            worksheet.merge_range('B4:D4', "Account Name : {}".format(
                account_name), merge_format_app)

            for val in header_key:
                if val == 'Lift%' or val == 'ROI':
                    continue
                _write_excel(worksheet, row, col, val, format_header)
                col += 1
            col = COL_CONST
            row += 1
            _write_excel(worksheet, row, col-1, account_name, format_header)
      
            base_total = total_summary_data.total_baseline
            simulated_total = total_summary_data.total_recomanded
            change = total_summary_data.total_change

            for k in total_header:
                if k == 'lift_percent' or k == 'roi':
                    continue
                comb_values = [base_total[k],simulated_total[k]]
                if k in percent_header:
                    comb_values.append(change[k])
                    numbers = utils.convert_to_perc(comb_values)
                    numbers[2] = f'({numbers[2]})'
                    _write_excel(worksheet,row, col, '\n'.join(numbers), format_value_percentage)
                    col += 1
                elif k in currency_header:
                    comb_values.append(change[k])
                    numbers = utils.currency_format(comb_values,const.CURRENCY,decimals=2)
                    numbers[2] = f'({numbers[2]})'
                    _write_excel(worksheet,row, col, '\n'.join(numbers), format_value_currency)
                    col += 1
                elif k in decimal_header:
                    comb_values.append(change[k])
                    numbers = utils.format_decimals(comb_values)
                    numbers[2] = f'({numbers[2]})'
                    _write_excel(worksheet,row, col, '\n'.join(numbers), format_value)
                    col += 1
                else:
                    comb_values.append(change[k])
                    numbers = utils.numerize_numbers(comb_values,decimals=2)
                    numbers[2] = f'({numbers[2]})'
                    _write_excel(worksheet,row, col, '\n'.join(numbers), format_value)
                    col += 1

            row = 12
            col = COL_CONST

            for val in header_key:
                _write_excel(worksheet, row, col, val, format_header)
                col += 1
    
            for ppg_wise_data in unit_summary_data:
                
                col = COL_CONST
                row += 1
                _write_excel(worksheet, row, col-1, ppg_wise_data.product_group, format_header)

                base_total = ppg_wise_data.total_baseline
                simulated_total = ppg_wise_data.total_recomanded
                change = ppg_wise_data.total_change

                for k in total_header:
                    comb_values = [base_total[k],simulated_total[k]]
                    if k in percent_header:
                        comb_values.append(change[k])
                        numbers = utils.convert_to_perc(comb_values)
                        numbers[2] = f'({numbers[2]})'
                        _write_excel(worksheet,row, col, '\n'.join(numbers), format_value_percentage)
                        col += 1
                    elif k in currency_header:
                        comb_values.append(change[k])
                        numbers = utils.currency_format(comb_values,const.CURRENCY)
                        numbers[2] = f'({numbers[2]})'
                        _write_excel(worksheet,row, col, '\n'.join(numbers), format_value_currency)
                        col += 1
                    elif k in decimal_header:
                        comb_values.append(change[k])
                        numbers = utils.format_decimals(comb_values)
                        numbers[2] = f'({numbers[2]})'
                        _write_excel(worksheet,row, col, '\n'.join(numbers), format_value)
                        col += 1
                    else:
                        comb_values.append(change[k])
                        numbers = utils.numerize_numbers(comb_values)
                        numbers[2] = f'({numbers[2]})'
                        _write_excel(worksheet,row, col, '\n'.join(numbers), format_value)
                        col += 1
                    
    workbook.close()
    output.seek(0)
    return output

def download_excel_promo(data):
    output = io.BytesIO()
    summary_all_list = []
    workbook = xlsxwriter.Workbook(output)
    percent_header = ['RP_Perc', 'Mac_Perc']
    decimal_header = ['roi']
    currency_header = ['total_rsv_w_o_vat', 'lsv', 'nsv','mac', 'te', 'rp']
    percent_header = ['lift_percent']
    total_header = ['units', 'lift_percent','roi','total_rsv_w_o_vat','rp','lsv','nsv','mac','te']
    header_key = ['Units', 'Lift%','ROI','RSV','Trade Margin','LSV','NSV','MAC','Trade Expense']

    optimized_cal = list(col for col in range(1,53))
    optimized_cal.insert(0,'Week')
    ROW_CONST = 6
    COL_CONST = 1
    _optimial_cal_list = []
    for _index, _d in enumerate(data['data']):

        simulated_weekly = _d['simulated']['weekly']

        merge_format_date = workbook.add_format({
            'bold': 1,
            'align': 'center',
            'valign': 'vcenter',
        })

        merge_format_app = workbook.add_format({
            'bold': 1,
            'align': 'center',
            'valign': 'vcenter'
        })

        merge_format_cell = workbook.add_format({
            'bold': 1,
            'align': 'center'
        })

        merge_format_app.set_font_size(20)
        account_name = _d['account_name']
        product_group = _d['product_group']

        simulated_total = _d['simulated']['total']
        base_total = _d['base']['total']

        format_header = workbook.add_format({'bold': 1,
                                             'border': 1,
                                             'align': 'center',
                                             'valign': 'vcenter'})
        format_header.set_font_size(14)
        format_name = workbook.add_format({'bold': 1,
                                           'border': 1,
                                           'align': 'center',
                                           'valign': 'vcenter'})
        format_name.set_font_size(20)
        format_value = workbook.add_format({
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
            'text_wrap': True})
        format_value_raw_value = workbook.add_format({
            'border': 1,
            'bold': 1,
            'align': 'center',
            'valign': 'vcenter',
            'text_wrap': True})
        format_value_raw_value.set_font_size(14)

        row = ROW_CONST
        col = COL_CONST

        format_value_percentage = workbook.add_format(
            {'border': 1, 'align': 'center', 'text_wrap': True, 'valign': 'vcenter', 'num_format': '0.00 %'})

        format_value_currency = workbook.add_format({'border': 1, 'align': 'center', 'text_wrap': True,
                                                     'valign': 'vcenter', 
                                                     'num_format': '[<999950]0.0,"K €";[<*********]0.0,,"M €";0.0,,,"B €"'})
        summary_value_percentage = workbook.add_format(
            {'bold': 1, 'border': 1, 'align': 'center', 'text_wrap': True, 'valign': 'vcenter', 'num_format': '0.00 %'})
        summary_value_percentage.set_font_size(14)

        summary_value_currency = workbook.add_format({'bold': 1, 'border': 1, 'align': 'center', 'text_wrap': True,
                                                      'valign': 'vcenter', 
                                                      'num_format': '[<999950]0.0,"K €";[<*********]0.0,,"M €";0.0,,,"B €"'})
        summary_value_currency.set_font_size(14)

        summary_value_number = workbook.add_format({'bold': 1, 'border': 1, 'align': 'center', 'text_wrap': True,
                                                    'valign': 'vcenter', 
                                                    'num_format': '[<999950]0.0,"K";[<*********]0.0,,"M";0.0,,,"B"'})
        summary_value_number.set_font_size(14)

        summary_data = get_summary_data(
            account_name = account_name,
            product_group = product_group,
            simulated_total=simulated_total,
            base_total=base_total,
            total_header=total_header,
            percent_header=percent_header,
            currency_header=currency_header,
            decimal_header=decimal_header,
        )
        summary_all_list.append(summary_data)
        _optimial_cal_list.append({'product_group':product_group,'weekly_data':simulated_weekly})

        if len(data['data']) == len(summary_all_list):
            #Promo Simulator Calendar
            optimial_cal_worksheet = workbook.add_worksheet('promo_simulator_calendar')
            optimial_cal_worksheet.hide_gridlines(2)
            optimial_cal_worksheet.merge_range('B2:N2', 'Downloaded on {}'.format(
                dateformat()), merge_format_date)
            optimial_cal_worksheet.merge_range(
                'B3:N3', 'Promo Simulator Tool', merge_format_app)
            optimial_cal_worksheet.set_column('B:N', 20)
            optimial_cal_worksheet.merge_range('B4:N4', "Account Name : {}".format(
                account_name), merge_format_app)
    
            row += 1

            for key in optimized_cal:
                _write_excel(optimial_cal_worksheet, row, col-1, key, format_header)
                col += 1
            row += 1
            col = COL_CONST

            for _indx,opt in enumerate(_optimial_cal_list):
                _write_excel(optimial_cal_worksheet, row+_indx, col-1, opt['product_group'], format_header)

            for key in optimized_cal[1:]:
                for _indx,opt in enumerate(_optimial_cal_list):
                    if key>len(opt['weekly_data']):
                        simulated_week={}
                        simulated_week['mechanic'] = ''
                        simulated_week['type_of_promo'] = ''
                    else:
                        simulated_week = opt['weekly_data'][key-1]
                    mech = simulated_week['mechanic']
                    promo_type = simulated_week['type_of_promo'].title()
                    _write_excel(optimial_cal_worksheet, row+_indx, col\
                        ,f'{promo_type} - {mech}' , format_value)
                    optimial_cal_worksheet.set_column(key, key, 20)
                col+=1

            # Summary
            row = ROW_CONST
            col = COL_CONST
            total_summary_data,unit_summary_data = calculate_summary_data(
                summary_data_list=summary_all_list)

            worksheet = workbook.add_worksheet(f'Summary')
            worksheet.hide_gridlines(2)

            worksheet.merge_range('B2:D2', 'Downloaded on {}'.format(
                dateformat()), merge_format_date)
            worksheet.merge_range(
                'B3:D3', 'Promo Optimizer Tool', merge_format_app)
            worksheet.set_column('B:D', 20)
            worksheet.merge_range('B4:D4', "Account Name : {}".format(
                account_name), merge_format_app)

            for val in header_key:
                if val == 'Lift%' or val == 'ROI':
                    continue
                _write_excel(worksheet, row, col, val, format_header)
                col += 1
            col = COL_CONST
            row += 1
            _write_excel(worksheet, row, col-1, account_name, format_header)
      
            base_total = total_summary_data.total_baseline
            simulated_total = total_summary_data.total_recomanded
            change = total_summary_data.total_change

            for k in total_header:
                if k == 'lift_percent' or k == 'roi':
                    continue
                comb_values = [base_total[k],simulated_total[k]]
                if k in percent_header:
                    comb_values.append(change[k])
                    numbers = utils.convert_to_perc(comb_values)
                    numbers[2] = f'({numbers[2]})'
                    _write_excel(worksheet,row, col, '\n'.join(numbers), format_value_percentage)
                    col += 1
                elif k in currency_header:
                    comb_values.append(change[k])
                    numbers = utils.currency_format(comb_values,const.CURRENCY,decimals=2)
                    numbers[2] = f'({numbers[2]})'
                    _write_excel(worksheet,row, col, '\n'.join(numbers), format_value_currency)
                    col += 1
                elif k in decimal_header:
                    comb_values.append(change[k])
                    numbers = utils.format_decimals(comb_values)
                    numbers[2] = f'({numbers[2]})'
                    _write_excel(worksheet,row, col, '\n'.join(numbers), format_value)
                    col += 1
                else:
                    comb_values.append(change[k])
                    numbers = utils.numerize_numbers(comb_values,decimals=2)
                    numbers[2] = f'({numbers[2]})'
                    _write_excel(worksheet,row, col, '\n'.join(numbers), format_value)
                    col += 1

            
            row = 12
            col = COL_CONST

            for val in header_key:
                _write_excel(worksheet, row, col, val, format_header)
                col += 1
    
            for ppg_wise_data in unit_summary_data:
                
                col = COL_CONST
                row += 1
                _write_excel(worksheet, row, col-1, ppg_wise_data.product_group, format_header)

                base_total = ppg_wise_data.total_baseline
                simulated_total = ppg_wise_data.total_recomanded
                change = ppg_wise_data.total_change

                for k in total_header:
                    comb_values = [base_total[k],simulated_total[k]]
                    if k in percent_header:
                        comb_values.append(change[k])
                        numbers = utils.convert_to_perc(comb_values)
                        numbers[2] = f'({numbers[2]})'
                        _write_excel(worksheet,row, col, '\n'.join(numbers), format_value_percentage)
                        col += 1
                    elif k in currency_header:
                        comb_values.append(change[k])
                        numbers = utils.currency_format(comb_values,const.CURRENCY)
                        numbers[2] = f'({numbers[2]})'
                        _write_excel(worksheet,row, col, '\n'.join(numbers), format_value_currency)
                        col += 1
                    elif k in decimal_header:
                        comb_values.append(change[k])
                        numbers = utils.format_decimals(comb_values)
                        numbers[2] = f'({numbers[2]})'
                        _write_excel(worksheet,row, col, '\n'.join(numbers), format_value)
                        col += 1
                    else:
                        comb_values.append(change[k])
                        numbers = utils.numerize_numbers(comb_values)
                        numbers[2] = f'({numbers[2]})'
                        _write_excel(worksheet,row, col, '\n'.join(numbers), format_value)
                        col += 1 
    workbook.close()
    output.seek(0)
    return output

def excel_download_input_pricing(data,scenario_type):
    """Excel download input pricing

    Args:
        data (dict): dict consists of pricing data
    Returns:
        io.BytesIO: pricing data
    """
    products = data
    output = io.BytesIO()
    workbook = xlsxwriter.Workbook(output)
    percentage_format = workbook.add_format(
        {'border': 1, 'text_wrap': True, 'valign': 'vcenter','align': 'center', 'num_format': '0.00 %'})
    worksheet = workbook.add_worksheet('Set Price Increase-Input')
    header_format = workbook.add_format({
        'border': 1,
        'bg_color': '#2F75B5',
        'bold': True,
        'text_wrap': True,
        'valign': 'vcenter',
        'align': 'center',
        'font_color': 'white',
    })
    header_format_grey = workbook.add_format({
        'border': 1,
        'bold': True,
        'text_wrap': True,
        'valign': 'vcenter',
        'align': 'center',
        'bg_color': '#808080',
        'font_color': 'white',
    })
    body_format_blue = workbook.add_format({
        'border': 1,
        'bold': True,
        'valign': 'vcenter',
        'bg_color': '#DDEBF7',
        "locked": False,
        'align': 'center',
    })
    body_format_blue_percent = workbook.add_format({
        'border': 1,
        'bold': True,
        'valign': 'vcenter',
        'bg_color': '#DDEBF7',
        "locked": False,
        'align': 'center',
        'num_format': '0.00 %'
    })
    body_format_grey = workbook.add_format({
        'border': 1,
        'bold': True,
        'valign': 'vcenter',
        'bg_color': '#E2EFDA'
    })
    body_format =  workbook.add_format({
        'border': 1,
        'bold': False,
        'valign': 'vcenter',
        'align': 'center',
        'num_format': '0.00'
    })
    body_format_nsv = workbook.add_format({
        'border': 1,
        'bold': False,
        'valign': 'vcenter',
        'align': 'center',
        'num_format': '#,##0'
    })

    worksheet.set_column('A:A', 25)
    worksheet.set_column('B:B', 35)
    worksheet.set_column('C:C', 25)
    worksheet.set_column('D:R', 15)
    
    worksheet.merge_range('A1:A2', "OGSM Type", header_format_grey)
    worksheet.merge_range('B1:B2', "PPG", header_format_grey)
    worksheet.merge_range('C1:C2', "NSV[zł]", header_format_grey)

    worksheet.merge_range('D1:F1', "Non Promo Price[zł]", header_format)

    worksheet.write('D2', "Base", header_format)
    worksheet.write('E2', "% Change", header_format)

    worksheet.merge_range('G1:I1', "List Price[zł]", header_format)

    worksheet.write('G2', "Base", header_format)
    worksheet.write('H2', "% Change", header_format)

    if scenario_type == 'simulator':
        worksheet.merge_range('J1:L1', "Promo Price [zł]", header_format)
        worksheet.write('J2', "Base", header_format)
        worksheet.write('K2', "% Change", header_format)
        worksheet.merge_range('M1:N1', "Non Promo Volume (%)", header_format)
        worksheet.write('M2', "Base", header_format)
        worksheet.write('N2', "New", header_format)
        worksheet.merge_range('O1:P1', "Promo Volume (%)", header_format)
        worksheet.write('O2', "Base", header_format)
        worksheet.write('P2', "New", header_format)
        worksheet.merge_range('Q1:Q2', "Type of Price Increase", header_format)
        worksheet.merge_range('R1:R2', "COGS/t Change %", header_format)
        worksheet.write('F2', "New", header_format)
        worksheet.write('I2', "New", header_format)
        worksheet.write('L2', "New", header_format)
    else:
        worksheet.merge_range('J1:J2', "COGS/t Change %", header_format)
        worksheet.write('F2', "Max", header_format)
        worksheet.write('I2', "Max", header_format)

    col = 0
    row_offset = 1


    for i,_v in enumerate(products):
        row = i + row_offset
        worksheet.write(row+1, col, _v['ogsm_type'],body_format_grey)
        worksheet.write(row+1, col+1, _v['product_group'],body_format_grey)
        worksheet.write(row+1, col+2, _v['nsv_sum'],body_format_nsv)

        worksheet.write(row+1, col+3, _v['non_promo_price_per_unit'],body_format)
        worksheet.write(row+1, col+5,_v['non_promo_price_new'],body_format_blue)

        formula = f"=(({xl_range(row+1, col+5, row+1, col+5)}/{xl_range(row+1, col+3, row+1, col+3)})-1)"
        worksheet.write_formula(row+1, col+4, formula,percentage_format)

        worksheet.write(row+1, col+6, _v['list_price'],body_format)
        formula = f"={xl_range(row+1, col+4, row+1, col+4)}*0.9*1.2"
        worksheet.write_formula(row+1, col+7, formula,percentage_format)

        formula = f"={xl_range(row+1, col+6, row+1, col+6)}*(1+{xl_range(row+1, col+7, row+1, col+7)})"
        worksheet.write_formula(row+1, col+8, formula,body_format)
 
        if scenario_type == 'simulator':
            worksheet.write(row+1, col+9, _v['promo_price_per_unit'],body_format)
            worksheet.write(row+1, col+11, _v['promo_price_new'],body_format_blue)
            
            formula = f"=(({xl_range(row+1, col+11, row+1, col+11)}/{xl_range(row+1, col+9, row+1, col+9)})-1)"
            worksheet.write_formula(row+1, col+10, formula,percentage_format)
            
            worksheet.write(row+1, col+12, (_v['non_promo_sold_volume']/(_v['promo_sold_volume']+_v['non_promo_sold_volume'])),percentage_format)
            formula = f"=(1-{xl_range(row+1, col+15, row+1, col+15)})"
            worksheet.write_formula(row+1, col+13, formula,percentage_format)

            worksheet.write(row+1, col+14, (_v['promo_sold_volume']/(_v['promo_sold_volume']+_v['non_promo_sold_volume'])),percentage_format)
            worksheet.write(row+1, col+15,(_v['promo_sold_volume_new']/(_v['promo_sold_volume_new']+_v['non_promo_sold_volume_new'])),body_format_blue_percent)
            # worksheet.write(row+1, col+15, (_v['non_promo_sold_volume_new']/(_v['promo_sold_volume_new']+_v['non_promo_sold_volume_new'])),percentage_format)
            

            worksheet.write(row+1, col+16, _v['type_of_price_inc'].capitalize(),body_format_blue)  # mechanic
            worksheet.data_validation(row+1, col+16, row+1, col+16, {'validate': 'list',
                                                                'source': ['Base', 'Direct','Indirect'],
                                                                'error_title': 'Invalid Name',
                                                                'error_message':'Please enter valid name'
                                                                })
            worksheet.write(row+1, col+17, _v['changed_cogs_t_percent']/100,body_format_blue)
        else:
            worksheet.write(row+1, col+9, _v['changed_cogs_t_percent']/100,body_format_blue)


    worksheet.freeze_panes(2, 0)
    worksheet.protect()
    workbook.close()
    output.seek(0)
    return output


def excel_download_input_scenario_planner(data,scenario_type):
    """Excel download input pricing

    Args:
        data (dict): dict consists of pricing data
    Returns:
        io.BytesIO: pricing data
    """
    products = data
    output = io.BytesIO()
    workbook = xlsxwriter.Workbook(output)
    percentage_format = workbook.add_format(
        {'border': 1, 'text_wrap': True, 'valign': 'vcenter', 'align': 'center','num_format': '0.00 %'})
    worksheet = workbook.add_worksheet('Scenario Planner-Input')
    header_format = workbook.add_format({
        'border': 1,
        'bg_color': '#2F75B5',
        'bold': True,
        'text_wrap': True,
        'valign': 'vcenter',
        'align': 'center',
        'font_color': 'white',
    })
    header_format_grey = workbook.add_format({
        'border': 1,
        'bold': True,
        'text_wrap': True,
        'valign': 'vcenter',
        'align': 'center',
        'bg_color': '#808080',
        'font_color': 'white',
    })
    body_format_blue = workbook.add_format({
        'border': 1,
        'bold': True,
        'valign': 'vcenter',
        'bg_color': '#DDEBF7',
        'locked': False,
        'align': 'center',
    })
    body_format_grey = workbook.add_format({
        'border': 1,
        'bold': True,
        'valign': 'vcenter',
        'bg_color': '#E2EFDA'
    })
    body_format =  workbook.add_format({
        'border': 1,
        'bold': False,
        'valign': 'vcenter',
        'align': 'center',
        'num_format': '0.00'
    })
    body_format_percent = workbook.add_format({
        'border': 1,
        'bold': False,
        'valign': 'vcenter',
        'num_format': '0.00%',
        'align': 'center',
    })

    worksheet.set_column('A:A', 25)
    worksheet.set_column('B:B', 25)
    worksheet.set_column('C:C', 35)
    worksheet.set_column('D:D', 15)
    worksheet.set_column('E:E', 15)
    worksheet.set_column('F:F', 15)
    worksheet.set_column('G:G', 15)
    worksheet.set_column('H:H', 15)
    worksheet.set_column('I:I', 15)
    worksheet.set_column('J:J', 15)
    worksheet.set_column('K:K', 15)
    worksheet.set_column('L:L', 15)
    worksheet.set_column('M:M', 15)
    worksheet.set_column('N:N', 15)
    worksheet.merge_range('A1:A2', "OGSM Type", header_format_grey)
    worksheet.merge_range('B1:B2', "Retailer", header_format_grey)
    worksheet.merge_range('C1:C2', "PPG", header_format_grey)

    worksheet.merge_range('D1:E2', "List Price[zł]", header_format_grey)

    worksheet.write('F1', "Non-Promo Price[zł]", header_format_grey)
    worksheet.write('F2', "New", header_format_grey)

    worksheet.write('G1', "Promo Price[zł]", header_format_grey)
    worksheet.write('G2', "New", header_format_grey)

    worksheet.merge_range('H1:H2',"Floor Prize",header_format)

    worksheet.merge_range('I1:K1', "Net Net[zł]", header_format)

    worksheet.write('I2', "Base", header_format)
    worksheet.write('J2', "% Change", header_format)
    worksheet.write('K2', "New", header_format)

    worksheet.merge_range('L1:N1', "Dead Net [zł]", header_format)

    worksheet.write('L2', "Base", header_format)
    worksheet.write('M2', "% Change", header_format)
    worksheet.write('N2', "New", header_format)


    col = 0
    row_offset = 1
    for i,_v in enumerate(products):
        row = i + row_offset
        worksheet.write(row+1, col, _v['ogsm_type'],body_format_grey)
        worksheet.write(row+1, col+1, _v['customer'],body_format_grey)
        worksheet.write(row+1, col+2, _v['product_group'],body_format_grey)
        worksheet.write(row+1, col+3, _v['list_price_new'],body_format)
        worksheet.write(row+1, col+4, _v['changed_lp_percent'],body_format)
        worksheet.write(row+1, col+5, _v['non_promo_price_new'],body_format)
        worksheet.write(row+1, col+6, _v['promo_price_new'],body_format)
        worksheet.write(row+1,col+7, _v['floor_price'],body_format_percent)

        worksheet.write(row+1, col+8, _v['net_net'],body_format)
        worksheet.write(row+1, col+10, _v['net_net_new'],body_format_blue)
        
        formula = f"=({xl_range(row+1, col+10, row+1, col+10)}/{xl_range(row+1, col+8, row+1, col+8)})-1"
        worksheet.write_formula(row+1, col+9, formula,percentage_format)

        worksheet.write(row+1, col+11, _v['dead_net'],body_format)
        worksheet.write(row+1, col+13, _v['dead_net_new'],body_format_blue)

        formula = f"=({xl_range(row+1, col+13, row+1, col+13)}/{xl_range(row+1, col+11, row+1, col+11)})-1"
        worksheet.write_formula(row+1, col+12, formula,percentage_format)

    worksheet.freeze_panes(2, 0)
    worksheet.protect()  
    workbook.close()
    output.seek(0)
    return output

def excel_download_output_planner(data):
    """Excel download input pricing

    Args:
        data (dict): dict consists of pricing data
    Returns:
        io.BytesIO: pricing data
    """
    products = data
    output = io.BytesIO()
    workbook = xlsxwriter.Workbook(output)
    worksheet = workbook.add_worksheet('Planner Output')
    header_format = workbook.add_format({
        'border': 1,
        'bg_color': '#2F75B5',
        'bold': True,
        'text_wrap': True,
        'valign': 'vcenter',
        'align': 'center',
        'font_color': 'white'
    })
    header_format_yellow = workbook.add_format({
        'border': 1,
        'bg_color': '#BF8F00',
        'bold': True,
        'text_wrap': True,
        'valign': 'vcenter',
        'align': 'center',
        'font_color': 'white'
    })
    header_format_brown = workbook.add_format({
        'border': 1,
        'bg_color': '#C65911',
        'bold': True,
        'text_wrap': True,
        'valign': 'vcenter',
        'align': 'center',
        'font_color': 'white'
    })
    header_format_green = workbook.add_format({
        'border': 1,
        'bg_color': '#548235',
        'bold': True,
        'text_wrap': True,
        'valign': 'vcenter',
        'align': 'center',
        'font_color': 'white',
    })
    header_format_grey = workbook.add_format({
        'border': 1,
        'bold': True,
        'text_wrap': True,
        'valign': 'vcenter',
        'align': 'center',
        'bg_color': '#808080',
        'font_color': 'white',
    })
    body_format_grey = workbook.add_format({
        'border': 1,
        'bold': True,
        'valign': 'vcenter',
        'bg_color': '#E2EFDA'
    })
    body_format =  workbook.add_format({
        'border': 1,
        'bold': False,
        'valign': 'vcenter',
        'align': 'center',
        'num_format': '#,##0'
    })
    body_format_blue = workbook.add_format({
        'border': 1,
        'bold': True,
        'valign': 'vcenter',
        'align': 'center',
        'num_format': '0.00%'
    })
    body_format_percent = workbook.add_format({
        'border': 1,
        'bold': False,
        'valign': 'vcenter',
        'align': 'center',
        'num_format': '0.00%'
    })
    body_format_flat = workbook.add_format({
        'border': 1,
        'bold': False,
        'valign': 'vcenter',
        'align': 'center',
        'num_format': '0.00'
    })
    worksheet.set_column('A:B',25)
    worksheet.set_column('C:C',35)
    worksheet.set_column('D:BV2', 15)
    worksheet.merge_range('A1:A2', "OGSM Type", header_format_grey)
    worksheet.merge_range('B1:B2', "Retailer", header_format_grey)
    worksheet.merge_range('C1:C2', "PPG", header_format_grey)

    worksheet.merge_range('D1:K1', "Net Pricing", header_format)
    worksheet.write('D2', "% Price Impact (TOTAL)", header_format)
    worksheet.write('E2',"NSV Pricing Impact (Direct)[zł]",header_format)
    worksheet.write('F2',"NSV Pricing Impact (Indirect)[zł]",header_format)
    worksheet.write('G2',"NSV Pricing Impact (Base)[zł]",header_format)
    worksheet.write('H2',"NSV Pricing Impact (Total)[zł]",header_format)
    worksheet.write('I2',"Trade Term Drift [zł]",header_format)
    worksheet.write('J2',"Total Price Impact incl. TT Drift",header_format)
    worksheet.write('K2',"Total Price Impact incl. TT Drift %",header_format)

    worksheet.merge_range('L1:AI1', "Financials", header_format_yellow)
    worksheet.write('L2', "Sell In volume [t]", header_format_yellow)
    worksheet.write('M2',"Sell In volume % Change",header_format_yellow)
    worksheet.write('N2',"Sell In volume [t] new",header_format_yellow)
    worksheet.write('O2',"LSV [zł]",header_format_yellow)
    worksheet.write('P2',"Lsv % Change",header_format_yellow)
    worksheet.write('Q2',"Lsv new [zł]",header_format_yellow)
    worksheet.write('R2',"NSV [zł]",header_format_yellow)
    worksheet.write('S2',"NSV % Change",header_format_yellow)
    worksheet.write('T2',"NSV new [zł]",header_format_yellow)
    worksheet.write('U2',"NSV/t [zł]",header_format_yellow)
    worksheet.write('V2',"NSV/t % Change",header_format_yellow)
    worksheet.write('W2',"NSV/t new [zł]",header_format_yellow)
    worksheet.write('X2',"TT%",header_format_yellow)
    worksheet.write('Y2',"TT% %Change",header_format_yellow)
    worksheet.write('Z2',"TT% new",header_format_yellow)
    worksheet.write('AA2',"COGS/t [zł]",header_format_yellow)
    worksheet.write('AB2',"COGS/t % Change",header_format_yellow)
    worksheet.write('AC2',"COGS/t new [zł]",header_format_yellow)
    worksheet.write('AD2',"MMAC abs [zł]",header_format_yellow)
    worksheet.write('AE2',"MMAC abs % Change",header_format_yellow)
    worksheet.write('AF2',"MMAC abs new [zł]",header_format_yellow)
    worksheet.write('AG2',"MMAC % [zł]",header_format_yellow)
    worksheet.write('AH2',"MMAC % % Change",header_format_yellow)
    worksheet.write('AI2',"MMAC % new [zł]",header_format_yellow)

    worksheet.merge_range('AJ1:AU1', "RSV", header_format_brown)
    worksheet.write('AJ2',"Value Sales = RSV [zł]",header_format_brown)
    worksheet.write('AK2',"Value Sales = RSV % Change",header_format_brown)
    worksheet.write('AL2',"Value Sales = RSV new [zł]",header_format_brown)
    worksheet.write('AM2',"Sell Out Volume Sales [t]",header_format_brown)
    worksheet.write('AN2',"Sell Out Volume Sales % Change",header_format_brown)
    worksheet.write('AO2',"Sell Out Volume Sales new [t]",header_format_brown)
    worksheet.write('AP2',"Sell Out Units",header_format_brown)
    worksheet.write('AQ2',"Sell Out Units % Change",header_format_brown)
    worksheet.write('AR2',"Sell Out Units new",header_format_brown)
    worksheet.write('AS2',"Average Price Per Unit [t]",header_format_brown)
    worksheet.write('AT2',"Average Price Per Unit % Change",header_format_brown)
    worksheet.write('AU2',"Average Price Per Unit new [t]",header_format_brown)
    

    worksheet.merge_range('AV1:BG1', "Customer Perspective", header_format_green)
    worksheet.write('AV2',"% Trade Margin Non Promo",header_format_green)
    worksheet.write('AW2',"% Trade Margin Non Promo % Change",header_format_green)
    worksheet.write('AX2',"% Trade Margin Non Promo new",header_format_green)
    worksheet.write('AY2',"% Trade Margin Promo",header_format_green)
    worksheet.write('AZ2',"% Trade Margin Promo % Change",header_format_green)
    worksheet.write('BA2',"% Trade Margin Promo new",header_format_green)
    worksheet.write('BB2',"Average Trade Margin",header_format_green)
    worksheet.write('BC2',"Average Trade Margin % Change",header_format_green)
    worksheet.write('BD2',"Average Trade Margin new",header_format_green)
    worksheet.write('BE2',"Customer Profit [zł]",header_format_green)
    worksheet.write('BF2',"Customer Profit % Change",header_format_green)
    worksheet.write('BG2',"Customer Profit new [zł]",header_format_green)

    worksheet.merge_range('BH1:BV1',"Simulated Price",header_format_green)
    worksheet.write('BH2',"List Price",header_format_green)
    worksheet.write('BI2',"List Price % Change",header_format_green)
    worksheet.write('BJ2',"List Price new",header_format_green)
    worksheet.write('BK2',"Promo Price",header_format_green)
    worksheet.write('BL2',"Promo Price % Change",header_format_green)
    worksheet.write('BM2',"Promo Price new",header_format_green)
    worksheet.write('BN2',"Non Promo Price",header_format_green)
    worksheet.write('BO2',"Non Promo Price % Change",header_format_green)
    worksheet.write('BP2',"Non Promo Price new",header_format_green)
    worksheet.write('BQ2',"Dead Net",header_format_green)
    worksheet.write('BR2',"Dead Net % Change",header_format_green)
    worksheet.write('BS2',"Dead Net new",header_format_green)
    worksheet.write('BT2',"Net Net",header_format_green)
    worksheet.write('BU2',"Net Net % Change",header_format_green)
    worksheet.write('BV2',"Net Net new",header_format_green)
   
    col = 0
    row_offset = 1
    combined_data_list = []
    headers = [
        'sell_in_volume_t_new', 'lsv_new', 'nsv_new', 'nsv_t_new',
        'tt_percent_new', 'cogs_t_new', 'gmac_abs_new', 'gmac_percent_new','avg_price_per_unit_new',
        'rsv_new', 'sell_out_volume_sales_t_new', 'percent_trade_margin_non_promo_new','promo_price_new','non_promo_price_new',
        'percent_trade_margin_promo_new', 'avg_trade_margin_new', 'customer_profit_new','net_net_new','dead_net_new','list_price_new'
    ]
    for record in products:
        identifier = (record['customer'], record['product_group'])
        existing_record = next((item for item in combined_data_list if item['identifier'] == identifier), None)
        if existing_record is None:
            new_record = {'customer': record['customer'], 'product_group': record['product_group'], 'identifier': identifier}
            combined_data_list.append(new_record)
            existing_record = new_record
        for header in headers:
            if header not in ['customer', 'product_group', 'is_base']:
                if record['is_base'] == 1:
                    if record['sell_out_unit']:
                        base_column = header.replace('_new', '')
                        existing_record[base_column] = record[header]
                        existing_record['sell_out_unit_base'] = record['sell_out_unit']
                elif record['is_base'] == 0:
                    if record['sell_out_unit']:
                        existing_record[header] = record[header]
                        existing_record['sell_out_unit'] = record['sell_out_unit']
        if record['is_base'] == 0:
            existing_record['ogsm_type'] = record['ogsm_type']
            existing_record['customer'] = record['customer']
            existing_record['percent_pricing_impact_total'] = float(record['percent_pricing_impact_total'])
            existing_record['nsv_pricing_impact_direct'] = float(record['nsv_pricing_impact_direct'])
            existing_record['nsv_pricing_impact_indirect'] = float(record['nsv_pricing_impact_indirect'])
            existing_record['nsv_pricing_impact_base'] = float(record['nsv_pricing_impact_base'])
            existing_record['nsv_pricing_impact_total'] = float(record['nsv_pricing_impact_total'])
            existing_record['tt_drift'] = float(record['tt_drift'])
            existing_record['total_price_impact_inc_tt_drift'] = record['total_price_impact_inc_tt_drift']
            existing_record['total_price_impact_inc_tt_drift_percent'] = float(record['total_price_impact_inc_tt_drift_percent'])
        # breakpoint()
    combined_data_list.sort(key=lambda x: (x['customer'], x['product_group']))
    for i,_v in enumerate(combined_data_list):
        row = i + row_offset
        worksheet.write(row+1, col, _v['ogsm_type'],body_format_grey)
        worksheet.write(row+1, col+1, _v['customer'],body_format_grey)
        worksheet.write(row+1, col+2, _v['product_group'],body_format_grey)
        worksheet.write(row+1, col+3, _v['percent_pricing_impact_total'],body_format_percent)
        worksheet.write(row+1, col+4, float(_v['nsv_pricing_impact_direct']),body_format)
        worksheet.write(row+1, col+5, _v['nsv_pricing_impact_indirect'],body_format)
        worksheet.write(row+1, col+6, _v['nsv_pricing_impact_base'],body_format)
        worksheet.write(row+1, col+7, _v['nsv_pricing_impact_total'],body_format)
        worksheet.write(row+1, col+8, _v['tt_drift'],body_format)
        worksheet.write(row+1, col+9, float(_v['total_price_impact_inc_tt_drift']),body_format)
        worksheet.write(row+1, col+10, _v['total_price_impact_inc_tt_drift_percent'],body_format_percent)


        worksheet.write(row+1, col+11, float(_v['sell_in_volume_t']),body_format)
        formula = f"=IFERROR(({xl_range(row+1, col+13, row+1, col+13)}-{xl_range(row+1, col+11, row+1, col+11)})/{xl_range(row+1, col+11, row+1, col+11)}, 0)"
        worksheet.write_formula(row+1, col+12, formula,body_format_blue)
        worksheet.write(row+1, col+13, float(_v['sell_in_volume_t_new']),body_format)

        worksheet.write(row+1, col+14, float(_v['lsv']),body_format)
        formula = f"=IFERROR(({xl_range(row+1, col+16, row+1, col+16)}-{xl_range(row+1, col+14, row+1, col+14)})/{xl_range(row+1, col+14, row+1, col+14)}, 0)"
        worksheet.write_formula(row+1, col+15, formula,body_format_blue)
        worksheet.write(row+1, col+16, float(_v['lsv_new']),body_format)

        worksheet.write(row+1, col+17, float(_v['nsv']),body_format)
        formula = f"=IFERROR(({xl_range(row+1, col+19, row+1, col+19)}-{xl_range(row+1, col+17, row+1, col+17)})/{xl_range(row+1, col+17, row+1, col+17)}, 0)"
        worksheet.write_formula(row+1, col+18, formula,body_format_blue)
        worksheet.write(row+1, col+19, float(_v['nsv_new']),body_format)
        worksheet.write(row+1, col+20, float(_v['nsv_t']),body_format_flat)
        formula = f"=IFERROR(({xl_range(row+1, col+22, row+1, col+22)}-{xl_range(row+1, col+20, row+1, col+20)})/{xl_range(row+1, col+20, row+1, col+20)}, 0)"
        worksheet.write_formula(row+1, col+21, formula,body_format_blue)
        worksheet.write(row+1, col+22, float(_v['nsv_t_new']),body_format_flat)
        worksheet.write(row+1, col+23, float(_v['tt_percent']),body_format_percent)
        formula = f"=IFERROR(({xl_range(row+1, col+25, row+1, col+25)}-{xl_range(row+1, col+23, row+1, col+23)})/{xl_range(row+1, col+23, row+1, col+23)}, 0)"
        worksheet.write_formula(row+1, col+24, formula,body_format_blue)
        worksheet.write(row+1, col+25, float(_v['tt_percent_new']),body_format_percent)
        worksheet.write(row+1, col+26, _v['cogs_t'],body_format)
        formula = f"=IFERROR(({xl_range(row+1, col+28, row+1, col+28)}-{xl_range(row+1, col+26, row+1, col+26)})/{xl_range(row+1, col+26, row+1, col+26)}, 0)"
        worksheet.write_formula(row+1, col+27, formula,body_format_blue)
        worksheet.write(row+1, col+28, _v['cogs_t_new'],body_format)
        worksheet.write(row+1, col+29, float(_v['gmac_abs']),body_format)
        formula = f"=IFERROR(({xl_range(row+1, col+31, row+1, col+31)}-{xl_range(row+1, col+29, row+1, col+29)})/{xl_range(row+1, col+29, row+1, col+29)}, 0)"
        worksheet.write_formula(row+1, col+30, formula,body_format_blue)
        worksheet.write(row+1, col+31, float(_v['gmac_abs_new']),body_format)
        worksheet.write(row+1, col+32, float(_v['gmac_percent']),body_format_percent)
        formula = f"=IFERROR(({xl_range(row+1, col+34, row+1, col+34)}-{xl_range(row+1, col+32, row+1, col+32)})/{xl_range(row+1, col+32, row+1, col+32)}, 0)"
        worksheet.write_formula(row+1, col+33, formula,body_format_blue)
        worksheet.write(row+1, col+34, float(_v['gmac_percent_new']),body_format_percent)


        worksheet.write(row+1, col+35, float(_v['rsv']),body_format)
        formula = f"=IFERROR(({xl_range(row+1, col+37, row+1, col+37)}-{xl_range(row+1, col+35, row+1, col+35)})/{xl_range(row+1, col+35, row+1, col+35)}, 0)"
        worksheet.write_formula(row+1, col+36, formula,body_format_blue)
        worksheet.write(row+1, col+37, float(_v['rsv_new']),body_format)
        worksheet.write(row+1, col+38, float(_v['sell_out_volume_sales_t']),body_format)
        formula = f"=IFERROR(({xl_range(row+1, col+40, row+1, col+40)}-{xl_range(row+1, col+38, row+1, col+38)})/{xl_range(row+1, col+38, row+1, col+38)}, 0)"
        worksheet.write_formula(row+1, col+39, formula,body_format_blue)
        worksheet.write(row+1, col+40, float(_v['sell_out_volume_sales_t_new']),body_format)
        worksheet.write(row+1, col+41, float(_v['sell_out_unit_base']),body_format)
        formula = f"=IFERROR(({xl_range(row+1, col+43, row+1, col+43)}-{xl_range(row+1, col+41, row+1, col+41)})/{xl_range(row+1, col+41, row+1, col+41)}, 0)"
        worksheet.write_formula(row+1, col+42, formula,body_format_blue)
        worksheet.write(row+1, col+43, float(_v['sell_out_unit']),body_format)
        worksheet.write(row+1, col+44, float(_v['avg_price_per_unit']),body_format_flat)
        formula = f"=IFERROR(({xl_range(row+1, col+46, row+1, col+46)}-{xl_range(row+1, col+44, row+1, col+44)})/{xl_range(row+1, col+44, row+1, col+44)}, 0)"
        worksheet.write_formula(row+1, col+45, formula,body_format_blue)
        worksheet.write(row+1, col+46, float(_v['avg_price_per_unit_new']),body_format_flat)
        
        worksheet.write(row+1, col+47, float(_v['percent_trade_margin_non_promo']),body_format_percent)
        formula = f"=IFERROR(({xl_range(row+1, col+49, row+1, col+49)}-{xl_range(row+1, col+47, row+1, col+47)})/{xl_range(row+1, col+47, row+1, col+47)}, 0)"
        worksheet.write_formula(row+1, col+48, formula,body_format_blue)
        worksheet.write(row+1, col+49, float(_v['percent_trade_margin_non_promo_new']),body_format_percent)
        worksheet.write(row+1, col+50, float(_v['percent_trade_margin_promo']),body_format_percent)
        formula = f"=IFERROR(({xl_range(row+1, col+52, row+1, col+52)}-{xl_range(row+1, col+50, row+1, col+50)})/{xl_range(row+1, col+50, row+1, col+50)}, 0)"
        worksheet.write_formula(row+1, col+51, formula,body_format_blue)
        worksheet.write(row+1, col+52, float(_v['percent_trade_margin_promo_new']),body_format_percent)
        worksheet.write(row+1, col+53, float(_v['avg_trade_margin']),body_format_percent)
        formula = f"=IFERROR(({xl_range(row+1, col+55, row+1, col+55)}-{xl_range(row+1, col+53, row+1, col+53)})/{xl_range(row+1, col+53, row+1, col+53)}, 0)"
        worksheet.write_formula(row+1, col+54, formula,body_format_blue)
        worksheet.write(row+1, col+55, float(_v['avg_trade_margin_new']),body_format_percent)
        worksheet.write(row+1, col+56, float(_v['customer_profit']),body_format)
        formula = f"=IFERROR(({xl_range(row+1, col+58, row+1, col+58)}-{xl_range(row+1, col+56, row+1, col+56)})/{xl_range(row+1, col+56, row+1, col+56)}, 0)"
        worksheet.write_formula(row+1, col+57, formula,body_format_blue)
        worksheet.write(row+1, col+58, float(_v['customer_profit_new']),body_format)

        worksheet.write(row+1, col+59, float(_v['list_price']),body_format_flat)
        formula = f"=IFERROR(({xl_range(row+1, col+61, row+1, col+61)}-{xl_range(row+1, col+59, row+1, col+59)})/{xl_range(row+1, col+59, row+1, col+59)}, 0)"
        worksheet.write_formula(row+1, col+60, formula,body_format_blue)
        worksheet.write(row+1, col+61, float(_v['list_price_new']),body_format_flat)
        worksheet.write(row+1, col+62, float(_v['promo_price']),body_format_flat)
        formula = f"=IFERROR(({xl_range(row+1, col+64, row+1, col+64)}-{xl_range(row+1, col+62, row+1, col+62)})/{xl_range(row+1, col+62, row+1, col+62)}, 0)"
        worksheet.write_formula(row+1, col+63, formula,body_format_blue)
        worksheet.write(row+1, col+64, float(_v['promo_price_new']),body_format_flat)
        worksheet.write(row+1, col+65, float(_v['non_promo_price']),body_format_flat)
        formula = f"=IFERROR(({xl_range(row+1, col+67, row+1, col+67)}-{xl_range(row+1, col+65, row+1, col+65)})/{xl_range(row+1, col+65, row+1, col+65)}, 0)"
        worksheet.write_formula(row+1, col+66, formula,body_format_blue)
        worksheet.write(row+1, col+67, float(_v['non_promo_price_new']),body_format_flat)
        worksheet.write(row+1, col+68, float(_v['dead_net']),body_format_flat)
        formula = f"=IFERROR(({xl_range(row+1, col+70, row+1, col+70)}-{xl_range(row+1, col+68, row+1, col+68)})/{xl_range(row+1, col+68, row+1, col+68)}, 0)"
        worksheet.write_formula(row+1, col+69, formula,body_format_blue)
        worksheet.write(row+1, col+70, float(_v['dead_net_new']),body_format_flat)
        worksheet.write(row+1, col+71, float(_v['net_net']),body_format_flat)
        formula = f"=IFERROR(({xl_range(row+1, col+73, row+1, col+73)}-{xl_range(row+1, col+71, row+1, col+71)})/{xl_range(row+1, col+71, row+1, col+71)}, 0)"
        worksheet.write_formula(row+1, col+72, formula,body_format_blue)
        worksheet.write(row+1, col+73, float(_v['net_net_new']),body_format_flat)
    
    worksheet.freeze_panes(2,3)
    workbook.close()
    output.seek(0)
    return output