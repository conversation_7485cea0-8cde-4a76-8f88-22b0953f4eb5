from core.generics.unit_of_work import AbstractUnitOfWork

def get_region(uow: AbstractUnitOfWork,allowed_groups):
    """Return the Baseline data based on with or without scenario id input.

    Parameters
    ----------
    scenario id(Optional)
        Saved Scenario Id.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    list
        list of dicts if successful, empty list otherwise.

    """
    
    with uow as unit_of_work:
        if allowed_groups:
            return unit_of_work.repo_obj.filter_by_region(allowed_groups)
        return unit_of_work.repo_obj.get_all()

def get_user_groups(uow: AbstractUnitOfWork,allowed_groups\
                    ,is_high=False\
                    ,is_all=False\
                    ,is_promo=False):
    
    with uow as unit_of_work:
        if is_promo:
            if is_all:
                return unit_of_work.repo_obj.filter_promo_all_group()
            return unit_of_work.repo_obj.filter_promo_all_and_self_group(allowed_groups)
        if is_high:
            return unit_of_work.repo_obj.filter_high_priority_group()
        return unit_of_work.repo_obj.filter_by_group(allowed_groups)
    