from django.db import transaction
from apps.pricing.set_pricing import unit_of_work as spuow
from apps.pricing.pricing_common import unit_of_work as pcuow


def get_ogsm_type_ppg_lvl(ppgs=None):
    uow = spuow.SetPricingIncreaseScenarioUnitOfWork(transaction)
    with uow as unit_of_work:
        sp_query_set = unit_of_work.repo_obj.filter_by_ppg(ppgs)
        
    return sp_query_set.values('product_group','ogsm_type')

def get_ogsm_type_customer_lvl(customers=None):
    uow = pcuow.PricingScenarioCustomerLevelUnitOfWork(transaction)
    with uow as unit_of_work:
        sp_query_set = unit_of_work.repo_obj.get_customer_scenario_data(customers)

    return sp_query_set.values('customer','ogsm_type')

