""" Test Optimizer Repository"""
import pytest
from .. import repository

@pytest.mark.django_db
def test_active_save_scenario_repo(_saosf):
    _ossr = repository.SavedScenarioRepository()
    assert _ossr.get(_id=0) is None
    assert _ossr.get(_id=5) is not None
    assert _saosf.id == 5
    assert _saosf.status_type == 'active'

@pytest.mark.django_db
def test_completed_save_scenario_repo(_scosf):
    _opr = repository.SavedScenarioRepository()
    assert _opr.get(_id=0) is None
    assert _opr.get(_id=6) is not None
    assert _scosf.id == 6
    assert _scosf.status_type == 'completed'

@pytest.mark.django_db
def test_completed_save_scenario_wo_data_repo(_scoswodf):
    _opr = repository.ScenarioPlannerPromotionRepository()
    assert _opr.get(_id=0) is None
    assert _opr.get(_id=5) is not None
    assert _scoswodf.id == 5
    assert _scoswodf.saved_scenario_id == 5

@pytest.mark.django_db
def test_completed_save_scenario_with_data_repo(_scoswdf):
    _opr = repository.ScenarioPlannerPromotionRepository()
    assert _opr.get(_id=0) is None
    assert _opr.get(_id=6) is not None
    assert _scoswdf.id == 6
    assert _scoswdf.saved_scenario_id == 6
