from enum import Enum


AUTH_TYPES=['Bearer','Token']
CURRENCY = '€'

DATA_HEADER = [
    'Retailer',
    'Segment',
    'PPG',
    'Brand',
    'Brand_Tech',
    'Product_Type',
    'Year',
    'Date',
    'Month',
    'Week',
    'wk_sold_doll_byppg',
    'wk_sold_qty_byppg',
    'wk_sold_avg_price_byppg',
    'wk_sold_qty_byppg_log',
    'ACV_Selling',
    'C_1_promoted_discount',
    'C_1_regular_price',
    'C_2_promoted_discount',
    'C_2_regular_price',
    'C_3_promoted_discount',
    'C_3_regular_price',
    'C_4_regular_price',
    'Down_t_lag',
    'Flag_promotype_Leaflet',
    'Flag_promotype_advertising_without_price',
    'Flag_promotype_all_brand',
    'Flag_promotype_back_page',
    'Flag_promotype_bonus',
    'Flag_promotype_coupon',
    'Flag_promotype_edlp',
    'Flag_promotype_front_page',
    'Flag_promotype_joint_promo_1',
    'Flag_promotype_joint_promo_10',
    'Flag_promotype_joint_promo_11',
    'Flag_promotype_joint_promo_12',
    'Flag_promotype_joint_promo_13',
    'Flag_promotype_joint_promo_14',
    'Flag_promotype_joint_promo_15',
    'Flag_promotype_joint_promo_2',
    'Flag_promotype_joint_promo_3',
    'Flag_promotype_joint_promo_4',
    'Flag_promotype_joint_promo_5',
    'Flag_promotype_joint_promo_6',
    'Flag_promotype_joint_promo_7',
    'Flag_promotype_joint_promo_8',
    'Flag_promotype_joint_promo_9',
    'Flag_promotype_logo',
    'Flag_promotype_multibuy',
    'Flag_promotype_newsletter',
    'Flag_promotype_pas',
    'Holiday_Flag1',
    'Holiday_Flag2',
    'Holiday_Flag3',
    'Holiday_Flag4',
    'Holiday_flag1',
    'Holiday_flag2',
    'Holiday_flag3',
    'Holiday_flag4',
    'Holiday_flag5',
    # 'Holiday_flag6',
    # 'Holiday_flag7',
    'Median_Base_Price_log',
    'NonPromo_flag_date_1',
    'NonPromo_flag_date_2',
    'NonPromo_flag_date_3',
    'NonPromo_flag_date_4',
    'Nov_2022_trend',
    'Nov_trend',
    'Oct_2022_trend',
    'Oct_trend',
    'P09_2022',
    'P10_11_2022',
    'P10_2022',
    'P10_2022_trend',
    'P11_2022',
    'P11_2022_trend',
    'P9_2022',
    'P9_2022_trend',
    'Promo_Flag_1',
    'Promo_Flag_2',
    'Promo_Flag_3',
    'Promo_flag_date_1',
    'Promo_flag_date_2',
    'Promo_flag_date_3',
    'Promo_flag_date_4',
    'Promo_flag_date_correction_2020_05_02',
    'Promo_flag_date_correction_2020_08_29',
    'Promo_flag_date_correction_2020_10_10',
    'Promo_flag_date_correction_2021_09_25',
    'Promo_flag_date_correction_2022_04_23',
    'Promo_flag_date_correction_2022_05_14',
    'Promo_flag_date_correction_2022_08_13',
    'Quarter_Trend_2021',
    'SI',
    'SI_Period',
    'SI_quarterly',
    'Sept_2022_trend',
    'TPR',
    'TPR_10_above',
    'TPR_5_10',
    'TPR_lag_1',
    'TPR_lag_2',
    'Y_2020',
    'Y_2021',
    'Y_2022',
    'Year_Trend',
    'Year_Trend_2020',
    'Year_Trend_2022',
    'down_promo_flag',
    'mars_period',
    'mars_period_2021_first_half_trend',
    'mars_period_2021_second_half_trend',
    'mars_quarter',
    'quarter2_2022',
    'quarter_4_2022',
    'quarter_trend',
    'quarter_trend_2019',
    'quarter_trend_2020',
    'quarter_trend_2021',
    'tpr_discount_byppg_2019',
    'year_trend_less_than_2022',
    'Promotion_Level'
]

COEFF_HEADER = [
    'Retailer',
    'Segment',
    'PPG',
    'Brand',
    'Brand_Tech',
    'Product_Type',
    'WMAPE',
    'Rsq',
    'ACV_Selling',
    'C_1_promoted_discount',
    'C_1_regular_price',
    'C_2_promoted_discount',
    'C_2_regular_price',
    'C_3_promoted_discount',
    'C_3_regular_price',
    'C_4_regular_price',
    'Down_t_lag',
    'Flag_promotype_Leaflet',
    'Flag_promotype_advertising_without_price',
    'Flag_promotype_all_brand',
    'Flag_promotype_back_page',
    'Flag_promotype_bonus',
    'Flag_promotype_coupon',
    'Flag_promotype_edlp',
    'Flag_promotype_front_page',
    'Flag_promotype_joint_promo_1',
    'Flag_promotype_joint_promo_10',
    'Flag_promotype_joint_promo_11',
    'Flag_promotype_joint_promo_12',
    'Flag_promotype_joint_promo_13',
    'Flag_promotype_joint_promo_14',
    'Flag_promotype_joint_promo_15',
    'Flag_promotype_joint_promo_2',
    'Flag_promotype_joint_promo_3',
    'Flag_promotype_joint_promo_4',
    'Flag_promotype_joint_promo_5',
    'Flag_promotype_joint_promo_6',
    'Flag_promotype_joint_promo_7',
    'Flag_promotype_joint_promo_8',
    'Flag_promotype_joint_promo_9',
    'Flag_promotype_logo',
    'Flag_promotype_multibuy',
    'Flag_promotype_newsletter',
    'Flag_promotype_pas',
    'Holiday_Flag1',
    'Holiday_Flag2',
    'Holiday_Flag3',
    'Holiday_Flag4',
    'Holiday_flag1',
    'Holiday_flag2',
    'Holiday_flag3',
    'Holiday_flag4',
    'Holiday_flag5',
    # 'Holiday_flag6',
    # 'Holiday_flag7',
    'Median_Base_Price_log',
    'NonPromo_flag_date_1',
    'NonPromo_flag_date_2',
    'NonPromo_flag_date_3',
    'NonPromo_flag_date_4',
    'Nov_2022_trend',
    'Nov_trend',
    'Oct_2022_trend',
    'Oct_trend',
    'P09_2022',
    'P10_11_2022',
    'P10_2022',
    'P10_2022_trend',
    'P11_2022',
    'P11_2022_trend',
    'P9_2022',
    'P9_2022_trend',
    'Promo_Flag_1',
    'Promo_Flag_2',
    'Promo_Flag_3',
    'Promo_flag_date_1',
    'Promo_flag_date_2',
    'Promo_flag_date_3',
    'Promo_flag_date_4',
    'Promo_flag_date_correction_2020_05_02',
    'Promo_flag_date_correction_2020_08_29',
    'Promo_flag_date_correction_2020_10_10',
    'Promo_flag_date_correction_2021_09_25',
    'Promo_flag_date_correction_2022_04_23',
    'Promo_flag_date_correction_2022_05_14',
    'Promo_flag_date_correction_2022_08_13',
    'Quarter_Trend_2021',
    'SI',
    'SI_Period',
    'SI_quarterly',
    'Sept_2022_trend',
    'TPR',
    'TPR_10_above',
    'TPR_5_10',
    'TPR_lag_1',
    'TPR_lag_2',
    'Y_2020',
    'Y_2021',
    'Y_2022',
    'Year_Trend',
    'Year_Trend_2020',
    'Year_Trend_2022',
    'down_promo_flag',
    'mars_period',
    'mars_period_2021_first_half_trend',
    'mars_period_2021_second_half_trend',
    'mars_quarter',
    'quarter2_2022',
    'quarter_4_2022',
    'quarter_trend',
    'quarter_trend_2019',
    'quarter_trend_2020',
    'quarter_trend_2021',
    'tpr_discount_byppg_2019',
    'year_trend_less_than_2022',
    'Intercept'
]
ROI_HEADER = [
    'Retailer',
    'Segment',
    'PPG',
    'Brand',
    'Brand_Tech',
    'Product_Type',
    'Date',
    'Year',
    'Period',
    'Quarter',
    'Week',
    'List Price',
    'Total Trade Investment',
    'GSV',
    'NSV',
    'Volume',
    'Units',
    'NSV_Per_Unit_Future',
    'COGS_Per_Unit_Future',
    'GSV_Per_Unit_Future',
    'COGS',
    'total_sold_unit',
    'total_sold_volume',
    'Pack Weight',
    'Promo_Price_Per_Unit',
    'Tactic_Medium_HZ',
    'sum_non_promo_units',
    'sum_promo_units'
]

COEFF_MAP_HEADER = [
    'Retailer',
    'Segment',
    'PPG',
    'Brand',
    'Brand_Tech',
    'Product_Type',
    'Coefficient',
    'Coefficient_new',
    'Value',
    'PPG_Item_No',
]

RETAILER_PPG_MAPPING_WEEKLY_HEADER=[
    'Retailer',
    'PPG',
    'ref_ppg',
    'ref_ppg_idx',
    'Promo_Type',
    'type_promo',
    'Avg_units_per_Week',
    'TE_perweek',
    'ListPrice_perweek',
    'COGS_perweek',
    'NSV_perweek',
    'type_promo_id',
    # 'Ret_idx',
    # 'PPG_idx',
    'GSV_perweek'
]

TACTIC_HEADER=[
    'Id',
'Created By',
'Created At',
'Modified By',
'Modified At',
'Is Delete',
'Is Active',
'Promo Type',
'Tpr Discount Byppg',
'Leaflet Flag',
'Newsletter Flag',
'Advertising Without Price Flag',
'Back Page Flag',
'Bonus Flag',
'Front Page Flag',
'Logo Flag',
'Pas Flag',
'Coupon Flag',
'Edlp Flag',
'Multibuy Flag',
'Max Week',
'Min Week',
'Min Week Edlp',
'Max Week Edlp',
]

RETAILER_PPG_MAPPING_HEADER=[
    'Retailer',
    'PPG',
    'Ret_idx',
    'PPG_idx',
    'PPG_Type'

]

ITEM_MAPPING_HEADER=[
    'Retailer',
    'PPG',
    'PPG_Item_No'
]

COEFFICIENT_VALUES = [
    'account_name',
    'corporate_segment',
    'product_group',
    'brand',
    'brand_tech',
    'product_type,'
    'wmape',
    'rsq',
    'acv_selling',
    'c_1_promoted_discount',
    'c_1_regular_price',
    'c_2_promoted_discount',
    'c_2_regular_price',
    'c_3_promoted_discount',
    'c_3_regular_price',
    'c_4_regular_price',
    'down_t_lag',
    'flag_promotype_leaflet',
    'flag_promotype_advertising_without_price',
    'flag_promotype_all_brand',
    'flag_promotype_back_page',
    'flag_promotype_bonus',
    'flag_promotype_coupon',
    'flag_promotype_edlp',
    'flag_promotype_front_page',
    'flag_promotype_joint_promo_1',
    'flag_promotype_joint_promo_10',
    'flag_promotype_joint_promo_11',
    'flag_promotype_joint_promo_12',
    'flag_promotype_joint_promo_13',
    'flag_promotype_joint_promo_14',
    'flag_promotype_joint_promo_15',
    'flag_promotype_joint_promo_2',
    'flag_promotype_joint_promo_3',
    'flag_promotype_joint_promo_4',
    'flag_promotype_joint_promo_5',
    'flag_promotype_joint_promo_6',
    'flag_promotype_joint_promo_7',
    'flag_promotype_joint_promo_8',
    'flag_promotype_joint_promo_9',
    'flag_promotype_logo',
    'flag_promotype_multibuy',
    'flag_promotype_newsletter',
    'flag_promotype_pas',
    'holiday_Flag_1',
    'holiday_Flag_2',
    'holiday_Flag_3',
    'holiday_Flag_4',
    'holiday_flag1',
    'holiday_flag2',
    'holiday_flag3',
    'holiday_flag4',
    'holiday_flag5',  
    # 'holiday_flag6',
    # 'holiday_flag7',
    'median_base_price_log',
    'non_promo_flag_date_1',
    'non_promo_flag_date_2',
    'non_promo_flag_date_3',
    'non_promo_flag_date_4',
    'nov_2022_trend',
    'nov_trend',
    'oct_2022_trend',
    'oct_trend',
    'p09_2022',
    'p10_11_2022',
    'p10_2022',
    'p10_2022_trend',
    'p11_2022',
    'p11_2022_trend',
    'p9_2022',
    'p9_2022_trend',
    'promo_flag_1',
    'promo_flag_2',
    'promo_flag_3',
    'promo_flag_date_1',
    'promo_flag_date_2',
    'promo_flag_date_3',
    'promo_flag_date_4',
    'promo_flag_date_correction_2020_05_02',
    'promo_flag_date_correction_2020_08_29',
    'promo_flag_date_correction_2020_10_10',
    'promo_flag_date_correction_2021_09_25',
    'promo_flag_date_correction_2022_04_23',
    'promo_flag_date_correction_2022_05_14',
    'promo_flag_date_correction_2022_08_13',
    'quarter_trend_2021_1',
    'si_week',
    'si_period',
    'si_quarter',
    'sept_2022_trend',
    'tpr_discount_byppg',
    'tpr_discount_byppg_10_above',
    'tpr_discount_byppg_5_10',
    'tpr_discount_byppg_lag1',
    'tpr_discount_byppg_lag2',
    'y_2020',
    'y_2021',
    'y_2022',
    'year_trend',
    'year_trend_2020',
    'year_trend_2022',
    'down_promo_flag',
    'mars_period',
    'mars_period_2021_first_half_trend',
    'mars_period_2021_second_half_trend',
    'mars_quarter',
    'quarter2_2022',
    'quarter_4_2022',
    'quarter_trend',
    'quarter_trend_2019',
    'quarter_trend_2020',
    'quarter_trend_2021',
    'tpr_discount_byppg_2019',
    'year_trend_less_than_2022',
    'intercept'
]

DATA_VALUES = [
    'account_name', 
    'corporate_segment', 
    'product_group', 
    'brand',
    'brand_tech',
    'product_type',
    'year', 
    'date',
    'month', 
    'week', 
    'wk_sold_doll_byppg',
    'wk_sold_qty_byppg',
    'wk_sold_avg_price_byppg',
    'wk_sold_qty_byppg_log', 
    'acv_selling',
    'c_1_promoted_discount',
    'c_1_regular_price',
    'c_2_promoted_discount',
    'c_2_regular_price',
    'c_3_promoted_discount',
    'c_3_regular_price',
    'c_4_regular_price',
    'down_t_lag',
    'flag_promotype_leaflet',
    'flag_promotype_advertising_without_price',
    'flag_promotype_all_brand',
    'flag_promotype_back_page',
    'flag_promotype_bonus',
    'flag_promotype_coupon',
    'flag_promotype_edlp',
    'flag_promotype_front_page',
    'flag_promotype_joint_promo_1',
    'flag_promotype_joint_promo_10',
    'flag_promotype_joint_promo_11',
    'flag_promotype_joint_promo_12',
    'flag_promotype_joint_promo_13',
    'flag_promotype_joint_promo_14',
    'flag_promotype_joint_promo_15',
    'flag_promotype_joint_promo_2',
    'flag_promotype_joint_promo_3',
    'flag_promotype_joint_promo_4',
    'flag_promotype_joint_promo_5',
    'flag_promotype_joint_promo_6',
    'flag_promotype_joint_promo_7',
    'flag_promotype_joint_promo_8',
    'flag_promotype_joint_promo_9',
    'flag_promotype_logo',
    'flag_promotype_multibuy',
    'flag_promotype_newsletter',
    'flag_promotype_pas',
    'holiday_Flag_1',
    'holiday_Flag_2',
    'holiday_Flag_3',
    'holiday_Flag_4',
    'holiday_flag1',
    'holiday_flag2',
    'holiday_flag3',
    'holiday_flag4',
    'holiday_flag5',
    # 'holiday_flag6',
    # 'holiday_flag7',
    'median_base_price_log',
    'non_promo_flag_date_1',
    'non_promo_flag_date_2',
    'non_promo_flag_date_3',
    'non_promo_flag_date_4',
    'nov_2022_trend',
    'nov_trend',
    'oct_2022_trend',
    'oct_trend',
    'p09_2022',
    'p10_11_2022',
    'p10_2022',
    'p10_2022_trend',
    'p11_2022',
    'p11_2022_trend',
    'p9_2022',
    'p9_2022_trend',
    'promo_flag_1',
    'promo_flag_2',
    'promo_flag_3',
    'promo_flag_date_1',
    'promo_flag_date_2',
    'promo_flag_date_3',
    'promo_flag_date_4',
    'promo_flag_date_correction_2020_05_02',
    'promo_flag_date_correction_2020_08_29',
    'promo_flag_date_correction_2020_10_10',
    'promo_flag_date_correction_2021_09_25',
    'promo_flag_date_correction_2022_04_23',
    'promo_flag_date_correction_2022_05_14',
    'promo_flag_date_correction_2022_08_13',
    'quarter_trend_2021_1',
    'si_week',
    'si_period',
    'si_quarter',
    'sept_2022_trend',
    'tpr_discount_byppg',
    'tpr_discount_byppg_10_above',
    'tpr_discount_byppg_5_10',
    'tpr_discount_byppg_lag1',
    'tpr_discount_byppg_lag2',
    'y_2020',
    'y_2021',
    'y_2022',
    'year_trend',
    'year_trend_2020',
    'year_trend_2022',
    'down_promo_flag',
    'mars_period',
    'mars_period_2021_first_half_trend',
    'mars_period_2021_second_half_trend',
    'mars_quarter',
    'quarter2_2022',
    'quarter_4_2022',
    'quarter_trend',
    'quarter_trend_2019',
    'quarter_trend_2020',
    'quarter_trend_2021',
    'tpr_discount_byppg_2019',
    'year_trend_less_than_2022',
    'promotion_levels'
]

COEFFICIENT_MAP_VALUES = [
    'account_name', 
    'corporate_segment', 
    'product_group', 
    'brand',
    'brand_tech',
    'product_type',
    'coefficient_old',
    'coefficient_new',
    'value',
    'ppg_item_no'
]

ROI_VALUES = [
    'account_name', 
    'corporate_segment', 
    'product_group',
    'brand',
    'brand_tech',
    'product_type',
    'date',
    'year', 
    'period',
    'quarter', 
    'week', 
    'list_price',
    'total_trade_investment',
    'gsv',
    'nsv',
    'volume',
    'units',
    'nsv_per_unit_future',
    'cogs_per_unit_future',
    'gsv_per_unit_future',
    'cogs',
    'total_sold_unit',
    'total_sold_volume',
    'pack_weight',
    'promo_price',
    'tactic_medium_hz',
    'sum_non_promo_units',
    'sum_promo_units'
]

TACTIC_VALUES=[
    'id',
'created_by',
'created_at',
'modified_by',
'modified_at',
'is_delete',
'is_active',
'promo_type',
'tpr_discount_byppg',
'leaflet_flag',
'newsletter_flag',
'advertising_without_price_flag',
'back_page_flag',
'bonus_flag',
'front_page_flag',
'logo_flag',
'pas_flag',
'coupon_flag',
'edlp_flag',
'multibuy_flag',
'max_week',
'min_week',
'min_week_edlp',
'max_week_edlp',
]


RETAILER_PPG_MAPPING_WEEKLY_VALUES=[
    'account_name',
    'product_group',
    'ref_ppg',
    'ref_ppg_idx',
    'promo_type',
    'type_promo',
    'avg_units_per_week',
    'te_per_week',
    'list_price_per_week',
    'cogs_per_week',
    'nsv_per_week',
    'type_promo_id',
    'gsv_per_week',
]

RETAILER_PPG_MAPPING_VALUES=[
    'account_name',
    'product_group',
    'retailer_index',
    'ppg_index',
    'ppg_type'
]

ITEM_MAPPING_VALUES=[
    'account_name',
    'product_group',    
    'ppg_item_no'
]







# class ModelData(Enum):
#     MODEL_NAME = 'model_data'
#     MODEL_COLUMN = DATA_HEADER
#     MODEL_VALUES = DATA_VALUES
#     FK_MODEL = 'model_meta'
#     FK_NAME = 'model_meta'
#     YEAR_WISE = False
#     FIN_YEAR_WISE = False
#     REQUIRED_COLUMN = [('a.id','model_data_id')]
#     EXTRA_COLUMNS = [('b.id','meta_id')]

# class ModelCoeff(Enum):
#     MODEL_NAME = 'model_coefficient'
#     MODEL_COLUMN = COEFF_HEADER
#     MODEL_VALUES = COEFFICIENT_VALUES
#     FK_MODEL = 'model_meta'
#     FK_NAME = 'model_meta'
#     YEAR_WISE = False
#     REQUIRED_COLUMN = [('a.id','coeff_id')]
#     EXTRA_COLUMNS = [('b.id','meta_id')]

# class ModelROI(Enum):
#     MODEL_NAME = 'model_roi'
#     MODEL_COLUMN = ROI_HEADER
#     MODEL_VALUES = ROI_VALUES
#     FK_MODEL = 'model_meta'
#     FK_NAME = 'model_meta'
#     YEAR_WISE = False
#     FIN_YEAR_WISE = True
#     REQUIRED_COLUMN = [('a.id','roi_id')]
#     EXTRA_COLUMNS = [('b.id','meta_id')]

# class ModelCoeffMap(Enum):
#     MODEL_NAME = 'coefficient_mapping'
#     MODEL_COLUMN = COEFF_MAP_HEADER
#     MODEL_VALUES = COEFFICIENT_MAP_VALUES
#     FK_MODEL = 'model_meta'
#     FK_NAME = 'model_meta'
#     YEAR_WISE = False
#     REQUIRED_COLUMN = [('a.id','coeff_map_id')]
#     EXTRA_COLUMNS = [('b.id','meta_id')]

# class ModelRetailerPPGMap(Enum):
#     MODEL_NAME = 'retailer_ppg_mapping'
#     MODEL_COLUMN = RETAILER_PPG_MAPPING_HEADER
#     MODEL_VALUES = RETAILER_PPG_MAPPING_VALUES
#     REQUIRED_COLUMN = [('a.id','retailer_ppg_map_id')]
#     EXTRA_COLUMNS=[('type_of_promo','Type_of_Promo')]

# class ModelTactic(Enum):
#     MODEL_NAME = 'model_tactic'
#     MODEL_COLUMN = TACTIC_HEADER
#     MODEL_VALUES = TACTIC_VALUES
#     FK_MODEL = 'retailer_ppg_mapping'
#     FK_NAME = 'retailer_ppg_map'
#     REQUIRED_COLUMN = [('a.id','tactic_id')]
#     EXTRA_COLUMNS = [('retailer_index','Retailer_Index'),
#                     ('ppg_index','PPG_Index'),
#                     ('type_of_promo','Type_of_Promo'),
#                     ('ppg_type','PPG_Type')
#                     ]

# class ModelRETAILER_PPG_MAPPING_WEEKLY(Enum):
#     MODEL_NAME = 'retailer_ppg_mapping_weekly'
#     MODEL_COLUMN = RETAILER_PPG_MAPPING_WEEKLY_HEADER
#     MODEL_VALUES = RETAILER_PPG_MAPPING_WEEKLY_VALUES
#     FK_MODEL = 'retailer_ppg_mapping_weekly'
#     FK_NAME = 'retailer_ppg_map'
#     REQUIRED_COLUMN = [('a.id','rpm_weekly_id')]
#     EXTRA_COLUMNS = [('retailer_index','Retailer_Index'),
#                     ('ppg_index','PPG_Index'),
#                     ('type_of_promo','Type_of_Promo')]
# class ITEMMap(Enum):
#     MODEL_NAME = 'item_map'
#     MODEL_COLUMN = ITEM_MAPPING_HEADER
#     MODEL_VALUES = ITEM_MAPPING_VALUES
#     REQUIRED_COLUMN = [('a.id','item_map_id')]
#     EXTRA_COLUMNS=[]

