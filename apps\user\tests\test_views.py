""" Test Common View"""
import json
import pytest
from rest_framework.test import APIClient
from ..tests import _constants as CONST

apiclient = APIClient()

@pytest.mark.skip(reason="This api used for getting user groups using graph api")
@pytest.mark.django_db(transaction=True)
def test_user_group_view(auth):
    """test_user_group_view

    Args:
        auth (_type_): authentication
    """
    # get all meta data
    apiclient.credentials(**auth['headers'])
    data_resp = apiclient.get(
        f"{CONST.USER_BASEURL}/get_user", format="json",
    )
    assert data_resp.status_code == 200
    assert json.loads(data_resp.content)["data"] is not None
    assert json.loads(data_resp.content)["data"] !=[]
