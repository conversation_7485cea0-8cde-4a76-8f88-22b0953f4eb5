""" Common Services"""
import json
import os
import re
from urllib.parse import urlparse
from django.template.loader import get_template
import requests
from apps.pricing.pricing_optimizer.stage_1 import optimizer
from apps.pricing.pricing_optimizer.stage_2 import stage_2_optimizer
from apps.pricing.pricing_common.models import PricingCommonScenarioView
import numpy as np
import logging
import pandas as pd
from django.utils import timezone
from contextlib import closing
from django.db import connection
from apps.pricing.pricing_common.query_filter import query_deep_drive_filter
import utils #pylint: disable=E0401
from apps.common import utils as cmn_utils
from utils import convert_list_to_tuple_str
from apps.pricing.pricing_common import queries,serializers as cmn_ser,constants as CONST,calculations as calc,utils as pc_utils
from core.generics import(constants, unit_of_work as _uow,resp_utils\
                          ,exceptions,resp_utils)
from tasks.parallel_task import sync_to_async
import ast
import pytz# we keep this module even though it is not used because,while we serialize after deserializing a dict, timestamp will give a run-time warning as zone info is not saved, to avoid that we need to import pytz


logger = logging.getLogger(__name__)

def changed_scenario_batch_insert(
                     models,
                     n_records,
                     instance_id,
                    is_row_column=False):

    values = models['MODEL_VALUES'].value.copy()
    columns = models['MODEL_COLUMN'].value.copy() if not is_row_column else []
    new_columns = models['REQUIRED_COLUMN'].value+models['EXTRA_COLUMNS'].value
    if new_columns:
        for index,(name,_mask_name) in enumerate(new_columns):
            cmn_utils.insert_extra_var(values,index,name)
            if not is_row_column:
                cmn_utils.insert_extra_var(columns,index,_mask_name)
    basedata_query = queries.changed_scenario_batch_insert_query(utils.convert_list_to_string_list(values),
                                    len(n_records))                           
    basedata_query = re.sub("[\"\']", "", basedata_query)
    params = []
    
    for i in n_records:
        params.append([instance_id\
                        ,i['level_param']\
                        ,i['customer_level_param']\
                        ,i['inner_mode']\
                        ,i['ogsm_param']\
                        ,i['promo_vol_change_new']\
                        ,i['base_growth_th_invest_new']\
                        ,i['changed_net_net_change_percent']\
                        ,i['changed_dead_net_change_percent']\
                        ,i['status_flag']\
                        ,i['type_of_price_inc']\
                        ,i['competitor_follows']\
                        ,i['list_price_new']\
                        ,i['pack_weight_kg_new']\
                        ,i['dead_net_new']\
                        ,i['net_net_new']\
                        ,i['non_promo_price_new']\
                        ,i['promo_price_new']\
                        ,i['floor_price']\
                        ,i['changed_lp_percent']
                    ])
    
    with closing(connection.cursor()) as cursor:
        # cursor.fast_executemany=True
        cursor.execute(queries.delete_changed_scenario_query(instance_id))
        cursor.executemany(basedata_query, params)
   
def base_and_simulated_scenario_batch_insert(
                     models,
                     n_records,
                     instance_id,
                    is_row_column=False):
    # n_records=n_records[:50]
    values = models['MODEL_VALUES'].value.copy()
    columns = models['MODEL_COLUMN'].value.copy() if not is_row_column else []
    new_columns = models['REQUIRED_COLUMN'].value+models['EXTRA_COLUMNS'].value
    if new_columns:
        for index,(name,_mask_name) in enumerate(new_columns):
            cmn_utils.insert_extra_var(values,index,name)
            if not is_row_column:
                cmn_utils.insert_extra_var(columns,index,_mask_name)

    basedata_query = queries.base_and_simulated_scenario_query(utils.convert_list_to_string_list(values),
                                    len(n_records))                           
    basedata_query = re.sub("[\"\']", "", basedata_query)
    params = []

    for i in n_records:
        if 'optimized_trade_margin' not in i.keys():
            i['optimized_trade_margin'] = 0
        if 'optimized_units' not in i.keys():
            i['optimized_units'] = 0

        params.append([instance_id\
                       ,i['ogsm_type']\
                        ,i['customer']\
                        ,i['technology']\
                        ,i['brand']\
                        ,i['product_group']\
                        ,i['percent_pricing_impact_total']\
                        ,i['nsv_pricing_impact_direct']\
                        ,i['nsv_pricing_impact_indirect']\
                        ,i['nsv_pricing_impact_base']\
                        ,i['nsv_pricing_impact_total']\
                        ,i['total_price_impact_inc_tt_drift']\
                        ,i['total_price_impact_inc_tt_drift_percent']\
                        ,i['sell_in_volume_t_new']\
                        ,i['lsv_new']\
                        ,i['nsv_new']\
                        ,i['nsv_t_new']\
                        ,i['cogs_t_new']\
                        ,i['tt_percent_new']\
                        ,i['gmac_abs_new']\
                        ,i['gmac_percent_change']\
                        ,i['gmac_percent_new']\
                        ,i['rsv_new']\
                        ,i['sell_out_volume_sales_t_new']\
                        ,i['unit_sales_000']\
                        ,i['promo_share']\
                        ,i['avg_price_per_unit_new']\
                        ,i['percent_trade_margin_non_promo_new']\
                        ,i['percent_trade_margin_promo_new']\
                        ,i['avg_trade_margin_new']\
                        ,i['customer_profit_new']\
                        ,i['nsv']\
                        ,i['mac']\
                        ,i['list_price']\
                        ,i['tax']\
                        ,i['rsv_wo_vat']\
                        ,i['tt_drift']\
                        ,i['mars_profit']\
                        ,i['is_base']\
                        ,i['implied_price_impact']\
                        ,i['implied_vol_impact']\
                        ,i['implied_mix_impact']\
                        ,i['total_growth']\
                        ,i['nsv_percent_change']\
                        ,i['nsv_t_percent_change']\
                        ,i['rsv_percent_change']\
                        ,i['profit_pool_customer_profit']\
                        ,i['profit_pool_mars_profit']\
                        ,i['customer_profit_percent_change']\
                        ,i['net_net_new']\
                        ,i['dead_net_new']\
                        ,i['sell_out_unit_new']\
                        ,i['list_price_new']\
                        ,i['changed_dead_net_change_percent']\
                        ,i['changed_net_net_change_percent']\
                        ,i['changed_lp_percent']\
                        ,i['non_promo_price_new']\
                        ,i['promo_price_new']\
                        ,i['pack_weight_kg_new']\
                        ,i['net_non_promo_price_elasticity']\
                        ,i['non_promo_price_per_unit']\
                        ,i['sell_in_unit']\
                        ,i['sell_out_unit']\
                        ,i['tpr']\
                        ,i['tpr_new']\
                        ,i['promo_price_elasticity']\
                        ,i['non_promo_price_elasticity']\
                        ,i['pack_weight_kg']\
                        ,i['sell_in_unit_new']\
                        ,i['optimized_trade_margin']\
                        ,i['optimized_units']\
                        ,i['changed_promo_vol_change_percent']\
                        ,i['changed_cogs_t_percent']\
                        ,i['changed_pack_weight_kg_percent']\
                        ,i['type_of_price_inc']\
                        ,i['competitor_follows']\
                        ,i['status_flag']\
                        ,i['net_net_nsv_product']\
                        ,i['lp_nsv_product']\
                        ,i['nn_change_percent']\
                        ,i['exclude_retailer']\
                        ,i['is_planner']\
                        ,i['promo_sold_volume']\
                        ,i['promo_sold_volume_new']\
                        ,i['non_promo_sold_volume']\
                        ,i['non_promo_sold_volume_new']\
                        ,i['promo_sold_volume_percent']\
                        ,i['promo_sold_volume_percent_new']

                    ])

    print('exicuting')

    with closing(connection.cursor()) as cursor:
        # cursor.fast_executemany=True
        cursor.execute(queries.delete_base_and_simulated_scenario_query(instance_id))
        cursor.executemany(basedata_query, params)
        print('done')

def summary_scenario_batch_insert(
                     models,
                     n_records,
                     instance_id,
                    is_row_column=False):

    values = models['MODEL_VALUES'].value.copy()
    columns = models['MODEL_COLUMN'].value.copy() if not is_row_column else []
    new_columns = models['REQUIRED_COLUMN'].value+models['EXTRA_COLUMNS'].value
    if new_columns:
        for index,(name,_mask_name) in enumerate(new_columns):
            cmn_utils.insert_extra_var(values,index,name)
            if not is_row_column:
                cmn_utils.insert_extra_var(columns,index,_mask_name)
    basedata_query = queries.summary_scenario_batch_insert_query(utils.convert_list_to_string_list(values),
                                    models['MODEL_NAME'].value,
                                    )                           
    basedata_query = re.sub("[\"\']", "", basedata_query)
    params = []
    params.extend([instance_id,n_records.get('base'),n_records.get('simulated')])
    with closing(connection.cursor()) as cursor:
        cursor.execute(basedata_query, params)

def bulk_delete(saved_scenario_id):
    with closing(connection.cursor()) as cursor:
        cursor.execute(queries.delete_changed_scenario_query(saved_scenario_id))
        cursor.execute(queries.delete_summary_scenario_query(saved_scenario_id))
        cursor.execute(queries.delete_overall_summary_scenario_query(saved_scenario_id))
        cursor.execute(queries.delete_saved_scenario_query(saved_scenario_id))

def get_scenario(uow: _uow.AbstractUnitOfWork\
                 ,scenario_id: int=None\
                ,user=None\
                ,scenario_view=''\
                ,scenario_type=''\
                ,module_type='set_pricing',
                is_load=False
                ):
    """Return the Baseline data based on with or without scenario id input.

    Parameters
    ----------
    scenario id(Optional)
        Saved Scenario Id.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    list
        list of dicts if successful, empty list otherwise.

    """

    with uow as unit_of_work:
        if scenario_id:
            return unit_of_work.repo_obj.filter_by_id(scenario_id)
        if scenario_view == 'self':
            return unit_of_work.repo_obj.get_my_scenario_by_scenario_type(scenario_type,user)
        if scenario_view == 'shared':
            if module_type:
                return unit_of_work.repo_obj.get_shared_scenario_by_module_type(scenario_type,module_type=module_type,user=user)
            return unit_of_work.repo_obj.get_shared_scenario_by_scenario_type(scenario_type,user)
        if scenario_view =='review':
            if module_type:
                return unit_of_work.repo_obj.get_review_scenario_by_module_type(scenario_type,module_type=module_type,user=user)
            return unit_of_work.repo_obj.get_review_scenario_by_scenario_type(scenario_type,user)
        if scenario_view =='published':
            if module_type:
                return unit_of_work.repo_obj.get_published_scenario_by_module_type(scenario_type,module_type=module_type,user=user)
            return unit_of_work.repo_obj.get_published_scenario_by_scenario_type(scenario_type,user)
        if scenario_view =='track':
            if module_type:
                return unit_of_work.repo_obj.get_track_scenario_by_module_type(scenario_type,module_type=module_type,user=user)
            return unit_of_work.repo_obj.get_track_scenario(scenario_type,user)
        if scenario_view =='track published':
            if module_type:
                return unit_of_work.repo_obj.get_track_published_scenario_by_module_type(scenario_type,module_type=module_type,user=user)
            return unit_of_work.repo_obj.get_track_published_scenario(scenario_type,user)
        if scenario_type:
            return unit_of_work.repo_obj.get_by_scenario_type(scenario_type,user=user)
        return unit_of_work.repo_obj.get_all_scenarios_by_user(user=user)

def save_changed_promo_scenaro(**data):
    changed_scenario_batch_insert(CONST.ChangedPricingScenario\
                     ,data.get('request_data').get('changed_records')\
                    ,data.get('request_data').get('pricing_saved_scenario_id')
                     )

# @sync_to_async
def save_summary_promo_scenaro(**data):
    serialized_data = cmn_ser.PricingSummeryScenarioSerializer(
        data=data.get('request_data'))
    if serialized_data.is_valid():
        serialized_data.save(pricing_saved_scenario_id=data.get('saved_scenario_id'))
        return {'saved_id':data.get('saved_scenario_id')}
    
    raise exceptions.MissingRequestParamsError("status", serialized_data.errors)

# @sync_to_async
def save_overall_summary_promo_scenaro(**data):
    serialized_data = cmn_ser.PricingOverallSummeryScenarioSerializer(
        data=data.get('request_data'))
    if serialized_data.is_valid():
        serialized_data.save(pricing_saved_scenario_id=data.get('saved_scenario_id'))
        return {'saved_id':data.get('saved_scenario_id')}
    
    raise exceptions.MissingRequestParamsError("status", serialized_data.errors)

# @sync_to_async
def save_base_and_simulated_promo_scenaro(**data):
    base_and_simulated_scenario_batch_insert(CONST.BaseAndSimulatedPricingScenario\
                    ,data.get('request_data')\
                    ,data.get('saved_scenario_id')
                    )
    
@resp_utils.handle_json
def update_promo_scenaro(**data):
    serialized_data = cmn_ser.ChangedSavedPricingScenarioSerializer(
        data.get('instance'),data=data.get('request_data'),partial=True)
    if serialized_data.is_valid():
        serialized_data.save()
        return {'updated_id':data.get('instance').id}
    
    raise exceptions.MissingRequestParamsError("status", serialized_data.errors)


def save_scenario(request_data: dict\
                  , uow: _uow.AbstractUnitOfWork\
                    ,user\
                    ,is_commit=False):
    """Save Scenrio

    Parameters
    ----------
    data
        To insert the data into the database.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    str
        Success or Failure in inserting the data

    """
    data = request_data.copy()
    non_committed_id = data.pop('non_committed_id',0)
    changed_records = {
        'changed_records':data.pop('pricing_payload'),
    }

    scenario_instance_id = non_committed_id
    with uow as unit_of_work:
        if non_committed_id:
            data['is_committed'] = True
            data['modified_by'] = user
            data['modified_at'] = str(timezone.now())
            data['user'] = user
            # breakpoint()
            unit_of_work.repo_obj.update(scenario_instance_id,ast.literal_eval(str(data)))
            unit_of_work.commit()
        else:
            data['created_by'] = user
            data['modified_by'] = user
            data['user'] = user
            data['is_committed'] = is_commit
            # breakpoint()
            scenario_instance = unit_of_work.repo_obj.add(ast.literal_eval(str(data)))
            scenario_instance_id = scenario_instance.id
            unit_of_work.commit()
    
    changed_records.update({
        'pricing_saved_scenario_id':scenario_instance_id
    })
    save_changed_promo_scenaro(request_data=changed_records)
    return {'saved_id':scenario_instance_id}
  
    
def put_scenario(scenario_instance: dict, request_data: dict, uow: _uow.AbstractUnitOfWork,user):
    """Return the Baseline data based on with or without scenario id input.

    Parameters
    ----------
    scenario id(Optional)
        Saved Scenario Id.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    list
        list of dicts if successful, empty list otherwise.
    """

    if not scenario_instance:
        raise exceptions.MissingRequestParamsError("id", scenario_instance)
    
    data = request_data.copy()
    promotion_data = {
        'input_constraints':data.pop('input_constraints'),
        "data":data.pop('data')
    }
    data['modified_by'] = user
    with uow as unit_of_work:
        unit_of_work.repo_obj.update(scenario_instance.id,data)
        unit_of_work.commit()
        
    return update_promo_scenaro(request_data=promotion_data,
                                instance=scenario_instance)

def delete_scenario(scenario_id: int,user,uow: _uow.AbstractUnitOfWork):
    """Return the Baseline data based on with or without scenario id input.

    Parameters
    ----------
    scenario id(Optional)
        Saved Scenario Id.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    list
        list of dicts if successful, empty list otherwise.
    """

    bulk_delete(scenario_id)
    return {'status':'success'}


def load_scenario(uow: _uow.AbstractUnitOfWork,scenario_saved_id):
    with uow as unit_of_work:
        query_set = unit_of_work.repo_obj.get_scenario_by_saved_id(scenario_saved_id)
    
    if not query_set.exists():
        raise  exceptions.NoDataError(scenario_saved_id)

    return query_set

def get_name_of_id(uow: _uow.AbstractUnitOfWork, scenario_saved_id):
    with uow as unit_of_work:
        data = unit_of_work.repo_obj.get_name(scenario_saved_id)
    if data:
        return {'scenario_name':data['scenario_name'],
                'module_type':data['module_type'],
                'scenario_type':data['scenario_type']}

def get_permissions(uow: _uow.AbstractUnitOfWork,scenario_saved_id):
    with uow as unit_of_work:
        query_set = unit_of_work.repo_obj.get_scenario_by_saved_id(scenario_saved_id)
    
    if not query_set.exists():
        raise  exceptions.NoDataError(scenario_saved_id)

    return query_set

def global_constraints(uow: _uow.AbstractUnitOfWork,scenario_saved_id:int=0,customers=''):
        with uow as unit_of_work:
            if  scenario_saved_id:
               planner_scenario = unit_of_work.repo_obj.filter_by_saved_id(scenario_saved_id)
               return {'optimized_nn_target':planner_scenario.optimized_nn_target,
                       "nsv":planner_scenario.nsv,
                       'weighted_lp_nsv_product':planner_scenario.weighted_lp_nsv_product,
                       'target':planner_scenario.target,
                       'actual_invest':planner_scenario.actual_invest,
                       'invest_budget':planner_scenario.invest_budget,
                       'calculated_nn_percent':planner_scenario.calculated_nn_percent
                       }
       
            planner_scenario =  unit_of_work.repo_obj.get_customer_scenario_data(customers)
        if planner_scenario.exists():
            planner_scenario_df = pd.DataFrame(planner_scenario.values())
            planner_scenario_df.rename(columns={'nsv':'base_nsv'},inplace=True)
            return calc.calc_global_constraints(planner_scenario_df,is_scenario_planner=True)
        raise exceptions.NoDataError(scenario_saved_id)

def get_deep_drive_summary(uow: _uow.AbstractUnitOfWork,scenario_saved_id,filter_data):
        with uow as unit_of_work:
           deep_drive_summary =  unit_of_work.repo_obj.filter_base_and_simulated_pricing_scenario_by_multifilter(scenario_saved_id\
                                                                                                            ,filter_data=filter_data)
        if deep_drive_summary.exists():
            summary_df = pd.DataFrame(deep_drive_summary.values())
            return calc.calc_deep_drive_summary(summary_df)
        raise exceptions.NoDataError(scenario_saved_id)
    
def get_compare_scenario_kpis(uow: _uow.AbstractUnitOfWork,scenario_saved_ids):
        with uow as unit_of_work:
            deep_drive_summary =  unit_of_work.repo_obj.filter_multiple_scenario(scenario_saved_ids)
        if deep_drive_summary.exists():
            summary_df = pd.DataFrame(deep_drive_summary.values())
            return calc.calc_compare_scenario_kpi(summary_df)
        raise exceptions.NoDataError(scenario_saved_ids)

def get_deep_drive_customer_level(uow: _uow.AbstractUnitOfWork,scenario_saved_id,filter_data):
        level = filter_data.get('level')
        with uow as unit_of_work:
           deep_drive_summary =  unit_of_work.repo_obj.filter_base_and_simulated_pricing_scenario_by_multifilter(scenario_saved_id\
                                                                                                          ,filter_data=filter_data
                                                                                                              )
        if deep_drive_summary.exists():
            summary_df = pd.DataFrame(deep_drive_summary.values())
            if ',' in level:
                level=level.split(',')
            return calc.calculate_deep_drive_customer_level(summary_df,filter_data.get('param'),level)
        raise exceptions.NoDataError(scenario_saved_id)

def get_top_10(uow:_uow.AbstractUnitOfWork,scenario_saved_id,filter_data):
    with uow as unit_of_work:
        deep_drive_summary =  unit_of_work.repo_obj.filter_base_and_simulated_pricing_scenario_by_multifilter(scenario_saved_id\
                                                                  
                                                                                                 ,filter_data=filter_data)
    deep_drive_summary_df = pd.DataFrame(deep_drive_summary.values())
    if deep_drive_summary.exists():
        final_df = pd.DataFrame()
        level_param =filter_data.get('level_param')
        agg_func = 'sum'
        if  filter_data.get('level_param') in ['nsv_t','gmac_percent','avg_trade_margin']:
            agg_func = 'mean'
        for base in [1,0]:
            new_deep_drive_summary_df = deep_drive_summary_df.loc[deep_drive_summary_df['is_base']==bool(base)]
            if final_df.shape[0]:
                
                filters_params = final_df[filter_data.get('level')].unique().tolist()
                new_deep_drive_summary_df = new_deep_drive_summary_df.loc[new_deep_drive_summary_df[filter_data.get('level')].isin(filters_params)]
            
            if level_param in ['gmac_percent','avg_trade_margin']:
                agg_deep_drive_summary_df = calc.calc_gmac_vs_trade_margin(new_deep_drive_summary_df,filter_data.get('level'))
                top_10_agg_deep_drive_summary_df = agg_deep_drive_summary_df.sort_values('nsv', ascending=False).head(10)
                top_10_agg_deep_drive_summary_df = top_10_agg_deep_drive_summary_df[['gmac_percent','avg_trade_margin','nsv',filter_data.get('level')]]
            else:
                agg_deep_drive_summary_df = calc.calculate_deep_drive_top_10_level(new_deep_drive_summary_df\
                                                                                                ,filter_data.get('level')\
                                                                                                ,level_param\
                                                                                                ,agg_func=agg_func)
                
                sort_by = 'nsv_sum' if level_param in ['dead_net','net_net'] else f'{level_param}_sum'
                top_10_agg_deep_drive_summary_df = agg_deep_drive_summary_df.sort_values(sort_by, ascending=False).head(10)
            
            top_10_agg_deep_drive_summary_df['is_base'] = base
            
            final_df = final_df.append(top_10_agg_deep_drive_summary_df)
        
        base = final_df.loc[final_df['is_base']==True]
        sim = final_df.loc[final_df['is_base']==False]

        return {'base':base.to_dict(orient='records')\
            ,'simulated':sim.to_dict(orient='records')}
      
    raise exceptions.NoDataError(scenario_saved_id) 

def get_list(uow: _uow.AbstractUnitOfWork,scenario_saved_id):
        with uow as unit_of_work:
           constraints =  unit_of_work.repo_obj.get_simulated_constraints_by_saved_id(scenario_saved_id)
        
        return constraints
    
def saved_input_load_scenario(uow: _uow.AbstractUnitOfWork,suow,scenario_saved_id,level,customers,is_optimizer=False):
    with suow as unit_of_work:
        saved_scenario = unit_of_work.repo_obj.get(_id=scenario_saved_id)
        customers_with_inner_mode_all = unit_of_work.repo_obj.verify_and_filter_customers_with_inner_mode_all(saved_scenario.id,customers)
    return {'scenario_name':saved_scenario.name,'data':list(get_raw_dict_from_query(uow,[CONST.PricingScenario]\
                                   ,scenario_id=scenario_saved_id\
                                    ,mode=saved_scenario.mode\
                                    ,level=level\
                                    ,customers=customers,
                                    customers_with_inner_mode_all=customers_with_inner_mode_all,
                                    db_table='scenario_planner_common_view',
                                    is_optimizer=is_optimizer
                                    ))[0]}
    
def upload_input_load_scenario(uow: _uow.AbstractUnitOfWork,suow,scenario_saved_id,level,customers):
    with suow as unit_of_work:
        saved_scenario = unit_of_work.repo_obj.get(_id=scenario_saved_id)

    return list(get_raw_dict_from_query(uow,[CONST.PricingScenario]\
                                   ,scenario_id=scenario_saved_id\
                                    ,mode=saved_scenario.mode\
                                    ,level=level\
                                    ,customers=customers,
                                    customers_with_inner_mode_all=customers
                                    ))[0]

def get_saved_filltered_data(uow: _uow.AbstractUnitOfWork,scenario_saved_id,level):
    filter_data =  list(get_raw_dict_from_query(uow,[CONST.PricingScenario]\
                                   ,scenario_id=scenario_saved_id\
                                    ))[0]
    return list(map(lambda x:{'ogsm_type':x['ogsm_type'],\
                              'customer':x['customer']\
                            ,'product_group':x['product_group']\
                            ,'brand':x['brand']\
                            ,'technology':x['technology']\
                            },filter_data))
    
def get_summary(uow: _uow.AbstractUnitOfWork,scenario_saved_id,level_param,level):
    with uow as unit_of_work:
        
        if level_param:
            summary = unit_of_work.repo_obj.filter_base_and_simulated_pricing_scenario_by_customer(scenario_saved_id,level_param)
        else:
            summary = unit_of_work.repo_obj.get_scenario_by_saved_id(scenario_saved_id)
    if summary.exists():
        summary_df = pd.DataFrame(summary.values())
        if ',' in level:
            level=level.split(',')
        
        summary_df['nsv_new'] = summary_df['nsv_new'].astype(float)
        summary_df =  summary_df.sort_values('nsv_new', ascending=False)

        return calc.calculate_summary_by_filter(summary_df,level)
    raise exceptions.NoDataError(scenario_saved_id)

def get_overall_summary(uow: _uow.AbstractUnitOfWork,scenario_saved_id,custome_ppg_data):
    custome_ppg_data=custome_ppg_data.get('customer_ppg_data')
    with uow as unit_of_work:
        summary = unit_of_work.repo_obj.get_customer_ppg_scenario_data(custome_ppg_data,scenario_saved_id)
    if summary.exists():
        summary_df = pd.DataFrame(summary.values())
        return calc.calculate_overall_summary_by_filter(summary_df)
    raise exceptions.NoDataError(scenario_saved_id)

def compare_scenario(uow: _uow.AbstractUnitOfWork,saved_ids:list=None):
    """Return the Saved data based on with scenario id input.

    Parameters
    ----------
    scenario id(Optional)
        Saved Scenario Id.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    list
        list of dicts if successful, empty list otherwise.
    """

    with uow as unit_of_work:
        query_set = unit_of_work.repo_obj.filter_by_multiple_id(saved_ids)

    if not query_set.exists():
        raise  exceptions.NoDataError(saved_ids)
    return query_set

def search_scenario(uow: _uow.AbstractUnitOfWork,query_params:str=''):
    """Returns searched data based on given query.

    Parameters
    ----------
    query_params:
        search query.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    Queryset
        raw query set.
    """

    with uow as unit_of_work:
        query_params_values = list(query_params.values())
        if 'viewall' in query_params_values:
            query_params_values.remove('viewall')
        query_params_values[0] = '%' + query_params_values[0] + '%'
        query = {}
        if len(query_params_values) == 3:
            query = queries.search_pricing_query() 
        else: 
            query = queries.search_pricing_global_scenario_query() 
        _data = unit_of_work.search(query,query_params_values)
    if not _data:
        raise  exceptions.NoDataError(query_params)
    return _data

def get_pricing_retailer_ppg_elasticity_info(list_of_ppgs):
    pricing_retailer_ppg_elasticity_query = queries.generate_retailer_ppg_elasticity_info_query(list_of_ppgs)
    resp_dict = cmn_utils.raw_query_executor(pricing_retailer_ppg_elasticity_query,['customer','product_group','non_promo_price_elasticity','promo_price_elasticity'])

    return resp_dict


@pc_utils.validate_scenario
def get_pricing_scenario_meta_data(uow:_uow.AbstractUnitOfWork\
                                    ,suow:_uow.AbstractUnitOfWork=None\
                                    ,scenario_name=''\
                                    ,scenario_type=''\
                                    ,level=None\
                                    ,user:dict=None):
    """Return the  Baseline data based on meta id.

    Parameters
    ----------
    meta_id : model meta id to get baseline meta data. 
    allowed_groups(Optionl): Contains list of groups that assigned to user.
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    QuerySet
        queryset.
    """
    
    with uow as unit_of_work:
        if level=='ppg':
            return unit_of_work.repo_obj.get_meta_data_ppg_level()
        if level=='customer_ppg':
            return unit_of_work.repo_obj.get_meta_data_customer_ppg_level()
        
        return unit_of_work.repo_obj.get_all()


def get_pricing_scenario_data(uow:_uow.AbstractUnitOfWork,req_data=None,customer=None,level='customer',user:dict=None):
    """Return the  Baseline data based on meta id.

    Parameters
    ----------
    meta_id : model meta id to get baseline meta data. 
    allowed_groups(Optionl): Contains list of groups that assigned to user.
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    QuerySet
        queryset.
    """
    
    with uow as unit_of_work:
        if level=='customer':
            return unit_of_work.repo_obj.get_customer_scenario_data(customer) 
        if level == 'customer & product_group':
            return unit_of_work.repo_obj.get_customer_ppg_scenario_data(req_data)
        if level =='bulk':
            return unit_of_work.repo_obj.get_meta_data_bulk_data(req_data)
        return unit_of_work.repo_obj.get_all()

def get_download_scenario_planner(uow:_uow.AbstractUnitOfWork,req_data=None,customer=None,level='customer',user:dict=None):
    
    with uow as unit_of_work:
        if level=='customer':
            return unit_of_work.repo_obj.get_customer_scenario_data(customer) 
        if level == 'customer & product_group':
            customer_ppg_data = [{'customer': cust} for cust in customer]
            return unit_of_work.repo_obj.get_customer_ppg_scenario_data(customer_ppg_data)
        return unit_of_work.repo_obj.get_all()
    
def get_upload_scenario_data(uow:_uow.AbstractUnitOfWork,req_data=None,customer=None,level='customer',non_committed_id=None):
    """Return the  Baseline data based on meta id.

    Parameters
    ----------
    meta_id : model meta id to get baseline meta data. 
    allowed_groups(Optionl): Contains list of groups that assigned to user.
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    QuerySet
        queryset.
    """
    
    with uow as unit_of_work:
        if level=='customer':
            return unit_of_work.repo_obj.get_customer_scenario_data(non_committed_id,customer) 
        if level == 'customer,product_group':
            return unit_of_work.repo_obj.get_customer_ppg_scenario_data(non_committed_id,req_data)
    return unit_of_work.repo_obj.get_scenario_by_saved_id(non_committed_id)
        


def get_saved_pricing_scenario_data(uow:_uow.AbstractUnitOfWork,saved_id:int=None):
    """Return the  Baseline data based on meta id.

    Parameters
    ----------
    meta_id : model meta id to get baseline meta data. 
    allowed_groups(Optionl): Contains list of groups that assigned to user.
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    QuerySet
        queryset.
    """
    
    with uow as unit_of_work:
        return unit_of_work.repo_obj.get_meta_data_bulk_data(saved_id)

def get_df_from_query(uow:_uow.AbstractUnitOfWork,
                              models_list:list,
                              lvl:str,
                              lvl_param:list,
                              scenario_id:int=0,
                              mode:str='all',
                              start_index:int=0,
                              is_row_column=False,
                              is_scenario_planner=False,
                              customers=None,
                              db_table='scenario_planner_common_view'
                              ):
    """Return the  Baseline data data frame based on account name and product group.
    Parameters
    ----------
    models_list : list of baseline models 
    account name: retailer name
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    DataFrame
        dataframe.
    """

    for models in models_list: 
        with uow as unit_of_work:

            values = models['MODEL_VALUES'].value.copy()
            columns = models['MODEL_COLUMN'].value.copy() if not is_row_column else []
            new_columns = models['REQUIRED_COLUMN'].value+models['EXTRA_COLUMNS'].value
            if new_columns:
                for index,(name,_mask_name) in enumerate(new_columns):
                    cmn_utils.insert_extra_var(values,index,name)
                    if not is_row_column:
                        cmn_utils.insert_extra_var(columns,index,_mask_name)

            if is_scenario_planner:
                basedata_query = queries.agg_changed_inputs_with_customer_query(scenario_id,customers,db_table)
            elif mode == 'all':
                basedata_query = queries.all_agg_changed_inputs_query(scenario_id,db_table)   
            else:
                basedata_query = queries.agg_changed_inputs_query(scenario_id,db_table)
           
            base_data_df = unit_of_work.get_data_df(
                basedata_query,
                named_columns=columns,
                start_index=start_index
                )
        # breakpoint()  
        yield base_data_df

def raw_queryset_as_values_list( raw_qs,start_index:int=0):
    """ Returns Raw queryset as values list."""
    columns = raw_qs.columns[start_index:]
    for row in raw_qs:
        yield tuple(getattr(row, col) for col in columns)
          
def get_raw_dict_from_query(uow:_uow.AbstractUnitOfWork,
                              models_list:list,
                              scenario_id:int=0,
                              mode:str=None,
                              level:str='customer',
                              customers=None,
                              customers_with_inner_mode_all=None,
                              start_index:int=0,
                              is_row_column=False,
                              db_table='scenario_planner_common_view',
                              is_optimizer=False
                              ):
    """Return the  Baseline data data frame based on account name and product group.
    Parameters
    ----------
    models_list : list of baseline models 
    account name: retailer name
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    DataFrame
        dataframe.
    """

    for models in models_list: 
        with uow as unit_of_work:

            values = models['MODEL_VALUES'].value.copy()
            columns = models['MODEL_COLUMN'].value.copy() if not is_row_column else []
            new_columns = models['REQUIRED_COLUMN'].value+models['EXTRA_COLUMNS'].value
            if new_columns:
                for index,(name,_mask_name) in enumerate(new_columns):
                    cmn_utils.insert_extra_var(values,index,name)
                    if not is_row_column:
                        cmn_utils.insert_extra_var(columns,index,_mask_name)
        
            if is_optimizer:
                if level=='customer,product_group':
                    basedata_query = queries.all_agg_optimized_output_customer_query(scenario_id,customers)
                else:
                    basedata_query = queries.agg_optimized_output_customer_level_query(scenario_id)
            elif level=='customer,product_group':
                basedata_query = queries.filter_changed_inputs_with_customer_level_query(scenario_id,customers,customers_with_inner_mode_all,db_table)
            elif mode == 'all':
                basedata_query = queries.all_agg_changed_inputs_customer_level_query(scenario_id)
            elif mode=='filtered':
                basedata_query = queries.agg_changed_inputs_customer_level_query(scenario_id)
            else:
                basedata_query = queries.fetch_inputs_filter(scenario_id,db_table)
           
            base_data_df = unit_of_work.get_raw_query_data(
                basedata_query,
                start_index=start_index
                ) 

        yield base_data_df

def simulate_pricing_scenario(uow:_uow.AbstractUnitOfWork\
                              ,suow:_uow.AbstractUnitOfWork\
                            ,data=None\
                            ,user:dict=None
                            ):
    """Return the  Baseline data based on meta id.

    Parameters
    ----------
    meta_id : model meta id to get baseline meta data. 
    allowed_groups(Optionl): Contains list of groups that assigned to user.
    uow: Unit of work to get the data from the DB.

    Returns
    -------
    QuerySet
        queryset.
    """

    #save scenario
    scenario_saved = save_scenario(data, suow,user)
    
    level_params = list(map(lambda x:x['level_param'],data.get('pricing_payload')))
    customers = list(set(list(map(lambda y:y['customer_level_param'],list(filter(lambda x:x.get('inner_mode')=='all' and x.get('level_param')\
                        ,data.get('pricing_payload')))))))
    scenario_pricing_scenario_df = list(get_df_from_query(
                                uow,
                                [CONST.PricingScenario],
                                data.get('level'),
                                level_params,
                                scenario_id=scenario_saved.get('saved_id'),
                                mode=data.get('mode'),
                                is_row_column=True,
                                is_scenario_planner=True,
                                customers=customers,
                                db_table='scenario_planner_common_view'
                                
                            ))[0]
    
    
    scenario_pricing_scenario_df['is_divided_by_100'] = 0
    if data.get('scenario_type')=='optimizer':
        #Stage 2 optimizer code will be called here
       
        updated_pricing_df = calc.user_defined_input_setup(scenario_pricing_scenario_df,scenario_type=data.get('scenario_type'),is_scenario_planner=True)
        # updated_pricing_df['OptUnit_rounded'] = 1
        # updated_pricing_df['OptTradeMargin%'] = 0.8
        updated_pricing_df['optimized_net_net']=updated_pricing_df['net_net_new']
        updated_pricing_df['optimized_net_net_change']=updated_pricing_df['changed_net_net_change_percent']
        optimized_df = stage_2_optimizer(updated_pricing_df)
        scenario_pricing_scenario_df = scenario_pricing_scenario_df.reset_index(drop=True)
        optimized_df = optimized_df.reset_index(drop=True)
        optimized_df.rename(columns={'product_group':'PPG'},inplace=True)
        optimized_df = optimized_df.sort_values(by=['Customer','PPG'])
        scenario_pricing_scenario_df= scenario_pricing_scenario_df.sort_values(by=['customer','product_group'])
        selected_optimized_df = optimized_df[['Customer','PPG','newnetnet','net_net_change','deadnet_new_stage2']]
        scenario_pricing_scenario_df = pd.merge(scenario_pricing_scenario_df, selected_optimized_df,  how='left', left_on=['customer','product_group'], right_on = ['Customer','PPG'])
        scenario_pricing_scenario_df.drop(['net_net_new', 'dead_net_new','changed_net_net_change_percent','changed_dead_net_change_percent'], axis=1, inplace=True)
        scenario_pricing_scenario_df.rename(columns={'newnetnet':'net_net_new'\
                                                    ,'net_net_change':'changed_net_net_change_percent'\
                                                    ,'deadnet_new_stage2':'dead_net_new'
                                                    },inplace=True)
        
        scenario_pricing_scenario_df['changed_dead_net_change_percent'] = scenario_pricing_scenario_df['changed_net_net_change_percent']
 
    
    return calc.calculate_pricing_scenario(scenario_pricing_scenario_df=scenario_pricing_scenario_df\
                                           ,saved_scenario_id=scenario_saved.get('saved_id'),
                                           scenario_type=data.get('scenario_type','simulator'),
                                           
                                           )
    
def get_scenarios(uow: _uow.AbstractUnitOfWork,scenario_id: int=None):
    query_set  = get_scenario(
                        uow,
                        scenario_id
                    ).order_by('-id')

    scenario_inputs = {}
    if query_set.exists():

        scenario_inputs = list(map(lambda x:{
                                            'product_group':x.level_param,
                                            'ogsm_type':x.ogsm_param
                                            }\
                                            ,query_set))
    
    return scenario_inputs



def publish_scenario(
                    uow: _uow.AbstractUnitOfWork\
                    ,non_committed_id=0\
                    ,user=None):
    
    with uow as unit_of_work:
       non_commited_scenario = unit_of_work.repo_obj.get(non_committed_id)
       unit_of_work.repo_obj.update(non_committed_id,{'is_published':True})
       unit_of_work.commit()

    if not non_committed_id:
        return  {'saved_id not provided'}
    publisheddata_query, params = queries.update_published_flag(non_committed_id\
                                                                ,non_commited_scenario.module_type\
                                                                ,non_commited_scenario.scenario_type)

    with closing(connection.cursor()) as cursor:
        cursor.execute(publisheddata_query,params)
    return {'data':'published successfully'}
    
def save_post_load_scenario(
                    uow: _uow.AbstractUnitOfWork\
                    ,non_committed_id=0\
                    ,user=None):

    with uow as unit_of_work:
        if non_committed_id:
            unit_of_work.repo_obj.update_status_type(non_committed_id,{'is_committed':True})
            unit_of_work.commit()
            return {'data':'saved successfully'}

    return {'data':'provide id'}

def get_download_data(
                    uow: _uow.AbstractUnitOfWork\
                    ,non_committed_id=0
                ):
    with uow as unit_of_work:
        if non_committed_id:
            data = unit_of_work.repo_obj.get_scenario_by_saved_id(non_committed_id)
            return data
        
def get_ppg_data_customer(uow:_uow.AbstractUnitOfWork,req_data=None,customer=None,level='customer',user:dict=None):
    with uow as unit_of_work:
        if level=='customer':
            return unit_of_work.repo_obj.get_customer_scenario_data(customer) 
        if level == 'customer & product_group':
            return unit_of_work.repo_obj.get_customer_ppg_scenario_data(req_data)
        if level =='bulk':
            return unit_of_work.repo_obj.get_meta_data_bulk_data(req_data)
        return unit_of_work.repo_obj.get_all()
    
def save_uploaded_promo_scenaro(**data):
    # breakpoint()
    uploaded_scenario_batch_insert(CONST.UploadedPricingScenario\
                     ,data.get('request_data').get('changed_records')\
                    ,data.get('request_data').get('pricing_saved_scenario_id')
                     )
    
def uploaded_scenario_batch_insert(
                     models,
                     n_records,
                     instance_id,
                    is_row_column=False):

    values = models['MODEL_VALUES'].value.copy()
    columns = models['MODEL_COLUMN'].value.copy() if not is_row_column else []
    # breakpoint()
    new_columns = models['REQUIRED_COLUMN'].value+models['EXTRA_COLUMNS'].value
    if new_columns:
        for index,(name,_mask_name) in enumerate(new_columns):
            cmn_utils.insert_extra_var(values,index,name)
            if not is_row_column:
                cmn_utils.insert_extra_var(columns,index,_mask_name)
    basedata_query = queries.uploaded_scenario_batch_insert_query(utils.convert_list_to_string_list(values),
                                    len(n_records))                           
    basedata_query = re.sub("[\"\']", "", basedata_query)
    params = []
    
    for i in n_records:
        params.append([instance_id\
                        ,i['level_param']\
                        ,i['customer_level_param']\
                        ,i['ogsm_param']\
                        ,i['changed_net_net_change_percent']\
                        ,i['changed_dead_net_change_percent']\
                        ,i['list_price_new']\
                        ,i['pack_weight_new']\
                        ,i['dead_net_new']\
                        ,i['net_net_new']\
                        ,i['non_promo_price_new']\
                        ,i['promo_price_new']\
                        ,i['floor_price']
                    ])
   
    with closing(connection.cursor()) as cursor:
        cursor.execute(queries.delete_changed_scenario_query(instance_id))
        cursor.executemany(basedata_query, params)

def upload_scenario(request_data: dict\
                  , uow: _uow.AbstractUnitOfWork\
                    ,non_committed_id\
                    ,user\
                    ,is_commit=False):
    """Save Scenrio

    Parameters
    ----------
    data
        To insert the data into the database.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    str
        Success or Failure in inserting the data

    """
    data = request_data.pop('payload')
    changed_records = {
        'changed_records':request_data.pop('pricing_payload'),
    }
    scenario_instance_id = non_committed_id
    with uow as unit_of_work:
        if non_committed_id:
            data['is_committed'] = True
            unit_of_work.repo_obj.update(scenario_instance_id,data)
            unit_of_work.commit()
        else:
            data['is_committed'] = is_commit
            scenario_instance = unit_of_work.repo_obj.add(data)
            scenario_instance_id = scenario_instance.id
            unit_of_work.commit()
    changed_records.update({
        'pricing_saved_scenario_id':scenario_instance_id
    })
    save_uploaded_promo_scenaro(request_data=changed_records)
    return {'uploaded_id':scenario_instance_id}

def get_ppg_data(uow:_uow.AbstractUnitOfWork,non_committed_id=None):

    with uow as unit_of_work:
        if non_committed_id:
            return unit_of_work.repo_obj.get_ppg_cutomer_data(non_committed_id)

def get_customer_data(uow:_uow.AbstractUnitOfWork,non_committed_id=None):

    with uow as unit_of_work:
        if non_committed_id:
            return unit_of_work.repo_obj.get_ppg_cutomer_level_data(non_committed_id)
        else:
            return unit_of_work.repo_obj.get_ppg_cutomer_level_data()