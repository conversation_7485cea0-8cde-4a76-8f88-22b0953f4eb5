# Ignore the .env file


# ignore log files
# **/logs/
# **/*.log

# ignore joblib files
*.joblib

# ignore package version file
**/_version.py

# Ignore dataset and model folders
data
artifacts
mlruns
dbs/
apps/common/migrations/
#conf/local
**/credential*
**/secret*

# ignore code archive folder
code-archives

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/
docs/source/_autosummary/
docs_template/source/_autosummary
docs_template/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# Pycharm folders
.idea/

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
#.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# Vscode project settings
.vscode

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# ignore vim swap files
*.swp


# credetials and tokens
scripts/code_archive/token.pickle
scripts/automatic_upload_to_gdrive/client_secrets.json
scripts/automatic_upload_to_gdrive/*.txt
scripts/code_archive/client_secrets.json
scripts/code_archive/*.txt
scripts/code_archive/*.zip

#ignore sqlite3 db
db.sqlite3
testsqldb.sqlite3

dpt-env/

# ignore the venv file
srm_mars/

#ignoring .env file
.env