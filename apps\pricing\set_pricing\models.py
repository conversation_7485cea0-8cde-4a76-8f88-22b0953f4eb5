from django.conf import settings
from django.db import models
from apps.pricing.pricing_common.models import PricingCommonModel
from config.db_handler import db_table_format

class SetPriceIncreaseView(models.Model):
    id = models.IntegerField(primary_key=True)
    ogsm_type=models.CharField(max_length=100,verbose_name="OGSM Type")
    product_group=models.CharField(max_length=100,verbose_name="Technology")
    technology=models.CharField(max_length=100,verbose_name="Technology")
    competitor_follows=models.CharField(max_length=100,verbose_name="OGSM Type")
    type_of_price_inc=models.CharField(max_length=100,verbose_name="Customer")
    brand=models.CharField(max_length=100,verbose_name="Brand")
    status_flag=models.CharField(max_length=100,verbose_name="Technology")
    nsv_sum=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV [€]')
    cogs_t=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='COGS / t [€]')
    list_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='LP [€]')
    pack_weight_kg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Pack Weight [kg]')
    shelf_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Non Promo Price per kg [€]')
    changed_shelf_price_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='ChangedNon Promo Price per Kg [€]')
    changed_lp_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='ChangedLP [€]')
    changed_pack_weight_kg_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed Pack Weight [kg]')
    changed_cogs_t_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    list_price_change_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='List Price Change Percent')
    list_price_change_percent_kg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='List Price Change Percent KG')
    nsv_price_impact_direct=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact direct')
    nsv_price_impact_indirect=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    nsv_price_impact_base=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact base')
    new_lp_after_base_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    type_of_base_inc_after_change=models.CharField(max_length=100,verbose_name="Type Of Base Inc After  Change",default='base')
    shelf_price_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    list_price_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    pack_weight_kg_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    dead_net_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    net_net_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    dead_net=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    net_net=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    changed_non_promo_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    changed_promo_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    non_promo_price_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    non_promo_price_per_unit=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    promo_price_per_unit=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    promo_price_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    non_promo_price_elasticity=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')

    promo_price_elasticity=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='New LP after base price')

    net_non_promo_price_elasticity=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='New LP after base price')
    competitor_coefficient=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='New LP after base price')
    min_shelf_price = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='Avg Shelf Promo Price')
    floor_price = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='Floor Price')
    promo_sold_volume = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='promo volume')
    non_promo_sold_volume = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='non promo volume')
    promo_sold_volume_new = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='promo volume')
    non_promo_sold_volume_new = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='non promo volume')


    class Meta:
        '''Meta Class For Model Saved Scenario.'''
        verbose_name = "Set Pricing Increase Scenario"
        db_table = 'set_pricing_ppg_level_view'
        ordering = ["-nsv_sum"]
        if hasattr(settings, 'PRICING_DATABASE_SCHEMA'):
            db_table = db_table_format(db_table,settings.PRICING_DATABASE_SCHEMA)

class SetPriceIncreaseOGSMView(models.Model):
    id = models.IntegerField(primary_key=True)
    ogsm_type=models.CharField(max_length=100,verbose_name="OGSM Type")
    product_group=models.CharField(max_length=100,verbose_name="Technology")
    technology=models.CharField(max_length=100,verbose_name="Technology")
    competitor_follows=models.CharField(max_length=100,verbose_name="OGSM Type")
    type_of_price_inc=models.CharField(max_length=100,verbose_name="Customer")
    brand=models.CharField(max_length=100,verbose_name="Brand")
    status_flag=models.CharField(max_length=100,verbose_name="Technology")
    nsv_sum=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV [€]')
    cogs_t=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='COGS / t [€]')
    list_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='LP [€]')
    pack_weight_kg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Pack Weight [kg]')
    shelf_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Non Promo Price per kg [€]')
    changed_shelf_price_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='ChangedNon Promo Price per Kg [€]')
    changed_lp_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='ChangedLP [€]')
    changed_pack_weight_kg_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed Pack Weight [kg]')
    changed_cogs_t_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    list_price_change_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='List Price Change Percent')
    list_price_change_percent_kg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='List Price Change Percent KG')
    nsv_price_impact_direct=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact direct')
    nsv_price_impact_indirect=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    nsv_price_impact_base=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact base')
    new_lp_after_base_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    type_of_base_inc_after_change=models.CharField(max_length=100,verbose_name="Type Of Base Inc After  Change",default='base')
    min_shelf_price = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='Avg Shelf Promo Price')
    floor_price = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='Floor Price')
    
    class Meta:
        '''Meta Class For Model Saved Scenario.'''
        verbose_name = "Set Pricing Increase Scenario"
        db_table = 'set_pricing_ppg_ogsm_level_view'
        ordering = ["-nsv_sum"]
        if hasattr(settings, 'PRICING_DATABASE_SCHEMA'):
            db_table = db_table_format(db_table,settings.PRICING_DATABASE_SCHEMA)
            
class SetPricingCommonView(PricingCommonModel):
    product_group=models.CharField(max_length=100,verbose_name="Product Group")
    brand=models.CharField(max_length=100,verbose_name="Brand")
    technology=models.CharField(max_length=100,verbose_name="Technology")
    sell_in_volume_t=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Sell In Volume [t]')
    bww_gsv=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='BWW / GSV [€]')
    nsv=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV [€]')
    nsv_t=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV/t [€]')
    tt=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='TT%')
    net_net=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Net Net [€]')
    dead_net=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Promo NN (=Dead Net) [€]')
    cogs_t=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='COGS / t [€]')
    gmac_abs=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='GMAC abs [€]')
    gmac=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='GMAC %')
    tax=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Tax')
    sell_out_volume_sales_t=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Sell Out Volume Sales [t]')
    unit_sales_000=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Unit Sales (000)')
    value_sales_rsv=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Value Sales = RSV [€]')
    floor_price = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Floor Price')
    sell_in_unit=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    non_promo_price_per_unit_ppg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Avg Shelf Price')
    promo_price_per_unit_ppg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Avg Shelf Promo Price')
    is_optimizer_ppg = models.BooleanField(default=False,verbose_name='is_optimizer_ppg')
    min_shelf_price = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='Avg Shelf Promo Price')
    exclude_retailer = models.BooleanField(default=False,verbose_name='exclude retailer')
    net_net_nsv_product = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                  , null=False,verbose_name='net net lsv product')
    lp_nsv_product = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                  , null=False,verbose_name='lp nsv product')
    is_outlier = models.BooleanField(default=False,verbose_name='exclude retailer')
    
    class Meta:
        """ Meta Class For Coefficient Mapping Model."""
        verbose_name = "Pricing Scenario"
        db_table = 'set_pricing_common_view'
        ordering = ["-nsv"]
        if hasattr(settings, 'PRICING_DATABASE_SCHEMA'):
            db_table = db_table_format(db_table,settings.PRICING_DATABASE_SCHEMA)

class SetPricingOptimizerView(PricingCommonModel):
    product_group=models.CharField(max_length=100,verbose_name="Product Group")
    brand=models.CharField(max_length=100,verbose_name="Brand")
    technology=models.CharField(max_length=100,verbose_name="Technology")
    sell_in_volume_t=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Sell In Volume [t]')
    bww_gsv=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='BWW / GSV [€]')
    nsv=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV [€]')
    nsv_t=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV/t [€]')
    tt=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='TT%')
    net_net=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Net Net [€]')
    dead_net=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Promo NN (=Dead Net) [€]')
    cogs_t=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='COGS / t [€]')
    gmac_abs=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='GMAC abs [€]')
    gmac=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='GMAC %')
    tax=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Tax')
    sell_out_volume_sales_t=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Sell Out Volume Sales [t]')
    unit_sales_000=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Unit Sales (000)')
    value_sales_rsv=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Value Sales = RSV [€]')
    floor_price = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Floor Price')
    sell_in_unit=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    non_promo_price_per_unit_ppg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Avg Shelf Price')
    promo_price_per_unit_ppg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Avg Shelf Promo Price')
    min_shelf_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Avg Shelf Promo Price')
    is_optimizer_ppg = models.BooleanField(default=False,verbose_name='is_optimizer_ppg')
    exclude_retailer = models.BooleanField(default=False,verbose_name='exclude retailer')
    net_net_nsv_product = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                  , null=False,verbose_name='net net lsv product')
    lp_nsv_product = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                  , null=False,verbose_name='lp nsv product')
    is_outlier = models.BooleanField(default=False,verbose_name='exclude retailer')
    
    class Meta:
        """ Meta Class For Coefficient Mapping Model."""
        verbose_name = "Pricing Scenario"
        db_table = 'set_pricing_optimizer_view'
        ordering = ["-nsv"]
        if hasattr(settings, 'PRICING_DATABASE_SCHEMA'):
            db_table = db_table_format(db_table,settings.PRICING_DATABASE_SCHEMA)

class SetPricingOptimizerPPGLevelView(models.Model):
    id = models.IntegerField(primary_key=True)
    ogsm_type=models.CharField(max_length=100,verbose_name="OGSM Type")
    product_group=models.CharField(max_length=100,verbose_name="Technology")
    technology=models.CharField(max_length=100,verbose_name="Technology")
    competitor_follows=models.CharField(max_length=100,verbose_name="OGSM Type")
    type_of_price_inc=models.CharField(max_length=100,verbose_name="Customer")
    brand=models.CharField(max_length=100,verbose_name="Brand")
    status_flag=models.CharField(max_length=100,verbose_name="Technology")
    nsv_sum=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV [€]')
    cogs_t=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='COGS / t [€]')
    list_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='LP [€]')
    pack_weight_kg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Pack Weight [kg]')
    shelf_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Non Promo Price per kg [€]')
    changed_shelf_price_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='ChangedNon Promo Price per Kg [€]')
    changed_lp_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='ChangedLP [€]')
    changed_pack_weight_kg_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed Pack Weight [kg]')
    changed_cogs_t_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    list_price_change_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='List Price Change Percent')
    list_price_change_percent_kg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='List Price Change Percent KG')
    nsv_price_impact_direct=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact direct')
    nsv_price_impact_indirect=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    nsv_price_impact_base=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact base')
    new_lp_after_base_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    type_of_base_inc_after_change=models.CharField(max_length=100,verbose_name="Type Of Base Inc After  Change",default='base')
    shelf_price_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    list_price_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    pack_weight_kg_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    dead_net_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    net_net_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    dead_net=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    net_net=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    changed_non_promo_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    changed_promo_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    non_promo_price_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    non_promo_price_per_unit=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    promo_price_per_unit=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    promo_price_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    non_promo_price_elasticity=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')

    promo_price_elasticity=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='New LP after base price')

    net_non_promo_price_elasticity=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='New LP after base price')
    competitor_coefficient=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='New LP after base price')

    min_shelf_price = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='Avg Shelf Promo Price')
    floor_price = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='Floor Price')
    class Meta:
        '''Meta Class For Model Saved Scenario.'''
        verbose_name = "Set Pricing Increase Scenario"
        db_table = 'set_pricing_optimizer_ppg_level_view'
        ordering = ["-nsv_sum"]
        if hasattr(settings, 'PRICING_DATABASE_SCHEMA'):
            db_table = db_table_format(db_table,settings.PRICING_DATABASE_SCHEMA)