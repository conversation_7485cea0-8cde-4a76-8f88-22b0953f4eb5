""" Test Optimizer Models"""
import pytest
from apps.common import models as db_model

@pytest.mark.django_db
def test_create_saved_scenario(_django_data_setup):
    """test_create_saved_scenario

    Args:
        _django_data_setup (db): inserting data
    """
    assert set(u.id for u in db_model.SavedScenario.objects\
        .filter(scenario_type='promo',is_delete=False)) == {5,6} # pylint: disable=no-member

@pytest.mark.django_db
def test_create_scenario_promotion_save(_django_data_setup):
    """test_create_scenario_promotion_save

    Args:
        _django_data_setup (db): inserting data
    """
    assert set(u.id for u in db_model.ScenarioPromotionSave.objects\
        .filter(saved_scenario__scenario_type='promo',is_delete=False)) == {5,6} # pylint: disable=no-member
