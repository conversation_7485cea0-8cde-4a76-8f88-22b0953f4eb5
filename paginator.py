"""Import statement"""
from collections import OrderedDict
from functools import wraps
from rest_framework.pagination import PageNumberPagination


class StandardPaginator(PageNumberPagination):
    """
    We are required to create a custom paginator so that we can enable
    page_size control through query parameter.

    We just need to set the name of the query parameter, the rest is
    taken care by the base class `PageNumberPagination`.
    """
    page_size_query_param = 'page_size'

    def get_paginated_response(self,data):
        return OrderedDict([
            ('count', self.page.paginator.count),
            ('next', self.get_next_link()),
            ('previous', self.get_previous_link()),
            ('results', data)
        ])

def pagination(extra_kwargs):
    def _decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args,**kwargs)
            sp_instance = StandardPaginator()
            page = sp_instance.paginate_queryset(result,args[1])
            if page is not None:
                serializer = extra_kwargs.get('serializer')(page, many=True)
                resp = sp_instance.get_paginated_response(serializer.data)
                return resp
            return 
        return wrapper
    return _decorator



