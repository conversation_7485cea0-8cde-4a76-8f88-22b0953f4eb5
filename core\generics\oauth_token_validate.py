import os
import json
import structlog
from functools import wraps
import string
from django.conf import settings
from dotenv import load_dotenv
from django.http import HttpResponse, HttpRequest
import jwt
from rest_framework.authtoken.models import Token
from rest_framework.exceptions import AuthenticationFailed
import requests
from cryptography.hazmat.primitives.asymmetric.rsa import RSAPublicNumbers
import base64
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import serialization
from utils import convert_string_of_list_to_list
from apps.user.auth import(token_generator,token_validity,user_group)
from core.generics import constants as CONST

tenant_id = os.environ.get("AZ_TENANT", '1234567')
client_id = os.environ.get("AZ_CLIENT_ID", '1234567')

b2c_policy_name = None
b2c_domain_name = None

logger = structlog.get_logger(__name__)

try :
    ENV_MRK = os.environ.get("ENV_MRK", "0")
    if ENV_MRK == "0":
        dotenv_path = os.path.join(os.getcwd(), os.environ.get("ENV")) + ".env"
        load_dotenv(dotenv_path)
except Exception as exception :
    pass

class AuthError(Exception):
    def __init__(self, error_msg: str, status: int):
        super().__init__(error_msg)

        self.error_msg = error_msg
        self.status = status

def ensure_bytes(key):
    if isinstance(key, str):
        key = key.encode('utf-8')
    return key

def decode_value(val):
    decoded = base64.urlsafe_b64decode(ensure_bytes(val) + b'==')
    return int.from_bytes(decoded, 'big')

def rsa_pem_from_jwk(jwk):
    return RSAPublicNumbers(
        n=decode_value(jwk['n']),
        e=decode_value(jwk['e'])
    ).public_key(default_backend()).public_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PublicFormat.SubjectPublicKeyInfo
    )

def get_token_auth_header(request: HttpRequest,type='token',is_check=False):
    auth = request.headers.get("Authorization", None)
    if not auth:
        raise AuthError("Authentication error: Authorization header is missing", 401)

    parts = auth.split()
    if parts[0].lower() != type.lower():
        if is_check:
            print(parts[0],'passed')
            return parts[0]
        raise AuthError(
            "Authentication error: Authorization header must start with ' Bearer'", 401
        )
    elif len(parts) == 1:
        raise AuthError("Authentication error: Token not found", 401)
    elif len(parts) > 2:
        raise AuthError(f"Authentication error: Authorization header must be '{type.title()} <token>'", 401)

    token = parts[1]
    value = type if is_check else token
    return value

def check_token_header(request,type):
    print(type,'check')
    _type = get_token_auth_header(request,type=type,is_check=True)
    return _type

def __decode_jwt(token_version, token, rsa_key, alg):
   
    if token_version == "1.0":
        _issuer = f"https://sts.windows.net/{tenant_id}/"
        _audience = f"api://{client_id}"
    else:
        _issuer = f"https://login.microsoftonline.com/{tenant_id}/v2.0"
        _audience = f"{client_id}"
    try:
        
        k = rsa_pem_from_jwk(rsa_key)
        payload = jwt.decode(
            token, k, algorithms=[alg], audience=_audience, issuer=_issuer,options={'verify_signature':False}
        )
        return payload
    except jwt.ExpiredSignatureError:
        raise AuthError("Token error: The token has expired", 401)
    except Exception:
        raise AuthError("Token error: Unable to parse authentication", 401)

def __get_token_version(token, alg):
    
    unverified_claims = jwt.decode(token, algorithms=[alg], options={'verify_signature':False})
    if unverified_claims.get("ver"):
        return unverified_claims["ver"]
    else:
        raise AuthError("Missing version claim from token. Unable to validate", 403)

def get_token_claims(request: HttpRequest, alg:string):
    token = get_token_auth_header(request)
    unverified_claims = jwt.decode(token, algorithms=[alg], options={'verify_signature':False})
    return unverified_claims

def validate_scope(required_scope: str, request: HttpRequest, alg:string):
    has_valid_scope = False
    token = get_token_auth_header(request)
    unverified_claims = jwt.decode(token, algorithms=[alg], options={'verify_signature':False})
    
    ## check to ensure that either a valid scope or a role is present in the token
    if unverified_claims.get("scp") is None and unverified_claims.get("roles") is None:
        raise AuthError(
            "IDW10201: No scope or app permission (role) claim was found in the bearer token", 403
        )

    is_app_permission = True if unverified_claims.get("roles") is not None else False

    if is_app_permission:
        if unverified_claims.get("roles"):
            # the roles claim is an array
            for scope in unverified_claims["roles"]:
                if scope.lower() == required_scope.lower():
                    has_valid_scope = True
        else:
            raise AuthError(
                "IDW10201: No app permissions (role) claim was found in the bearer token", 403
            )
    else:
        if unverified_claims.get("scp"):
            # the scp claim is a space delimited string
            token_scopes = unverified_claims["scp"].split()
            for token_scope in token_scopes:
                if token_scope.lower() == required_scope.lower():
                    has_valid_scope = True
        else:
            raise AuthError("IDW10201: No scope claim was found in the bearer token", 403)

    if is_app_permission and not has_valid_scope:
        raise AuthError(
            f'IDW10203: The "role" claim does not contain role {required_scope} or was not found',
            403,
        )
    elif not has_valid_scope:
        raise AuthError(
            f'IDW10203: The "scope" or "scp" claim does not contain scopes {required_scope} or was not found',
            403,
        )

def get_rsa_key(tenant_id: string, token:string):
    
    rsa_key = {}
    alg = ''
    logger.info("Inside get_rsa_key")
    print("Inside rsa_key")
    try:
        # breakpoint()
        url = f"https://login.microsoftonline.com/{tenant_id}/discovery/v2.0/keys"
        logger.info(f" url - {url}")
        print(f"url - {url}")
        resp = requests.get(url, timeout = None)
        print(f"Response - {resp}")
        logger.info(f" Response - {resp}")
        if resp.status_code != 200:
            raise AuthError("Problem with Azure AD discovery URL", status=404)
        jwks = resp.json()
        unverified_header = jwt.get_unverified_header(token)
        alg = unverified_header['alg']
        for key in jwks["keys"]:
            if key["kid"] == unverified_header["kid"]:
                rsa_key = {
                    "kty": key["kty"],
                    "kid": key["kid"],
                    "use": key["use"],
                    "n": key["n"],
                    "e": key["e"],
                }

    except Exception as error:
        logger.info(f"Error is {error}")
        print(error)
        return HttpResponse(
            content="Invalid_header: Unable to parse authentication", status=401
        )
    return rsa_key, alg

def authenticate_credentials(key):

        try:
            token = Token.objects.get(key = key)
        except Token.DoesNotExist:
            raise AuthenticationFailed("Invalid Token")
        
        if not token.user.is_active:
            raise AuthenticationFailed("User is not active")

        is_expired, token = token_validity.token_expire_handler(token)
        if is_expired:
            raise AuthenticationFailed("The Token is expired")
        
        return (token.user, token)

def sso_auth(f,*args,**kwargs):
    try:
        print("inside sso_auth")
        index = kwargs.get('index',0)
        token = get_token_auth_header(args[index],type='Bearer')
        rsa_key, alg = get_rsa_key(tenant_id, token)
        
        if rsa_key:
            try:
                token_version = __get_token_version(token, alg)
                payload = __decode_jwt(token_version, token, rsa_key, alg)
                payload['auth_token'] = token
                payload['auth_type'] = 'SSO'
                kwargs["auth_data"] = payload
                return f(*args, **kwargs)
            except AuthError as auth_err:
                HttpResponse(content=auth_err.error_msg, status=auth_err.status)
                return HttpResponse(
                    content="Invalid header error: Unable to find appropriate key", status=401
                )
    except Exception as e:
        return HttpResponse(
            content="Invalid_header: Unable to parse authentication", status=401
        )

def token_auth(f,*args,**kwargs):
    try:
       
        index = kwargs.get('index',0)
        token = get_token_auth_header(args[index],type='Token')
        if token:
            try:
                user,token = authenticate_credentials(token)
                payload={}
                payload['auth_token'] = token.key
                payload['auth_type'] = 'Token'
                payload['user_id'] = user.id
                payload['username'] = user.name
                payload['email'] = user.email
                kwargs["auth_data"] = {**payload,**kwargs['data']}
                return f(*args, **kwargs)
            except AuthError as auth_err:
                HttpResponse(content=auth_err.error_msg, status=auth_err.status)
                return HttpResponse(
                    content="Invalid header error: Unable to find appropriate key", status=401
                )
    except Exception as e:
        return HttpResponse(
            content=f"Invalid_header: Unable to parse authentication.See more error:{str(e)}", status=401
        )

def request_accessor(extra_kwargs):
    def _decorator(func):
        @wraps(func)
        def wrapper_func(*args, **kwargs):
            print('req acc func')
            kwargs['index'] = extra_kwargs['index']
            return func(*args, **kwargs)
        return wrapper_func
    return _decorator

     
def auth_validate(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        try:
            print("Inside auth_validate")
            index = kwargs.get('index',0)
            try:
                body_unicode = args[index].body.decode('utf-8')
                kwargs['data'] = json.loads(body_unicode) if body_unicode else {}
            except ValueError as _valexp:
                try:
                    kwargs['data'] = args[index].data
                except:
                    kwargs['data'] = {}
        
            if ('email' and 'password') in kwargs['data']:
                return token_generator.generate_token(f,*args,**kwargs)
            kwargs['data'] = {}
            for auth_type in CONST.AUTH_TYPES:
                _type =  check_token_header(args[index],type=auth_type)
                if _type:
                    if _type == 'Token':
                        return token_auth(f,*args,**kwargs)
                    return sso_auth(f,*args,**kwargs)
        except Exception as e:
            return HttpResponse(
                content=str(e), status=401
            )
          
    return decorated

def get_user_group(data):
    print("Inside get_user_group")
    auth_data = data.get('auth_data')
    allowed_groups = user_group.get_token_user_group()
    if auth_data.get('auth_type') == 'Token':
        return allowed_groups['group_name'].tolist()
    token = auth_data.get('auth_token')
    user_id = auth_data.get('oid')
    url = f"https://graph.microsoft.com/v1.0/users/{user_id}/getMemberGroups"
    payload = json.dumps({"securityEnabledOnly": True})
    headers = {
        "Authorization": f"{token}",
        "Content-Type": "application/json",
    }
    response = requests.request("POST", url, headers=headers, data=payload)
    message =  json.dumps(response.json())
    
    allowed_groups_ids = []
    if response:
        group_ids = response.json()["value"]
        allowed_groups_ids = list(filter(lambda x:x in allowed_groups['group_id'].tolist(), group_ids))
        message = "Successfully retrieved user data."
    
    allowed_groups = allowed_groups.loc[allowed_groups['group_id'].isin(allowed_groups_ids)\
        ,['group_name','is_national_region','limited_access','is_promo','is_pricing','is_pricing_all','is_promo_all']]
    is_national_region = allowed_groups.loc[(allowed_groups['is_national_region']==True)]
    
    is_promo = allowed_groups.loc[allowed_groups['is_promo']==True]
    is_pricing = allowed_groups.loc[allowed_groups['is_pricing']==True]
    has_all_promo_access = allowed_groups.loc[allowed_groups['is_promo_all']==True]
    has_all_pricing_access = allowed_groups.loc[allowed_groups['is_pricing_all']==True]
    limited_access = is_pricing['limited_access'].all()
    if allowed_groups.shape[0]:
        allowed_groups = allowed_groups['group_name'].unique().tolist()
    else:
        allowed_groups = []
    _data = {
    "message": message,
        "allowed_groups":allowed_groups,
        "is_national_promotion_allowed":bool(is_national_region.shape[0]),
        'limited_access':limited_access,
        'is_promo':bool(is_promo.shape[0]),
        'is_pricing':bool(is_pricing.shape[0]),
        'has_all_promo_access':bool(has_all_promo_access.shape[0]),
        'has_all_pricing_access':bool(has_all_pricing_access.shape[0])
        
    }
    return _data

def get_user_info(auth_data):

    user_id = auth_data.get('oid')
    user_email = None
    
    if 'unique_name' in auth_data:
        if '@effem.com' in auth_data['unique_name']:
            user_email = auth_data['unique_name']
    if 'preferred_username' in auth_data:
        if '@effem.com' in auth_data['preferred_username']:
            user_email = auth_data['preferred_username']
    
    return  {
        'username':auth_data['name'],
        'user_id':user_id,
        'email':user_email,
    }
    
def requires_auth(f):
    @auth_validate
    @wraps(f)
    def decorated(*args, **kwargs):
        index = kwargs.get('index',0)
        print("Inside requires_auth")
        try:
            index = kwargs.get('index',0)
            if 'auth_type' in  kwargs['auth_data']:
                if kwargs['auth_data']['auth_type'] == 'SSO':
                    user_group_info = get_user_group(kwargs)
                    user_info = get_user_info(kwargs['auth_data'])

                    allowed_groups = user_group_info.get('allowed_groups')
                    if allowed_groups:
                        args[index]._user = {
                                **user_info,
                                'allowed_groups':allowed_groups,
                                'is_national_promotion_allowed':user_group_info.get('is_national_promotion_allowed'),
                                'limited_access':user_group_info.get('limited_access'),
                                'is_promo':user_group_info.get('is_promo'),
                                'is_pricing':user_group_info.get('is_pricing'),
                                'has_all_promo_access':user_group_info.get('has_all_promo_access'),
                                'has_all_pricing_access':user_group_info.get('has_all_pricing_access')
                            }
                    else:
                        return HttpResponse(
                            content="Permission Denied: you don't have access to this tool.", status=401
                        )

                else:
                   
                    args[index]._user = {
                        **kwargs['auth_data'],
                        'allowed_groups':get_user_group(kwargs),
                        'is_national_promotion_allowed':True,
                        'limited_access':False,
                        'is_promo':False,
                        'is_pricing':False
                    }

                return f(*args, **kwargs)
            return HttpResponse(
                        content="Invalid Permission: Unable to verify user group", status=401
                    )
        except Exception as e:
            logger.exception(str(e))
            return HttpResponse(
                content="Invalid_header: Unable to parse authentication", status=401
            )
    return decorated
    