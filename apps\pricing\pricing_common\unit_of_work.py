import pandas as pd
from core.generics.unit_of_work import ORMModelUnitOfWork 
from . import repository as rp

class PricingCommonScenarioUnitOfWork(ORMModelUnitOfWork):
    def __init__(self, trans):
        super().__init__(trans, rp.PricingScenarioCommonRepository)
    
    def raw_queryset_as_values_list(self, raw_qs,start_index:int=0):
        """ Returns Raw queryset as values list."""
        columns = raw_qs.columns[start_index:]
        for row in raw_qs:
            yield tuple(getattr(row, col) for col in columns)

    def get_data_df(self, query_string,
                    params:list=None,
                    named_columns:list=None,
                    start_index:int=0):
                    
        """ Returns Dataframe."""
        columns=named_columns[start_index:]
        results = self.repo_obj._model.raw(query_string,params)
        if not named_columns:
            columns = results.columns[start_index:]
        return pd.DataFrame(
            self.raw_queryset_as_values_list(results,start_index), columns=columns
        )
    
    def get_json_data(self, query_string,
                    params:list=None,
                    named_columns:list=None,
                    start_index:int=0):
        return self.get_data_df(query_string,
                    params,
                    named_columns=named_columns,
                    start_index=start_index).to_json(orient="records")


    def get_raw_query_data(self, query_string,
                        params:list=None,start_index:int=0):
        """ Returns Raw queryset data."""
        results = self.repo_obj._model.raw(query_string, params)
        columns = results.columns[start_index:]
        return [
            dict(tuple((col, getattr(row, col)) for col in columns)) for row in results
        ]

    def get_raw_query_list(self, query_string,
                         params,
                         start_index:int=0):
        """ Returns Raw queryset as list."""
        results = self.repo_obj._model.raw(query_string, params)
        columns = results.columns[start_index:]
        return [
            list(tuple(getattr(row, col) for col in columns)) for row in results
        ]

    def search(self,query_string,params):
        """ Search Query"""
        results = self.repo_obj._model.raw(query_string,params)
        return results
    
    def get_raw_queryset_data(self, query_string,
                        params:list=None,start_index:int=0):
        """ Returns Raw queryset data."""
        results = self.repo_obj._model.raw(query_string, params)
        return results

class PricingScenarioCustomerLevelUnitOfWork(ORMModelUnitOfWork):
    def __init__(self, trans):
        super().__init__(trans, rp.PricingScenarioCustomerLevelRepository)
    
    def raw_queryset_as_values_list(self, raw_qs,start_index:int=0):
        """ Returns Raw queryset as values list."""
        columns = raw_qs.columns[start_index:]
        for row in raw_qs:
            yield tuple(getattr(row, col) for col in columns)

    def get_data_df(self, query_string,
                    params:list=None,
                    named_columns:list=None,
                    start_index:int=0):
                    
        """ Returns Dataframe."""
        columns=named_columns[start_index:]
        results = self.repo_obj._model.raw(query_string,params)
        if not named_columns:
            columns = results.columns[start_index:]
        return pd.DataFrame(
            self.raw_queryset_as_values_list(results,start_index), columns=columns
        )
    
    def get_json_data(self, query_string,
                    params:list=None,
                    named_columns:list=None,
                    start_index:int=0):
        return self.get_data_df(query_string,
                    params,
                    named_columns=named_columns,
                    start_index=start_index).to_json(orient="records")


    def get_raw_query_data(self, query_string,
                        params:list=None,start_index:int=0):
        """ Returns Raw queryset data."""
        results = self.repo_obj._model.raw(query_string, params)
        columns = results.columns[start_index:]
        return [
            dict(tuple((col, getattr(row, col)) for col in columns)) for row in results
        ]

    def get_raw_query_list(self, query_string,
                         params,
                         start_index:int=0):
        """ Returns Raw queryset as list."""
        results = self.repo_obj._model.raw(query_string, params)
        columns = results.columns[start_index:]
        return [
            list(tuple(getattr(row, col) for col in columns)) for row in results
        ]

    def search(self,query_string,params):
        """ Search Query"""
        results = self.repo_obj._model.raw(query_string,params)
        return results
    
    def get_raw_queryset_data(self, query_string,
                        params:list=None,start_index:int=0):
        """ Returns Raw queryset data."""
        results = self.repo_obj._model.raw(query_string, params)
        return results
  
class PricingPublishedSavedScenarioUnitOfWork(ORMModelUnitOfWork):
    def __init__(self, trans):
        super().__init__(trans, rp.PricingPublishedSavedScenarioRepository)
    
    def raw_queryset_as_values_list(self, raw_qs,start_index:int=0):
        """ Returns Raw queryset as values list."""
        columns = raw_qs.columns[start_index:]
        for row in raw_qs:
            yield tuple(getattr(row, col) for col in columns)

    def get_data_df(self, query_string,
                    params:list=None,
                    named_columns:list=None,
                    start_index:int=0):
                    
        """ Returns Dataframe."""
        columns=named_columns[start_index:]
        results = self.repo_obj._model.raw(query_string,params)
        if not named_columns:
            columns = results.columns[start_index:]
        return pd.DataFrame(
            self.raw_queryset_as_values_list(results,start_index), columns=columns
        )
    
    def get_json_data(self, query_string,
                    params:list=None,
                    named_columns:list=None,
                    start_index:int=0):
        return self.get_data_df(query_string,
                    params,
                    named_columns=named_columns,
                    start_index=start_index).to_json(orient="records")


    def get_raw_query_data(self, query_string,
                        params,start_index:int=0):
        """ Returns Raw queryset data."""
        results = self.repo_obj._model.raw(query_string, params)
        columns = results.columns[start_index:]
        return [
            dict(tuple((col, getattr(row, col)) for col in columns)) for row in results
        ]

    def get_raw_query_list(self, query_string,
                         params,
                         start_index:int=0):
        """ Returns Raw queryset as list."""
        results = self.repo_obj._model.raw(query_string, params)
        columns = results.columns[start_index:]
        return [
            list(tuple(getattr(row, col) for col in columns)) for row in results
        ]

    def get_raw_queryset_data(self, query_string,
                        params:list=None,start_index:int=0):
        """ Returns Raw queryset data."""
        results = self.repo_obj._model.raw(query_string, params)
        return results
    
    def search(self,query_string,params):
        """ Search Query"""
        results = self.repo_obj._model.raw(query_string,params)
        return results
  
class PricingSummeryScenarioUnitOfWork(ORMModelUnitOfWork):
    def __init__(self, trans):
        super().__init__(trans, rp.PricingSummeryScenarioRepository)
    
    def raw_queryset_as_values_list(self, raw_qs,start_index:int=0):
        """ Returns Raw queryset as values list."""
        columns = raw_qs.columns[start_index:]
        for row in raw_qs:
            yield tuple(getattr(row, col) for col in columns)

    def get_data_df(self, query_string,
                    params:list=None,
                    named_columns:list=None,
                    start_index:int=0):
                    
        """ Returns Dataframe."""
        columns=named_columns[start_index:]
        results = self.repo_obj._model.raw(query_string,params)
        if not named_columns:
            columns = results.columns[start_index:]
        return pd.DataFrame(
            self.raw_queryset_as_values_list(results,start_index), columns=columns
        )
    
    def get_json_data(self, query_string,
                    params:list=None,
                    named_columns:list=None,
                    start_index:int=0):
        return self.get_data_df(query_string,
                    params,
                    named_columns=named_columns,
                    start_index=start_index).to_json(orient="records")


    def get_raw_query_data(self, query_string,
                        params,start_index:int=0):
        """ Returns Raw queryset data."""
        results = self.repo_obj._model.raw(query_string, params)
        columns = results.columns[start_index:]
        return [
            dict(tuple((col, getattr(row, col)) for col in columns)) for row in results
        ]

    def get_raw_query_list(self, query_string,
                         params,
                         start_index:int=0):
        """ Returns Raw queryset as list."""
        results = self.repo_obj._model.raw(query_string, params)
        columns = results.columns[start_index:]
        return [
            list(tuple(getattr(row, col) for col in columns)) for row in results
        ]

    def search(self,query_string,params):
        """ Search Query"""
        results = self.repo_obj._model.raw(query_string,params)
        return results

class PricingOverallSummeryScenarioUnitOfWork(ORMModelUnitOfWork):
    def __init__(self, trans):
        super().__init__(trans, rp.PricingOverallSummeryScenarioRepository)
    
    def raw_queryset_as_values_list(self, raw_qs,start_index:int=0):
        """ Returns Raw queryset as values list."""
        columns = raw_qs.columns[start_index:]
        for row in raw_qs:
            yield tuple(getattr(row, col) for col in columns)

    def get_data_df(self, query_string,
                    params:list=None,
                    named_columns:list=None,
                    start_index:int=0):
                    
        """ Returns Dataframe."""
        columns=named_columns[start_index:]
        results = self.repo_obj._model.raw(query_string,params)
        if not named_columns:
            columns = results.columns[start_index:]
        return pd.DataFrame(
            self.raw_queryset_as_values_list(results,start_index), columns=columns
        )
    
    def get_json_data(self, query_string,
                    params:list=None,
                    named_columns:list=None,
                    start_index:int=0):
        return self.get_data_df(query_string,
                    params,
                    named_columns=named_columns,
                    start_index=start_index).to_json(orient="records")


    def get_raw_query_data(self, query_string,
                        params,start_index:int=0):
        """ Returns Raw queryset data."""
        results = self.repo_obj._model.raw(query_string, params)
        columns = results.columns[start_index:]
        return [
            dict(tuple((col, getattr(row, col)) for col in columns)) for row in results
        ]

    def get_raw_query_list(self, query_string,
                         params,
                         start_index:int=0):
        """ Returns Raw queryset as list."""
        results = self.repo_obj._model.raw(query_string, params)
        columns = results.columns[start_index:]
        return [
            list(tuple(getattr(row, col) for col in columns)) for row in results
        ]

    def search(self,query_string,params):
        """ Search Query"""
        results = self.repo_obj._model.raw(query_string,params)
        return results
  
class ChangedPricingScenarioUnitOfWork(ORMModelUnitOfWork):
    def __init__(self, trans):
        super().__init__(trans, rp.ChangedPricingSavedScenarioRepository)
    
    def raw_queryset_as_values_list(self, raw_qs,start_index:int=0):
        """ Returns Raw queryset as values list."""
        columns = raw_qs.columns[start_index:]
        for row in raw_qs:
            yield tuple(getattr(row, col) for col in columns)

    def get_data_df(self, query_string,
                    params:list=None,
                    named_columns:list=None,
                    start_index:int=0):
                    
        """ Returns Dataframe."""
        columns=named_columns[start_index:]
        results = self.repo_obj._model.raw(query_string,params)
        if not named_columns:
            columns = results.columns[start_index:]
        return pd.DataFrame(
            self.raw_queryset_as_values_list(results,start_index), columns=columns
        )
    
    def get_json_data(self, query_string,
                    params:list=None,
                    named_columns:list=None,
                    start_index:int=0):
        return self.get_data_df(query_string,
                    params,
                    named_columns=named_columns,
                    start_index=start_index).to_json(orient="records")


    def get_raw_query_data(self, query_string,
                        params,start_index:int=0):
        """ Returns Raw queryset data."""
        results = self.repo_obj._model.raw(query_string, params)
        columns = results.columns[start_index:]
        return [
            dict(tuple((col, getattr(row, col)) for col in columns)) for row in results
        ]

    def get_raw_query_list(self, query_string,
                         params,
                         start_index:int=0):
        """ Returns Raw queryset as list."""
        results = self.repo_obj._model.raw(query_string, params)
        columns = results.columns[start_index:]
        return [
            list(tuple(getattr(row, col) for col in columns)) for row in results
        ]

    def search(self,query_string,params):
        """ Search Query"""
        results = self.repo_obj._model.raw(query_string,params)
        return results
 
class BaseAndSimulatedPricingScenarioUnitOfWork(ORMModelUnitOfWork):
    def __init__(self, trans):
        super().__init__(trans, rp.BaseAndSimulatedPricingScenarioRepository)
    
    def raw_queryset_as_values_list(self, raw_qs,start_index:int=0):
        """ Returns Raw queryset as values list."""
        columns = raw_qs.columns[start_index:]
        for row in raw_qs:
            yield tuple(getattr(row, col) for col in columns)

    def get_data_df(self, query_string,
                    params:list=None,
                    named_columns:list=None,
                    start_index:int=0):
                    
        """ Returns Dataframe."""
        columns=named_columns[start_index:]
        results = self.repo_obj._model.raw(query_string,params)
        if not named_columns:
            columns = results.columns[start_index:]
        return pd.DataFrame(
            self.raw_queryset_as_values_list(results,start_index), columns=columns
        )
    
    def get_json_data(self, query_string,
                    params:list=None,
                    named_columns:list=None,
                    start_index:int=0):
        return self.get_data_df(query_string,
                    params,
                    named_columns=named_columns,
                    start_index=start_index).to_json(orient="records")


    def get_raw_query_data(self, query_string,
                        params,start_index:int=0):
        """ Returns Raw queryset data."""
        results = self.repo_obj._model.raw(query_string, params)
        columns = results.columns[start_index:]
        return [
            dict(tuple((col, getattr(row, col)) for col in columns)) for row in results
        ]

    def get_raw_query_list(self, query_string,
                         params,
                         start_index:int=0):
        """ Returns Raw queryset as list."""
        results = self.repo_obj._model.raw(query_string, params)
        columns = results.columns[start_index:]
        return [
            list(tuple(getattr(row, col) for col in columns)) for row in results
        ]

    def search(self,query_string,params):
        """ Search Query"""
        results = self.repo_obj._model.raw(query_string,params)
        return results

