from django.http import HttpResponse
from rest_framework.authtoken.models import Token
from ..serializers import AuthTokenSerializer,UserSerializer
from .token_validity import token_expire_handler,expires_in

def generate_token(f,*args,**kwargs):

    try:
        index = kwargs.get('index',0)
        serializer = AuthTokenSerializer(data=kwargs['data'],
                                            context={'request': args[index]})
        if serializer.is_valid(raise_exception=True):
            user = serializer.validated_data['user']
            token = Token.objects.get_or_create(user=user)[0] # pylint: disable=no-member
            is_expired, token = token_expire_handler(token)
            user_serializer = UserSerializer(token.user)
            kwargs['auth_data'] = {
                        'auth_token': token.key,
                        'expires_in':f"{expires_in(token)}",
                        'user_id': user_serializer.data['id'],
                        'name': user_serializer.data['name'],
                        'email': user_serializer.data['email'],
                        'auth_type':'Token'
                    }

            return f(*args,**kwargs)
        else:
            return HttpResponse(
            content="Invalid User: Pass Valid Credentials", status=401
        )
    except Exception as e:
        return HttpResponse(
            content=f"Invalid User: Unable to authenticate, error : {str(e)}", status=401
        )
