""" Optimizer calculations"""
import copy
from utils import _regex #pylint: disable=E0401
from apps.common.utils import _make_promotion_var_zero

def update_for_set_pricing(data_list:list 
                        ,roi_list:list,
                        querydict:dict,
                        d_columns:list,
                        )->list:
    """_summary_

    Args:
        data_list (list): model data
        roi_list (list): roi data
        querydict (dict): optimizer query dict

    Returns:
        list: cloned_list,cloned_roi
    """
    cloned_list = copy.deepcopy(data_list)
    cloned_roi = copy.deepcopy(roi_list)
    week_traced=[]
    
    for i in querydict.keys():
        week_regex = _regex(r'week-\d{1,2}', i)
        if week_regex:
            week = int(_regex(r'\d{1,2}', week_regex.group()).group())
            index = week - 1
            week_traced.append(index)

            cloned_list[index][d_columns.index('flag_promotype_leaflet')] = 1 if querydict[i].get('type_of_promo','') else 0
            cloned_list[index][d_columns.index('flag_promotype_advertising_without_price')] = 0
            cloned_list[index][d_columns.index('flag_promotype_bonus')] = 0
            cloned_list[index][d_columns.index('flag_promotype_multibuy')] = 0
            cloned_list[index][d_columns.index('flag_promotype_pas')] = 0
            cloned_list[index][d_columns.index('flag_promotype_coupon')] = 0
            cloned_list[index][d_columns.index('flag_promotype_edlp')] = 0
            selected_promotion = querydict[i]['promo_mechanics']

            if not selected_promotion:
                cloned_list[index][d_columns.index('flag_promotype_newsletter')] = 0
                cloned_list[index][d_columns.index('flag_promotype_leaflet')] = 0

            if 'AWP' in selected_promotion:
                cloned_list[index][d_columns.index('flag_promotype_advertising_without_price')
                                    ] = 1
            if 'Bonus' in selected_promotion:
                cloned_list[index][d_columns.index('flag_promotype_bonus')
                                    ] = 1
            if 'PAS' in selected_promotion:
                cloned_list[index][d_columns.index('flag_promotype_pas')
                                    ] = 1
            if 'Multibuy' in selected_promotion:
                cloned_list[index][d_columns.index('flag_promotype_multibuy')
                                    ] = 1
            if 'Coupon' in selected_promotion:
                cloned_list[index][d_columns.index('flag_promotype_coupon')
                                    ] = 1
            if 'EDLP' in selected_promotion:
                cloned_list[index][d_columns.index('flag_promotype_edlp')
                                    ] = 1
                
            #JOINT
            if querydict[i].get('type_of_promo','') == 'joint':
                if querydict[i]['joint_flag']:       
                    for _jnt_flag in querydict[i]['joint_flag'].split(' & '):
                        cloned_list[index][d_columns.index(_jnt_flag)] = 1
                cloned_list[index][d_columns.index('flag_promotype_all_brand')] = 0

            #ALL BRAND
            if querydict[i].get('type_of_promo','') == 'all_brand':
                cloned_list[index][d_columns.index('flag_promotype_all_brand')] = 1

            #SINGLE
          
            if querydict[i].get('type_of_promo','') == 'single':
                cloned_list[index][d_columns.index('flag_promotype_all_brand')] = 0
                if querydict[i].get('joint_flag',''):
                    for _jnt_flag in querydict[i]['joint_flag'].split(' & '):
                        cloned_list[index][d_columns.index(_jnt_flag)] = 0
            
            cloned_list[index][d_columns.index('tpr_discount_byppg')] = float(querydict[i].get('promo_depth',0))
            cloned_list[index][d_columns.index('mechanic')] = selected_promotion if 'Custom' in selected_promotion else ''
            cloned_list[index][d_columns.index('product_group_new')] = querydict[i].get('product_group','')
            cloned_list[index][d_columns.index('is_standalone')] = False
   
    for _i,_val in enumerate(cloned_list):
        if _i in week_traced:
            continue     
        if 'joint_flag' in querydict and querydict[_i].get('joint_flag',''):
            for _jnt_flag in querydict[_i]['joint_flag'].split(' & '):
                cloned_list[_i][d_columns.index(_jnt_flag)] = 0

        cloned_list[_i][d_columns.index('flag_promotype_all_brand')] = 0
        cloned_list[_i][d_columns.index('mechanic')] = ''
        cloned_list[_i][d_columns.index('promotion_levels')]=''
        cloned_list[_i][d_columns.index('tpr_discount_byppg')] = 0
        cloned_list[_i] = _make_promotion_var_zero(cloned_list[_i])
    
    return cloned_list,cloned_roi
