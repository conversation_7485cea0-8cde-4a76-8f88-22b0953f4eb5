import pandas as pd
from core.generics.exceptions import AlreadyExists


def get_objective_func():
    obj_choces = {
        "MAC":"Maximize MMAC",
        "NSV":"Maximize NSV"
    }

    return obj_choces

def validate_scenario(func):
    def wrapper(*args, **kwargs):
        scenario_name = kwargs.get('scenario_name',None)
        scenario_type = kwargs.get('scenario_type',None)
        uow = kwargs.get('suow')
        with uow as unit_of_work:
            data = unit_of_work.repo_obj.filter_by_scenario_name_and_scenario_type(scenario_name,scenario_type) 
        if data.exists():
            raise  AlreadyExists(scenario_name)
        return func(*args, **kwargs)
    return wrapper


def convert_to_df(list_obj:list):
    df = pd.DataFrame(list_obj)
    return df
