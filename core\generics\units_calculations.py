"""
Unit Calculation
"""
import re
import numpy as np
import pandas as pd
from utils import convert_to_df #pylint: disable=E0401
from . import constants as CONST

ACCOUNT_VARIABLE = 'Retailer'
PPG_VARIABLE = 'PPG'

def predict_sales(coeffs:pd.DataFrame, data:pd.DataFrame):
    """Predict Sales

    Args:
        coeffs (DataFrame): DataFrame consists of coefficient data
        data (DataFrame): DataFrame consists of coefficient model data

    Returns:
        Series: Predicted Volume
    """
    predict = 0
    for i in coeffs['names']:
        try:
            if i=="intercept":
                predict = predict + coeffs[coeffs['names']==i]["model_coefficients"].values
            elif i =="tpr_discount_byppg":
                predict = predict+ data['TPR'].astype(float) * coeffs[coeffs['names']==i]["model_coefficients"].values 
            else:
                predict = predict+ data[i].astype(float) * coeffs[coeffs['names']==i]["model_coefficients"].values
        except:
            breakpoint()

    data['pred_vol'] = predict
    data['Predicted_Volume'] = np.exp(data['pred_vol'])
    return(data['Predicted_Volume'])


def get_quarter_by_period(_x):
    """Get quarter by period

    Args:
        _x (string): Period

    Returns:
        string: Period
    """
    if _x in  ('P01' ,  'P02' ,  'P03'):
        return 'Q01'
    if  _x in ('P04' ,  'P05' ,  'P06'):
        return 'Q02'
    if _x in ('P07' ,  'P08' ,  'P09'):
        return 'Q03'
    if _x in ('P10' ,  'P11' ,  'P12' ,  'P13'):
        return 'Q04'
    return _x

def _predict(pred_df:pd.DataFrame,
             model_coef:pd.DataFrame,
             var_col="model_coefficient_name",
             coef_col="model_coefficient_value",
             intercept_name="(Intercept)",flag=None):
    """Predict Sales.

    Parameters
    ----------
    pred_df : pd.DataFrame
        Dataframe consisting of IDVs
    model_coef : pd.DataFrame
        Dataframe consisting of model coefficient and its value
    var_col : str
        Column containing model variable
    coef_col : str
        Column containing model variables and their coefficient estimate
    intercept_name : str
        Name of intercept variable

    Returns
    -------
    ndarray
    """
    pred_df['tpr_discount_byppg'] = pred_df['TPR']
    idv_cols = [col for col in model_coef[var_col] if col not in [intercept_name,'model_meta_id']]
    idv_coef = model_coef.loc[model_coef[var_col].isin(idv_cols), coef_col]
    idv_df = pred_df.loc[:, idv_cols]
    intercept = model_coef.loc[~model_coef[var_col].isin(idv_cols), coef_col].to_list()[0]
    prediction = idv_df.values.astype(float).dot(idv_coef.values) + intercept

    return prediction

def promo_wave_cal(tpr_data):
    tpr_data['Promo_wave']=0
    _c=1
    i=0
    while(i<=tpr_data.shape[0]-1):
        if tpr_data.loc[i,'TPR']>0:#####Also tpr ??since in validation consdered tpr
            tpr_data.loc[i,'Promo_wave']=_c
            j=i+1
            if j==tpr_data.shape[0]:
                    break
            while((j<=tpr_data.shape[0]-1) & (tpr_data.loc[j,'TPR']>0)):
                tpr_data.loc[j,'Promo_wave']=_c
                i = j+1
                j = i
                if j==tpr_data.shape[0]:
                    break
            _c=_c+1
        i=i+1
    return tpr_data['Promo_wave']



def get_var_contribution_wo_baseline_defined(_df:pd.DataFrame,
                                             model_coef:pd.DataFrame,
                                             wk_sold_price:pd.DataFrame,
                                             all_df=None,
                                             var_col="model_coefficient_name",
                                             coef_col="model_coefficient_value",
                                             intercept_name="(Intercept)",
                                             base_var=None,
                                             flag=None):
    """Get variable contribution without baseline defined.

    Parameters
    ----------
    df : pd.DataFrame
        Dataframe consisting of IDVs
    model_coef : pd.DataFrame
        Dataframe consisting of model variables and their coefficient estimate
    wk_sold_price : pd.DataFrame
        Dataframe consisting of weekly sold price
    all_df : pd.DataFrame
        Dataframe consisting of IDVs of all PPGs
    var_col : str
        Column containing model variable
    coef_col : str
        Column containing model variables' coefficient estimate
    intercept_name : str
        Name of intercept variable
    base_var : list of str
        Base variables

    Returns
    -------
    tuple of pd.DataFrame
    """

    # Predict Sales

    ppg_cols = ["PPG_Cat", "PPG_MFG", "PPG_Item_No", "PPG_Description"]
    ppg = _df["PPG_Item_No"].to_list()[0]

    model_df = _df.drop(columns=ppg_cols).copy()
    model_df["Predicted_sales"] = np.exp(
        _predict(model_df,
                 model_coef,
                 var_col=var_col,
                 coef_col=coef_col,
                 intercept_name=intercept_name,
                 flag=flag))
    # Get base and impact variables
    if base_var is None:
        rp_features = [i for i in model_coef[var_col]]
        base_var = ["wk_sold_median_base_price_byppg_log"] + rp_features+ ["acv", "category_trend", "flag_qtr2", "flag_qtr3", "flag_qtr4", "monthno"]
        base_var = [i for i in base_var if i in model_coef[var_col].to_list()]
    base_var = [intercept_name] + base_var
    impact_var = [i for i in model_coef[var_col] if i not in base_var]

    # Get base and impact variables
    model_df[intercept_name] = 1
    tmp_model_coef = model_coef[model_coef[var_col].isin(base_var)]
    tmp_model_df = model_df.copy()
    if all_df is not None:
        if "wk_sold_median_base_price_byppg_log" in tmp_model_df.columns:
            tmp_model_df = tmp_model_df.merge(all_df.loc[all_df["PPG_Item_No"] == ppg, [
                                              "Date", "Final_baseprice"]].drop_duplicates(), how="left", on="Date")
            tmp_model_df["Final_baseprice"] = tmp_model_df["Final_baseprice"].astype(np.float64)
            tmp_model_df["wk_sold_median_base_price_byppg_log"] = np.log(tmp_model_df["Final_baseprice"])
            tmp_model_df = tmp_model_df.drop(columns=["Final_baseprice"])
        if tmp_model_df.columns.str.contains(".*RegularPrice.*RegularPrice", regex=True).any():
            rp_interaction_cols = [col for col in tmp_model_df.columns if re.search(
                ".*RegularPrice.*RegularPrice", col) is not None]
            for col in rp_interaction_cols:
                col_adj = re.sub(ppg, "", col)
                col_adj = re.sub("_RegularPrice_", "", col_adj)
                col_adj = re.sub("_RegularPrice", "", col_adj)
                tmp_model_df = tmp_model_df.merge(all_df.loc[all_df["PPG_Item_No"] == ppg, [
                                                  "Date", "Final_baseprice"]], how="left", on="Date")
                temp = all_df.loc[all_df["PPG_Item_No"] == col_adj, ["Date", "wk_sold_median_base_price_byppg_log"]]
                temp = temp.rename(columns={"wk_sold_median_base_price_byppg_log": "wk_price_log"})
                tmp_model_df = tmp_model_df.merge(temp[["Date", "wk_price_log"]], how="left", on="Date")
                tmp_model_df[col] = np.log(tmp_model_df["Final_baseprice"]) * tmp_model_df["wk_price_log"]
                tmp_model_df = tmp_model_df.drop(columns=["Final_baseprice", "wk_price_log"])
    if 'model_meta_id' in var_col:
        var_col.remove('model_meta_id')
    if 'model_meta_id' in impact_var:
        impact_var.remove('model_meta_id')
    base_val = tmp_model_df[tmp_model_coef[var_col].to_list()].values.astype(float).dot(tmp_model_coef[coef_col].values)

    tmp_model_coef = model_coef[model_coef[var_col].isin(impact_var)]
    impact_val = model_df[tmp_model_coef[var_col].to_list()].values.astype(float).dot(tmp_model_coef[coef_col].values)

    model_df["baseline_contribution"] = np.exp(base_val)
    model_df["incremental_contribution"] = model_df["Predicted_sales"] - model_df["baseline_contribution"]

    # Calculate raw contribution for impact variables
    row_sum = 0
    abs_sum = 0
    for i in impact_var:
        tmp_impact_val = model_df[i].values.astype(float) * model_coef.loc[model_coef[var_col] == i, coef_col].to_list()[0]
        model_df[i + "_contribution_impact"] = np.exp(base_val + impact_val) - \
            np.exp(base_val + impact_val - tmp_impact_val)
        row_sum = row_sum + model_df[i + "_contribution_impact"]
        abs_sum = abs_sum + abs(model_df[i + "_contribution_impact"])

    y_b_s = model_df["incremental_contribution"] - row_sum

    impact_contribution = model_df[["Date", "Predicted_sales"]].copy()
    for i in impact_var:
        i_adj = i + "_contribution_impact"
        impact_contribution[i_adj] = model_df[i_adj] + (abs(model_df[i_adj])/abs_sum) * y_b_s

    # Calculate raw contribution for base variables
    base_rc = model_coef.loc[model_coef[var_col] == intercept_name, coef_col].to_list()[0]
    impact_contribution[intercept_name + "_contribution_base"] = np.exp(base_rc)
    for i in base_var[1:]:
        _t = tmp_model_df[i].astype(float) * model_coef.loc[model_coef[var_col] == i, coef_col].to_list()[0] + base_rc
        impact_contribution[i + "_contribution_base"] = np.exp(_t) - np.exp(base_rc)
        base_rc = _t

    impact_contribution = impact_contribution.fillna(0)
    unit_dist_df = impact_contribution.copy()

    # Get Dollar Sales
    price_dist_df = unit_dist_df.copy()
    numeric_cols = price_dist_df.select_dtypes(include="number").columns.to_list()
    price_dist_df[numeric_cols] = price_dist_df[numeric_cols].mul(wk_sold_price.values.astype(float), axis=0)

    # Get variable contribution variants
    dt_unit_dist_df, qtr_unit_dist_df, yr_unit_dist_df = get_var_contribution_variants(
        unit_dist_df, "model_coefficient_name", value_col_name="units", flag=flag)
    dt_price_dist_df, qtr_price_dist_df, yr_price_dist_df = get_var_contribution_variants(
        price_dist_df, "model_coefficient_name", value_col_name="price")

    overall_dt_dist_df = dt_unit_dist_df.merge(dt_price_dist_df, how="left", on=["Date", "model_coefficient_name"])
    overall_qtr_dist_df = qtr_unit_dist_df.merge(qtr_price_dist_df, how="left", on=[
                                                 "Quarter", "model_coefficient_name"])
    overall_yr_dist_df = yr_unit_dist_df.merge(yr_price_dist_df, how="left", on=["Year", "model_coefficient_name"])

    ppg_df = _df[ppg_cols].drop_duplicates().values.tolist()
    overall_dt_dist_df[ppg_cols] = pd.DataFrame(ppg_df, index=overall_dt_dist_df.index)
    overall_qtr_dist_df[ppg_cols] = pd.DataFrame(ppg_df, index=overall_qtr_dist_df.index)
    overall_yr_dist_df[ppg_cols] = pd.DataFrame(ppg_df, index=overall_yr_dist_df.index)

    return overall_dt_dist_df, overall_qtr_dist_df, overall_yr_dist_df


def get_var_contribution_variants(dist_df:pd.DataFrame,
                                  var_col_name:str,
                                  value_col_name:str,
                                  flag=None):
    """Get variable contribution by different variants.

    Parameters
    ----------
    dist_df : pd.DataFrame
        Dataframe consisting of IDVs
    var_col_name : str
        Column name for melted IDV columns
    value_col_name : str
        Column name for IDV values

    Returns
    -------
    tuple of pd.DataFrame
    """
    _ = flag
    pct_dist_df = dist_df.copy()
    numeric_cols = pct_dist_df.select_dtypes(include="number").columns.to_list()
    pct_dist_df[numeric_cols] = pct_dist_df[numeric_cols].div(pct_dist_df["Predicted_sales"], axis=0)
    dist_df_1 = pd.merge(dist_df.melt(id_vars=["Date"],
                                      var_name=var_col_name,
                                      value_name=value_col_name),
                         pct_dist_df.melt(id_vars=["Date"],
                                          var_name=var_col_name,
                                          value_name="pct_" + value_col_name),
                         how="left", on=["Date", var_col_name])

    qtr_dist_df = dist_df.copy()
    qtr_dist_df["Quarter"] = qtr_dist_df["Date"].dt.year.astype(
        str) + "-" + "Q" + qtr_dist_df["Date"].dt.quarter.astype(str)

    qtr_dist_df = qtr_dist_df.drop(columns=["Date"])
    qtr_dist_df = qtr_dist_df.groupby(by=["Quarter"], as_index=False).agg(np.sum)

    pct_qtr_dist_df = qtr_dist_df.copy()
    pct_qtr_dist_df[numeric_cols] = pct_qtr_dist_df[numeric_cols].div(pct_qtr_dist_df["Predicted_sales"], axis=0)
    dist_df_2 = pd.merge(qtr_dist_df.melt(id_vars=["Quarter"], var_name=var_col_name, value_name=value_col_name),
                         pct_qtr_dist_df.melt(id_vars=["Quarter"], var_name=var_col_name,
                                              value_name="pct_" + value_col_name),
                         how="left", on=["Quarter", var_col_name])


    yr_dist_df = dist_df.copy()
    yr_dist_df["Year"] = yr_dist_df["Date"].dt.year.astype(str)
    yr_dist_df = yr_dist_df.drop(columns=["Date"])
    yr_dist_df = yr_dist_df.groupby(by=["Year"], as_index=False).agg(np.sum)

    pct_yr_dist_df = yr_dist_df.copy()
    pct_yr_dist_df[numeric_cols] = pct_yr_dist_df[numeric_cols].div(pct_yr_dist_df["Predicted_sales"], axis=0)
    dist_df_3 = pd.merge(yr_dist_df.melt(id_vars=["Year"], var_name=var_col_name, value_name=value_col_name),
                         pct_yr_dist_df.melt(id_vars=["Year"], var_name=var_col_name,
                                             value_name="pct_" + value_col_name),
                         how="left", on=["Year", var_col_name])

    return dist_df_1, dist_df_2, dist_df_3


def base_var_cont(model_df:pd.DataFrame,
                  model_df1:pd.DataFrame,
                  roi_frame:pd.DataFrame,
                  baseline_var:pd.DataFrame,
                  baseline_var_othr:pd.DataFrame,
                  model_coef:pd.DataFrame,
                  _flag:str='base',
                  base_incremental_split=None
                  ):

    """Base variables Count

    Args:
        model_df (DataFrame): DataFrame Consists of Model Data
        model_df1 (DataFrame): DataFrame Consists Shadow of Model Data
        baseline_var (DataFrame): DataFrame Consists of baseline variables
        baseline_var_othr (DataFrame): DataFrame Consists of other baseline variables
        model_coef (DataFrame): DataFrame consists of model coeffiecient

    Returns:
        DataFrame: With Updated new Columns DataFrame
    """

    _m=1
    dt_dist = pd.DataFrame()
    quar_dist = pd.DataFrame()
    year_dist = pd.DataFrame()
    year_dist1 = pd.DataFrame()    

    # no promo
    # flag_cols = [x for x in model_df.columns if 'Flag_promotype' in x]
    # model_df[flag_cols] = 0
    # model_df['TPR'] = 0

    # only tpr, leaflet
    # flag_cols = [x for x in model_df.columns 
    #              if ('Flag_promotype' in x) and ('Flag_promotype_Leaflet' not in x)]
    # model_df[flag_cols] = 0

    # only tpr,leaflet, logo
    # flag_cols = [x for x in model_df.columns 
    #              if ('Flag_promotype' in x) and 
    #              ('Flag_promotype_Leaflet' not in x) and
    #              ('Flag_promotype_multibuy' not in x)]
    # model_df[flag_cols] = 0

    ppg_name = model_coef['PPG'].unique()[0]
    save_ppg_name = ppg_name.replace('/', '')
    model_df.to_excel(f'{save_ppg_name}_{_flag}_model_df.xlsx', index=False)

    price = "wk_sold_avg_price_byppg"
    price_val = model_df[[price]]
    model_df[["PPG_Cat", "PPG_MFG", "PPG_Item_No", "PPG_Description"]] = pd.DataFrame([["TEMP"]*4]\
                                                                        , index=model_df.index)
    base_var = baseline_var["Variable"].to_list()
    overall_dt_dist_df, overall_qtr_dist_df\
    , overall_yr_dist_df = get_var_contribution_wo_baseline_defined(model_df, 
                                                                    model_coef,
                                                                    price_val,
                                                                    all_df=None,
                                                                    var_col="Variable",
                                                                    coef_col="Value",
                                                                    intercept_name="intercept",
                                                                    base_var=base_var,flag=_flag)
    overall_dt_dist_df['Iteration'] = _m
    overall_qtr_dist_df['Iteration'] =_m
    overall_yr_dist_df['Iteration'] = _m    

    overall_yr_dist_df1 = overall_yr_dist_df[overall_yr_dist_df['model_coefficient_name']!= 'Predicted_sales']
    yearly_1 = overall_yr_dist_df1[['Year','units']].groupby(by=["Year"], as_index=False)\
                                    .agg(np.sum).rename(columns = {'units':"Yearly_Units"})
    overall_yr_dist_df1 = pd.merge(overall_yr_dist_df1,yearly_1,on = "Year",how = "left")
    overall_yr_dist_df1['units%'] = overall_yr_dist_df1['units']/overall_yr_dist_df1['Yearly_Units']*100
    dt_dist  = pd.concat([dt_dist,overall_dt_dist_df],ignore_index = False)
    quar_dist  = pd.concat([quar_dist,overall_qtr_dist_df],ignore_index = False)
    year_dist  = pd.concat([year_dist,overall_yr_dist_df],ignore_index = False)
    year_dist1  = pd.concat([year_dist1,overall_yr_dist_df1],ignore_index = False)
    
    #########Converting to Required Format
    dt_dist = pd.pivot_table(dt_dist[['Date','model_coefficient_name','units','Iteration']]\
                            ,index = ["Date","Iteration"],columns = "model_coefficient_name")
    dt_dist = pd.DataFrame(dt_dist).reset_index()
    dt_dist.columns = dt_dist.columns.droplevel(0)
    _a = ["Date","Iteration"]+list(dt_dist.columns[2:])
    
    dt_dist.columns = _a
    year_dist = pd.pivot_table(year_dist[['Year','model_coefficient_name','units','Iteration']]\
                            ,index = ["model_coefficient_name","Iteration"],columns = "Year")
    year_dist = pd.DataFrame(year_dist).reset_index()
    year_dist.columns = year_dist.columns.droplevel(0)

    _a = ["model_coefficient_name","Iteration"]+list(year_dist.columns[2:])
    year_dist.columns = _a

    year_dist1 = pd.pivot_table(year_dist1[['Year','model_coefficient_name','units%','Iteration']]\
                                ,index = ["model_coefficient_name","Iteration"],columns = "Year")
    year_dist1 = pd.DataFrame(year_dist1).reset_index()
    year_dist1.columns = year_dist1.columns.droplevel(0)
    _a = ["model_coefficient_name","Iteration"]+list(year_dist1.columns[2:])
    year_dist1.columns = _a

    _aa = dt_dist.columns
    comp = [i for i in _aa if "_discount" in i]
    dt_dist['Comp'] = dt_dist[comp].sum(axis = 1)
    
    no_promo_flag = [col for col in _aa if ("NonPromo_flag_date" in col or 'non_promo_flag' in col.lower() or 
                                            "nonpromo_flag_date" in col.lower())]
    # Here some date specific Non_Promo_flags are mapped to Holiday_Flag with 'F'. 
    # Typical holiday flags goes like this Holiday_flag with 'f'
    no_promo_holiday_flag = [col for col in _aa if 'holiday_flag' in col]
    no_promo_flag = no_promo_flag + no_promo_holiday_flag
    all_flag_dates = [col for col in _aa if "promo_flag_date" in col] + no_promo_holiday_flag
    promo_flag_dates = list(set(all_flag_dates) - set(no_promo_flag))
    
    promo_features = ["TPR", "Catalogue", "flag_N_pls_1", "flag_IN_OUT"]
    promo_features = [col for col in promo_features if col in _aa]
    promo_features = (promo_features + 
                      [col for col in _aa if "flag_promotype_" in col] + 
                      promo_flag_dates)
    incremental = ([i for i in _aa if "tpr_discount_byppg_contribution" in i] + 
                   [i for i in _aa if "Catalogue" in i] + 
                   promo_features + 
                   [i for i in _aa if 'flag_promo_' in i])    
    # Filter only those columns in incremental that are present in model_coef['Variable]
    model_cols = model_coef.loc[(model_coef['Value']!=0), 'Variable'].to_list()
    temp_incremental = []
    for model_col_val in model_cols:
        incr = [k for k in incremental if model_col_val == k.split('_contribution_')[0]]
        temp_incremental = temp_incremental + incr    
    incremental = temp_incremental
    incremental = list(set(incremental))

    base_others = baseline_var_othr["Variable"].to_list()        
    if "intercept" in model_cols:
        model_cols.remove("intercept")    
    temp_incremental = [x.split('_contribution_')[0] for x in incremental]
    rem_base = base_others + base_var + temp_incremental    

    b_other = [k for k in model_cols if k not in rem_base]
    if 'model_meta_id' in b_other:
        b_other.remove('model_meta_id')
    base_others = base_others + b_other
    base_others = list(set(base_others))

    base = ["intercept_contribution_base"] + [i+'_contribution_base' for i in base_var]\
           +[i+'_contribution_impact' for i in base_others]
 
    if 'TPR_lag1_contribution_impact' in incremental:
        incremental.remove('TPR_lag1_contribution_impact')
    if 'TPR_lag2_contribution_impact' in incremental:
        incremental.remove('TPR_lag2_contribution_impact')
    
    dt_dist['Incremental'] = dt_dist[incremental].sum(axis = 1)
    dt_dist['Base'] = dt_dist[base].sum(axis = 1)
    ppg_name = model_coef['PPG'].unique()[0]

    # Incremental & Base values - same for base and simulated - till here
    save_dt_dist = dt_dist.copy()    
    save_ppg_name = ppg_name.replace('/', '')
    save_dt_dist.to_csv(f'{save_ppg_name}_{_flag}_dt_dist_after_func.csv', index=False)

    model_df = model_df1.copy()
    req = model_df[['Date','Iteration']]
    req.loc[:, 'Date'] = pd.to_datetime(req['Date'], format='%Y-%m-%d')    

    dt_dist = pd.merge(dt_dist,req,how = "left")
    temp_dt_dist_1 = dt_dist.loc[(dt_dist['Date']<='2023-12-31')]
    temp_dt_dist_2 = dt_dist.loc[(dt_dist['Date']>'2023-12-31')]
    temp_dt_dist = pd.concat([temp_dt_dist_2, temp_dt_dist_1], axis=0, ignore_index=True)    
    dt_dist = temp_dt_dist.copy()

    base_incrementa_df = pd.DataFrame()
    base_incrementa_df['Predicted_sales'] = dt_dist['Predicted_sales']
    dt_dist['Flag_promotype_Leaflet_contribution_impact'] =dt_dist['flag_promotype_defects_contribution_impact']+dt_dist['flag_promotype_lottery_contribution_impact']+dt_dist['flag_promotype_miscellaneous_contribution_impact']+dt_dist['flag_promotype_multibuy_contribution_impact']+dt_dist['flag_promotype_multibuy_tpr_contribution_impact']+dt_dist['flag_promotype_tpr_contribution_impact']+dt_dist['flag_promotype_unpublished_contribution_impact']
    base_incrementa_df['Flag_promotype_Leaflet_contribution_impact'] = dt_dist['Flag_promotype_Leaflet_contribution_impact']
    if _flag == 'simulated':
        base_incrementa_df = pd.DataFrame(base_incremental_split)

    if _flag == 'base':
        dt_dist['ratio'] = roi_frame['Total Sold Unit'] / base_incrementa_df['Predicted_sales']
        promo_ratio = dt_dist.loc[(dt_dist['Flag_promotype_Leaflet_contribution_impact'] > 0), 'ratio'].mean()
        non_promo_ratio = dt_dist.loc[(dt_dist['Flag_promotype_Leaflet_contribution_impact'] == 0), 'ratio'].mean()
    elif _flag=='simulated':
        promo_ratio = base_incrementa_df.loc[(base_incrementa_df['Flag_promotype_Leaflet_contribution_impact'] > 0), 'ratio_new'].unique()
        promo_ratio = promo_ratio[0] if promo_ratio else 1
        non_promo_ratio = base_incrementa_df.loc[(base_incrementa_df['Flag_promotype_Leaflet_contribution_impact'] == 0), 'ratio_new'].unique()[0]

    dt_dist['ratio_new'] = np.where(
        dt_dist['Flag_promotype_Leaflet_contribution_impact'] > 0, 
        promo_ratio, 
        non_promo_ratio)

    dt_dist['Incremental'] = (dt_dist['Incremental'] * dt_dist['ratio_new']).astype(float)
    dt_dist['Base'] = (dt_dist['Base'] * dt_dist['ratio_new']).astype(float)
    dt_dist['ACV_Selling_contribution_base'] = (dt_dist['acv_contribution_base'] * dt_dist['ratio_new']).astype(float)

    # breakpoint()
    # dt_dist['Incremental'] = (dt_dist['Incremental']/base_incrementa_df['Predicted_sales'])*roi_frame['total_sold_unit'].astype(float)
    # dt_dist['Base'] = (dt_dist['Base']/base_incrementa_df['Predicted_sales'])*roi_frame['total_sold_unit'].astype(float)
    # dt_dist['ACV_Selling_contribution_base'] = (dt_dist['ACV_Selling_contribution_base']/base_incrementa_df['Predicted_sales'])*roi_frame['total_sold_unit'].astype(float)

    # Smoothen trend flags using loops
    trend_flags_to_smoothen = ["quarter_4_2022_contribution_impact","Nov_trend_contribution_impact", 
                               "Oct_trend_contribution_impact", "P9_2022_trend_contribution_impact",
                               "P10_2022_trend_contribution_impact","P11_2022_trend_contribution_impact",
                               "P10_11_2022_contribution_impact","P9_2022_contribution_impact", 
                               "Sept_2022_trend_contribution_impact", "Nov_2022_trend_contribution_impact", 
                               "Oct_2022_trend_contribution_impact", "P10_2022_contribution_impact", 
                               "P09_2022_contribution_impact", "P11_2022_contribution_impact"]

    dt_dist['Base_Units_Corrected'] = dt_dist['Base']
    dt_dist['Incremental_Units_Corrected'] = dt_dist['Incremental']

    group_col = ['Retailer','PPG']
    dt_dist['Retailer'] = roi_frame['Retailer']
    dt_dist['PPG'] = roi_frame['PPG']
    dt_dist_trend_flags_to_smoothen = [x for x in dt_dist.columns if x in trend_flags_to_smoothen]
    
    for flag in dt_dist_trend_flags_to_smoothen:
        dt_dist[flag] = (dt_dist[flag]/base_incrementa_df['Predicted_sales'])*roi_frame['Total Sold Unit']
        # dt_dist[flag] = (dt_dist[flag]/dt_dist['Predicted_sales'])*roi_frame['total_sold_unit']
        
        # dt_dist['promo_check'] =  (dt_dist.Incremental>0).astype(int)
        dt_dist['promo_check'] =  (dt_dist.Flag_promotype_Leaflet_contribution_impact>0).astype(int)
        dt_dist[f'non_promo_{flag}_contribution'] = dt_dist[flag].where((dt_dist.promo_check==0) & (dt_dist[flag] > 0))
        # Moving Average with only non promo week ACV contrib within 7 weeks        

        dt_dist[f'non_promo_{flag}_ma_2_weeks'] = dt_dist.groupby(group_col)[f'non_promo_{flag}_contribution'].transform(lambda x: x.rolling(3, 1).mean())
        dt_dist[f'non_promo_{flag}_ma_2_to_1weeks'] = dt_dist.groupby(group_col)[f'non_promo_{flag}_contribution'].transform(lambda x: x.rolling(window=4, min_periods=1,center=True).mean())

        dt_dist[f'non_promo_{flag}_ma'] = np.where(dt_dist[f'non_promo_{flag}_ma_2_weeks'] > 0, 
                                                   dt_dist[f'non_promo_{flag}_ma_2_weeks'], 
                                                   dt_dist[f'non_promo_{flag}_ma_2_to_1weeks'])

        # Taking Moving Avg as base for promo weeks, for non promo weeks as in ACV contrib
        dt_dist[f'Base_{flag}_contrib'] = dt_dist[flag].where(dt_dist.promo_check==0,dt_dist[f'non_promo_{flag}_ma'])
        # Filling NAN (Because of no non-promo weeks in the starting weeks i.e 2020)
        
        dt_dist[f'Base_{flag}_contrib'] = dt_dist.groupby(group_col)[f'Base_{flag}_contrib'].apply(lambda x: x.bfill())
        # If Base ACV Contrib > ACV Contrib, then replace with ACV Contrib
        dt_dist[f'Base_{flag}_contrib'] = dt_dist[f'Base_{flag}_contrib'].where(dt_dist[f'Base_{flag}_contrib']<=dt_dist[flag],dt_dist[flag])  
        # Calc Incremental ACV Contrib
        dt_dist[f'Incremental_{flag}_contrib'] = dt_dist[flag] - dt_dist[f'Base_{flag}_contrib']
        # Correcting Base & Incremental Units

        dt_dist[flag] = dt_dist[flag].fillna(0)
        dt_dist[f'Base_{flag}_contrib'] = dt_dist[f'Base_{flag}_contrib'].fillna(0)
        dt_dist[f'Incremental_{flag}_contrib'] = dt_dist[f'Incremental_{flag}_contrib'].fillna(0)

        dt_dist['Base_Units_Corrected'] = dt_dist['Base_Units_Corrected'] - dt_dist[flag] + dt_dist[f'Base_{flag}_contrib']
        dt_dist['Incremental_Units_Corrected'] = dt_dist['Incremental_Units_Corrected'] +  dt_dist[f'Incremental_{flag}_contrib']

    save_dt_dist = dt_dist.copy()
    save_ppg_name = ppg_name.replace('/', '')
    save_dt_dist.to_csv(f'{save_ppg_name}_{_flag}_dt_dist_before_acv_corr.csv', index=False)

    dt_dist['promo_check'] = np.where(dt_dist.Flag_promotype_Leaflet_contribution_impact>0,1,0)
    # if _flag=='simulated':
    #     dt_dist['promo_check'] = base_incrementa_df['promo_check']
    dt_dist['non_promo_ACV_Selling_contribution'] = dt_dist['ACV_Selling_contribution_base'].where(dt_dist['promo_check']==0)
    
    # Moving Average with only non promo week ACV contrib within 7 weeks
    dt_dist['non_promo_ACV_Selling_contribution_ma'] = dt_dist.groupby(group_col)['non_promo_ACV_Selling_contribution'].transform(lambda x: x.rolling(7, 1).mean())
    # Taking Moving Avg as base for promo weeks, for non promo weeks as in ACV contrib
    dt_dist['Base_ACV_contrib'] = dt_dist['ACV_Selling_contribution_base'].where(dt_dist['promo_check']==0,dt_dist['non_promo_ACV_Selling_contribution_ma'])

    # Filling NAN (Because of no non-promo weeks in the starting weeks i.e 2020)
    dt_dist['Base_ACV_contrib'] = dt_dist.groupby(group_col)["Base_ACV_contrib"].apply(lambda x: x.bfill())
    # If Base ACV Contrib > ACV Contrib, then replace with ACV Contrib
    dt_dist['Base_ACV_contrib'] = dt_dist['Base_ACV_contrib'].where(dt_dist['Base_ACV_contrib']<=dt_dist['ACV_Selling_contribution_base'],dt_dist['ACV_Selling_contribution_base'])
    # Calc Incremental ACV Contrib
    dt_dist['Incremental_ACV_contrib'] = dt_dist['ACV_Selling_contribution_base'] - dt_dist['Base_ACV_contrib']
    # Correcting Base & Incremental Units
    dt_dist['Base_Units_Corrected'] = (dt_dist['Base_Units_Corrected'] - 
                                       dt_dist['ACV_Selling_contribution_base'] + 
                                       dt_dist['Base_ACV_contrib'])
    dt_dist['Incremental_Units_Corrected'] = (dt_dist['Incremental_Units_Corrected'] + 
                                              dt_dist['Incremental_ACV_contrib'])

    dt_dist["Incremental_ACV_contrib"] = np.where(dt_dist["Base_Units_Corrected"]<0, 
                                                  0, 
                                                  dt_dist["Incremental_ACV_contrib"])
    dt_dist["Base_ACV_contrib"] = np.where(dt_dist["Base_Units_Corrected"]<0,
                                           dt_dist["ACV_Selling_contribution_base"], 
                                           dt_dist["Base_ACV_contrib"])
    dt_dist["Incremental_Units_Corrected"] = np.where(dt_dist["Base_Units_Corrected"]<0,
                                                      dt_dist["Incremental"], 
                                                      dt_dist["Incremental_Units_Corrected"])
    dt_dist["Base_Units_Corrected"] = np.where(dt_dist["Base_Units_Corrected"]<0,
                                               dt_dist["Base"], 
                                               dt_dist["Base_Units_Corrected"])
        
    dt_dist['Lift'] = dt_dist['Incremental_Units_Corrected']/(dt_dist['Base_Units_Corrected'])
    save_dt_dist = dt_dist.copy()
    save_ppg_name = ppg_name.replace('/', '')
    save_dt_dist.to_csv(f'{save_ppg_name}_{_flag}_dt_dist_after_acv.csv', index=False)

    return dt_dist

def main(data_frame:pd.DataFrame, 
         roi_frame:pd.DataFrame,
        coeff_frame:pd.DataFrame, 
        _flag:str='base',
        base_incremental_split=None
        ):
    """Main

    Args:
        data_frame (pd.DataFrame): DataFrame consists of model data
        coeff_frame (pd.DataFrame): DataFrame consists of model coefficient
        flag (str): base or simulated

    Returns:
        DataFrame: final df
    """
    base_data = data_frame

    train_coef = coeff_frame


    coeff_1 = train_coef.melt(id_vars =['Retailer', 
                                        'Segment',
                                        'PPG',
                                        'Brand',
                                        'Brand_Tech',
                                        'Product_Type',
                                        'WMAPE',
                                        'Rsq'], 
                                        var_name = "Variable",
                                        value_name='Value')

    coeff = coeff_1.copy()
    coeff['Value'] = coeff['Value'].astype(float)
    base_data.loc[:, 'Date'] = pd.to_datetime(base_data['Date'])
    train_data = base_data.copy()
    model_coef = coeff.copy()
    # train_data['Promo_wave']=promo_wave_cal(train_data)
    train_data = train_data.fillna(0)

    predict_df = model_coef.copy()
    predict_df.rename(columns = {'Variable': 'names', 'Value':'model_coefficients'}, inplace=True)
    train_data['Units']=predict_sales(predict_df,train_data)
    train_data['wk_sold_avg_price_byppg']=np.exp(train_data['wk_sold_median_base_price_byppg_log']\
                                         .astype(float))*(1-train_data['TPR']\
                                        .astype(float)/100)

    model_df=train_data.copy()
    baseline_var = pd.DataFrame(columns =["Variable"])
    baseline_var_othr = pd.DataFrame(columns =["Variable"])
    # col = model_coef["Variable"].to_list()
    col = model_coef.loc[(model_coef['Value']!=0), 'Variable'].to_list()
    baseprice_cols = ['wk_sold_median_base_price_byppg_log'] + [i for i in col if "_regularprice" in i]
    holiday_df = model_coef.loc[(model_coef['Variable'].str.startswith('holiday_flag'))]
    holiday_df = holiday_df.loc[(holiday_df['Value']!=0)].reset_index(drop=True)
    holiday_cols = holiday_df['Variable'].to_list()
    holiday =[i for i in holiday_cols if "holiday_flag" in i ]
    # holiday =[i for i in col if "Holiday" in i ]
    si_cols = [i for i in col if ("SI" in i) or("si" in i) ]
    trend_cols = [i for i in col if ('Trend' in i) or ('trend' in i)]
    base_list = baseprice_cols + si_cols + trend_cols + [i for i in col if "acv" in i ]
    base_others = holiday + [i for i in col if "_promoteddiscount" in i]
    baseline_var["Variable"]=base_list
    baseline_var_othr["Variable"]=base_others
    ppg_name = model_coef['PPG'].unique()[0]

    model_df.loc[:, 'Iteration']=1
    model_df1=model_df.copy()
    model_coef.loc[:, 'Iteration']=1
    base_scenario=base_var_cont(model_df,
                                model_df1,
                                roi_frame,
                                baseline_var,
                                baseline_var_othr,
                                model_coef,
                                _flag,
                                base_incremental_split
                                )
    base_scenario['TPR'] = model_df['TPR']
    return base_scenario

def list_to_frame(coeff:pd.DataFrame\
                  ,data:pd.DataFrame\
                ,roi_data:pd.DataFrame\
                ,flag='base'\
                ,extra_columns=[],
                base_incremental_split=None
                ):
    """list to frame

    Args:
        coeff (list): list of coefficient
        data (list): list of model data
        tpr_updated (bool, optional): tpr updated. Defaults to False.
        flag (_type_, optional): base or simulated. Defaults to None.

    Returns:
        DataFrame: final df
    """
    model_column = CONST.ModelData['MODEL_COLUMN'].value.copy()
    model_column = model_column+extra_columns
    
    pd.set_option('display.max_columns', None)  # or 1000
    pd.set_option('display.max_rows', 100)  # or 1000
    
    coeff_df,data_df,roi_df = convert_to_df(
        data_list=[coeff,data,roi_data],
        columns=[CONST.ModelCoeff['MODEL_COLUMN'].value,
        model_column,CONST.ModelROI['MODEL_COLUMN'].value]
    )
    # breakpoint()
    val = main(data_df,roi_df,coeff_df, _flag=flag,base_incremental_split=base_incremental_split)

    return val


def get_holiday_features(all_features):
    """
    Get holiday feature names

    Parameters
    ----------
    all_features: `list`
        List of features

    Returns
    -------
    `list`
        list of holiday feature names

    """

    holiday_flags_a = [i for i in all_features if "day" and "flag" in i]
    holiday_flags_b = [i for i in all_features if "Holiday" in i]
    holiday_flags = list(set(holiday_flags_a) | set(holiday_flags_b))

    col_remove = [
        "flag_N_pls_1",
        "flag_IN_OUT",
        "date",
        "Non_Promo_flag",
        "Date",
        "Flag_nonpromo",
        "promo",
        "Promo",
        "non_promo",
        "Non_promo",
        "Non_Promo",
        "fact_flag",
        "covid_impact_flag",
        "covid_recovery_flag",
    ]
    for i in holiday_flags:
        for j in col_remove:
            if j in i:
                n = holiday_flags[:]
                n.remove(i)
                holiday_flags = n
                break
    return holiday_flags