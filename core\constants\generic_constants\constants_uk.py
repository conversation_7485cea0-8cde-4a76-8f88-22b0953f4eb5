ROI_HEADER =[
    'Id',
'Date',
'Units',
'Volume',
'Pack Weight',
'Total Sold Unit',
'Total Sold Volume',
'Gsv Per Unit',
'Cogs Per Unit',
'Year',
'Period',
'Quarter',
'Week',
'Promo Price',
'List Price',
'Nsv',
'Gsv',
'Nsv Per Unit Future',
'Cogs',
'Tactic Medium Hz',
'Total Trade Investment',
'Sum Promo Units',
'Sum Non Promo Units',
'Is Delete',
'Is Active',
'Created By',
'Modified By',
'Created At',
'Modified At',
]

ROI_VALUES =[
    'id',
'date',
'units',
'volume',
'pack_weight',
'total_sold_unit',
'total_sold_volume',
'gsv_per_unit',
'cogs_per_unit',
'year',
'period',
'quarter',
'week',
'promo_price',
'list_price',
'nsv',
'gsv',
'nsv_per_unit_future',
'cogs',
'tactic_medium_hz',
'total_trade_investment',
'sum_promo_units',
'sum_non_promo_units',
'is_delete',
'is_active',
'created_by',
'modified_by',
'created_at',
'modified_at'
]

TACTIC_HEADER =[
'id',
'feature_baragin zone 1',
'feature_bollard cover',
'feature_bulk plinth 1',
'feature_dump bin',
'feature_event',
'feature_fos',
'feature_fos pd',
'feature_fpu',
'feature_fsdu',
'feature_floorstack',
'feature_ge1',
'feature_ge2',
'feature_ge3',
'feature_ge30',
'feature_ge7',
'feature_gssu',
'feature_gondola end',
'feature_hfss plinth 11d',
'feature_impulse',
'feature_incat fsdu',
'feature_ladder rack',
'feature_mod',
'feature_mgr special',
'feature_non hfss',
'feature_ofd',
'feature_pl14',
'feature_pl20a',
'feature_pl21',
'feature_pl22',
'feature_pl23',
'feature_pl24',
'feature_pl28',
'feature_pl30',
'feature_pl35',
'feature_pl36',
'feature_pl9',
'feature_pallet',
'feature_panel end',
'feature_plinth',
'feature_power aisle',
'feature_rt',
'feature_rollback event',
'feature_running track',
'feature_sa1',
'feature_sr8',
'feature_ss1',
'feature_ss2',
'feature_ss4',
'feature_ss9',
'feature_seasonal aisle',
'feature_side rack',
'feature_sidestack',
'feature_stack',
'feature_till point',
'feature_wov',
'feature_xmas',
'promo_2_for_1.5',
'promo_2_for_1.6',
'promo_2_for_1.8',
'promo_2_for_1.9',
'promo_2_for_10',
'promo_2_for_12',
'promo_2_for_13',
'promo_2_for_18',
'promo_2_for_2',
'promo_2_for_2.2',
'promo_2_for_2.25',
'promo_2_for_2.5',
'promo_2_for_2.6',
'promo_2_for_3',
'promo_2_for_3.2',
'promo_2_for_3.5',
'promo_2_for_7',
'promo_2_for_8',
'promo_2_for_9',
'promo_3_for_1',
'promo_3_for_2',
'promo_3_for_2.5',
'promo_3_for_27',
'promo_3_for_3',
'promo_4_for_1.75',
'promo_5_for_3',
'promo_6_for_3',
'promo_7_for_3',
'promo_introductory_offer',
'promo_introductory_offer.will_be_8',
'promo_new',
'promo_price_hold',
'promo_reduced_to_clear',
'promo_rollback',
'promo_save_0.45',
'promo_save_0.5',
'promo_save_0.55',
'promo_save_0.6',
'promo_save_0.65',
'promo_save_0.7',
'promo_save_0.75',
'promo_save_1',
'promo_save_1.05',
'promo_save_1.15',
'promo_save_1.25',
'promo_save_1.3',
'promo_save_1.5',
'promo_save_1.6',
'promo_save_1.75',
'promo_save_2',
'promo_save_4.5',
'promo_0.5_pd',
'promo_0.65_pd',
'promo_0.83_pd',
'promo_0.89_pd',
'promo_0.9_pd',
'promo_1_pd',
'promo_1.15_pd',
'promo_1.2_pd',
'promo_1.25_pd',
'promo_1.29_pd',
'promo_1.3_pd',
'promo_1.4_pd',
'promo_1.49_pd',
'promo_1.5_pd',
'promo_1.75_pd',
'promo_1.95_pd',
'promo_10_pd',
'promo_10.5_pd',
'promo_10.75_pd',
'promo_11_pd',
'promo_11.5_pd',
'promo_12_pd',
'promo_12.99_pd',
'promo_13_pd',
'promo_13.5_pd',
'promo_13.6_pd',
'promo_14.99_pd',
'promo_2_pd',
'promo_2.25_pd',
'promo_2.5_pd',
'promo_2.75_pd',
'promo_2.8_pd',
'promo_2.86_pd',
'promo_3_pd',
'promo_3.5_pd',
'promo_3.7_pd',
'promo_3.75_pd',
'promo_4_pd',
'promo_4.25_pd',
'promo_4.3_pd',
'promo_4.45_pd',
'promo_4.5_pd',
'promo_4.89_pd',
'promo_5_pd',
'promo_5.5_pd',
'promo_5.99_pd',
'promo_6.5_pd',
'promo_7_pd',
'promo_7.5_pd',
'promo_8_pd',
'promo_9_pd',
'promo_type',
'depth_of_discount',
'maxweek',
'minweek',
'minweek_edlp',
'maxweek_edlp',
'retailer',
'ppg',
'is_delete',
'is_active',
'created_by',
'modified_by',
'created_at',
'modified_at',
]

TACTIC_VALUES =[
    'id',
'feature_baragin zone 1',
'feature_bollard cover',
'feature_bulk plinth 1',
'feature_dump bin',
'feature_event',
'feature_fos',
'feature_fos pd',
'feature_fpu',
'feature_fsdu',
'feature_floorstack',
'feature_ge1',
'feature_ge2',
'feature_ge3',
'feature_ge30',
'feature_ge7',
'feature_gssu',
'feature_gondola end',
'feature_hfss plinth 11d',
'feature_impulse',
'feature_incat fsdu',
'feature_ladder rack',
'feature_mod',
'feature_mgr special',
'feature_non hfss',
'feature_ofd',
'feature_pl14',
'feature_pl20a',
'feature_pl21',
'feature_pl22',
'feature_pl23',
'feature_pl24',
'feature_pl28',
'feature_pl30',
'feature_pl35',
'feature_pl36',
'feature_pl9',
'feature_pallet',
'feature_panel end',
'feature_plinth',
'feature_power aisle',
'feature_rt',
'feature_rollback event',
'feature_running track',
'feature_sa1',
'feature_sr8',
'feature_ss1',
'feature_ss2',
'feature_ss4',
'feature_ss9',
'feature_seasonal aisle',
'feature_side rack',
'feature_sidestack',
'feature_stack',
'feature_till point',
'feature_wov',
'feature_xmas',
'promo_2_for_1.5',
'promo_2_for_1.6',
'promo_2_for_1.8',
'promo_2_for_1.9',
'promo_2_for_10',
'promo_2_for_12',
'promo_2_for_13',
'promo_2_for_18',
'promo_2_for_2',
'promo_2_for_2.2',
'promo_2_for_2.25',
'promo_2_for_2.5',
'promo_2_for_2.6',
'promo_2_for_3',
'promo_2_for_3.2',
'promo_2_for_3.5',
'promo_2_for_7',
'promo_2_for_8',
'promo_2_for_9',
'promo_3_for_1',
'promo_3_for_2',
'promo_3_for_2.5',
'promo_3_for_27',
'promo_3_for_3',
'promo_4_for_1.75',
'promo_5_for_3',
'promo_6_for_3',
'promo_7_for_3',
'promo_introductory_offer',
'promo_introductory_offer.will_be_8',
'promo_new',
'promo_price_hold',
'promo_reduced_to_clear',
'promo_rollback',
'promo_save_0.45',
'promo_save_0.5',
'promo_save_0.55',
'promo_save_0.6',
'promo_save_0.65',
'promo_save_0.7',
'promo_save_0.75',
'promo_save_1',
'promo_save_1.05',
'promo_save_1.15',
'promo_save_1.25',
'promo_save_1.3',
'promo_save_1.5',
'promo_save_1.6',
'promo_save_1.75',
'promo_save_2',
'promo_save_4.5',
'promo_0.5_pd',
'promo_0.65_pd',
'promo_0.83_pd',
'promo_0.89_pd',
'promo_0.9_pd',
'promo_1_pd',
'promo_1.15_pd',
'promo_1.2_pd',
'promo_1.25_pd',
'promo_1.29_pd',
'promo_1.3_pd',
'promo_1.4_pd',
'promo_1.49_pd',
'promo_1.5_pd',
'promo_1.75_pd',
'promo_1.95_pd',
'promo_10_pd',
'promo_10.5_pd',
'promo_10.75_pd',
'promo_11_pd',
'promo_11.5_pd',
'promo_12_pd',
'promo_12.99_pd',
'promo_13_pd',
'promo_13.5_pd',
'promo_13.6_pd',
'promo_14.99_pd',
'promo_2_pd',
'promo_2.25_pd',
'promo_2.5_pd',
'promo_2.75_pd',
'promo_2.8_pd',
'promo_2.86_pd',
'promo_3_pd',
'promo_3.5_pd',
'promo_3.7_pd',
'promo_3.75_pd',
'promo_4_pd',
'promo_4.25_pd',
'promo_4.3_pd',
'promo_4.45_pd',
'promo_4.5_pd',
'promo_4.89_pd',
'promo_5_pd',
'promo_5.5_pd',
'promo_5.99_pd',
'promo_6.5_pd',
'promo_7_pd',
'promo_7.5_pd',
'promo_8_pd',
'promo_9_pd',
'promo_type',
'depth_of_discount',
'maxweek',
'minweek',
'minweek_edlp',
'maxweek_edlp',
'retailer',
'ppg',
'is_delete',
'is_active',
'created_by',
'modified_by',
'created_at',
'modified_at',
]

RETAILER_PPG_MAPPING_VALUES = [
    'id',
'account_name',
'product_group',
'retailer_index',
'ppg_index',
'is_delete',
'is_active',
'created_by',
'modified_by',
'created_at',
'modified_at',
]

RETAILER_PPG_MAPPING_HEADER = [
    'Id',
'Account Name',
'Product Group',
'Retailer Index',
'Ppg Index',
'Is Delete',
'Is Active',
'Created By',
'Modified By',
'Created At',
'Modified At',
]

RETAILER_PPG_MAPPING_WEEKLY_VALUES = [
    'Id',
'Promo Type',
'Type Promo',
'Avg Units Per Week',
'Te Perweek',
'Gsv Perweek',
'Cogs Perweek',
'Nsv Perweek',
'Type Promo Id',
'Ret Idx',
'Ppg Idx',
'Is Delete',
'Is Active',
'Created By',
'Modified By',
'Created At',
'Modified At',
'List Price Per Week',
]

RETAILER_PPG_MAPPING_WEEKLY_HEADER =[
    'Id',
'Promo Type',
'Type Promo',
'Avg Units Per Week',
'Te Perweek',
'Gsv Perweek',
'Cogs Perweek',
'Nsv Perweek',
'Type Promo Id',
'Ret Idx',
'Ppg Idx',
'Is Delete',
'Is Active',
'Created By',
'Modified By',
'Created At',
'Modified At',
'List Price Per Week',
]