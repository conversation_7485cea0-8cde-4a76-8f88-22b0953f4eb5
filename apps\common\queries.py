""" SQL QUERY"""
from config.db_handler import db_table_format_in_sql_query_str


def get_meta_data_query(list_of_columns,model_name):
    """To Form Query based on table and columns passed."""
    query_string = f"""SELECT {','.join(list_of_columns)} FROM {db_table_format_in_sql_query_str(model_name)}"""
    return query_string

def query_enhancer_with_where_column(query,column_name,values_seperated_by_comma,addition_type='first'):
    """ To form Query which takes in columns to be added in where column"""
    if addition_type == 'first':
        return f"{query} where {column_name} in ({values_seperated_by_comma})"
    if addition_type == 'consecutive':
        return f"{query} and {column_name} in ({values_seperated_by_comma})"

def get_list_value(columns,model_name1,model_name2,fk_name,ppgs):
    """ Query for getting data."""
    query_string = f"""
                        SELECT {columns} FROM {db_table_format_in_sql_query_str(model_name1)} a 
                        INNER JOIN {db_table_format_in_sql_query_str(model_name2)} b
                        ON 
                        a.{fk_name}_id = b.id
                        WHERE 
                        b.is_delete = 0
                        AND
                        a.is_delete = 0
                        AND
                        b.account_name = %s
                        AND
                        b.product_group in ({','.join(["%s"] * len(ppgs))})
                    """
    return query_string

def get_list_value_from_base_model(columns,model_name1,ppgs):
    """ Query for getting data."""
    query_string = f"""
                        SELECT {columns} FROM {db_table_format_in_sql_query_str(model_name1)} a
                        WHERE 
                        a.is_delete = 0
                        AND
                        a.account_name = %s
                        AND
                        a.product_group in ({','.join(["%s"] * len(ppgs))})
                    """
    return query_string

def get_list_value_from_base_model_by_retailer(columns,model_name1):
    """ Query for getting data."""
    query_string = f"""
                        SELECT {columns} FROM {db_table_format_in_sql_query_str(model_name1)} a
                        WHERE 
                        a.is_delete = 0
                        AND
                        a.account_name = %s
                    """
    return query_string

def get_list_value_by_yearwise(columns,model_name1,model_name2,fk_name,ppgs):
    """ Query for getting data."""
    query_string = f"""
                        SELECT {columns} FROM {db_table_format_in_sql_query_str(model_name1)} a 
                        INNER JOIN {db_table_format_in_sql_query_str(model_name2)} b
                        ON 
                        a.{fk_name}_id = b.id
                        WHERE 
                        b.is_delete = 0
                        AND
                        a.is_delete = 0
                        AND
                        b.account_name = %s
                        AND
                        b.product_group in ({','.join(["%s"] * len(ppgs))})
                        AND
                        a.year = '2021'
                        ORDER BY week
                        
                    """
    return query_string

def get_brandwise_list_value_by_yearwise(columns,model_name1,model_name2,fk_name,brands):
    """ Query for getting data."""
    query_string = f"""
                        SELECT {columns} FROM {db_table_format_in_sql_query_str(model_name1)} a 
                        INNER JOIN {db_table_format_in_sql_query_str(model_name2)} b
                        ON 
                        a.{fk_name}_id = b.id
                        WHERE 
                        b.is_delete = 0
                        AND
                        a.is_delete = 0
                        AND
                        b.account_name = %s
                        AND
                        b.brand in ({','.join(["%s"] * len(brands))})
                        AND
                        a.year = '2021'
                        ORDER BY week
                        
                    """
    return query_string


def get_brandwise_list_value(columns,model_name1,model_name2,fk_name,ppgs):
    """ Query for getting data."""
    query_string = f"""
                        SELECT {columns} FROM {db_table_format_in_sql_query_str(model_name1)} a 
                        INNER JOIN {db_table_format_in_sql_query_str(model_name2)} b
                        ON 
                        a.{fk_name}_id = b.id
                        WHERE 
                        b.is_delete = 0
                        AND
                        a.is_delete = 0
                        AND
                        b.account_name = %s
                        AND
                        b.brand in ({','.join(["%s"] * len(ppgs))})
                    """
    return query_string

def get_list_value_by_retailer(columns,model_name1,model_name2,fk_name):
    """ Query for getting data."""
    query_string = f"""
                        SELECT {columns} FROM {db_table_format_in_sql_query_str(model_name1)} a 
                        INNER JOIN {db_table_format_in_sql_query_str(model_name2)} b
                        ON 
                        a.{fk_name}_id = b.id
                        WHERE 
                        b.is_delete = 0
                        AND
                        a.is_delete = 0
                        AND
                        b.account_name = %s
                        AND
                        a.year = '2021'
                        ORDER BY week
                        
                    """
    return query_string

def get_meta_data():
    """ Query for getting meta data."""
    query_string = f"""
                        SELECT * FROM {db_table_format_in_sql_query_str('model_meta')} a
                        WHERE 
                        a.model_meta.is_delete = 0
                        AND
                        a.model_meta.account_name = %s
                        AND
                        a.model_meta.account_name = %
                    """
    return query_string


def update_query(model):
    """ Query for updating data."""
    query_string = f"""update {db_table_format_in_sql_query_str(model)} set is_delete=true"""
    return query_string

def search_deep_query():
    """ Query for deep search."""
    query_string = f"""
    SELECT j.*, j2.*
    FROM {db_table_format_in_sql_query_str('scenario_promotion_save')} j
    CROSS APPLY OPENJSON(input_constraints) WITH (
        account_name VARCHAR(100)
    ) j2
    WHERE 
    j1.name = %s 
    OR 
    j1.user__name = %s 
    OR 
    j2.account_name = %s
    OR 
    j2.product_group = %s
    OR 
    j2.brand = %s
    OR 
    j2.promo_type = %s
    OR 
    j2.status_type = %s
    OR 
    j2.scenario_type = %s
    """

    return query_string

def search_scenario_query():
    """ Query for getting saved scenario."""
    query_string = f"""
    SELECT * FROM {db_table_format_in_sql_query_str('saved_scenario')} s
    WHERE 
    (s.name = %s
    OR 
    s.promo_type = %s)
    AND
    (s.scenario_type = %s
    AND 
    s.status_type = %s)
    ORDER BY
    s.id DESC
    """
    
    return query_string

def search_global_scenario_query():
    """ Query for getting saved scenario."""
    query_string = f"""
    SELECT * FROM {db_table_format_in_sql_query_str('saved_scenario')} s
    WHERE 
    (s.name = %s
    OR 
    s.promo_type = %s)
    AND
    (s.scenario_type = %s)
    ORDER BY
    s.id DESC
    """
    
    return query_string
    
def get_basedata(columns,ids):

    query_string = f"""
    SELECT {columns} FROM {db_table_format_in_sql_query_str('model_meta')} a 
        INNER JOIN {db_table_format_in_sql_query_str('model_data')} b
        ON 
        b.model_meta_id = a.id
        WHERE
        b.model_meta_id in ({','.join(["%s"] * len(ids))})
        AND
        b.year = '2021'
    """
    return query_string