from django.urls import path
from . import views

urlpatterns = [
    path('get_pricing_scenario_data/', views.SetPricingScenarioView.as_view({'post':'get_pricing_scenario_data'})),
    path("save/", views.SetPricingSavedScenarioView.as_view({'post':'save_scenario'})),
    path("publish/<int:saved_id>/", views.SetPricingSavedScenarioView.as_view({'post':'publish_scenario'})),
    path('post_simulate_scenario/', views.SetPricingScenarioView.as_view({'post':'post_simulate_scenario'})),
    path('load/<int:saved_id>/', views.SetPricingSavedScenarioView.as_view({'get':'load_scenario'})),
    path('published_scenario/<int:saved_id>/', views.SetPricingSavedScenarioView.as_view({'get':'load_published_scenario'})),
    path('global_constraints/', views.SetPricingSavedScenarioView.as_view({'get':'get_constraints'})),
    path("calculate_min_shelf_price/",views.SetPricingScenarioView.as_view({'post':'calculate_min_shelf_price'})),
]
