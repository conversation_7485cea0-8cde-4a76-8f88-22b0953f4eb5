""" Test Common View"""
import json
import pytest
from rest_framework.test import APIClient
from apps.promo.promo_scenario_planner.tests import (_constants as SP_CONST\
                                    ,factories as SP_FACT)
from apps.promo.promo_optimizer.tests import (_constants as OPT_CONST,
                                  factories as OPT_FACT
                                  )


from ..tests import _constants as CONST

apiclient = APIClient()

@pytest.mark.django_db(transaction=True)
def test_meta_data_view(auth):
    """test_meta_data_view

    Args:
        auth (_type_): authentication
    """
    
    # get all meta data 
    apiclient.credentials(**auth['headers'])
    data_resp = apiclient.get(
        f"{CONST.COMMON_BASEURL}/meta_data", format="json",
    )

    assert data_resp.status_code == 200
    assert json.loads(data_resp.content)["data"] is not None
    assert json.loads(data_resp.content)["data"] !=[]


    #get weekly constraints
    optimizer_payload={
        'account_name':'test 1',
        'product_group':["test product group 1"],
        "scenario_name":"new scenario",
        "scenario_type":'optimizer'
    }
    data_resp = apiclient.post(
        f"{CONST.COMMON_BASEURL}/get_weekly_constraints/", format="json",data=optimizer_payload
    )
    assert data_resp.status_code == 200
    assert json.loads(data_resp.content)["data"] is not None
    assert json.loads(data_resp.content)["data"] !=[]

    #get weekly constraints with empty payload
    optimizer_payload={
        'account_name':'test 1',
        'product_group':["test product group 1"]
    }
    data_resp = apiclient.post(
        f"{CONST.COMMON_BASEURL}/get_weekly_constraints/", format="json",data={}
    )
    assert data_resp.status_code == 406
    assert json.loads(data_resp.content)["status"]=="ERROR IN PARAMS"

    #get optimizer constraints with empty payload
    data_resp = apiclient.post(
        f"{OPT_CONST.OPTIMIZER_BASEURL}/list/", format="json",data={}
    )
    assert data_resp.status_code == 406
    assert json.loads(data_resp.content)["status"]=="ERROR IN PARAMS"

    # Skipping optimizer api
    # data_resp = apiclient.post(
    #     f"{OPT_CONST.OPTIMIZER_BASEURL}/optimize/", format="json",data=OPT_FACT.optimizer_payload
    # )
    # assert data_resp.status_code == 200
    # assert json.loads(data_resp.content)["data"] is not None
    # assert json.loads(data_resp.content)["data"] !=[]


    data_resp = apiclient.post(
        f"{SP_CONST.PROMO_BASEURL}/simulate/", format="json",data=SP_FACT.scenario_planner_payload
    )

    assert data_resp.status_code == 200
    assert json.loads(data_resp.content)["data"] is not None
    assert json.loads(data_resp.content)["data"] !=[]