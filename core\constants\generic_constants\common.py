from enum import Enum

from apps.common.tests.factories import cmn_instance


WEEKLY_CONSTRAINTS_RESPONSE = cmn_instance.get('WEEKLY_CONSTRAINTS_RESPONSE')
META_DATA_RESPONSE = cmn_instance.get('META_DATA_RESPONSE')


AUTH_TYPES=['Bearer','Token']
CURRENCY = '€'


META_HEADER=[
    'Id',
    'Created By',
    'Created At',
    'Modified By',
    'Modified At',
    'Is Delete',
    'Is Active',
    'Account Name',
    'Corporate Segment',
    'Product Group',
    'Product Type',
    'Brand',
    'Brand Tech',
    'Slug',    
]

META_VALUES=[
    'id',
    'created_by',
    'created_at',
    'modified_by',
    'modified_at',
    'is_delete',
    'is_active',
    'account_name',
    'corporate_segment',
    'product_group',
    'product_type',
    'brand',
    'brand_tech',
    'slug',
]

META_HEADER_EXT_LIST = list(set(META_HEADER) - set(['Id','Created By','Created At','Modified By','Modified At','Is Delete','Is Active',]))

META_VALUES_EXT_LIST = list(set(META_VALUES) - set(['id','created_by','created_at','modified_by','modified_at','is_delete','is_active',]))


DATA_HEADER = [
    'Id',
    'Created By',
    'Created At',
    'Modified By',
    'Modified At',
    'Is Delete',
    'Is Active'
    'Year',
    'Date',
    'Month',
    'Week',
].extend(META_HEADER_EXT_LIST)



META_VALUES=[
    'id',
    'created_by',
    'created_at',
    'modified_by',
    'modified_at',
    'is_delete',
    'is_active',
    'account_name',
    'corporate_segment',
    'product_group',
    'product_type',
    'brand',
    'brand_tech',
    'slug',
]

DATA_VALUES = [
    'id',
    'created_by',
    'created_at',
    'modified_by',
    'modified_at',
    'is_delete',
    'is_active',
    'year',
    'week',
    'date',
    'month'
].extend(META_VALUES_EXT_LIST)



ROI_HEADER = [
    'Id',
    'Created By',
    'Created At',
    'Modified By',
    'Modified At',
    'Is Delete',
    'Is Active',
    'Date',
    'Year',
    'Quarter',
    'Week',
    'Period',
    'Promo Price',
    'List Price',
    'Gsv',
    'Nsv',
    'Volume',
    'Units',
    'Nsv Per Unit Future',
    'Cogs Per Unit Future',
    'Gsv Per Unit Future',
    'Cogs',
    'Total Sold Unit',
    'Total Sold Volume',
    'Pack Weight'
    'Total Trade Investment',
    'Tactic Medium Hz',
    'Sum Non Promo Units',
    'Sum Promo Units',
].extend(META_HEADER_EXT_LIST)

ROI_VALUES = [
    'id',
    'created_by',
    'created_at',
    'modified_by',
    'modified_at',
    'is_delete',
    'is_active',
    'date',
    'year',
    'quarter',
    'week',
    'period',
    'promo_price',
    'list_price',
    'gsv',
    'nsv',
    'volume',
    'units',
    'nsv_per_unit_future',
    'cogs_per_unit_future',
    'gsv_per_unit_future',
    'cogs',
    'total_sold_unit',
    'total_sold_volume',
    'pack_weight',
    'total_trade_investment',
    'tactic_medium_hz',
    'sum_non_promo_units',
    'sum_promo_units',
].extend(META_VALUES_EXT_LIST)

COEFFICIENT_VALUES = [
    'id',
    'created_by',
    'created_at',
    'modified_by',
    'modified_at',
    'is_delete',
    'is_active',
].extend(META_VALUES_EXT_LIST)

COEFF_HEADER = [
    'Id',
    'Created By',
    'Created At',
    'Modified By',
    'Modified At',
    'Is Delete',
    'Is Active',
].extend(META_HEADER_EXT_LIST)
TACTIC_VALUES =[
        # 'ppg_item',
    #   'manufacturer',
      'customer',
      'promo_type',
      'effective_tpr',
      'ppg',
      'tactic_miscellaneous',
      'tactic_multibuy_tpr',
      'tactic_multibuy',
      'tactic_tpr',
      'tactic_unpublished',
      'tactic_defects',
      'tactic_lottery',
      'leaflet_flag',
      'min_week',
      'max_week',
      'retailer_ppg_map_id',
    #   'type_of_promo',
    #   'id',
    #   'created_by',
    #   'created_at',
    #   'modified_by',
    #   'modified_at',
    #   'is_delete',
    #   'is_active'
]

RETAILER_2021 = ['Edeka Hessenring', 'Edeka LEH', 'Edeka Minden', 'Edeka North',
             'Edeka Northern Bavaria', 'Edeka Rhein-Ruhr',
             'Edeka Southern Bavaria', 'Edeka Suedwest',
             'Netto Markendiscount', 'Penny', 'Rewe VS']


META_DATA_API_VALUES = [
    #primary key (id) needs to be passed here as we are doing raw query, account_name i.e., first column should be passed as query param if you want to pass the columns below it as query params, else raises an exception
    'id',
    'account_name',
    'product_type',
    'brand',
    'brand_tech',
    'product_group',
]

class ModelMeta(Enum):
    MODEL_NAME = 'model_meta'
    MODEL_COLUMN = META_HEADER
    MODEL_VALUES = META_VALUES
    YEAR_WISE = False
    REQUIRED_COLUMN = [('a.id','meta_id')]
    EXTRA_COLUMNS=[]

class ModelData(Enum):
    MODEL_NAME = 'model_data'
    MODEL_COLUMN = DATA_HEADER
    MODEL_VALUES = DATA_VALUES
    FK_MODEL = 'model_meta'
    FK_NAME = 'model_meta'
    YEAR_WISE = False
    FIN_YEAR_WISE = False
    REQUIRED_COLUMN = [('a.id','model_data_id')]
    EXTRA_COLUMNS = [('b.id','meta_id')]


class ModelROI(Enum):
    MODEL_NAME = 'model_roi'
    MODEL_COLUMN = ROI_HEADER
    MODEL_VALUES = ROI_VALUES
    FK_MODEL = 'model_meta'
    FK_NAME = 'model_meta'
    YEAR_WISE = False
    FIN_YEAR_WISE = True
    REQUIRED_COLUMN = [('a.id','roi_id')]
    EXTRA_COLUMNS = [('b.id','meta_id')]

class ModelCoeff(Enum):
    MODEL_NAME = 'model_coefficient'
    MODEL_COLUMN = COEFF_HEADER
    MODEL_VALUES = COEFFICIENT_VALUES
    FK_MODEL = 'model_meta'
    FK_NAME = 'model_meta'
    YEAR_WISE = False
    REQUIRED_COLUMN = [('a.id','coeff_id')]
    EXTRA_COLUMNS = [('b.id','meta_id')]

class ModelTactic(Enum):
    MODEL_NAME = 'model_tactic'
    MODEL_COLUMN = TACTIC_VALUES
    MODEL_VALUES = TACTIC_VALUES
    FK_MODEL = 'retailer_ppg_mapping'
    FK_NAME = 'retailer_ppg_map'
    REQUIRED_COLUMN = [('a.id','tactic_id')]
    EXTRA_COLUMNS = [('retailer_index','retailer_index'),
                    ('ppg_index','ppg_index'),
                    # ('type_of_promo','type_of_promo')
                    ]

COEFF_MAP_HEADER =[
    'Id',
'Created By',
'Created At',
'Modified By',
'Modified At',
'Is Delete',
'Is Active',
'Coefficient Old',
'Coefficient New',
'Value',
'Ppg Item No',
].extend(META_HEADER_EXT_LIST)
COEFFICIENT_MAP_VALUES =[
    'id',
'created_by',
'created_at',
'modified_by',
'modified_at',
'is_delete',
'is_active',
'coefficient_old',
'coefficient_new',
'value',
'ppg_item_no',
].extend(META_VALUES_EXT_LIST)
class ModelCoeffMap(Enum):
    MODEL_NAME = 'coefficient_mapping'
    MODEL_COLUMN = COEFF_MAP_HEADER
    MODEL_VALUES = COEFFICIENT_MAP_VALUES
    FK_MODEL = 'model_meta'
    FK_NAME = 'model_meta'
    YEAR_WISE = False
    REQUIRED_COLUMN = [('a.id','coeff_map_id')]
    EXTRA_COLUMNS = [('b.id','meta_id')]

ITEM_MAPPING_HEADER = [
    'Id',
'Created By',
'Created At',
'Modified By',
'Modified At',
'Is Delete',
'Is Active',
'Account Name',
'Product Group',
'Ppg Item No',
]
ITEM_MAPPING_VALUES = [
    'id',
'created_by',
'created_at',
'modified_by',
'modified_at',
'is_delete',
'is_active',
'account_name',
'product_group',
'ppg_item_no',
]
class ITEMMap(Enum):
    MODEL_NAME = 'item_map'
    MODEL_COLUMN = ITEM_MAPPING_HEADER
    MODEL_VALUES = ITEM_MAPPING_VALUES
    REQUIRED_COLUMN = [('a.id','item_map_id')]
    EXTRA_COLUMNS=[]

RETAILER_PPG_MAPPING_HEADER = [
    'id',
'created_by',
'created_at',
'modified_by',
'modified_at',
'is_delete',
'is_active',
'account_name',
'product_group',
'retailer_index',
'ppg_index',
]
RETAILER_PPG_MAPPING_VALUES =[
    'id',
'created_by',
'created_at',
'modified_by',
'modified_at',
'is_delete',
'is_active',
'account_name',
'product_group',
'retailer_index',
'ppg_index',
]

class ModelRetailerPPGMap(Enum):
    MODEL_NAME = 'retailer_ppg_mapping'
    MODEL_COLUMN = RETAILER_PPG_MAPPING_HEADER
    MODEL_VALUES = RETAILER_PPG_MAPPING_VALUES
    REQUIRED_COLUMN = [('a.id','retailer_ppg_map_id')]
    EXTRA_COLUMNS=[('type_of_promo','Type_of_Promo')]

RETAILER_PPG_MAPPING_WEEKLY_HEADER =[
    'id',
'created_by',
'created_at',
'modified_by',
'modified_at',
'is_delete',
'is_active',
'promo_type',
'type_promo_id',
'type_promo',
'avg_units_per_week',
'cogs_per_week',
'nsv_per_week',
'te_per_week',
'gsv_per_week',
'list_price_per_week',
]
RETAILER_PPG_MAPPING_WEEKLY_VALUES =['Id',
'Created By',
'Created At',
'Modified By',
'Modified At',
'Is Delete',
'Is Active',
'Promo Type',
'Type Promo Id',
'Type Promo',
'Avg Units Per Week',
'Cogs Per Week',
'Nsv Per Week',
'Te Per Week',
'Gsv Per Week',
'List Price Per Week',]

class ModelRETAILER_PPG_MAPPING_WEEKLY(Enum):
    MODEL_NAME = 'retailer_ppg_mapping_weekly'
    MODEL_COLUMN = RETAILER_PPG_MAPPING_WEEKLY_HEADER
    MODEL_VALUES = RETAILER_PPG_MAPPING_WEEKLY_VALUES
    FK_MODEL = 'retailer_ppg_mapping_weekly'
    FK_NAME = 'retailer_ppg_map'
    REQUIRED_COLUMN = [('a.id','rpm_weekly_id')]
    EXTRA_COLUMNS = [('retailer_index','Retailer_Index'),
                    ('ppg_index','PPG_Index'),
                    ('type_of_promo','Type_of_Promo')]