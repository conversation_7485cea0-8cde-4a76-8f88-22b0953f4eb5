""" promo Test Conftest"""
import sys
from pathlib import Path
import pytest
from pytest_factoryboy import register
from django.contrib.auth import get_user_model
from rest_framework.authtoken.models import Token
from ...user.serializers import UserSerializer
from .. import repository
from . import factories

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent.parent.parent
sys.path.append(str(BASE_DIR))

@pytest.fixture(scope="session")
def django_db_setup():
    import os
    from django.conf import settings
    settings.DATABASES['default'] = {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': os.path.join(os.path.join(BASE_DIR,'dbs'), 'testing_db1.sqlite3')
    }

# this fixture is used to temporarily create data in the model for testing

@pytest.fixture(scope="session")
def _django_data_setup(django_db_blocker):
    print("setup")
    with django_db_blocker.unblock():
        try:
            user_group_repo = repository.UserGroupRetailerRepository()
            user_group_repo.add(factories.user_groups)
        except Exception as _e:
            print("Skip adding data.")

@pytest.fixture
@pytest.mark.django_db
def auth_factory():
    print('************** user factory *************')

    def auth_info():

        user = get_user_model().objects.get_or_create(email='<EMAIL>')[0]
        token, _created = Token.objects.get_or_create(user=user)# pylint: disable=no-member
        user_ser = UserSerializer(token.user)
        headers = {'HTTP_AUTHORIZATION': f'Token {token.key}'}
        return {
            'token': token.key,
            'user': user_ser.data,
            'headers': headers
        }

    return auth_info

@pytest.fixture
@pytest.mark.django_db
def auth(auth_factory):

    print('************** user info *************')
    return auth_factory()

register(factories.UserGroupRetailerFactory,
        'ugrp',
        id= 1,
        region= "test region",
        group_name= "test_group",
        group_id=1
        )
