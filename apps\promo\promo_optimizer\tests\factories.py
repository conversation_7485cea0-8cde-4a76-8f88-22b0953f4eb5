""" Test Optimizer Factories"""
import factory
from pytest_factoryboy import register
from apps.common import models
from django.core.cache import cache as base_data
from core.generics.respository import AbstractRepository #pylint: disable=E0401
from core.generics.unit_of_work import AbstractUnitOfWork #pylint: disable=E0401


opt_instance = base_data.get('optimizer',{})
save_active_optimizer_scenario =  opt_instance.get('save_active_optimizer_scenario')
save_completed_optimizer_scenario = opt_instance.get('save_completed_optimizer_scenario')
save_active_optimizer_scenario_partial_data = opt_instance.get('save_active_optimizer_scenario_partial_data')
save_completed_optimizer_scenario_with_data = opt_instance.get('save_completed_optimizer_scenario_with_data')
save_completed_optimizer_scenario_with_data_for_api = opt_instance.get('save_completed_optimizer_scenario_with_data_for_api')
save_active_optimizer_scenario_partial_data_for_api = opt_instance.get('save_active_optimizer_scenario_partial_data_for_api')
optimizer_scenario_constraints_payload = opt_instance.get('optimizer_scenario_constraints_payload')
optimizer_payload = opt_instance.get('optimizer_payload')
data = opt_instance.get('data')
input_constraints = opt_instance.get('input_constraints')
optimizer_list_payload=opt_instance.get('optimizer_list_payload')
    
@register
class SaveActiveOptimizerScenarioFactory(factory.Factory):
    id= 1
    scenario_type =  "optimizer"
    promo_type="single"
    status_type="active"
    name = "optimizer save"
    comments = ""

    class Meta:
        model = models.SavedScenario

@register
class SaveCompletedOptimizerScenarioFactory(factory.Factory):
    id= 2
    scenario_type =  "optimizer"
    promo_type="single"
    status_type="completed"
    name = "optimizer save"
    comments = ""

    class Meta:
        model = models.SavedScenario

@register
class SaveActiveOptimizerScenarioWoDataFactory(factory.Factory):
    id= 1
    saved_scenario_id=1
    data=[]
    input_constraints={}

    class Meta:
        model = models.ScenarioPromotionSave


@register
class SaveCompletedOptimizerScenarioWithDataFactory(factory.Factory):
    id= 2
    saved_scenario_id=2
    input_constraints =  input_constraints
    data= data
    class Meta:
        model = models.ScenarioPromotionSave

class FakeModel:
    def __init__(self, model):
        super().__init__([])
        self._model = dict(model)
        super().__init__([])

    def add(self, entity):
        self._model[entity[0]] = entity[1:]

    def delete(self, _id):
        self._model.pop(_id)

    def get(self, _id=0):
        return [id] + self._model.get(_id, [])

    def update(self, entity):
        self._model.update({entity[0]: entity[1:]})


class FakeRepository(AbstractRepository):
    def __init__(self, model):
        super().__init__([])
        self._model = dict(model)
        super().__init__([])

    def add(self, entity):
        self._model[entity[0]] = entity[1:]

    def delete(self, _id):
        self._model.pop(_id)

    def get(self, _id=0):
        return [_id] + self._model.get(_id, [])

    def get_all(self):
        return [0] + self._model.get(0, [])

    def get_by_scenario_type(self,scenario_type,fetch_save_scenario):
        return [scenario_type] + self._model.get(0, [])

    def update(self, entity):
        self._model.update({entity[0]: entity[1:]})


class FakeUnitOfWork(AbstractUnitOfWork):
    def __init__(self):
        self.model = FakeRepository([])
        self.repo_obj = FakeRepository([])
        self.committed = False

    def commit(self):
        self.committed = True

    def rollback(self):
        return True

    def add(self, entity):
        self.model.add(entity)

    def get_data_dict(self):
        return self.model.get()

    def search(self,_query_string,params):
        """_summary_

        Args:
            _query_string (str): sql query string
            params (list): parameters

        Returns:
            list: searched data from db
        """
        if params:
            return [
                [i[0]] + list(i[1])
                for i in self.model._model.items()
                if i[1][0] in params
            ]
        
        return list(self.model._model.items())
