from django.conf import settings
from django.db import models

from apps.common import models as cmn_model
from config.db_handler import db_table_format


class SetTargetView(models.Model):
    id = models.IntegerField(primary_key=True)
    customer=models.CharField(max_length=100,verbose_name="customer")
    nsv_sum = models.IntegerField()
    changed_nn_change_percent = models.DecimalField(max_digits=30, decimal_places=15, default=0.0)
    nn_change_percent =  models.DecimalField(max_digits=30, decimal_places=15, default=0.0)
    status_flag=models.CharField(max_length=100,verbose_name="Technology")
    nn_change_percent_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0)


    class Meta:
        '''Meta Class For Model Saved Scenario.'''
        verbose_name = "Set Targets Scenario"
        db_table = 'pricing_target_view'
        if hasattr(settings, 'PRICING_DATABASE_SCHEMA'):
            db_table = db_table_format(db_table,settings.PRICING_DATABASE_SCHEMA)
