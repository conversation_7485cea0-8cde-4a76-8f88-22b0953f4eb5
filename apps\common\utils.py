import decimal
import json
import random
from base64 import b64decode
from Cryptodome.Cipher import AES
from Cryptodome.Protocol.KDF import PBKDF2 
from functools import partial
import numpy as np
import pandas as pd
from django.core.cache import cache as base_data
from apps.promo.promo_optimizer import generic as opt_generic
from core.generics import constants as core_const,resp_utils
from utils import day_month

from contextlib import closing
from django.db import connection

def raw_query_executor(query,columns_list):
    with closing(connection.cursor()) as cursor:
        # breakpoint()
        cursor.execute(query)
        results = cursor.fetchall()
        df = pd.DataFrame(results,columns = columns_list)
        response_df = df.to_dict(orient='records')
        # breakpoint()
        return response_df

def generate_promotion(data,
                      type_of_promo='single',
                      weeklycomb=False,
                      ppg=None,
                      weeks=None,
                      rswk=None,
                      scenario_type='optimizer',
                      is_standalone=False,
                      _jflags=None,
                      one_shot_ppg=None,
                      ppg1=''
                      ):
    data = data.copy()
    for _i,val in enumerate(data):
        joint_flags = [jflag for jflag in val if 'joint_promo' in jflag]
        promo_string = ''
        visibility_string=''
        val['mechanic'] = val['mechanic'] if 'mechanic' in val else promo_string
        val['type_of_promo']= val['type_of_promo'] if val['promo_present']!=0 in val else ''
        val['visibility']= val['visibility']  if 'visibility' in val else visibility_string
        val['promo_depth'] = val['tpr_discount_byppg'] if 'promo_depth' in val else 0 
        val['day_month'] = day_month(val['date'])
        val['month_char'] = val['day_month'].split(' ')[0]
        val['is_standalone'] = val['is_standalone'] if 'is_standalone' in val else False
        val['joint_flag'] = val['joint_flag'] if 'joint_flag' in val else ''
        val['is_one_shot_ppg'] = val['is_one_shot_ppg'] if 'is_one_shot_ppg' in val else False
    
        if weeklycomb:
            val['promo_depth'] = val['tpr_discount_byppg']
            val['type_of_promo']= val['type_of_promo']
            
            
        if weeks:
            if val['week'] in weeks:
                val['product_group'] = ppg
                val['joint_flag'] = _jflags
                val['is_one_shot_ppg'] = one_shot_ppg
        #         if not val['promotion_levels']:
        #             val['is_standalone'] = is_standalone
        # if rswk:
        #     if not val['promotion_levels']:
        #         val['is_standalone'] = is_standalone
        
        # if type_of_promo == 'joint' and (all( [ True if val[j]  == 0 else False for j in joint_flags]) or val['flag_promotype_all_brand']==1):
        #     continue   
        
        if type_of_promo == 'all_brand' and ((val['flag_promotype_all_brand']==0)):
            continue
        
        if type_of_promo == 'single' and not (all([ True if val[j]  == 0 else False for j in joint_flags]) and val['flag_promotype_all_brand']==0):         
            continue
        
        if val['flag_promotype_multibuy_tpr'] == 1:
            promo_string+='+Multibuy[TPR]'
        if val['flag_promotype_lottery'] == 1:
            promo_string+='+Lottery'
        if val['flag_promotype_multibuy'] == 1:
            promo_string+='+Multibuy'
        if val['flag_promotype_unpublished'] == 1:
            promo_string+='+unpublished'
        if val['flag_promotype_tpr'] == 1:
            promo_string+='+TPR'
        if val['flag_promotype_miscellaneous'] == 1:
            promo_string+='+miscellaneous'
        if val['flag_promotype_regular_price_communication'] == 1:
            promo_string+='+regular_price_communication'
    
        if val['flag_visibility_gazetka'] == 1:
            visibility_string+='+gazetka'
        if val['flag_visibility_gazetka_bystrzaki'] == 1:
            visibility_string+='+gazetka - BYSTRZAKI'
        if val['flag_visibility_gazetka_k_card'] == 1:
            visibility_string+='+gazetka - Kcard'
        if val['flag_visibility_gazetka_mocniaki'] == 1:
            visibility_string+='+gazetka - MOCNIAKI'
        if val['flag_visibility_gazetka_tv'] == 1:
            visibility_string+='+gazetka - TV'
        if val['flag_visibility_gazetka_modul_aranzowany'] == 1:
            visibility_string+='+gazetka_modul_aranzowany'
        if val['flag_visibility_promocja_polkowa'] == 1:
            visibility_string+='promocja_polkowa'
        
        if val['promo_present']:
            val['type_of_promo'] = type_of_promo
            val['promo_depth'] = val['tpr_discount_byppg']
            val['visibility'] =  visibility_string[1:]
            if one_shot_ppg and type_of_promo == 'single':
                val['is_one_shot_ppg'] = True
            val['mechanic'] = promo_string[1:]
        if scenario_type == 'promo':
            val['promo_depth'] = val['tpr_discount_byppg']

    return data


def convert_to_comma_seperated_strings(string_seperated_by_commas):
    "Useful when you have you want to use multiple query params you get from FE to perform raw query operations"
    list_of_strings_seperated_by_single_apostrophe = "'"+"','".join(i for i in string_seperated_by_commas.split(','))+"'"
    return list_of_strings_seperated_by_single_apostrophe

def insert_extra_var(var_list:list,index,val):
    var_list.insert(index,val)
    return var_list

def _make_promotion_var_zero(cloned_list):
    '''Changing Promotion values'''
    promo = ['flag_promotype_multibuy_tpr',
             'flag_promotype_lottery',
             'flag_promotype_unpublished',
             'flag_promotype_tpr',
             'flag_promotype_multibuy',
             'flag_promotype_miscellaneous',
             ]

    for key in promo:
        cloned_list[core_const.DATA_VALUES.index(key)] = decimal.Decimal(0)
    return cloned_list
    
def get_promo_mechanic(
                    flag_promotype_newsletter,
                    flag_promotype_leaflet,
                    flag_promotype_advertising_without_price,
                    flag_promotype_bonus,
                    flag_promotype_multibuy,
                    flag_promotype_pas,
                    flag_promotype_coupon,
                    flag_promotype_edlp,
                    flag_promotype_joint_promo_1,
                    flag_promotype_joint_promo_2,
                    flag_promotype_joint_promo_3,
                    flag_promotype_joint_promo_4,
                    flag_promotype_joint_promo_5,
                    flag_promotype_all_brand,
                    tpr,
                    custom_mechanic,
                    prev_mechanic,
                    flag,
                    previous_promo_type,
                    type_of_promo='single',
                    status='Infeasible',
                    index=0
                    ):
 
    promo_string = ''
    mechanic = prev_mechanic if prev_mechanic else ''
    promo_type = previous_promo_type if previous_promo_type else ''
    if type_of_promo=='joint' and (flag == 'base' or status=='Infeasible') and ((flag_promotype_joint_promo_1==0) \
        and (flag_promotype_joint_promo_2==0)\
        and (flag_promotype_joint_promo_3==0)\
        and (flag_promotype_joint_promo_4==0) \
        and (flag_promotype_joint_promo_5==0)\
        ):
        pass

    elif type_of_promo=='single' and (flag == 'base' or status=='Infeasible') and not((flag_promotype_joint_promo_1==0) \
        and (flag_promotype_joint_promo_2==0)\
        and (flag_promotype_joint_promo_3==0)\
        and (flag_promotype_joint_promo_4==0) \
        and (flag_promotype_joint_promo_5==0)\
        ):
        pass

    elif type_of_promo == 'all_brand' and (flag == 'base' or status=='Infeasible') and ((flag_promotype_all_brand==0)):
        pass

    else:     
        if flag_promotype_bonus:
            promo_string+='+Bonus'
        if flag_promotype_pas:
            promo_string+='+PAS'
        if flag_promotype_multibuy:
            promo_string+='+Multibuy'
        if flag_promotype_coupon:
            promo_string+='+Coupon'
        if flag_promotype_edlp:
            promo_string+='+EDLP'
        if flag_promotype_advertising_without_price == 1 and not promo_string:
            promo_string+='+Price Off(AWP)'
        if flag_promotype_advertising_without_price == 1 and not 'AWP' \
            in promo_string:
            promo_string=" ".join(promo_string.split('+'))
            promo_string+=' (AWP)'
        if (flag_promotype_newsletter == 1 or flag_promotype_leaflet == 1) \
            and not promo_string:
            promo_string='+Price Off'

        # non promo weeks
        if not flag_promotype_leaflet:
            promo_string = ''
            promo_type = ''
            mechanic = ''

        # promo weeks
        if flag_promotype_leaflet:
            promo_type = type_of_promo
            mechanic = promo_string[1:]
        
        # custom promo weeks 
        if custom_mechanic:
            mechanic = custom_mechanic
 
    return mechanic,promo_type


def get_scenario_planner_promo_mechanic(
                    flag_promotype_multibuy_tpr,
                    flag_promotype_multibuy,
                    flag_promotype_lottery,
                    flag_promotype_unpublished,
                    flag_promotype_tpr,
                    flag_promotype_miscellaneous,
                    promo_present,
                    joint_flags,
                    flag_promotype_all_brand,
                    custom_mechanic='',
                    flag='base',
                    index=0
                    ):
 
    promo_string = ''
    type_of_promo = ''
    mechanic = ''

    if not all( [ True if j  == 0 else False for j in joint_flags]):
        type_of_promo = 'joint'

    if all( [ True if j  == 0 else False for j in joint_flags]):
        type_of_promo = 'single'

    if ((flag_promotype_all_brand==1)):
        type_of_promo = 'all_brand'

    if flag_promotype_multibuy_tpr:
        promo_string+='+Multibuy[TPR]'
    if flag_promotype_lottery:
        promo_string+='+Lottery'
    if flag_promotype_multibuy:
        promo_string+='+Multibuy'
    if flag_promotype_unpublished:
        promo_string+='+Unpublished'
    if flag_promotype_tpr:
        promo_string+='+TPR'
    if flag_promotype_miscellaneous:
        promo_string+='miscellaneous'
    # if flag_promotype_advertising_without_price == 1 and not promo_string:
    #     promo_string+='+Price Off(AWP)'
    # if flag_promotype_advertising_without_price == 1 and not 'AWP' \
    #     in promo_string:
    #     promo_string=" ".join(promo_string.split('+'))
    #     promo_string+=' (AWP)'
    # if (flag_promotype_newsletter == 1 or flag_promotype_leaflet == 1) \
    #     and not promo_string:
    #     promo_string='+Price Off'

    # non promo weeks
    if not promo_present:
        promo_string = ''
        promo_type = ''
        mechanic = ''
    
    # promo weeks
    if promo_present:
        promo_type = type_of_promo
        mechanic = promo_string[1:]

    # custom promo weeks 
    if custom_mechanic:
        mechanic = custom_mechanic

    return mechanic,promo_type

def get_no_leaflets(vl): 

    if vl.get('product_group',''):
        if 'pg' not in vl:
            vl['pg'] = {vl['product_group']:{'single':0}}
        if 'no_of_slots' in vl:
            vl['pg'][vl['product_group']]['single'] = vl['no_of_slots']
        if 'joint_ppg_data' in vl:                       
            for _jntd in vl['joint_ppg_data']:
                if _jntd['product_group'] not in vl['pg']:
                    vl['pg'][_jntd['product_group']]={'joint':0}
                vl['pg'][_jntd['product_group']]['joint'] = _jntd['no_of_slots']
        if 'all_brand_ppg_data' in vl:
            for _ab in vl['all_brand_ppg_data']:
                if _ab['product_group'] not in vl['pg']:
                    vl['pg'][_ab['product_group']]={'all_brand':0}
                vl['pg'][_ab['product_group']]['all_brand'] = _ab['no_of_slots']
    else:
        vl['pg']={}
    return vl['pg']

def get_values(sl):
    s = sl.get('single',0)
    j = sl.get('joint',0)
    a = sl.get('all_brand',0)
    return s+j+a

def filter_by_flag(flag):
    if float(flag['flag_promotype_joint_promo_1']):
        return 'flag_promotype_joint_promo_1'
    if float(flag['flag_promotype_joint_promo_2']):
        return 'flag_promotype_joint_promo_2'
    if float(flag['flag_promotype_joint_promo_3']):
        return 'flag_promotype_joint_promo_3'
    if float(flag['flag_promotype_joint_promo_4']):
        return'flag_promotype_joint_promo_4'
    if float(flag['flag_promotype_joint_promo_5']):
        return 'flag_promotype_joint_promo_5'
    return 

def filter_by_joint_flag(vl,column):
    if float(vl[column.index('flag_promotype_joint_promo_1')]):
        return 'flag_promotype_joint_promo_1'
    if float(vl[column.index('flag_promotype_joint_promo_2')]):
        return 'flag_promotype_joint_promo_2'
    if float(vl[column.index('flag_promotype_joint_promo_3')]):
        return 'flag_promotype_joint_promo_3'
    if float(vl[column.index('flag_promotype_joint_promo_4')]):
        return'flag_promotype_joint_promo_4'
    if float(vl[column.index('flag_promotype_joint_promo_5')]):
        return 'flag_promotype_joint_promo_5'
    return 

def get_range_of_promo(tactic_df:pd.DataFrame):
    try:
        min_promo = float(tactic_df.loc[tactic_df['Promo_Type']!=1,'min_week'].values[0])
        max_promo = float(tactic_df.loc[tactic_df['Promo_Type']!=1,'max_week'].values[0])
    except:
        pass
    return min_promo,max_promo

def pre_update_natioal_promotion_mechanics(model_data,plvl_data,scenario_type='optimizer'):
    
    for  _pl in plvl_data:
        _index = _pl['week']-1
        if _pl['promotion_levels']:
            model_data[_index]['promotion_levels'] = _pl['promotion_levels']
   
    return model_data

def update_single_national_promo(**kwargs):  
    if  kwargs.get('model_data')[kwargs.get('_index')]['type_of_promo'] != 'single':
        if 'no_of_slots' in kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['data']:
            kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['data']['no_of_slots']+=1
            kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['data']['param_compulsory_promo_weeks'].append(kwargs.get('_pl')['week'])
            kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['data']['param_total_promo_min'] = kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['data']['param_total_promo_min'] if kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['data']['param_total_promo_min'] \
            >=len(kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['data']['param_compulsory_promo_weeks'])\
            else len(kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['data']['param_compulsory_promo_weeks'])

        if kwargs.get('model_data')[kwargs.get('_index')]['type_of_promo'] == 'joint':
            if kwargs.get('_jd_prev'):

                kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
                ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['no_of_slots']-=1
                kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
                ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['param_compulsory_promo_weeks'].remove(kwargs.get('_pl')['week'])
                kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
                ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['param_total_promo_min'] = kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
                ['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
                ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['param_total_promo_min'] if kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
                ['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
                ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['param_total_promo_min'] >=len(kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
                ['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
                ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['param_compulsory_promo_weeks']) else len(kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
                ['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
                ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['param_compulsory_promo_weeks'])
                
                for flags in kwargs.get('jnt_prev_flags'):
                    for f in flags.split(' & '):
                        kwargs.get('model_data')[kwargs.get('_index')][f] = 0
                if kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
                    ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['no_of_slots']<=0:
                    kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['joint_ppg_data'].remove(kwargs.get('_jd_prev')[0])
        
        if kwargs.get('model_data')[kwargs.get('_index')]['type_of_promo'] == 'all_brand':
            
            if kwargs.get('_ab_prev'):
                kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
                ['all_brand_ppg_data'].index(kwargs.get('_ab_prev')[0])]['no_of_slots']-=1
                kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
                ['all_brand_ppg_data'].index(kwargs.get('_ab_prev')[0])]['param_compulsory_promo_weeks'].remove(kwargs.get('_pl')['week'])
                kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
                ['all_brand_ppg_data'].index(kwargs.get('_ab_prev')[0])]['param_total_promo_min'] = kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
                ['all_brand_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data']\
                .index(kwargs.get('_ab_prev')[0])]['param_total_promo_min'] \
                if kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data']\
                [kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'].index(kwargs.get('_ab_prev')[0])]\
                ['param_total_promo_min'] >=len(kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data']\
                [kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'].index(kwargs.get('_ab_prev')[0])]\
                ['param_compulsory_promo_weeks'])\
                else len(kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data']\
                [kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'].index(kwargs.get('_ab_prev')[0])]\
                ['param_compulsory_promo_weeks'])
                if kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
                ['all_brand_ppg_data'].index(kwargs.get('_ab_prev')[0])]['no_of_slots']<=0:
                    kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'].remove(kwargs.get('_ab_prev')[0])     
            kwargs.get('model_data')[kwargs.get('_index')]['flag_promotype_all_brand'] = 0
    return kwargs.get('input_data_dict'),kwargs.get('model_data')

def update_joint_national_promo(**kwargs):
    
    if kwargs.get('_pl')['week'] not in kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['joint_ppg_data'].index(kwargs.get('_jd_curent')[0])]['param_compulsory_promo_weeks']:    
        kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
        ['joint_ppg_data'].index(kwargs.get('_jd_curent')[0])]['param_compulsory_promo_weeks'].append(kwargs.get('_pl')['week'])
        kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
        ['joint_ppg_data'].index(kwargs.get('_jd_curent')[0])]['no_of_slots']+=1
        kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
        ['joint_ppg_data'].index(kwargs.get('_jd_curent')[0])]['param_total_promo_min'] = kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
        ['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['joint_ppg_data']\
        .index(kwargs.get('_jd_curent')[0])]['param_total_promo_min'] if kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
        ['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
        ['joint_ppg_data'].index(kwargs.get('_jd_curent')[0])]['param_total_promo_min'] >=len(kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
        ['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['joint_ppg_data']\
        .index(kwargs.get('_jd_curent')[0])]['param_compulsory_promo_weeks'])else len(kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
        ['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
        ['joint_ppg_data'].index(kwargs.get('_jd_curent')[0])]['param_compulsory_promo_weeks'])
        
        for flags in kwargs.get('jnt_curr_flags'):
            for f in flags.split(' & '):
                kwargs.get('model_data')[kwargs.get('_index')][f] = 1
        if kwargs.get('_jd_prev'):
            kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['no_of_slots']-=1
            kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['param_compulsory_promo_weeks'].remove(kwargs.get('_pl')['week'])
            kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['param_total_promo_min'] = kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['param_total_promo_min'] if kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['param_total_promo_min'] >=len(kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['param_compulsory_promo_weeks'])\
                                                                else len(kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['joint_ppg_data']\
                                                                [kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['param_compulsory_promo_weeks'])
            if kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
                ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['no_of_slots']<=0:
                kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['joint_ppg_data'].remove(kwargs.get('_jd_prev')[0])
        
        for flags in kwargs.get('jnt_prev_flags'):
            for f in flags.split(' & '):
                kwargs.get('model_data')[kwargs.get('_index')][f] = 0
    
    if kwargs.get('model_data')[kwargs.get('_index')]['type_of_promo'] == 'single':
        if 'no_of_slots' in kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['data']:
            kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['data']['no_of_slots']-=1
            kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['data']['param_compulsory_promo_weeks'].remove(kwargs.get('_pl')['week'])
            kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['data']['param_total_promo_min'] = kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['data']['param_total_promo_min'] if kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['data']['param_total_promo_min'] \
            >=len(kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['data']['param_compulsory_promo_weeks']) else len(kwargs.get('input_data_dict')\
            [kwargs.get('ppg_main')]['data']['param_compulsory_promo_weeks'])
            if kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['data']['no_of_slots']<=0:
                kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['data']['type_of_promo'] = ''
    if kwargs.get('model_data')[kwargs.get('_index')]['type_of_promo'] == 'all_brand':
        if kwargs.get('_ab_prev'):
            kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['all_brand_ppg_data'].index(kwargs.get('_ab_prev')[0])]['no_of_slots']-=1 
            kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['all_brand_ppg_data'].index(kwargs.get('_ab_prev')[0])]['param_compulsory_promo_weeks'].remove(kwargs.get('_pl')['week'])
            kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['all_brand_ppg_data'].index(kwargs.get('_ab_prev')[0])]['param_total_promo_min'] = kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['all_brand_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data']\
            .index(kwargs.get('_ab_prev')[0])]['param_total_promo_min'] \
            if kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data']\
            [kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'].index(kwargs.get('_ab_prev')[0])]\
            ['param_total_promo_min'] >=len(kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data']\
            [kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'].index(kwargs.get('_ab_prev')[0])]\
            ['param_compulsory_promo_weeks'])\
            else len(kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data']\
            [kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'].index(kwargs.get('_ab_prev')[0])]\
            ['param_compulsory_promo_weeks'])
            if kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['all_brand_ppg_data'].index(kwargs.get('_ab_prev')[0])]['no_of_slots']<=0:
                kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'].remove(kwargs.get('_ab_prev')[0])
        kwargs.get('model_data')[kwargs.get('_index')]['flag_promotype_all_brand'] = 0
        

    return kwargs.get('input_data_dict'),kwargs.get('model_data')

def update_all_brand_national_promo(**kwargs):
    if  kwargs.get('_pl')['week'] not in kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'][kwargs.get('input_data_dict')\
        [kwargs.get('ppg_main')]['all_brand_ppg_data'].index(kwargs.get('_ab_curent')[0])]['param_compulsory_promo_weeks']: 
        kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
        ['all_brand_ppg_data'].index(kwargs.get('_ab_curent')[0])]['no_of_slots']+=1
        kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
        ['all_brand_ppg_data'].index(kwargs.get('_ab_curent')[0])]['param_compulsory_promo_weeks'].append(kwargs.get('_pl')['week'])
        kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
        ['all_brand_ppg_data'].index(kwargs.get('_ab_curent')[0])]['param_total_promo_min'] = kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data']\
        [kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'].index(kwargs.get('_ab_curent')[0])]['param_total_promo_min'] \
        if kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data']\
        [kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'].index(kwargs.get('_ab_curent')[0])]\
        ['param_total_promo_min'] >=len(kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data']\
        [kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'].index(kwargs.get('_ab_curent')[0])]\
        ['param_compulsory_promo_weeks'])\
        else len(kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data']\
        [kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'].index(kwargs.get('_ab_curent')[0])]\
        ['param_compulsory_promo_weeks'])
        
        if kwargs.get('_ab_prev'):
            kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['all_brand_ppg_data'].index(kwargs.get('_ab_prev')[0])]['no_of_slots']-=1
            kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['all_brand_ppg_data'].index(kwargs.get('_ab_prev')[0])]['param_compulsory_promo_weeks'].remove(kwargs.get('_pl')['week'])
            kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['all_brand_ppg_data'].index(kwargs.get('_ab_prev')[0])]['param_total_promo_min'] = kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['all_brand_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'].index(kwargs.get('_ab_prev')[0])]['param_total_promo_min'] \
            if kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data']\
            [kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'].index(kwargs.get('_ab_prev')[0])]\
            ['param_total_promo_min'] >=len(kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data']\
            [kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'].index(kwargs.get('_ab_prev')[0])]\
            ['param_compulsory_promo_weeks'])\
            else len(kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data']\
            [kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'].index(kwargs.get('_ab_prev')[0])]\
            ['param_compulsory_promo_weeks'])
            if kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
                ['all_brand_ppg_data'].index(kwargs.get('_ab_prev')[0])]['no_of_slots']<=0:
                    kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['all_brand_ppg_data'].remove(kwargs.get('_ab_prev')[0])

    if kwargs.get('model_data')[kwargs.get('_index')]['type_of_promo'] == 'joint':
        if kwargs.get('_jd_prev'):
            kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['no_of_slots']-=1
            kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['param_compulsory_promo_weeks'].remove(kwargs.get('_pl')['week'])
            kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['param_total_promo_min'] = kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['param_total_promo_min'] if kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['param_total_promo_min'] >=len(kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['param_compulsory_promo_weeks'])else len(kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
            ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['param_compulsory_promo_weeks'])
            
            for flags in kwargs.get('jnt_prev_flags'):
                for f in flags.split(' & '):
                    kwargs.get('model_data')[kwargs.get('_index')][f] = 0
            if kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['joint_ppg_data'][kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
                ['joint_ppg_data'].index(kwargs.get('_jd_prev')[0])]['no_of_slots']<=0:
                kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['joint_ppg_data'].remove(kwargs.get('_jd_prev')[0])

    if kwargs.get('model_data')[kwargs.get('_index')]['type_of_promo'] == 'single':
        kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['data']['no_of_slots']-=1
        kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['data']['param_compulsory_promo_weeks'].remove(kwargs.get('_pl')['week'])
        kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['data']['param_total_promo_min'] = kwargs.get('input_data_dict')[kwargs.get('ppg_main')]\
        ['data']['param_total_promo_min'] if kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['data']['param_total_promo_min'] \
        >=len(kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['data']['param_compulsory_promo_weeks'])\
        else len(kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['data']['param_compulsory_promo_weeks']) 
        if kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['data']['no_of_slots']<=0:
            kwargs.get('input_data_dict')[kwargs.get('ppg_main')]['data']['type_of_promo'] = ''   
    kwargs.get('model_data')[kwargs.get('_index')]['flag_promotype_all_brand'] = 1

    return kwargs.get('input_data_dict'),kwargs.get('model_data')

def post_update_natioal_promotion_mechanics(model_data,plvl_data,input_data_dict,ppg_main='',rpt=None):
    _plvl_data = list(filter(lambda x:x['promotion_levels'],plvl_data))
    jnt_ppg_list = list(filter(lambda x:x['type_of_promo']=='joint',model_data))
    
        ### updating slots ######
    for  _pl in _plvl_data:
        _index = _pl['week']-1         
        _jn_curr =  list(filter(lambda x:x['product_group']==_pl['product_group'],jnt_ppg_list))
        _jn_prev =  list(filter(lambda x:x['product_group']==model_data[_index]['product_group'],jnt_ppg_list))
        # mm=list(map(lambda x:(x['week'],x['product_group']),_jn_prev))
        jnt_curr_flags = list(set(list(map(lambda x:x['joint_flag'],_jn_curr)))) 
        jnt_prev_flags = list(set(list(map(lambda x:x['joint_flag'],_jn_prev))))
        _jd_curent = list(filter(lambda x:sorted(x['product_group'])==sorted(_pl['product_group'])\
        ,input_data_dict[ppg_main]['joint_ppg_data'])) 
        _jd_prev = list(filter(lambda x:sorted(x['product_group'])==sorted(model_data[_index]['product_group'])\
        ,input_data_dict[ppg_main]['joint_ppg_data']))
        _ab_curent = list(filter(lambda x:sorted(x['product_group'])==sorted(_pl['product_group'])\
        ,input_data_dict[ppg_main]['all_brand_ppg_data'])) 
        _ab_prev = list(filter(lambda x:sorted(x['product_group'])==sorted(model_data[_index]['product_group'])\
        ,input_data_dict[ppg_main]['all_brand_ppg_data']))

        jflags = ' & '.join(jnt_curr_flags) if jnt_curr_flags else ''
        if _pl['type_of_promo'] == 'joint':
               input_data_dict,model_data = update_joint_national_promo(input_data_dict=input_data_dict\
                                                               ,ppg_main=ppg_main\
                                                                ,_ab_prev=_ab_prev\
                                                                ,_jd_curent=_jd_curent\
                                                                ,jnt_prev_flags=jnt_prev_flags\
                                                                ,jnt_curr_flags=jnt_curr_flags\
                                                                ,_jd_prev=_jd_prev\
                                                                ,_pl=_pl\
                                                                ,_index=_index\
                                                                ,model_data=model_data)
                
        if _pl['type_of_promo'] == 'single':
            input_data_dict,model_data = update_single_national_promo(input_data_dict=input_data_dict\
                                                               ,ppg_main=ppg_main\
                                                                ,_ab_prev=_ab_prev\
                                                                ,_jd_curent=_jd_curent\
                                                                ,jnt_prev_flags=jnt_prev_flags\
                                                                ,jnt_curr_flags=jnt_curr_flags\
                                                                ,_jd_prev=_jd_prev\
                                                                ,_pl=_pl\
                                                                ,_index=_index\
                                                                ,model_data=model_data)
        if _pl['type_of_promo'] == 'all_brand':
            input_data_dict,model_data = update_single_national_promo(input_data_dict=input_data_dict\
                                                               ,ppg_main=ppg_main\
                                                                ,_ab_prev=_ab_prev\
                                                                ,_jd_curent=_jd_curent\
                                                                ,jnt_prev_flags=jnt_prev_flags\
                                                                ,jnt_curr_flags=jnt_curr_flags\
                                                                ,_jd_prev=_jd_prev\
                                                                ,_pl=_pl\
                                                                ,_index=_index\
                                                                ,model_data=model_data\
                                                                ,_ab_curent=_ab_curent)
        
        model_data[_index]['mechanic'] = _pl['mechanic']
        if _pl['mechanic'] and _pl['type_of_promo']:
            model_data[_index]['flag_promotype_leaflet'] = 1
        if 'AWP' in _pl['mechanic']:                    
            model_data[_index]['flag_promotype_advertising_without_price'] = 1
        if 'Bonus' in _pl['mechanic']:                    
            model_data[_index]['flag_promotype_bonus'] = 1
        if 'PAS' in _pl['mechanic']:                    
            model_data[_index]['flag_promotype_pas'] = 1
        if 'Multibuy' in _pl['mechanic']:                          
            model_data[_index]['flag_promotype_multibuy'] = 1
        if 'Coupon' in _pl['mechanic']:                    
            model_data[_index]['flag_promotype_coupon'] = 1
        if 'EDLP' in _pl['mechanic']:                    
            model_data[_index]['flag_promotype_edlp'] = 1

        model_data[_index]['promotion_levels'] = 'National'
        _ppg = _pl['product_group']+'-all_brand' if _pl['type_of_promo']=='all_brand' else _pl['product_group']
        model_data[_index]['product_group'] = get_ppg(_ppg,rpt)
        model_data[_index]['type_of_promo'] = _pl['type_of_promo']
        model_data[_index]['is_standalone'] = False
        model_data[_index]['joint_flag'] = jflags[3:] if  jflags.startswith(' & ') else jflags
    return input_data_dict

def is_national_promo_week(_list,weeks):
    national_promo = list(filter(lambda x:x['week'] in weeks,_list))[0]['promotion_levels']
    return bool(national_promo)

def fetch_is_standalone(col1,col2,col3,_df):
    if len(col1.split(' & '))==1 and col2 == 'all_brand':
        
        is_national = is_national_promo_week(_df,col3)
        return not is_national
    return False

def get_ppg(ppg,rpt,pl=None):
    if rpt:
        _new_ppg = list(filter(lambda x:sorted(opt_generic.format_ppg4(x[1]))==sorted(opt_generic.format_ppg2(ppg)),rpt))
        if _new_ppg:           
            _new_ppg = opt_generic.format_all_brand(opt_generic.format_ppg(_new_ppg[0][1]))
            return _new_ppg
    return opt_generic.format_all_brand(ppg)

def get_no_of_slots(data):
    base_reslt_f = pd.DataFrame()
    total_no_of_slots_with_duplicates \
    = total_no_of_slots_wo_duplicates = 0

    if data:
        for _sim in data:
            if 'simulated' in _sim.keys():
                df1 = pd.DataFrame(_sim['simulated']['weekly']).reset_index() 
            else:
                df1 = pd.DataFrame(_sim['weekly']).reset_index()   
            df1['PPG_MAIN'] = _sim['product_group'].replace('/','')
            base_reslt_f = base_reslt_f.append(df1)
    
        promo_df = base_reslt_f.loc[(base_reslt_f['promo_present']>0)]

        _excluded_standalone_df = promo_df[~promo_df['is_standalone']]
        try:
            _excluded_one_shot_df = _excluded_standalone_df[~_excluded_standalone_df['is_one_shot_ppg']]
        except:
            _excluded_one_shot_df = _excluded_standalone_df

        wo_dup_promo_df = _excluded_one_shot_df[~_excluded_one_shot_df.duplicated(['product_group', 'week'])]\
                        .groupby(['product_group','type_of_promo'])\
                        .agg({'promo_present':'sum'}).reset_index()
        total_no_of_slots_with_duplicates = _excluded_one_shot_df['promo_present'].sum()
        total_no_of_slots_wo_duplicates = wo_dup_promo_df['promo_present'].sum()

    return total_no_of_slots_with_duplicates,total_no_of_slots_wo_duplicates


def get_base_promotion(_data,user_id):
    df = pd.DataFrame(_data,columns=core_const.DATA_HEADER) if isinstance(_data,list) else _data
    df_group = df.groupby(['Retailer','PPG'])
    result_dict = dict.fromkeys(core_const.DATA_VALUES)
    # iterating through the dictionary and updating values
    for key, value in zip(result_dict.keys(), core_const.DATA_HEADER):
        result_dict[key] = value
    
    for key,item in df_group:
        flags_and_mech_columns = []
        flags_and_mech_columns = [x for x in df.columns if 'Flag' in x]+['Promotion_Level'\
                                                                         ,'is_standalone'\
                                                                            ,'joint_flag'\
                                                                            ,'type_of_promo'\
                                                                            ,'mechanic']
        a_group = df_group.get_group(key).reset_index()
        a_group = a_group.drop(['account_name_new','product_group_new'],axis=1, errors='ignore')
        acc_name = key[0]
        ppg = key[1]
        df2 = base_data.get(f'{user_id}_{acc_name}_{ppg}_df')
        df2 = df2.rename(columns={**result_dict})
        df1 = a_group.merge(df2, on='Week', how='left')
        df1=df1.reset_index(drop=True).drop(columns=['index_x'])
        df1=df1.rename(columns={'Retailer_y':'account_name_new','PPG_y':'product_group_new'})
        df1.columns = df1.columns.str.replace(r'_y$', '')
        flags_and_mech_columns=flags_and_mech_columns+['account_name_new','product_group_new']
        df.loc[(df['Retailer']==acc_name) & (df['PPG']==ppg),flags_and_mech_columns] = df1[flags_and_mech_columns].values
     
    result = df.values.tolist() if isinstance(_data,list) else df

    return result

def decrypt(encryptedt_data):
    data = b64decode(encryptedt_data)
    bytes = PBKDF2("lazydog".encode("utf-8"), "salt".encode("utf-8"), 48, 128)
    iv = bytes[0:16]
    key = bytes[16:48]
    cipher = AES.new(key, AES.MODE_CBC, iv)
    text = cipher.decrypt(data)
    text = text[:-text[-1]].decode("utf-8")

    return text

def sort_date(df):
    df = df.copy()
    df_1 = df.loc[(df["date"]>"2022-12-31")]
    df_2 = df.loc[(df["date"]<="2022-12-31")]
    df = pd.concat([df_1, df_2], axis=0, ignore_index=True)    
    return df

def get_last_52weeks_data(model_data_all,roi_df):
    model_data_all = model_data_all.sort_values(by=["account_name", "product_group", "date"], ignore_index=True)
    model_data_all["MBP_log_rolling_mean"] = model_data_all.groupby(["account_name", "product_group"], as_index=False)\
                                            ["wk_sold_median_base_price_byppg_log_previous_years"].transform(lambda x: x.rolling(4,1).mean())

    trend_flags_to_zero = []
    # breakpoint()
    for col in trend_flags_to_zero:
        # col = col.lower()
        if col in model_data_all.columns:
            model_data_all[col] = 0
    # model_data_all[trend_flags_to_zero] = 0
    # breakpoint()

    temp_model_data_all = model_data_all.groupby(["account_name", "product_group"], as_index=False).agg(max_date=("date", np.max))
    temp_model_data_all = temp_model_data_all.groupby(["account_name"], as_index=False).agg(max_date=("max_date", np.max))
    model_data_all = model_data_all.merge(temp_model_data_all, on=["account_name"])
    model_data_all['max_date'] = pd.to_datetime(model_data_all['max_date'], format='%Y-%m-%d')
    model_data_all['date'] = pd.to_datetime(model_data_all['date'], format='%Y-%m-%d')

    model_data_all = model_data_all.loc[(model_data_all["date"]<=model_data_all["max_date"])].reset_index(drop=True)
    model_data_all["num_weeks"] = ((model_data_all["max_date"] - model_data_all["date"])/np.timedelta64(1, 'W')).astype(int)
    model_data_all = model_data_all.loc[(model_data_all["num_weeks"]<52)].reset_index(drop=True)
    model_data_all = model_data_all.drop_duplicates(ignore_index=True)
    model_data_all["product_group"] = model_data_all["product_group"].map(lambda x: x.replace(" ", "_"))

    # Change week numbers
    date_df = model_data_all["date"].drop_duplicates().reset_index(drop=True)
    date_df = date_df.to_frame()
    date_df = date_df.sort_values(by=["date"], ignore_index=True)
    date_df_1 = date_df.loc[(date_df["date"]>"2022-12-31")]
    date_df_2 = date_df.loc[(date_df["date"]<="2022-12-31")]
    date_df = pd.concat([date_df_1, date_df_2], axis=0, ignore_index=True)
    date_df["week"] = list(np.arange(1, (date_df.shape[0]+1)))
    date_df['date'] = pd.to_datetime(date_df['date'], format='%Y-%m-%d')        
    
    model_data_all = model_data_all.drop(["week"], axis=1)
    model_data_all = model_data_all.merge(date_df, on=["date"])
    model_data_all = sort_date(model_data_all)
    model_data_all = model_data_all.sort_values(by=["account_name", "product_group", "week"]).reset_index(drop=True)
    
    # ROI Data
    roi_df['date'] = pd.to_datetime(roi_df['date'], format='%Y-%m-%d')
    temp_model_data = model_data_all[["account_name", "product_group", "date"]]
    roi_df["product_group"] = roi_df["product_group"].map(lambda x: x.replace(" ", "_"))
    roi_df['date'] = pd.to_datetime(roi_df['date'])
    roi_df = roi_df.merge(temp_model_data, on=["account_name", "product_group", "date"])
    roi_df = roi_df.drop(["week"], axis=1)
    # date_df['date'] = pd.to_datetime(date_df['date']).dt.tz_localize('UTC')
    roi_df = roi_df.merge(date_df, on=["date"])
    roi_df = sort_date(roi_df)
    roi_df = roi_df.sort_values(by=["account_name", "product_group", "week"]).reset_index(drop=True)
    roi_week_idx = core_const.ROI_VALUES.index('week')
    roi_week_col = roi_df.pop('week')
    roi_df.insert(roi_week_idx, roi_week_col.name, roi_week_col)
    mdl_data_week_idx = core_const.DATA_VALUES.index('week')
    mdl_data_week_col = model_data_all.pop('week')
    model_data_all.pop('num_weeks')
    model_data_all.pop('max_date')
    model_data_all.insert(mdl_data_week_idx, mdl_data_week_col.name, mdl_data_week_col)
    
    mbp_avg_df = (model_data_all.groupby(['account_name', 'product_group']).last()['MBP_log_rolling_mean'].reset_index())
    model_data_all.drop(['MBP_log_rolling_mean'], axis=1, inplace=True)
    model_data_all = model_data_all.merge(mbp_avg_df, on=['account_name', 'product_group'])    

    return model_data_all,roi_df

def get_2021_data(mdl_df,roi_df):
    mdl_df = mdl_df.sort_values(by=["account_name", "product_group", "date"], ignore_index=True)
    mdl_df["MBP_log_rolling_mean"] = mdl_df.groupby(["account_name", "product_group"], as_index=False)\
                                            ["median_base_price_log"].transform(lambda x: x.rolling(4,1).mean())

    trend_flags_to_zero = ['down_promo_flag', 'holiday_Flag1', 'holiday_Flag2', 'holiday_Flag3', 'holiday_Flag4', 
                           'nov_2022_trend', 'nov_trend', 'oct_2022_trend', 'oct_trend', 'p09_2022', 
                           'p10_11_2022', 'p10_2022', 'p10_2022_trend', 'p11_2022', 'p11_2022_trend', 
                           'p9_2022', 'p9_2022_trend', 'promo_flag_1', 'promo_flag_2', 'promo_flag_3', 
                           'promo_flag_date_1', 'promo_flag_date_2', 'promo_flag_date_3', 'promo_flag_date_4', 
                           'promo_flag_date_correction_2020_05_02', 'promo_flag_date_correction_2020_08_29', 
                           'promo_flag_date_correction_2020_10_10', 'promo_flag_date_correction_2021_09_25', 
                           'promo_flag_date_correction_2022_04_23', 'promo_flag_date_correction_2022_05_14', 
                           'promo_flag_date_correction_2022_08_13', 'quarter_trend_2019', 'quarter_trend_2020', 
                           'quarter_trend_2021', 'y_2020', 'year_trend_2020', 'non_promo_flag_date_1', 
                           'non_promo_flag_date_2', 'non_promo_flag_date_3', 'non_promo_flag_date_4']
    for col in trend_flags_to_zero:
        # col = col.lower()
        if col in mdl_df.columns:
            mdl_df[col] = 0        
    # mdl_df[trend_flags_to_zero] = 0    

    mdl_df = mdl_df.loc[mdl_df['year']==2021]
    roi_df = roi_df.loc[roi_df['year']==2021]
    roi_df = roi_df.sort_values(by=["account_name", "product_group", "week"], ignore_index=True)
    mdl_df = mdl_df.sort_values(by=["account_name", "product_group", "week"], ignore_index=True)

    mbp_avg_df = (mdl_df.groupby(['account_name', 'product_group']).last()['MBP_log_rolling_mean'].reset_index())
    mdl_df.drop(['MBP_log_rolling_mean'], axis=1, inplace=True)
    mdl_df = mdl_df.merge(mbp_avg_df, on=['account_name', 'product_group'])    

    return mdl_df,roi_df


def format_headers(h1,h2,df):
    result_dict = dict.fromkeys(h1)
    for key, value in zip(result_dict.keys(), h2):
        result_dict[key] = value
    df = df.rename(columns={**result_dict})
    return df

def to_json_serializable(jdata):
    jdata = json.dumps(jdata,cls=resp_utils.Encoder)
    return jdata

def isNan(num):
    return 0 if num!=num else num
