""" Summary"""
from functools import partial
import decimal
import re
import numpy as np
import pandas as pd
from pulp import (
                  lpSum,
                  LpVariable,
                  LpProblem,
                  LpMaximize,
                  LpStatus
                  )
from apps.promo.promo_optimizer.generic import handle_joint_and_all_brand_ppg, predict_sales, update_params
from apps.promo.promo_optimizer.process import change_datatype,change_datatype2 #pylint: disable=E0401

def recur_dictify(frame:pd.DataFrame)->dict:
    """recur_dictify

    Args:
        frame (pd.DataFrame): df

    Returns:
        dict: dict
    """
    if len(frame.columns) == 1:
        if frame.values.size == 1: 
            return frame.values[0][0]
        return frame.values.squeeze()
    grouped = frame.groupby(frame.columns[0])
    _d = {k: recur_dictify(g.iloc[:,1:]) for k,g in grouped}
    return _d
    
def optimization_fn(param_df: pd.DataFrame,
                    param_df_1: pd.DataFrame,
                    base_mac_agg:int,
                    config: dict,
                    count_dict:dict,
                    count_dict_new:dict,
                    max_promos:int,
                    total_slots:int
                    ) -> dict:
    """optimization_fn

    Args:
        param_df (pd.DataFrame): param_df
        config (dict): config
        count_dict (dict): count_dict
        count_dict_new (dict): count_dict_new
        max_promos (int): max_promos
        total_slots (int): total_slots

    Returns:
        dict: dict
    """
    optimizer_name = "_".join(("OPTIMIZE",config["NAME"]))
    print("optimizer_name : ", optimizer_name)
    prob = LpProblem(name = optimizer_name, sense = LpMaximize)


    # # # Setup Variables
    # 1. Week Variable 
    w_k = LpVariable.dicts("numWeek",((i,j,k) for i in count_dict.keys() 
                                    for j in count_dict[i].keys() 
                                    for k in range(1,count_dict[i][j]+1,1)),
                         lowBound=0, cat = 'Integer')

    mac_value = 0
    if (config["OBJECTIVE"]["MAC"]):
        print("Objective Function : Maximize MAC")
        for ret in count_dict.keys(): # retailer
            all_ppgs = list(count_dict[ret].keys())
            all_unique_ppgs = [x.split("_") for x in all_ppgs]
            all_unique_ppgs = [y for x in all_unique_ppgs for y in x]
            all_unique_ppgs = list(np.unique(all_unique_ppgs))
            joint_ppgs = [x for x in all_ppgs if len(x.split("_")) > 1]            
            if "all" in all_unique_ppgs:
                all_unique_ppgs.remove("all")
                all_unique_ppgs.remove("brand")
            for ppg in all_unique_ppgs:
                lp_sum_ppgs = []
                for joint_ppg_val in joint_ppgs:
                    joint_ppg_val_split = joint_ppg_val.split("_")
                    if ppg in joint_ppg_val_split:
                        lp_sum_ppgs.append(joint_ppg_val)
                if ppg in all_ppgs:
                    lp_sum_ppgs.append(ppg)

                num_promos = 0
                for j in lp_sum_ppgs:
                    # for k in range(2,10):
                    for k in  range(2,count_dict[ret][ppg]+1):
                        num_promos += w_k[(ret,j,k)]
                        mac_value += (param_df_1.loc[(ret,j,k,ppg,1), "NSV_perweek"] - 
                                      param_df_1.loc[(ret,j,k,ppg,1), "COGS_perweek"]) * w_k[(ret,j,k)]
                num_non_promos = 52 - num_promos
                mac_value += (param_df_1.loc[(ret,ppg,1,ppg,0), "NSV_perweek"] - 
                              param_df_1.loc[(ret,ppg,1,ppg,0), "COGS_perweek"]) * num_non_promos
        prob += mac_value

    # # # Setup Constraints
    # 1. Week Constraint
    # Ensure total promo + non-promo weeks = 52 -> for each retailer-ppg
    # Week and Slack week together shoiu;d be less than or equal to 52 for each Retailer x PPG combination
    if (config["CONSTRAINTS"]["WEEK_CONSTRAINT"]):
        print("Setup - Week Constraint")
        for i in count_dict.keys(): ## retailer
            for j in count_dict[i].keys(): ## ppg
                prob += lpSum([w_k[(i,j,k)] for k in range(1,count_dict[i][j]+1,1)]) <= 52 
                prob += lpSum([w_k[(i,j,k)] for k in range(1,count_dict[i][j]+1,1)]) >= 52
    # prob += mac_value >= np.float64(base_mac_agg)
    # 2. Total Slots Constraint
    # Ensure total promo weeks is within the total_slots limit
    # Total Slots must be well within the overall limit
     
    if (config["CONSTRAINTS"]["OVERALL_BUDGET_CONSTRAINT"]):
        print("Setup - Overall Slots constraint")
        prob += lpSum([param_df.loc[(i,j,k), "num_ppgs"] * w_k[(i,j,k)] 
                       for i in count_dict.keys() # retailer
                       for j in count_dict[i].keys() # ppg
                       for k in range(1, count_dict[i][j]+1, 1) # promo_type
                       if (param_df.loc[(i,j,k), "is_promo"])]) <= total_slots

    prob += mac_value >= base_mac_agg
    # 100% utilization
    if (config["CONSTRAINTS"]["ALL_SLOT_UTILIZATION_CONSTRAINT"]):
        prob += lpSum([param_df.loc[(i,j,k), "num_ppgs"] * w_k[(i,j,k)] 
                       for i in count_dict.keys() # retailer
                       for j in count_dict[i].keys() # ppg
                       for k in range(1, count_dict[i][j]+1, 1) # promo_type
                       if (param_df.loc[(i,j,k), "is_promo"])]) >= total_slots
    
        prob += lpSum([param_df.loc[(i,j,k), "num_ppgs"] * w_k[(i,j,k)] 
                       for i in count_dict.keys() # retailer
                       for j in count_dict[i].keys() # ppg
                       for k in range(1, count_dict[i][j]+1, 1) # promo_type
                       if (param_df.loc[(i,j,k), "is_promo"])]) <= total_slots
        
    ###3. Week Allocation Constraint
    if(config["CONSTRAINTS"]["WEEK_ALLOCATION_CONSTRAINT"]):
        print("Setup - Week Allocation Constraint")
        # Take each ppg's max_week value as limit       
        # Maximum Week Allocation - Individual PPGs
        is_joint_ppg = False
        for i in count_dict_new.keys(): # retailer
            for k in count_dict_new[i].keys(): # promo_type
                count_dict_new_str = [x for x in count_dict_new[i][k]] # count_dict_new[i][k] -> all ppgs
                joint_ppgs_str = [x for x in count_dict_new_str if len(x.split("_")) == 1] # individual_ppgs
                joint_ppgs_str_temp = [x for x in count_dict_new_str if len(x.split("_")) > 1] # joint_ppgs
                
                for j in joint_ppgs_str: # individual_ppgs
                    lpsum_vars = []

                    for joint_ppgs_str_temp_val in joint_ppgs_str_temp: # joint_ppgs
                        joint_ppgs_str_temp_val = joint_ppgs_str_temp_val.split("_")
                        if j in joint_ppgs_str_temp_val: # if individual ppg part of joint_ppgs
                            joint_ppgs_str_temp_val = "_".join(joint_ppgs_str_temp_val)
                            lpsum_vars.append(joint_ppgs_str_temp_val)                            
                            is_joint_ppg = True

                    lpsum_vars.append(j)
                    ref_ppg = lpsum_vars[-1]                    
                    if is_joint_ppg:
                        max_val = 0
                        for j in lpsum_vars:
                            max_val += param_df.loc[(i,j,k), "maxWeek"]                       
                        prob += lpSum([w_k[(i,j,k)] for j in lpsum_vars]) <= max_val                                              
                        is_joint_ppg = False
                    else:
                        prob += lpSum([w_k[(i,j,k)] for j in lpsum_vars]) <= param_df\
                                .loc[(i, ref_ppg, k), "maxWeek"]
                        
        is_joint_ppg = False
        # Minimum Week Allocation - Individual PPGs
        for i in count_dict_new.keys(): # retailer
            for k in count_dict_new[i].keys(): # promo_type                
                count_dict_new_str = [x for x in count_dict_new[i][k]] # count_dict_new[i][k] -> all ppgs
                num_joint_ppgs = [1 for x in count_dict_new_str if len(x.split("_"))>1] # num of joint_ppgs
                joint_ppgs_str = [x for x in count_dict_new_str if len(x.split("_"))==1] # individual_ppgs
                joint_ppgs_str_temp = [x for x in count_dict_new_str if len(x.split("_"))>1] # joint_ppgs
                
                for j in joint_ppgs_str: # individual_ppgs
                    # if k == 1:
                    lpsum_vars = []

                    for joint_ppgs_str_temp_val in joint_ppgs_str_temp: # joint_ppgs
                        joint_ppgs_str_temp_val = joint_ppgs_str_temp_val.split("_")                            
                        if j in joint_ppgs_str_temp_val:
                            joint_ppgs_str_temp_val = "_".join(joint_ppgs_str_temp_val)
                            lpsum_vars.append(joint_ppgs_str_temp_val)
                            is_joint_ppg = True

                    lpsum_vars.append(j)                        
                    ref_ppg = lpsum_vars[-1]
                    if is_joint_ppg:
                        min_val = 0
                        for j in lpsum_vars:
                            min_val += param_df.loc[(i,j,k), "minWeek"]                           
                        prob += lpSum([w_k[(i,j,k)] for j in lpsum_vars]) >= min_val                           
                        is_joint_ppg = False
                    else:
                        prob += lpSum([w_k[(i,j,k)] for j in lpsum_vars]) >= param_df\
                                .loc[(i, ref_ppg, k), "minWeek"]                            
                    # else:
                    #     prob +=  w_k[(i,j,k)] >= 0

        # Maximum Week Allocation - Joint PPGs
        for i in count_dict.keys(): # retailer
            for j in count_dict[i].keys(): # ppg
                for k in range(1, count_dict[i][j]+1): # promo_type = 1 to 11                    
                    prob += w_k[(i,j,k)] <= param_df.loc[(i,j,k), 'maxWeek']

        # Minimum Week Allocation - Joint PPGs
        for i in count_dict.keys(): # retailer
            for j in count_dict[i].keys(): # ppg
                for k in range(1, count_dict[i][j]+1): # promo_type = 1,2
                    if k == 1:
                        prob +=  w_k[(i,j,k)] >= param_df.loc[(i,j,k), 'minWeek']
                    else:
                        prob +=  w_k[(i,j,k)] >=0
                                
        bonus_promo_val = 4
        coupon_promo_val = 5
        multibuy_promo_val = 7
        bonus_coupon_promo_val = 10
        multibuy_coupon_promo_val = 11
        # num_bonus_weeks + num_bonus_coupon_weeks <= bonus_max_week
        for i in count_dict.keys(): # retailer
            for j in count_dict[i].keys(): # ppg
                if count_dict[i][j] >= bonus_coupon_promo_val:
                    prob += w_k[(i,j,bonus_promo_val)] + w_k[(i,j,bonus_coupon_promo_val)] \
                            <= param_df.loc[(i,j,bonus_promo_val), "maxWeek"]
                    
        # num_coupon_weeks + num_bonus_coupon_weeks <= coupon_max_week
        for i in count_dict.keys(): # retailer
            for j in count_dict[i].keys(): # ppg
                if count_dict[i][j] >= bonus_coupon_promo_val:
                    prob += w_k[(i,j,coupon_promo_val)] + w_k[(i,j,bonus_coupon_promo_val)] \
                            <= param_df.loc[(i,j,coupon_promo_val), "maxWeek"]
        
        # num_multibuy_weeks + num_multibuy_coupon_weeks <= multibuy_max_week
        for i in count_dict.keys(): # retailer
            for j in count_dict[i].keys(): # ppg
                if count_dict[i][j] >= multibuy_coupon_promo_val:
                    prob += w_k[(i,j,multibuy_promo_val)] + w_k[(i,j,multibuy_coupon_promo_val)] \
                            <= param_df.loc[(i,j,multibuy_promo_val), "maxWeek"]
                    
        # num_coupon_weeks + num_multibuy_coupon_weeks <= coupon_max_week
        for i in count_dict.keys(): # retailer
            for j in count_dict[i].keys(): # ppg
                if count_dict[i][j] >= multibuy_coupon_promo_val:
                    prob += w_k[(i,j,coupon_promo_val)] + w_k[(i,j,multibuy_coupon_promo_val)] \
                            <= param_df.loc[(i,j,coupon_promo_val), "maxWeek"]                    

        # max_promos constraint
        # For each retailer, for each ppg, get list of associated joint_ppgs
        # Make a list of individual and their associated joint ppgs
        # For each ppg in the list, for all the promo_loop, add up the decision variables
        for ret in count_dict.keys(): # retailer
            all_ppgs = list(count_dict[ret].keys())
            all_unique_ppgs = [x.split("_") for x in all_ppgs]
            all_unique_ppgs = [y for x in all_unique_ppgs for y in x]
            all_unique_ppgs = list(np.unique(all_unique_ppgs))
            if "all" in all_unique_ppgs:
                all_unique_ppgs.remove("all")
                all_unique_ppgs.remove("brand")
#             for ppg in count_dict[ret].keys(): # ppg
            for ppg in all_unique_ppgs:
#                 all_ppgs = list(count_dict[ret].keys())
                joint_ppgs = [x for x in all_ppgs if len(x.split("_")) > 1]
                
                lp_sum_ppgs = []
                for joint_ppg_val in joint_ppgs:
                    joint_ppg_val_split = joint_ppg_val.split("_")
                    if ppg in joint_ppg_val_split:
                        lp_sum_ppgs.append(joint_ppg_val)
                if ppg in all_ppgs:
                    lp_sum_ppgs.append(ppg)
                prob += lpSum([w_k[(ret,j,k)] for j in lp_sum_ppgs for k in range(2,count_dict[ret][ppg]+1)]) <= max_promos
    # Solve and Print Values

    prob.solve()
    objective_value = prob.objective.value()
    print("Status : ",LpStatus[prob.status])
    print(max_promos,"max promo")
    print("Objective Function Value : ", objective_value)

    # Return Dictionary Data
    variables_dict = {}
    for v in prob.variables():
        variables_dict[v.name] = v.varValue

    return variables_dict,LpStatus[prob.status]

def summary_fn(variables_df : pd.DataFrame, 
              input_df : pd.DataFrame) -> pd.DataFrame:
    """summary_fn

    Args:
        variables_df (pd.DataFrame): variables_df
        input_df (pd.DataFrame): input_df

    Returns:
        pd.DataFrame: _description_
    """
    variables_df_2 = variables_df
    variables_df_2["Name"] = variables_df_2["Name"].astype('str')
    variables_df_2 = variables_df_2.loc[(~variables_df_2["Name"].str.contains("slack")),:]
    variables_df_2["Combo"] = variables_df_2["Name"]
    variables_df_2["Combo"] = variables_df_2["Combo"].apply(lambda x: (re.findall(r'\(.*?\)',x))[0])
    variables_df_2["Combo"] = variables_df_2["Combo"].apply(lambda x: x.replace("(","")\
                            .replace(")",""))
    variables_df_2[["Retailer","PPG","Promo_Type"]] = variables_df_2["Combo"]\
                                                        .str.split(",", expand = True)
    variables_df_2["Retailer"] = variables_df_2["Retailer"].apply(lambda x: x.replace("_",""))
    variables_df_2["Promo_Type"] = variables_df_2["Promo_Type"].apply(lambda x: x.replace("_",""))
    variables_df_2["PPG"] = variables_df_2["PPG"].apply(lambda x: x[1:].replace("\'", ""))
    variables_df_2[["Retailer","Promo_Type"]] = variables_df_2\
                            [["Retailer","Promo_Type"]].apply(pd.to_numeric)
    variables_df_2 = variables_df_2.drop(["Name","Combo"], axis = "columns")\
                    .drop_duplicates()
    variables_df_2 = variables_df_2.rename(columns = {"Weeks" : "Optimized_Weeks"})

    input_df_2 = input_df
    input_df_2 = pd.merge(left = input_df_2, right = variables_df_2\
    , how = "left", on = ["Retailer", "PPG", "Promo_Type"])
    # input_df_2["Optimized_TE_perweek"] = input_df_2["TE_perweek"] \
    #                                 * input_df_2["Optimized_Weeks"]
    # input_df_2['OptWeek_Check'] = input_df_2.apply(lambda row: row['minWeek']\
    #                                 <=row['Optimized_Weeks']<=row['maxWeek'], axis=1)

    # # MAC Calculation
    # input_df_2['MAC_Opt'] = (input_df_2['GSV_perweek']  - input_df_2['COGS_perweek']  \
    #                         - input_df_2['TE_perweek']) * input_df_2['Optimized_Weeks']
    # input_df_2['MAC_Min'] = (input_df_2['GSV_perweek']  - input_df_2['COGS_perweek']  \
    #                         - input_df_2['TE_perweek']) * input_df_2['minWeek']
    # input_df_2['MAC_Max'] = (input_df_2['GSV_perweek']  - input_df_2['COGS_perweek']  \
    #                         - input_df_2['TE_perweek']) * input_df_2['maxWeek']
    # input_df_2['OptMac_Check'] = input_df_2.apply(lambda row: row['MAC_Min']<=row['MAC_Opt']\
    #                             <=row['MAC_Max'], axis=1)

    return input_df_2
    
def get_optimized_summary(**constraints)->dict:
    """get_optimized_summary

    Returns:
        dict: dict
    """
    model_data_all = constraints.get('mdl_df').copy()
    model_coeff = constraints.get('model_coeff_df').copy()
    coeff_mapping = constraints.get('coeff_mapping_df').copy()
    roi_data=constraints.get('roi_df')
    _df = constraints.get('tactic_df')
    param_df_1 = constraints.get('_rpm_weekly_df')
    retailer_id = _df.loc[0,'Retailer_Index']
    retailer = _df.loc[0,'Retailer']
    reatiler_ppgs_types = constraints.get('reatiler_ppgs_types')
    # breakpoint()
 
    _df.rename(columns={'PPG':'PPG_Index',
                        'Retailer':'Retailer_Index',
                        'Retailer_Index':'Retailer',
                        'PPG_Index':'PPG'},inplace=True)
    total_slots = constraints.get('total_slots')
    
    print(f"Total number of slots from historical data: {total_slots}")
    exclude_ppgs_list = [x for x in _df["PPG"].unique() if ("all_brand" in x) and (len(x.split("_")) == 3)]
    
    print(f"Total number of slots from historical data: {total_slots}")
   
    # one_shot_ppg_df = _df.loc[(_df["PPG_Type"]=="One Shot")]
    # breakpoint()
    non_optimized_one_shot_ppg = list(filter(lambda x:x[3]=='One Shot',reatiler_ppgs_types))
    one_shot_ppgs_ids = list(map(lambda x:x[5],non_optimized_one_shot_ppg))
    one_shot_ppgs = list(map(lambda x:x[1],non_optimized_one_shot_ppg))
    exclude_ppgs_list+=one_shot_ppgs_ids
  
    # one_shot_ppgs = []
    # if one_shot_ppg_df.shape[0]:
    #     one_shot_ppg_ids = list(one_shot_ppg_df["PPG"].unique())
    #     one_shot_ppgs = list(one_shot_ppg_df["PPG_Index"].unique())
    #     print(f"One Shot PPGs: {one_shot_ppg_ids}")

    #     exclude_ppgs_list += one_shot_ppg_ids
    #     print(f"exclude_ppg_list: {exclude_ppgs_list}")

    max_promos = 12
    _df = _df.loc[(~_df["PPG"].isin(exclude_ppgs_list))].reset_index(drop=True)
    _df["is_promo"] = False
  
    for col in _df.columns:
        if ("_flag" in col) or ("TPR" == col):
            _df["is_promo"] |= (_df[col] > 0)

    _df["num_ppgs"] = _df["PPG"].map(lambda x: len(x.strip("_all_brand").split("_")))
    
    _df["ppg"] = _df["PPG"]
    _df = change_datatype(_df)
    param_df_1 = change_datatype2(param_df_1)

    exclude_joint_list = []
    # temp_units_agg_df.loc[(temp_units_agg_df['Type_of_Promo']=='joint')]
    temp_units_agg_df = param_df_1.copy()
    temp_units_agg_df = temp_units_agg_df.loc[(temp_units_agg_df["Retailer_Index"]==retailer_id)].reset_index(drop=True)    
    temp_units_agg_df["num_ppgs"] = temp_units_agg_df["PPG_Index"].map(lambda x: len(x.strip("-all_brand").split("_")))
    temp_units_agg_df = temp_units_agg_df.loc[(temp_units_agg_df["num_ppgs"]>1)].reset_index(drop=True)
    u_ppgs = temp_units_agg_df["PPG_Index"].unique()
    num_promos = 11
    for u_ppg in u_ppgs:
        temp_u = temp_units_agg_df.loc[(temp_units_agg_df["PPG_Index"]==u_ppg)]
        n_ppgs = temp_u["num_ppgs"].values[0]

        joint_combo_shape = temp_u.shape[0]
        if joint_combo_shape != (11 * n_ppgs):
            print(u_ppg)
            exclude_joint_list.append(u_ppg)
    _df = _df.loc[(~_df["PPG"].isin(exclude_joint_list))].reset_index(drop=True)
    print(f"Excluded joint combinations: {exclude_joint_list}")

    temp_units_agg_df = param_df_1.groupby(["Retailer", "PPG", "PPG_Index"], as_index=False).agg(size=("Promo_Type", "count"))
    temp_units_agg_df = temp_units_agg_df.loc[(temp_units_agg_df["Retailer"]==retailer)].reset_index(drop=True)
    exclude_list = list(temp_units_agg_df.loc[(temp_units_agg_df["size"]<11), "PPG_Index"].unique())
    _df = _df.loc[(~_df["PPG"].isin(exclude_list))].reset_index(drop=True)
    print(f"Additional exclusion: {exclude_list}")
    # Dictionary Created to store the Retailer x PPG x Promo Type Combinations
    count_dict = {}
    _df1 = _df.groupby(by = ["Retailer","PPG"], as_index = False)\
            .agg({"Promo_Type" : "nunique"})
    count_dict = recur_dictify(_df1.copy())
    del _df1

    count_dict_new = _df.groupby(by=["Retailer", "Promo_Type"], as_index=False)\
                    .agg({"PPG": "unique"})
    count_dict_new = recur_dictify(count_dict_new.copy())

    config_default_3 = {
        "Retailer":'Default',"PPG":'Default','Segment':'Default','TE_threshold':1,
        'Objective_metric': "MAC", "Objective":"Maximize",
        'config_constrain':{
            'MAC': False,'RP': False,'Trade_Expense': False,'Units': False, "NSV": False\
            , "GSV": False,
            "Sales":False,'MAC_Perc':False,
            "RP_Perc":False,'min_consecutive_promo':True,'max_consecutive_promo':True,
            'promo_gap':True,'tot_promo_min':False,'tot_promo_max':False,
            'promo_price':False,'automation':False,'52Weeks':True
        },
        'constrain_params': {
            'MAC':1,'RP':1,'Trade_Expense':1,'Units':1,'NSV':1,
            'GSV':1,'Sales':1,'MAC_Perc':1,'RP_Perc':1,
            'min_consecutive_promo':1, 'max_consecutive_promo':1,
            'promo_gap':2, 'tot_promo_min':2, 'tot_promo_max':20, 'compul_no_promo_weeks':[],
            'compul_promo_weeks' :[],'promo_price':0
        }
    }

    _df_copy = _df.copy()
    _df = _df.sort_values(by='Promo_Type')
    grouped_df = _df.groupby(["Retailer",'PPG'])
    
    for key,_item in grouped_df:
        a_group = grouped_df.get_group(key).reset_index(drop=True)
        filtered_input_data = list(filter(partial(handle_joint_and_all_brand_ppg\
                                            ,acc_name=a_group['Retailer_Index'].values[0]\
                                            ,ppg=a_group['PPG_Index'].values[0],
                                            top=a_group['Type_of_Promo'].values[0])\
                        ,constraints.get('input_data')))
        
        if filtered_input_data:
            config_default_3 = update_params(config_default_3,
                                                filtered_input_data[0],
                                                constraints['total_slots'],
                                                constraints['min_length_gap'],
                                                constraints['no_of_promo'],
                                                )
                
            _df.loc[(_df['Retailer']==a_group['Retailer'].values[0]) & (_df['PPG']==a_group['PPG'].values[0]) \
                    & (_df['Promo_Type']==2),'maxWeek'] = config_default_3['constrain_params']['tot_promo_max']
            
            _df.loc[(_df['Retailer']==a_group['Retailer'].values[0]) & (_df['PPG']==a_group['PPG'].values[0]) \
                    & (_df['Promo_Type']==2),'minWeek'] = config_default_3['constrain_params']['tot_promo_min']

            _df.loc[(_df['Retailer']==a_group['Retailer'].values[0]) & (_df['PPG']==a_group['PPG'].values[0]) \
                    & (_df['Promo_Type']==1),'maxWeek'] = 52-config_default_3['constrain_params']['tot_promo_min']
            
            _df.loc[(_df['Retailer']==a_group['Retailer'].values[0]) & (_df['PPG']==a_group['PPG'].values[0]) \
                    & (_df['Promo_Type']==1),'minWeek'] = 52-config_default_3['constrain_params']['tot_promo_max']
        # else:
        #     _df = _df.loc[_df['PPG']!=key[1]]
    _df = _df[['Retailer', 'PPG', 'Promo_Type', 'minWeek', 'maxWeek']]
    _df_copy.drop(['minWeek', 'maxWeek'], axis=1, inplace=True)    
    _df_copy = _df_copy.merge(_df, on=['Retailer', 'PPG', 'Promo_Type'])    
    _df = _df_copy.copy()    
    param_df = _df.copy()
    param_df = param_df.set_index(['Retailer','PPG','Promo_Type'])
    param_df_1.drop(["Retailer", "PPG"], axis=1, inplace=True)
    param_df_1 = param_df_1.rename(columns={"Retailer_Index": "Retailer", "PPG_Index": "PPG"})
    param_df_1["PPG"] = param_df_1["PPG"].astype(str)

    param_df_1["ref_ppg_idx"] = param_df_1["ref_ppg_idx"].astype(str)
    param_df_1 = param_df_1.loc[(param_df_1["Retailer"]==retailer_id)].reset_index(drop=True)
    param_df_1 = param_df_1.set_index(['Retailer','PPG','Promo_Type', 'ref_ppg_idx', 'type_promo_id'])
    config = {
    
    "NAME" : "MAC",
    
    "OBJECTIVE" : {   
        "MAC" : True,
    },
    
    "CONSTRAINTS" : {
        "WEEK_CONSTRAINT" : True,
        "OVERALL_BUDGET_CONSTRAINT" : True,
        "WEEK_ALLOCATION_CONSTRAINT" : True,
        "TE_ALLOCATION_CONSTRAINT" : True,
        "NEW_TE_CONSTRAINT":True,
        "ALL_SLOT_UTILIZATION_CONSTRAINT":False
    },
    
    "SAVE_LP_FILE" : False,
    }
    config['CONSTRAINTS']['ALL_SLOT_UTILIZATION_CONSTRAINT'] = constraints.get('all_slot_utilization')
    stg1_ppg_df = _df[["Retailer_Index", "PPG_Index"]].drop_duplicates(ignore_index=True)
    stg1_ppg_df = stg1_ppg_df.rename(columns={"Retailer_Index": "Retailer", "PPG_Index": "PPG"})
    # stg1_ppg_df = stg1_ppg_df.merge(ret_ppg_id, on=["Retailer_Index", "PPG_Index"])
    stg_1_ppgs = stg1_ppg_df["PPG"].unique()
    stg_1_ppgs = [x.split("_-_") for x in stg_1_ppgs]
    stg_1_ppgs = [y for x in stg_1_ppgs for y in x]
    stg_1_ppgs = [x.strip("-all_brand") for x in stg_1_ppgs]
    stg_1_ppgs = list(np.unique(stg_1_ppgs))
    stg_1_ppgs = [x for x in stg_1_ppgs if x not in one_shot_ppgs]

    model_data_all["TPR"] = np.where(((model_data_all["promo_present"]==0) & 
                                  (model_data_all["TPR"]>0)), 0, model_data_all["TPR"])
    median_acv_df = model_data_all.groupby(["Retailer", "PPG"], as_index=False).agg(ACV_median=("acv", np.median))
    final_pred_data_all = pd.DataFrame()
    baseline_data_all = pd.DataFrame()
    model_coeff_all = pd.DataFrame()
 
    for i, ppg_name in enumerate(stg_1_ppgs):
        no_leaflet_flag = False
        slct_retailer = retailer
        slct_ppg = ppg_name
        slct_segment = "Pet"
        print(slct_retailer, slct_ppg)
        
        coeff_mapping_temp = coeff_mapping.loc[(coeff_mapping['Retailer']==slct_retailer) & 
                                                (coeff_mapping['PPG']==slct_ppg.replace(' ','_').replace('&','_').replace(',','_'))]
        col_dict = dict(zip(coeff_mapping_temp['Coefficient_new'], coeff_mapping_temp['Coefficient']))
        col_dict_2 = dict(zip(coeff_mapping_temp['Coefficient'], coeff_mapping_temp['Coefficient_new']))
        if "Intercept" in col_dict_2.keys():
            col_dict_2.pop('Intercept')
        if "Intercept" in col_dict.keys():
            col_dict.pop('Intercept')
        
        # getting idvs present for the retailer, ppg
        # idvs = coeff_mapping_temp['Coefficient_new'].to_list()
        idvs = [x.lower() for x in coeff_mapping_temp['Coefficient_new'].tolist()]
        if "intercept" in idvs:
            idvs.remove('intercept')
        if "tpr_discount_byppg" in idvs:
            index = idvs.index('tpr_discount_byppg')
            idvs[index] = 'TPR'
        # getting model data for retailer, ppg and renaming columns
        model_data1 = model_data_all.loc[(model_data_all['Retailer']==slct_retailer) & 
                                        (model_data_all['PPG']==slct_ppg.replace(' ','_').replace('&','_').replace(',','_'))].reset_index(drop=True)
        ### Replacing Baseline TPRs with AVG tpr value
        num_promo_weeks = model_data1[model_data1['promo_present']!=0].shape[0]
        is_leaflet_present = len([1 for x in coeff_mapping_temp['Coefficient_new'].unique() if 'promo_present' in x])

        if (num_promo_weeks == 0) or (is_leaflet_present == 0):
            avg_tpr_replace = 0
            print(f"Number of promo weeks is {num_promo_weeks}")
            if "promo_present" not in coeff_mapping_temp["Coefficient_new"].to_list():
                print(f"No Leaflets present")
                print()
                no_leaflet_flag = True
        else:
            avg_tpr_replace = round(model_data1[model_data1['promo_present']!=0]['TPR'].mean()) ## Leaflet promo change
        print("Averaged out tpr for this ret * ppg is", avg_tpr_replace)
        model_data1['TPR'] = np.where(model_data1['promo_present']>0, avg_tpr_replace, model_data1['TPR']) ## Leaflet promo change
        # Replace ACV_Selling with median ACV_Selling of base year
        model_data1 = model_data1.merge(median_acv_df, on=["Retailer", "PPG"])
        model_data1["ACV_Selling"] = model_data1["ACV_median"]

        # Replace Median_Base_Price_log with 4 weeks rolling mean
        # model_data1["Median_Base_Price_log"] = model_data1["MBP_log_rolling_mean"]

        # model_data1["TPR_10_above"] = np.where(model_data1["promo_present"]>0, model_data1["TPR"], 0)
        # model_data1["TPR_5_10"] = 0
        # model_data1["tpr_discount_byppg_2019"] = 0
        
        model_data1 = model_data1[['Date','Retailer'] + idvs] 
        # getting model coefficients values with original names and format 
        model_coeff = coeff_mapping_temp[['Coefficient','Value','PPG','Retailer','Coefficient_new']]
        model_coeff.rename(columns={'Value':'model_coefficients',
                                    'Coefficient_new':'names'},inplace=True)
        
        flag_vars = model_coeff.loc[model_coeff['names'].str.contains('promo_') |
                                    model_coeff['names'].str.contains('Covid')]['names'].to_list() ## change added 0902(prev only promo)
        print(model_data1.shape)
        promo_list_ppg = roi_data[(roi_data['Retailer'] == slct_retailer) & 
                                (roi_data['PPG'] == slct_ppg.replace(' ','_').replace('&','_').replace(',','_'))].reset_index(drop=True)
        print(promo_list_ppg.shape)
        period_data = promo_list_ppg[['Date','GSV_Per_Unit_Future',
                                    'NSV_Per_Unit_Future','COGS_Per_Unit_Future']] ## change added 0902

        model_coeff_list_Keep = list(model_coeff['names'])
        if "Intercept" in model_coeff_list_Keep:
            model_coeff_list_Keep.remove('Intercept')
            
        period_data.loc[:, 'Date'] = pd.to_datetime(period_data['Date'], format='%Y-%m-%d')
        model_data1.loc[:, 'Date'] = pd.to_datetime(model_data1['Date'], format='%Y-%m-%d')
        final_pred_data = pd.merge(period_data, model_data1, how="left", on="Date")
        final_pred_data['wk_base_price_perunit_byppg'] = np.exp(final_pred_data['wk_sold_median_base_price_byppg_log']) - 1 ## changed in Germany Pet TPO
    
        final_pred_data['Promo'] = np.where(final_pred_data['TPR'] == 0, final_pred_data['wk_base_price_perunit_byppg'],
                                            final_pred_data['wk_base_price_perunit_byppg'] * (1-(final_pred_data['TPR']/100))) ## change added 0902

        final_pred_data['wk_sold_avg_price_byppg'] = final_pred_data['Promo'] ## needed change?

        if 'TPR_lag1' in model_coeff_list_Keep:
            final_pred_data['TPR_lag1']= final_pred_data['TPR'].shift(1).fillna(0) ## change added 0902
        if 'TPR_lag2' in model_coeff_list_Keep:
            final_pred_data['TPR_lag2']= final_pred_data['TPR'].shift(2).fillna(0) ## change added 0902

        final_pred_data['Baseline_Prediction'] = predict_sales(model_coeff,final_pred_data)
        final_pred_data['Baseline_Sales'] = final_pred_data['Baseline_Prediction'] * final_pred_data['Promo']
        final_pred_data["Baseline_GSV"] = final_pred_data['Baseline_Prediction'] * final_pred_data['GSV_Per_Unit_Future']

        final_pred_data["Baseline_NSV"] = final_pred_data['Baseline_Prediction'] * final_pred_data['NSV_Per_Unit_Future']
        ## Only in promo weeks
        final_pred_data["Baseline_Trade_Expense"] = final_pred_data["Baseline_GSV"] - final_pred_data["Baseline_NSV"]

        if no_leaflet_flag:
            final_pred_data["Baseline_Trade_Expense_onPromo"] = 0
        else:
            final_pred_data["Baseline_Trade_Expense_onPromo"] = np.where(final_pred_data['promo_present']>0, 
                                                                        final_pred_data["Baseline_GSV"] - final_pred_data["Baseline_NSV"],
                                                                        0) ## Leaflet promo change

        final_pred_data["Baseline_MAC"] = (final_pred_data["Baseline_NSV"] - 
                                        (final_pred_data['Baseline_Prediction'] * final_pred_data['COGS_Per_Unit_Future']))
        final_pred_data["Baseline_RP"] = final_pred_data['Baseline_Sales']-final_pred_data["Baseline_NSV"]

        print(final_pred_data[['Baseline_Prediction','Baseline_Sales',"Baseline_GSV", 
                            "Baseline_Trade_Expense","Baseline_Trade_Expense_onPromo",
                            "Baseline_NSV","Baseline_MAC","Baseline_RP"]].sum().apply(lambda x: '%.3f' % x))

        final_pred_data['Retailer'] = slct_retailer
        final_pred_data['PPG'] = slct_ppg
        final_pred_data['Segment'] = slct_segment

        baseline_data = final_pred_data.copy()

        baseline_data_all = baseline_data_all.append(baseline_data)
        final_pred_data_all = final_pred_data_all.append(final_pred_data)
        model_coeff_all = model_coeff_all.append(model_coeff).reset_index(drop=True) # Change 18th March 2023

    num_base_ppgs = baseline_data_all["PPG"].unique().shape[0]
    baseline_data = baseline_data_all.loc[(baseline_data_all["PPG"].isin(stg_1_ppgs))].reset_index(drop=True)
    base_mac_actual = (baseline_data["Baseline_MAC"].sum())

    roi_data['one_shpt_ppg'] = np.where(roi_data["PPG"].isin(one_shot_ppgs),True,False)
    roi_data["List_Price"] = np.where(roi_data['one_shpt_ppg']==False,roi_data["GSV_Per_Unit_Future"],roi_data["List Price"])
    roi_data["COGS"] = np.where(roi_data['one_shpt_ppg']==False,roi_data["COGS_Per_Unit_Future"],roi_data["COGS"])
    roi_data["NSV"] = np.where(roi_data['one_shpt_ppg']==False,roi_data["NSV_Per_Unit_Future"],roi_data["NSV"])
    roi_data["TE"] = np.where(roi_data['one_shpt_ppg']==False,(roi_data["GSV_Per_Unit_Future"] - roi_data["NSV_Per_Unit_Future"]),0)
    roi_data.pop('one_shpt_ppg')
    # PRomo Data Creation
    promo_data = pd.DataFrame()
    promo_data = model_data_all.loc[:, [
        "Retailer",
        "PPG",
        "Date",
        "TPR",
        "promo_present",
    ]].drop_duplicates()

    roi_data.loc[:, "Date"] = pd.to_datetime(roi_data["Date"])
    promo_data.loc[:, "Date"] = pd.to_datetime(promo_data["Date"])

    roi_data = pd.merge(left=roi_data,
                        right=promo_data,
                        on=["Retailer", "PPG", "Date"],
                        how="inner")
    roi_data = roi_data.rename(columns={"TPR": "Promo_Depth"})

    listprice_cogs_agg_data = (roi_data.groupby(by=["Retailer", "PPG", "promo_present"], 
                                                as_index=False).agg(ListPrice_perweek=("List_Price", np.mean),
                                                                    NSV_perweek=("NSV", np.mean),
                                                                    COGS_perweek=("COGS", np.mean)))
    te_agg_data = (roi_data.groupby(by=["Retailer","PPG", "promo_present"], 
                                    as_index=False).agg(TE_perweek=("TE", np.mean)))

    list_price_te_data = listprice_cogs_agg_data.merge(te_agg_data, on=["Retailer", "PPG", "promo_present"])
    list_price_te_data.head()
    joint_cols = [x for x in baseline_data_all.columns if "Flag_promotype_joint" in x]
    baseline_data_all["is_joint"] = False
    for col in joint_cols:
        baseline_data_all["is_joint"] |= baseline_data_all[col]
    baseline_data_all["is_joint"] = baseline_data_all["is_joint"].astype(int)
    # baseline_data_all["Tactic_Medium_HZ"] = roi_data["promo_present"]
    try:
        baseline_data_all["Tactic_All_Brand"] = baseline_data_all["Flag_promotype_all_brand"]
        baseline_data_all["Tactic_All_Brand"] = np.where((baseline_data_all["Tactic_All_Brand"]==1) & 
                                                        (baseline_data_all["Tactic_Medium_HZ"]==1), 1, 0)
    except:
        baseline_data_all["Tactic_All_Brand"] = 0

    base_avg_units = baseline_data_all.groupby(by=["Retailer", "PPG", "is_joint", "Tactic_All_Brand"], 
                                            as_index=False).agg(num_weeks=("PPG", "count"),
                                                                Avg_units_per_Week=("Baseline_Prediction", np.mean))

    base_avg_units = base_avg_units.merge(list_price_te_data, on=["Retailer", "PPG"])
    # base_avg_units = base_avg_units.merge(list_price_te_data, on=["Retailer", "PPG"])

    base_avg_units["GSV_perweek"] = base_avg_units["Avg_units_per_Week"] * base_avg_units["ListPrice_perweek"]
    base_avg_units['COGS_perweek'] = base_avg_units['COGS_perweek'] * base_avg_units["Avg_units_per_Week"]
    base_avg_units['NSV_perweek'] = base_avg_units['NSV_perweek'] * base_avg_units["Avg_units_per_Week"]
    base_avg_units['TE_perweek'] = base_avg_units['TE_perweek'] * base_avg_units["Avg_units_per_Week"]

    # base_avg_units["MAC_perweek"] = base_avg_units["GSV_perweek"] - base_avg_units["TE_perweek"] - base_avg_units["COGS_perweek"]
    base_avg_units["MAC_perweek"] = base_avg_units["NSV_perweek"] - base_avg_units["COGS_perweek"]
    base_avg_units["MAC"] = base_avg_units["MAC_perweek"] * base_avg_units["num_weeks"]
    base_mac_agg = base_avg_units["MAC"].sum()
    
    variables_dict,status = optimization_fn(param_df.copy(),
                                    param_df_1.copy(),
                                    base_mac_agg,
                                    config,
                                    count_dict,
                                    count_dict_new,
                                    constraints.get('no_of_promo'),
                                    total_slots
                                )
    variables_data = pd.DataFrame(list(variables_dict.items()), columns = ["Name","Weeks"])
    
    summary_df = summary_fn(variables_df=variables_data.copy(), input_df=_df.copy())
    
    
    ### Adding One shot ppg
    first_row = summary_df.iloc[:1].copy()
    
    for one_shot_ppgs in non_optimized_one_shot_ppg:
        first_row['PPG_Index'] = one_shot_ppgs[1]
        first_row['Type_of_Promo'] = one_shot_ppgs[2]
        first_row['PPG_Type'] = 'One Shot'
        summary_df = summary_df.append(first_row)
    
    summary_df.rename(columns={'PPG':'PPG_idx',
                                'Retailer':'Ret_idx',
                                'PPG_Index':'PPG',
                                'Retailer_Index':'Retailer'}
                                ,inplace=True)
    
    summary_df['status'] = status
    return summary_df
