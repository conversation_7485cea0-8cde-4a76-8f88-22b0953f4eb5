""" Common App Serializers"""
from rest_framework import serializers
from django.db import transaction
from apps.common import models as db_model
from apps.promo.promo_optimizer.generic import format_ppg #pylint: disable=E0401
from apps.promo.promo_scenario_planner import unit_of_work as uow,services
from core.constants.generic_constants import constants_decider as generic_const

class FileUplaodSerializer(serializers.Serializer): # pylint: disable=W0223
    """ File Upload Serializer."""
    simulator_input = serializers.FileField()

    class Meta:
        """ Class Meta for File Uplaod Serializer."""
        ref_name = None

class ScenarioSavedList(serializers.ModelSerializer): # pylint: disable=W0223
    """ Scenario Saved List Serializer."""
    meta = serializers.SerializerMethodField('get_meta')
    class Meta:
        """ Meta Class for Scenario Saved List Serializer."""
        model = db_model.SavedScenario
        fields = '__all__'
        read_only_fields = ('id',)
        ref_name = None

    def get_meta(self,obj):
        """ Return Meta data"""

        obj  = services.get_scenarios(
                        uow.ScenarioPlannerPromotionUnitOfWork(transaction),
                        obj.id
                    )

        return obj

class ScenarioComapreSerializer(serializers.ModelSerializer): # pylint: disable=W0223
    """ Scenario Comapre Serializer."""
    class Meta:
        """ Class Meta for Scenario Comapre Serializer."""
        model = db_model.ScenarioPromotionSave
        fields = ('data','saved_scenario','multippg_type')
        ref_name = None
        
class ScenarioPromotionSerializer(serializers.ModelSerializer): # pylint: disable=W0223
    """ Scenario Promotion Serializer."""
    scenario_name = serializers.ReadOnlyField(source='saved_scenario.name')
    status_type = serializers.ReadOnlyField(source='saved_scenario.status_type')
    scenario_type = serializers.ReadOnlyField(source='saved_scenario.scenario_type')
    class Meta:
        """ Class Meta for Scenario Promotion Serializer."""
        model = db_model.ScenarioPromotionSave
        fields = '__all__'
        ref_name = None

class ModelMetaSerializer(serializers.ModelSerializer): # pylint: disable=W0223
    """ Model Meta Serializer."""
    class Meta:
        """ Class Meta for Model Meta Serializer."""
        model = db_model.ModelMeta
        fields = '__all__'
        ref_name = None
    
    def to_representation(self, instance):
        instance = super(ModelMetaSerializer, self).to_representation(instance)
        instance['product_group'] = format_ppg(instance['product_group'])
        return instance

class ModelDataSerializer(serializers.ModelSerializer): # pylint: disable=W0223
    """ Model Meta Serializer."""
    class Meta:
        """ Class Meta for Model Meta Serializer."""
        model = db_model.ModelData
        fields = '__all__'
        ref_name = None

class WeeklyConstraintsSerializer(serializers.Serializer):
    account_name = serializers.CharField()
    product_group = serializers.ListField()
    scenario_name = serializers.CharField()
    scenario_type = serializers.CharField()

class WeeklyConstraintsResponseSerializer(serializers.Serializer):
    weekly_data = generic_const.WEEKLY_CONSTRAINTS_RESPONSE

class MetaDataResponseSerializer(serializers.Serializer):
    meta_data = generic_const.META_DATA_RESPONSE
