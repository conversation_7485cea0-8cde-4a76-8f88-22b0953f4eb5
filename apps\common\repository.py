""" Common Repository"""
import operator
from functools import reduce
from django.db.models import Q
from rest_framework.serializers import ValidationError
from core.generics.respository import ORMModelRepository #pylint: disable=E0401
from apps.common import models as db_model #pylint: disable=E0401

class MetaRepository(ORMModelRepository):
    """ Meta Repository"""
    def __init__(self):
        super().__init__(db_model.ModelMeta)

    def get(self, _id):
        """ Filter data based on id"""
        return self._model.filter(id=_id).first()
        
    def filter_by_account_name(self,account_name):
        """ Filter data based on account name"""
        return self._model.filter(account_name=account_name,is_delete=False,is_active=True)
    
    def filter_by_region(self,region):
        """ Filter data based on account name"""
        query = reduce(operator.or_, (Q(account_name__icontains = item) \
                                      & Q(is_delete=False) \
                                        & Q(is_active=True) for item in region))
        return self._model.filter(query)

    def filter_by_retailer_and_ppg(self,acc_name,ppg):
        """ Filter data based on retailer and ppg"""
        return self._model.filter(account_name=acc_name,product_name=ppg,is_delete=False,is_active=True)

    def get_all(self, **kwargs):
        return self._model.filter(is_delete=False,is_active=True)


class BaselineDataRepository(ORMModelRepository):
    """ BaselineData Repository"""
    def __init__(self):
        super().__init__(db_model.BaselineDataView)

    def get(self, _id):
        """ Filter data based on id"""
        return self._model.filter(id=_id).first()
        
    def filter_by_account_name(self,account_name):
        """ Filter data based on account name"""
        return self._model.filter(account_name=account_name,is_delete=False)
    
    def filter_by_region(self,region):
        """ Filter data based on account name"""
  
        region = list(map(lambda x:x.split(','),region))
        region = reduce(lambda x,y: x+y, region)
        query = reduce(operator.or_, (Q(account_name__icontains = item) for item in region ))
        if 'all' in region:
            return self._model.all()
        return self._model.filter(query)

    def filter_by_retailer_and_ppg(self,acc_name,ppg):
        """ Filter data based on retailer and ppg"""
        return self._model.filter(account_name=acc_name,product_name=ppg,is_delete=False)

    def get_all(self, **kwargs):
        return self._model.filter()

class SelectedBaselineDataRepository(ORMModelRepository):
    """ BaselineData Repository"""
    def __init__(self):
        super().__init__(db_model.SelectedBaselineDataView)

    def get(self, _id):
        """ Filter data based on id"""
        return self._model.filter(id=_id).first()
        
    def filter_by_account_name(self,account_name):
        """ Filter data based on account name"""
        return self._model.filter(account_name=account_name)
    
    def filter_by_region(self,region):
        """ Filter data based on account name"""
  
        region = list(map(lambda x:x.split(','),region))
        region = reduce(lambda x,y: x+y, region)
        query = reduce(operator.or_, (Q(account_name__icontains = item) for item in region ))
        if 'all' in region:
            return self._model.all()
        return self._model.filter(query)

    def filter_by_retailer_and_ppg(self,acc_name,ppg):
        """ Filter data based on retailer and ppg"""
        return self._model.filter(account_name=acc_name,product_name=ppg,is_delete=False)

    def get_all(self, **kwargs):
        return self._model.filter()

class ModelDataRepository(ORMModelRepository):
    """ Model Data Repository"""
    def __init__(self):
        super().__init__(db_model.ModelData)

    def get(self, _id):
        """ Filter data based on id"""
        return self._model.filter(id=_id).first()

    def filter_by_account_name(self,account_name):
        """ Filter data based on account name"""
        return self._model.filter(model_meta__account_name=account_name,is_delete=False)

    def filter_by_retailer_and_ppg(self,acc_name,ppg):
        """ Filter data based on retailer and ppg"""
        return self._model.filter(model_meta__account_name=acc_name,product_name=ppg,is_delete=False)
    
class ModelROIRepository(ORMModelRepository):
    """ Model ROI Repository Repository"""
    def __init__(self):
        super().__init__(db_model.ModelROI)

    def get(self, _id):
        """ Filter data based on id"""
        return self._model.filter(id=_id).first()

class CoeffRepository(ORMModelRepository):
    """ Coeff Repository Repository"""
    def __init__(self):
        super().__init__(db_model.ModelCoefficient)

    def get(self, _id):
        """ Filter data based on id"""
        return self._model.filter(id=_id).first()

class CoeffMapRepository(ORMModelRepository):
    """ Coeff Map Repository Repository"""
    def __init__(self):
        super().__init__(db_model.CoefficientMapping)

    def get(self, _id):
        """ Filter data based on id"""
        return self._model.filter(id=_id).first()
    
class ScenarioPromotionRepository(ORMModelRepository):
    """ Scenario Promotion Repository"""
    def __init__(self):
        super().__init__(db_model.ScenarioPromotionSave)

    def get(self, _id):
        """ Filter data based on id"""
        return self._model.filter(id=_id).first()

    def filter_by_id(self, uid):
        """ Filter data based on scenario id"""
        return self._model.filter(saved_scenario_id=uid)

    def update(self, uid, entity):
        """ Update data based on id"""
        if self._model.filter(id=uid, is_delete=False).first():
            self._model.filter(id=uid).update(**entity)
        else:
            raise ValidationError("cannot update inactive records")

    def delete(self, uid):
        """ Delete data based on id"""
        if self._model.filter(id=uid, is_delete=False).first():
            self._model.filter(id=uid).update(is_delete=True)
            raise ValidationError("cannot delete inactive records")

class RetailerPPGMappingRepository(ORMModelRepository):
    """ Model Data Repository"""
    def __init__(self):
        super().__init__(db_model.RetailerPPGMapping)

    def get(self, _id):
        """ Filter data based on id"""
        return self._model.filter(id=_id).first()

    def filter_by_retailer_ppg_and_promo_type(self,r_p_pt):
        
        """ Filter data based on retailer and ppg"""
        clauses = (Q(account_name=r) \
                    & Q(product_group__icontains=p.replace('_', ' ')) \
                    & Q(type_of_promo=pt) \
                    &  Q(is_delete=False) for r,p,pt,_ in r_p_pt)
        query = reduce(operator.or_, clauses)
        return self._model.filter(query)

    def filter_by_retailer_ppg(self,r_p):
        """ Filter data based on retailer and ppg"""
        clauses = (Q(account_name=r) \
                    & Q(ppg_item__icontains=p) \
                    & Q(is_delete=False)
                    for r,p in r_p)
        query = reduce(operator.or_, clauses)
        return self._model.filter(query)
    
    def filter_by_retailer(self,acc_name):
        """ Filter data based on retailer and ppg"""
        return self._model.filter(account_name=acc_name,is_delete=False)
    
    def filter_by_retailer_ppg2(self,r_p_pt):
        """ Filter data based on retailer and ppg"""
        clauses = (Q(account_name=r) \
                    & Q(product_group='-all_brand'.join('_-_'.join(part.replace('_', ' ') for part in subpart.split('_-_')) for subpart in p.split('-all_brand'))) \
                    & Q(type_of_promo=pt) \
                    &  Q(is_delete=False) for r,p,pt,_ in r_p_pt)
        query = reduce(operator.or_, clauses)

        return self._model.filter(query)
    
class TacticRepository(ORMModelRepository):
    """ Model Data Repository"""
    def __init__(self):
        super().__init__(db_model.ModelTactic)

    def get(self, _id):
        """ Filter data based on id"""
        return self._model.filter(id=_id).first()

class ItemMapRepository(ORMModelRepository):
    """ Model Data Repository"""
    def __init__(self):
        super().__init__(db_model.ItemMap)

    def get(self, _id):
        """ Filter data based on id"""
        return self._model.filter(id=_id).first()


class BaseAndPromotionLevelDetailRepository(ORMModelRepository):
    """ Model Data Repository"""
    def __init__(self):
        super().__init__(db_model.BaseAndNationalPromoDetails)

    def filter(self, retailer,ppg):
        """ Filter data based on id"""
        return self._model.filter(account_name=retailer,product_group__in=ppg,is_delete=False)
    
    def delete(self):
        "Permanent Delete"
        self._model.all().delete()
    