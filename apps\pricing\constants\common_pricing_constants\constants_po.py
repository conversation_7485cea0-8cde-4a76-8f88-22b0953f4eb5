from enum import Enum


PRICING_HEADER=[
    'OGSM Type',
    'Customer',
    'Technology',
    'Brand',
    'Customer_PPG',
    'PPG',
    'Sell In Volume [t]',
    'BWW / GSV [€]',
    'NSV [€]',
    'NSV/t [€]',
    'TT%',
    'COGS / t [€]', 	 
    'GMAC abs [€]',
    'GMAC %',
    'LP [€]',
    'Pack Weight [kg]',
    'Tax',
    'Net Net [€]',	
    'Promo NN (=Dead Net) [€]',
    'Non Promo Price per Unit [€]',
    'Promo Price per Unit [€]',
    'Non Promo Price per kg [€]',
    'Promo Price Elasticity',
    'Promo Price per kg [€]'
    '% Promo Share',
    'TPR %',
    'Sell Out Volume Sales [t]',
    'Unit Sales (000)',
    'Value Sales = RSV [€]',
    'Non Promo Price Elasticity',
    'Competitor Coefficient'
]

PRICING_VALUES=[
    'sell_in_volume_t',
    'bww_gsv',
    'nsv',
    'nsv_t',
    'tt',
    'cogs_t', 	 
    'gmac_abs',
    'gmac',
    'list_price',
    'pack_weight_kg',
    'tax',
    'net_net',	
    'dead_net',
    'promo_vol_change',
    'promo_price_per_unit',
    'promo_price_elasticity',
    'non_promo_price_per_kg',
    'promo_share',
    'tpr',
    'sell_in_unit',
    # 'tpr_new',
    'sell_out_volume_sales_t',
    'unit_sales_000',
    'value_sales_rsv',
    'non_promo_price_elasticity',
    'base_growth_th_invest',
    'competitor_coefficient',
    'net_non_promo_price_elasticity',
    'non_promo_price_per_kg_new',
    'non_promo_price_per_unit',
    'promo_price_per_unit',
    'non_promo_price_new',
    'promo_price_new',
    'changed_lp_percent',
    'changed_pack_weight_kg_percent',
    'changed_cogs_t_percent',
    'changed_promo_vol_change_percent',
    'changed_base_growth_th_invest_percent',
    'changed_dead_net_change_percent',
    'changed_net_net_change_percent',
    'changed_nn_change_percent',
    'list_price_change_percent',
    'list_price_change_percent_kg',
    'nsv_price_impact_direct',
    'nsv_price_impact_indirect',
    'nsv_price_impact_base',
    'new_lp_after_base_price',
    'list_price_new',
    'pack_weight_kg_new',
    'net_net_new',
    'dead_net_new',
    'changed_non_promo_price',
    'net_net_multiplication_factor',
    'lp_multiplication_factor',
    'floor_price'
    ]

PRICING_CHANGED_SCENARIO_COLUMNS=[
    'pricing_saved_scenario_id',
    'level_param',
    'customer_level_param',
    'inner_mode',
    'ogsm_param',
    'promo_vol_change_new',
    'base_growth_th_invest_new',
    'changed_net_net_change_percent',
    'changed_dead_net_change_percent',
    'status_flag',
    'type_of_price_inc',
    'competitor_follows',
    'list_price_new',
    'pack_weight_kg_new',
    'dead_net_new',
    'net_net_new',
    'non_promo_price_new',
    'promo_price_new',
    'floor_price',
    'changed_lp_percent'
]

PRICING_UPLOADED_SCENARIO_COLUMNS=[
    'pricing_saved_scenario_id',
    'level_param',
    'ogsm_param',
    'customer_level_param',
    'changed_net_net_change_percent',
    'changed_dead_net_change_percent',
    'list_price_new',
    'pack_weight_kg_new',
    'dead_net_new',
    'net_net_new',
    'non_promo_price_new',
    'promo_price_new',
    'floor_price'
]


SUMMARY_PRICING_SCENARIO=[
    'pricing_saved_scenario_id',
    'base',
    'simulated'
]

PRICING_BASE_AND_SIMULATED_SCENARIO_COLUMNS=[
    'pricing_saved_scenario_id',
    'ogsm_type',
    'customer',
    'technology',
    'brand',
    'product_group',
    'percent_pricing_impact_total',
    'nsv_pricing_impact_direct',
    'nsv_pricing_impact_indirect',
    'nsv_pricing_impact_base',
    'nsv_pricing_impact_total',
    'total_price_impact_inc_tt_drift',
    'total_price_impact_inc_tt_drift_percent',
    'sell_in_volume_t_new',
    'lsv_new',
    'nsv_new',
    'nsv_t_new',
    'cogs_t_new',
    'tt_percent_new',
    'gmac_abs_new',
    'gmac_percent_change',
    'gmac_percent_new',
    'rsv_new',
    'sell_out_volume_sales_t_new',
    'unit_sales_000',
    'promo_share',
    'avg_price_per_unit_new',
    'percent_trade_margin_non_promo_new',
    'percent_trade_margin_promo_new',
    'avg_trade_margin_new',
    'customer_profit_new',
    'nsv',
    'mac',
    'list_price',
    'tax',
    'rsv_wo_vat',
    'tt_drift',
    'mars_profit',
    'is_base',
    'implied_price_impact',
    'implied_vol_impact',
    'implied_mix_impact',
    'total_growth',
    'nsv_percent_change',
    'nsv_t_percent_change',
    'rsv_percent_change' ,
    'profit_pool_customer_profit',
    'profit_pool_mars_profit',
    'customer_profit_percent_change',
    'net_net_new',
    'dead_net_new',
    'sell_out_unit_new',
    'list_price_new',
    'changed_dead_net_change_percent',
    'changed_net_net_change_percent',
    'changed_lp_percent',
    'non_promo_price_new',
    'promo_price_new',
    'pack_weight_kg_new',
    'net_non_promo_price_elasticity',
    'non_promo_price_per_unit',
    'sell_in_unit',
    'sell_out_unit',
    'tpr',
    'tpr_new',
    'promo_price_elasticity',
    'non_promo_price_elasticity',
    'pack_weight_kg',
    'sell_in_unit_new',
    'optimized_trade_margin',
    'optimized_units',
    'changed_promo_vol_change_percent',
    'changed_cogs_t_percent',
    'changed_pack_weight_kg_percent',
    'type_of_price_inc',
    'competitor_follows',
    'status_flag',
    'net_net_nsv_product',
    'lp_nsv_product',
    'nn_change_percent',
    'exclude_retailer',
    'is_planner',
    'promo_sold_volume',
    'promo_sold_volume_new',
    'non_promo_sold_volume',
    'non_promo_sold_volume_new',
    'promo_sold_volume_percent',
    'promo_sold_volume_percent_new'
]

PROFIT_POOL_COL = ['non_promo_price_new',
                   'promo_price_new','tax','net_net_new'\
                    ,'pack_weight_kg_new',
                    'dead_net_new',
                    'net_non_promo_price_elasticity'\
                    ,'non_promo_price_per_unit',
                    'promo_share',
                    'sell_in_unit',
                    'sell_out_unit',
                    'nsv_t_new',
                    'cogs_t_new',
                    'nsv_new',
                    'list_price_new',
                    'gmac_abs_new',
                    'customer_profit_new',
                    'avg_trade_margin_new',
                    'rsv_new',
                    'tpr',
                    'tpr_new',
                    'promo_price_elasticity',
                    'non_promo_price_elasticity'
                        ]

NUMERIC_COL = [
    'percent_pricing_impact_total',
    'nsv_pricing_impact_direct',
    'nsv_pricing_impact_indirect',
    'nsv_pricing_impact_base',
    'nsv_pricing_impact_total',
    'total_price_impact_inc_tt_drift',
    'total_price_impact_inc_tt_drift_percent',
    'sell_in_volume_t_new',
    'lsv_new',
    'nsv_new',
    'nsv_t_new',
    'cogs_t_new',
    'tt_percent_new',
    'gmac_abs_new',
    'gmac_percent_change',
    'rsv_new',
    'sell_out_volume_sales_t_new',
    'unit_sales_000',
    'promo_share',
    'avg_price_per_unit_new',
    'percent_trade_margin_non_promo_new',
    'percent_trade_margin_promo_new',
    'avg_trade_margin_new',
    'customer_profit_new',
    'nsv',
    'mac',
    'list_price',
    'tax',
    'rsv_wo_vat',
    'tt_drift',
    'profit_pool_customer_profit',
    'profit_pool_mars_profit',
    'tpr',
    'tpr_new'
      
]

SUMMARY_COL=[
    'percent_pricing_impact_total',
    'nsv_pricing_impact_direct',
    'nsv_pricing_impact_indirect',
    'nsv_pricing_impact_base',
    'nsv_pricing_impact_total',
    'total_price_impact_inc_tt_drift',
    'total_price_impact_inc_tt_drift_percent',
    'tt_drift',
    'customer_profit_new',
    'sell_in_volume_t_new',
    'lsv_new',
    'nsv_t_new',
    'nsv_new',
    'tt_percent_new',
    'gmac_abs_new',
    'gmac_percent_change',
    'rsv_new',
    'sell_out_volume_sales_t_new',
    'unit_sales_000',
    'promo_share',
    'avg_price_per_unit_new',
    'percent_trade_margin_non_promo_new',
    'percent_trade_margin_promo_new',
    'avg_trade_margin_new',
    'cogs_t_new',
    'list_price_new',
    'changed_dead_net_change_percent',
    'changed_net_net_change_percent',
    'changed_lp_percent',
    'dead_net_new',
    'net_net_new',
    'gmac_percent_new',
    'sell_out_unit_new',
    'non_promo_price_new',
    'promo_price_new',
]

BASE_SIM_CONFIG = {
    'gmac':'gmac_abs_new',
    'nsv':'nsv_new',
    'nsv_t':'nsv_t_new',
    'rsv':'rsv_new',
    'customer_profit':'customer_profit_new',
    'dead_net':'dead_net_new',
    'net_net':'net_net_new'
}
class PricingScenario(Enum):
    MODEL_NAME = 'pricing_baseline_changed_scenario_view'
    MODEL_COLUMN = ['*']
    MODEL_VALUES = ['*']
    FK_MODEL = 'pricing_meta_model'
    FK_NAME = 'pricing_meta_model'
    YEAR_WISE = False
    FIN_YEAR_WISE = False
    REQUIRED_COLUMN = []
    EXTRA_COLUMNS = []

class ChangedPricingScenario(Enum):
    MODEL_NAME = 'changed_pricing_scenario'
    MODEL_COLUMN = PRICING_CHANGED_SCENARIO_COLUMNS
    MODEL_VALUES = PRICING_CHANGED_SCENARIO_COLUMNS
    FK_MODEL = 'pricing_saved_scenario'
    FK_NAME = 'pricing_saved_scenario'
    YEAR_WISE = False
    FIN_YEAR_WISE = False
    REQUIRED_COLUMN = []
    EXTRA_COLUMNS = []

class UploadedPricingScenario(Enum):
    MODEL_NAME = 'changed_pricing_scenario'
    MODEL_COLUMN = PRICING_UPLOADED_SCENARIO_COLUMNS
    MODEL_VALUES = PRICING_UPLOADED_SCENARIO_COLUMNS
    FK_MODEL = 'pricing_saved_scenario'
    FK_NAME = 'pricing_saved_scenario'
    YEAR_WISE = False
    FIN_YEAR_WISE = False
    REQUIRED_COLUMN = []
    EXTRA_COLUMNS = []

class BaseAndSimulatedPricingScenario(Enum):
    MODEL_NAME = 'base_and_simulated_pricing_scenario'
    MODEL_COLUMN = PRICING_BASE_AND_SIMULATED_SCENARIO_COLUMNS
    MODEL_VALUES = PRICING_BASE_AND_SIMULATED_SCENARIO_COLUMNS
    FK_MODEL = 'pricing_saved_scenario'
    FK_NAME = 'pricing_saved_scenario'
    YEAR_WISE = False
    FIN_YEAR_WISE = False
    REQUIRED_COLUMN = []
    EXTRA_COLUMNS = []

class SummaryPricingScenario(Enum):
    MODEL_NAME = 'pricing_scenario_summery'
    MODEL_COLUMN = SUMMARY_PRICING_SCENARIO
    MODEL_VALUES = SUMMARY_PRICING_SCENARIO
    FK_MODEL = 'pricing_saved_scenario'
    FK_NAME = 'pricing_saved_scenario'
    YEAR_WISE = False
    FIN_YEAR_WISE = False
    REQUIRED_COLUMN = []
    EXTRA_COLUMNS = []

class OverallSummaryPricingScenario(Enum):
    MODEL_NAME = 'pricing_scenario_overall_summery'
    MODEL_COLUMN = SUMMARY_PRICING_SCENARIO
    MODEL_VALUES = SUMMARY_PRICING_SCENARIO
    FK_MODEL = 'pricing_saved_scenario'
    FK_NAME = 'pricing_saved_scenario'
    YEAR_WISE = False
    FIN_YEAR_WISE = False
    REQUIRED_COLUMN = []
    EXTRA_COLUMNS = []