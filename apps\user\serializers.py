'''importing  Modules'''
from django.contrib.auth import get_user_model, authenticate
from django.contrib.auth.models import Group
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers
from . import models

class RegionGroupSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = models.RegionGroup
        fields = '__all__'        
        ref_name = None
        
class RegionUserGroupMappingSerializer(serializers.ModelSerializer):

    class Meta:
        model = models.RegionUserGroupMapping
        fields = '__all__'        
        ref_name = None

class GroupSerializer(serializers.ModelSerializer):
    '''Creating a class for Model Serailizers with name GroupSerializer'''
    class Meta:
        '''creating inner class with Fields'''
        model = Group
        fields = ('name',)

class UserSerializer(serializers.ModelSerializer):
    '''Creating a class for Model Serailizers with name UserSerializer'''
    groups = GroupSerializer(many=True)
    class Meta:
        '''creating a inner class with Fields'''
        
        model = get_user_model()
        fields = ('name','email','password', 'id','groups')
        extra_kwargs = {
            'password': {'write_only': True, 'min_length': 5},
        }

    def create(self, validated_data):
        return get_user_model().objects.create_user(**validated_data)


class AuthTokenSerializer(serializers.Serializer):
    '''Creating a class for Model Serailizers with name AuthTokenSerializer'''
    email = serializers.CharField()
    password = serializers.CharField(
        style={'input_type': 'password'},
        trim_whitespace=False
    )

    def validate(self, attrs):
        email = attrs.get('email')
        password = attrs.get('password')
        user = authenticate(
            request=self.context.get('request'),
            username=email,
            password=password
        )
        if not user:
            msg = _('Unable to authenticate')
            raise serializers.ValidationError(msg, code='authentication')
        attrs['user'] = user
        return attrs

class CreateUserSerializer(serializers.Serializer):
    email = serializers.EmailField()
    password = serializers.CharField()
