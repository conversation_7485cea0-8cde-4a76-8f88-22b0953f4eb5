from django.conf import settings
from django.db import models
from django.utils import timezone
from django.utils.text import slugify
from django.db.models.signals import pre_save
from django.dispatch import receiver
from apps.common import models as cmn_model
from config.db_handler import db_table_format, model_fields_handler


class PricingMetaModel(cmn_model.BaseModel):
    """ Model Meta."""
    ogsm_type=models.CharField(max_length=100,verbose_name="OGSM Type")
    customer=models.CharField(max_length=100,verbose_name="Customer")
    technology=models.CharField(max_length=100,verbose_name="Technology")
    product_group=models.CharField(max_length=100,verbose_name="Technology")
    brand=models.CharField(max_length=100,verbose_name="Brand")
    # customer_ppg=models.CharField(max_length=100,verbose_name="Customer PPG")
    # slug = models.SlugField(max_length=255, unique=True,blank=True , default=0.0\
    #                             , null=False,verbose_name='Slug')

    class Meta:
        """ Meta Class For Model Meta."""
        constraints = [
            models.UniqueConstraint(fields=['customer','product_group'],name='customer')
        ]
        verbose_name = "Model Meta"
        db_table = "pricing_meta_model"
        ordering = ('customer',)
        if hasattr(settings, 'PRICING_DATABASE_SCHEMA'):
            db_table = db_table_format('pricing_meta_model',settings.PRICING_DATABASE_SCHEMA)


class PricingCommonModel(models.Model):
    id = models.IntegerField(primary_key=True)
    customer=models.CharField(max_length=100,verbose_name="Customer")
    ogsm_type=models.CharField(max_length=100,verbose_name="OGSM Type")
    competitor_follows=models.CharField(max_length=100,verbose_name="OGSM Type")
    type_of_price_inc=models.CharField(max_length=100,verbose_name="Customer")
    status_flag=models.CharField(max_length=100,verbose_name="Technology")
    
    list_price = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='LP [€]')
    pack_weight_kg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Pack Weight [kg]')
    net_net=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Net Net [€]')
    dead_net=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Promo NN (=Dead Net) [€]')
    shelf_price = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Non Promo Price per Unit [€]')
    promo_price_per_unit=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Promo Price per Unit [€]')
    non_promo_price_per_unit=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Non Promo Price per Unit [€]')
    non_promo_price_per_kg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Non Promo Price per kg [€]')
    promo_price_elasticity=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Promo Price Elasticity')
    promo_price_per_kg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Promo Price per kg [€]')
    promo_share=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='% Promo Share')
    tpr=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='TPR %')
    non_promo_price_elasticity=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Non Promo Price Elasticity')
    competitor_coefficient=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Competitor Coefficient')
    promo_vol_change=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Promo Volume Change')
    base_growth_th_invest=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Base Growth Through Invest')
    changed_shelf_price_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='ChangedNon Promo Price per Unit [€]')
    changed_lp_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='ChangedLP [€]')
    changed_pack_weight_kg_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed Pack Weight [kg]')
    changed_cogs_t_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    changed_promo_vol_change_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed Promo Volume Change')
    changed_base_growth_th_invest_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name=' Changed Base Growth Through Invest')
    changed_nn_change_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NN Changed Percent')
    changed_net_net_change_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed Net Net')
    changed_dead_net_change_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed NN Dead Net')
    changed_non_promo_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed Net Net')
    changed_promo_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed NN Dead Net')
    list_price_change_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='List Price Change Percent')
    list_price_change_percent_kg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='List Price Change Percent KG')
    nsv_price_impact_direct=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact direct')
    nsv_price_impact_indirect=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    nsv_price_impact_base=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact base')
    new_lp_after_base_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    type_of_base_inc_after_change=models.CharField(max_length=100,verbose_name="Type Of Base Inc After  Change")
    shelf_price_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    list_price_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    pack_weight_kg_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    promo_price_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    non_promo_price_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    non_promo_price_per_kg_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    promo_price_per_kg_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    investment=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    net_non_promo_price_elasticity=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    competitor_coefficient=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    dead_net_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    net_net_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    nsv_percent_change=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')

    nsv_t_percent_change=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='NSV Price Impact indirect')
    gmac_percent_change=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    rsv_percent_change=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    # objective_func = models.CharField(max_length=20)
    # net_net_multiplication_factor=models.FloatField(null=False)
    # lp_multiplication_factor =models.FloatField(null=False)
    # nationa_nn_index = models.FloatField(null=False)
 
    class Meta:
        abstract=True

# Create your models here.
class PricingScenario(cmn_model.BaseModel):
    STATUS_FLAG_CHOICE = (
        ("modified", "modified"),
        ("not modified", "not modified"),
    )
    PRICE_INC_CHOICE = (
        ("direct", "direct"),
        ("indirect", "indirect"),
        ("base", "base")
    )
    COMPETITOR_CHOICE = (
        ("Yes", "Yes"),
        ("No", "No")
    )
    status_flag = models.CharField(
        max_length=20,
        choices=STATUS_FLAG_CHOICE,
        default='not modified'
    )
    type_of_price_inc = models.CharField(
        max_length=20,
        choices=PRICE_INC_CHOICE,
        default='base'
    )
    competitor_follows = models.CharField(
        max_length=20,
        choices=COMPETITOR_CHOICE,
        default='No'
    )
    pricing_meta_model = models.ForeignKey(PricingMetaModel , related_name="pricing_scenario" \
                                    ,on_delete=models.CASCADE,verbose_name='Pricing Scenario fk')
    sell_in_volume_t=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Sell In Volume [t]')
    bww_gsv=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='BWW / GSV [€]')
    nsv=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV [€]')
    nsv_t=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV/t [€]')
    tt=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='TT%')
    cogs_t=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='COGS / t [€]')
    gmac_abs=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='GMAC abs [€]')
    gmac=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='GMAC %')
    lp=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='LP [€]')
    pack_weight_kg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Pack Weight [kg]')
    tax=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Tax')
    net_net=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Net Net [€]')
    promo_nn_dead_net=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Promo NN (=Dead Net) [€]')
    non_promo_price_per_unit=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Non Promo Price per Unit [€]')
    promo_price_per_unit=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Promo Price per Unit [€]')
    non_promo_price_per_kg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Non Promo Price per kg [€]')
    promo_price_elasticity=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Promo Price Elasticity')
    promo_price_per_kg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Promo Price per kg [€]')
    promo_share=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='% Promo Share')
    tpr=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='TPR %')
    sell_out_volume_sales_t=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Sell Out Volume Sales [t]')
    unit_sales_000=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Unit Sales (000)')
    sell_in_unit=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    value_sales_rsv=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Value Sales = RSV [€]')
    non_promo_price_elasticity=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Non Promo Price Elasticity')
    competitor_coefficient=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Competitor Coefficient')
    promo_vol_change=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Promo Volume Change')
    base_growth_th_invest=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Base Growth Through Invest')
    floor_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Floor Price')
    non_promo_price_per_unit_ppg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Avg Shelf Price')
    promo_price_per_unit_ppg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Avg Shelf Promo Price')
    is_optimizer_ppg = models.BooleanField(default=False,verbose_name='is_optimizer_ppg')
    
    class Meta:
        """ Meta Class For Coefficient Mapping Model."""
        verbose_name = "Pricing Scenario"
        db_table = 'pricing_scenario'
        if hasattr(settings, 'PRICING_DATABASE_SCHEMA'):
            db_table = db_table_format(db_table,settings.PRICING_DATABASE_SCHEMA)
    
class PricingPublishedSavedScenario(cmn_model.BaseModel):
    '''Saved Scenario Model.'''

    SCENARIO_CHOICES = (
        ("simulator", "simulator"),
        ("optimizer", "optimizer")
    )
    LEVEL_CHOICES = (
        ("product_group", "product_group"),
        ("customer", "customer"),
        ("ogsm_type", "ogsm_type"),
        ("brand", "brand"),
        ("technology", "technology")
    )
    MODULE_CHOICES = (
        ("set_pricing", "set_pricing"),
        ("set_target", "set_target"),
        ("scenario_planner", "scenario_planner")
    )
    PROMO_CHOICES = (
        ("single", "single"),
        ("joint", "joint"),
        ("all_brand", "all_brand")
    )
    STATUS_CHOICES = (
        ("active", "active"),
        ("completed", "completed"),
    )
    MODE_CHOICES = (
        ("all", "all"),
        ("filtered", "filtered"),
    )
    
    SCENARIO_APPROVAL_STATUS = (
        ("in_progress", "Awaiting Aprroval"),
        ("accepted", "Published"),
        ("rejected", "Rejected"),
    )
    
    MODE_CHOICES = (
        ("all", "all"),
        ("filtered", "filtered"),
    )
    
    promo_type = models.CharField(
        max_length=20,
        choices=PROMO_CHOICES,
        default='single'
    )
    
    scenario_type = models.CharField(
        max_length=20,
        choices=SCENARIO_CHOICES,
        default='simulator'
    )
    module_type = models.CharField(
        max_length=20,
        choices=MODULE_CHOICES,
        default='set_pricing'
    )
    status_type = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active'
    )
    level = models.CharField(
        max_length=20,
        choices=LEVEL_CHOICES,
        default='customer'
    )
    mode = models.CharField(
        max_length=20,
        choices=MODE_CHOICES,
        default='all'
    )
    
    approval_status = models.CharField(
        max_length=20,
        choices=SCENARIO_APPROVAL_STATUS,
        default='in_progress'
    )
    
    OBJ_CHOICES = (
        ("MAC", "MAC"),
        ("RP", "RP"),
        ("Trade_Expense", "Trade Expense"),
        ("Units", "Units"),
    )

    objective_func = models.CharField(
        max_length=20,
        choices=OBJ_CHOICES,
        default='MAC'
    )
    name = models.CharField(max_length=255)
    comments = models.CharField(max_length=500, default='',null=True,blank=True)
    user = model_fields_handler(attrs={'verbose_name':'saved_user'})
    shared_user = model_fields_handler(attrs={'verbose_name':'shared_user'})
    deleted_by = model_fields_handler(attrs={'verbose_name':'deleted by','allow_null':True})
    deleted_at = models.DateTimeField(auto_now_add=True,verbose_name='deleted At',null=True)
    is_committed = models.BooleanField(default=False,verbose_name='Delete')
    is_published = models.BooleanField(default=False,verbose_name='is_published')
    is_editable = models.BooleanField(default=False,verbose_name='Editable')
    view_only = models.BooleanField(default=False,verbose_name='view only')
    is_review = models.BooleanField(default=False,verbose_name='vis review')
    net_net_multiplication_factor=models.FloatField(null=False)
    lp_multiplication_factor =models.FloatField(null=False)
    nationa_nn_index = models.FloatField(null=False)
    optimized_nn_target = models.FloatField(null=False)
    target = models.FloatField(null=False)
    actual_invest = models.FloatField(null=False)
    invest_budget = models.FloatField(null=False)
    nsv = models.FloatField(null=False)
    weighted_lp_nsv_product = models.FloatField(null=False)
    calculated_nn_percent = models.FloatField(null=False)

    class Meta:
        '''Meta Class For Model Saved Scenario.'''
        verbose_name = "Model Saved Scenario"
        db_table = 'pricing_saved_scenario'
        if hasattr(settings, 'PRICING_DATABASE_SCHEMA'):
            db_table = db_table_format('pricing_saved_scenario',settings.PRICING_DATABASE_SCHEMA)

    def __str__(self):
        """ Returns Scenario Promotion Save name"""

        return f"{self.name}-{self.promo_type}-{self.scenario_type}-{self.status_type}\
                                                            -{self.user.get('user_id')}"

@receiver(pre_save, sender=PricingPublishedSavedScenario)
def save(sender, instance, *args, **kwargs):
    instance.created_at = timezone.now()
    instance.modified_at = timezone.now()
    return instance   
    
class PricingCommonScenarioView(PricingCommonModel):
    product_group=models.CharField(max_length=100,verbose_name="Product Group")
    brand=models.CharField(max_length=100,verbose_name="Brand")
    technology=models.CharField(max_length=100,verbose_name="Technology")
    sell_in_volume_t=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Sell In Volume [t]')
    bww_gsv=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='BWW / GSV [€]')
    nsv=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV [€]')
    nsv_t=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV/t [€]')
    tt=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='TT%')
    net_net=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Net Net [€]')
    dead_net=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Promo NN (=Dead Net) [€]')
    cogs_t=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='COGS / t [€]')
    gmac_abs=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='GMAC abs [€]')
    gmac=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='GMAC %')
    tax=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Tax')
    sell_out_volume_sales_t=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Sell Out Volume Sales [t]')
    unit_sales_000=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Unit Sales (000)')
    value_sales_rsv=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Value Sales = RSV [€]')
    floor_price = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Floor Price')
    sell_in_unit=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    non_promo_price_per_unit_ppg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Avg Shelf Price')
    promo_price_per_unit_ppg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Avg Shelf Promo Price')
    is_optimizer_ppg = models.BooleanField(default=False,verbose_name='is_optimizer_ppg')
    exclude_retailer = models.BooleanField(default=False,verbose_name='exclude retailer')
    nn_change_percent = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                  , null=False,verbose_name='NN_Percent%')
    net_net_nsv_product = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                  , null=False,verbose_name='net net lsv product')
    lp_nsv_product = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                  , null=False,verbose_name='lp nsv product')
    
    class Meta:
        """ Meta Class For Coefficient Mapping Model."""
        verbose_name = "Pricing Scenario"
        db_table = 'scenario_planner_common_view'
        ordering = ["-nsv"]
        if hasattr(settings, 'PRICING_DATABASE_SCHEMA'):
            db_table = db_table_format(db_table,settings.PRICING_DATABASE_SCHEMA)

class PricingScenarioCustomerLevelView(PricingCommonModel):
    nsv_sum = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Sum')
    floor_price = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Floor Price')
    nn_change_percent = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NN_Percent%')
    net_net_nsv_product = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                  , null=False,verbose_name='net net lsv product')
    lp_nsv_product = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                  , null=False,verbose_name='lp nsv product')
    actual_invest = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                  , null=False,verbose_name='net net lsv product')
    invest_budget = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                  , null=False,verbose_name='lp nsv product')
    
    class Meta:
        """ Meta Class For Coefficient Mapping Model."""
        verbose_name = "Pricing Scenario"
        db_table = 'pricing_customer_level_view'
   
        if hasattr(settings, 'PRICING_DATABASE_SCHEMA'):
            db_table = db_table_format(db_table,settings.PRICING_DATABASE_SCHEMA)

class ChangedPricingScenario(models.Model):
    PRICE_INC_CHOICE = (
        ("direct", "direct"),
        ("indirect", "indirect"),
        ("base", "base")
    )
    COMPETITOR_CHOICE = (
        ("Yes", "Yes"),
        ("No", "No")
    )
    STATUS_FLAG_CHOICE = (
        ("modified", "modified"),
        ("not modified", "not modified"),
    )
    competitor_follows = models.CharField(
        max_length=20,
        choices=COMPETITOR_CHOICE,
        default='No'
    )
    type_of_price_inc = models.CharField(
        max_length=20,
        choices=PRICE_INC_CHOICE,
        default='base'
    )
    status_flag = models.CharField(
        max_length=20,
        choices=STATUS_FLAG_CHOICE,
        default='not modified'
    )

    MODE_CHOICES = (
        ("all", "all"),
        ("filtered", "filtered"),
    )

    inner_mode = models.CharField(
        max_length=20,
        choices=MODE_CHOICES,
        default='all'
    )
    pricing_saved_scenario = models.ForeignKey(PricingPublishedSavedScenario , related_name="changed_saved_pricing_scenario" \
                                    ,on_delete=models.CASCADE,verbose_name='Changed Saved Pricing Scenario fk')
    level_param = models.CharField(max_length=100,verbose_name="Level Param")
    customer_level_param = models.CharField(max_length=100,verbose_name="Customer Level Param")
    ogsm_param = models.CharField(max_length=100,verbose_name="OGSM Param")
    changed_shelf_price_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='ChangedNon Promo Price per Unit [€]')
    changed_lp_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='ChangedLP [€]')
    changed_pack_weight_kg_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed Pack Weight [kg]')
    changed_cogs_t_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    changed_promo_vol_change_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed Promo Volume Change')
    changed_base_growth_th_invest_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name=' Changed Base Growth Through Invest')
    changed_nn_change_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NN Changed Percent')
    changed_non_promo_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NN Changed Percent')
    changed_promo_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NN Changed Percent')
    changed_net_net_change_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed Net Net')
    changed_dead_net_change_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed Dead Net')
    list_price_change_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='List Price Change Percent')
    list_price_change_percent_kg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='List Price Change Percent KG')
    nsv_price_impact_direct=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact direct')
    nsv_price_impact_indirect=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    nsv_price_impact_base=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact base')
    new_lp_after_base_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    type_of_base_inc_after_change=models.CharField(max_length=100,verbose_name="Type Of Base Inc After  Change")
    shelf_price_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    list_price_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    pack_weight_kg_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='New LP after base price')
    promo_price_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    non_promo_price_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    non_promo_price_per_kg_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    promo_price_per_kg_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    investment=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    # net_non_promo_price_elasticity=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
    #                                 , null=False,verbose_name='NSV Price Impact indirect')
    # competitor_coefficient=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
    #                                 , null=False,verbose_name='NSV Price Impact indirect')
    dead_net_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    net_net_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')

    class Meta:
        '''Meta Class For Model Saved Scenario.'''
        verbose_name = "Model Saved Scenario"
        db_table = 'changed_pricing_scenario'
        if hasattr(settings, 'PRICING_DATABASE_SCHEMA'):
            db_table = db_table_format('changed_pricing_scenario',settings.PRICING_DATABASE_SCHEMA)

    def __str__(self):
        """ Returns Scenario Promotion Save name"""

        return f"{self.pricing_saved_scenario.id}"
    
class PricingScenarioSummary(cmn_model.BaseModel):
    pricing_saved_scenario = models.ForeignKey(PricingPublishedSavedScenario , related_name="pricing_scenario_summery" \
                                    ,on_delete=models.SET_NULL,verbose_name='Pricing Summery fk',null=True)
    
    base = model_fields_handler(attrs={'verbose_name':'BASE'})
    simulated = model_fields_handler(attrs={'verbose_name':'SIMULATED'})
    optimized_nn_target=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')

    target=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')

    actual_invest=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    invest_budget=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')

    calculated_nn_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    nsv = models.FloatField(null=False)
    weighted_lp_nsv_product = models.FloatField(null=False)
   
    class Meta:
        '''Meta Class For Model Saved Scenario.'''
        verbose_name = "Model Pricing Scenario Summery"
        db_table = 'pricing_scenario_summery'
        if hasattr(settings, 'PRICING_DATABASE_SCHEMA'):
            db_table = db_table_format('pricing_scenario_summery',settings.PRICING_DATABASE_SCHEMA)

    def __str__(self):
        """ Returns Scenario Promotion Save name"""

        return f"{self.pricing_saved_scenario}"
    
class PublishedScenarioView(cmn_model.BaseModel):
    pricing_pubslished_scenario = models.ForeignKey(PricingPublishedSavedScenario , related_name="published_scenario_view" \
                                    ,on_delete=models.SET_NULL,verbose_name='Published Scenario fk',null=True)
    
    is_published = models.BooleanField(default=False,verbose_name='is_published')

    class Meta:
        '''Meta Class For Model Saved Scenario.'''
        verbose_name = "Model Pricing Scenario Summery"
        db_table = 'published_scenario_view'
        if hasattr(settings, 'PRICING_DATABASE_SCHEMA'):
            db_table = db_table_format('published_scenario_view',settings.PRICING_DATABASE_SCHEMA)

    def __str__(self):
        """ Returns Scenario Promotion Save name"""

        return f"{self.pricing_pubslished_scenario}"
    
class PricingScenarioOverallSummary(cmn_model.BaseModel):
    pricing_saved_scenario = models.ForeignKey(PricingPublishedSavedScenario , related_name="pricing_scenario_overall_summery" \
                                    ,on_delete=models.SET_NULL,verbose_name='Pricing Pricing Summery fk',null=True)
    base = model_fields_handler(attrs={'verbose_name':'BASE'})
    simulated = model_fields_handler(attrs={'verbose_name':'SIMULATED'})

    class Meta:
        '''Meta Class For Model Saved Scenario.'''
        verbose_name = "Model Pricing Scenario Overall Summery"
        db_table = 'pricing_scenario_overall_summery'
        if hasattr(settings, 'PRICING_DATABASE_SCHEMA'):
            db_table = db_table_format('pricing_scenario_overall_summery',settings.PRICING_DATABASE_SCHEMA)

    def __str__(self):
        """ Returns Scenario Promotion Save name"""

        return f"{self.pricing_saved_scenario.id}"
    
class BaseAndSimulatedPricingScenario(models.Model):
    PRICE_INC_CHOICE = (
        ("direct", "direct"),
        ("indirect", "indirect"),
        ("base", "base")
    )
    COMPETITOR_CHOICE = (
        ("Yes", "Yes"),
        ("No", "No")
    )
    STATUS_FLAG_CHOICE = (
        ("modified", "modified"),
        ("not modified", "not modified"),
    )
    competitor_follows = models.CharField(
        max_length=20,
        choices=COMPETITOR_CHOICE,
        default='No'
    )
    type_of_price_inc = models.CharField(
        max_length=20,
        choices=PRICE_INC_CHOICE,
        default='base'
    )
    status_flag = models.CharField(
        max_length=20,
        choices=STATUS_FLAG_CHOICE,
        default='not modified'
    )
    
    ogsm_type=models.CharField(max_length=100,verbose_name="OGSM Type")
    customer=models.CharField(max_length=100,verbose_name="Customer")
    technology=models.CharField(max_length=100,verbose_name="Technology")
    product_group=models.CharField(max_length=100,verbose_name="Technology")
    brand=models.CharField(max_length=100,verbose_name="Brand")
    pricing_saved_scenario = models.ForeignKey(PricingPublishedSavedScenario , related_name="base_simulated_pricing_scenario" \
                                    ,on_delete=models.SET_NULL,verbose_name='Pricing Scenario Base And Simulated fk',null=True)
    
    percent_pricing_impact_total=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Pricing Impcat COGS / t [€]')
    nsv_pricing_impact_direct=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    nsv_pricing_impact_indirect=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    nsv_pricing_impact_base=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    nsv_pricing_impact_total=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    total_price_impact_inc_tt_drift=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    total_price_impact_inc_tt_drift_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    sell_in_volume_t_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    bww_gsv_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    nsv_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    nsv_t_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    cogs_t_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    tt_percent_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    gmac_abs_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    gmac_percent_change=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    gmac_percent_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    rsv_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    sell_out_volume_sales_t_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    unit_sales_000=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    promo_share=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    avg_price_per_unit_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    percent_trade_margin_non_promo_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    percent_trade_margin_promo_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    promo_price_elasticity=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    non_promo_price_elasticity=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    avg_trade_margin_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    customer_profit_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    customer_profit_percent_change=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    nsv=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    tpr=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
    , null=False,verbose_name='Changed COGS / t [€]')
    tpr_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
    , null=False,verbose_name='Changed COGS / t [€]')
    lsv=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    lsv_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    mac=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    list_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    tax=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    rsv_wo_vat=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    trade_margin=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    tt_drift=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    customer_profit=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    mars_profit=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    price_vol_mix_nsv=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    net_net=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    dead_net=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    net_net_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    dead_net_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    implied_price_impact=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    implied_mix_impact=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    implied_vol_impact=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    total_growth=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Changed COGS / t [€]')
    is_base = models.BooleanField(default=False,verbose_name='Base')
    is_planner = models.BooleanField(default=False,verbose_name='is planner')
    exclude_retailer = models.BooleanField(default=False,verbose_name='exclude retailer')
    nsv_percent_change=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')

    nsv_t_percent_change=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='NSV Price Impact indirect')
    gmac_percent_change=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    rsv_percent_change=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    sell_out_unit_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    sell_out_unit=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    sell_in_unit=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    profit_pool_customer_profit=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    profit_pool_mars_profit=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    list_price_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')

    changed_dead_net_change_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='NSV Price Impact indirect')

    changed_net_net_change_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='NSV Price Impact indirect')
    changed_cogs_t_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='NSV Price Impact indirect')
    changed_pack_weight_kg_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='NSV Price Impact indirect')
    changed_lp_percent=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                        , null=False,verbose_name='NSV Price Impact indirect')
    promo_price_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    non_promo_price_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    pack_weight_kg_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    net_non_promo_price_elasticity=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    non_promo_price_per_unit=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    pack_weight_kg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='NSV Price Impact indirect')
    non_promo_price_per_unit_ppg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Avg Shelf Price')
    promo_price_per_unit_ppg=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Avg Shelf Promo Price')
    floor_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Avg Shelf Promo Price')
    is_optimizer_ppg = models.BooleanField(default=False,verbose_name='is_optimizer_ppg')
    sell_in_unit_new=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Avg Shelf Promo Price')

    optimized_dead_net=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Optimized Dead Net')
    optimized_net_net=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Optimized Net Net')
    optimized_non_promo_price=models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                    , null=False,verbose_name='Optimized Promo Price')
    net_net_nsv_product = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                  , null=False,verbose_name='net net lsv product')
    lp_nsv_product = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                  , null=False,verbose_name='lp nsv product')
    nn_change_percent = models.DecimalField(max_digits=30, decimal_places=15, default=0.0\
                                  , null=False,verbose_name='lp nsv product')
    
    class Meta:
        '''Meta Class For Model Saved Scenario.'''
        verbose_name = "Model Pricing Scenario Overall Summery"
        db_table = 'base_and_simulated_pricing_scenario_view'
        ordering = ["-nsv_new"]
        if hasattr(settings, 'PRICING_DATABASE_SCHEMA'):
            db_table = db_table_format('base_and_simulated_pricing_scenario',settings.PRICING_DATABASE_SCHEMA)

    def __str__(self):
        """ Returns Scenario Promotion Save name"""

        return f"{self.pricing_saved_scenario.id}"
    
class CoeffMap(models.Model):
    pricing_meta_model = models.ForeignKey(PricingMetaModel , related_name="coeff_map" \
                                    ,on_delete=models.CASCADE,verbose_name='Pricing Scenario fk')
    wmape = models.DecimalField(max_digits=30, decimal_places=2,verbose_name='WMAPE')
    rsq = models.DecimalField(max_digits=30, decimal_places=2,verbose_name='RSQ')
    parameters = models.CharField(max_length=250,verbose_name="parameters", default='')
    parameters_map = models.CharField(max_length=250,verbose_name="parameters", default='')

    class Meta:
        '''Meta Class For Model Coefficient.'''
        verbose_name = "Model Coefficient"
        db_table = 'model_coefficient_map'
        if hasattr(settings, 'DATABASE_SCHEMA'):
            db_table = db_table_format('model_coefficient_map')
            
class UploadedFile(models.Model):
    file = models.FileField(upload_to='uploads/')

class uploadfile(models.Model):
    file = models.FileField(upload_to='uploads/')
    slug = models.SlugField(blank=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.file.name)  # Use a suitable field for slug generation
        super().save(*args, **kwargs)
    # name = models.CharField(max_length=100)