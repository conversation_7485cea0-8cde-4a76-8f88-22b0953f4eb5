""" Optimizer API View"""
import structlog
import ast
from django.http import JsonResponse
from django.http import HttpR<PERSON>ponse, HttpResponseBadRequest
from rest_framework.pagination import PageNumberPagination
from django.db import transaction
from rest_framework import viewsets
from drf_spectacular.utils import (
    extend_schema,
    OpenApiParameter,
    OpenApiExample,
)
from apps.promo.promo_optimizer.constants import LOAD_CONSTANTS,MEAN_CONSTANTS,PERCENT_CONSTANTS
from apps.promo.promo_scenario_planner import (unit_of_work as sp_uow)
from core.generics import (oauth_token_validate,#pylint: disable=E0401
                            resp_utils,
                            search,
                            api_handler,
                            exceptions,
                            excel
                            )

from apps.common import (serializers,#pylint: disable=E0401
                        unit_of_work as uow,
                        services as cmn_serv
                    )
from . import services, unit_of_work as opt_uow,serializers as opt_ser

logger = structlog.get_logger(__name__)

class OptimizerScenario(viewsets.GenericViewSet):
    serializer_class = serializers.ScenarioPromotionSerializer
    filter_backends = [search.CustomSearchFilter]
    pagination_class = PageNumberPagination
    page_size_query_param = 'page_size'
    page_query_param = 'page'
    lookup_field = 'saved_scenario_id'
    lookup_url_kwarg = 'saved_id'

    def get_queryset(self):
        return services.get_scenario(
                        opt_uow.OptimizerScenarioPromotionUnitOfWork(transaction),
                        scenario_id= self.request.parser_context['kwargs'].get('saved_id',None),
                        user=self.request._user
                    ).order_by('-id')

    @extend_schema(
        summary="Optimizer Scenario",
        description="API end point that serves the Optimizer Scenario.",
        request=opt_ser.OptimizerRequestSerializer,
        responses=opt_ser.OptimizerRequestAndResponseSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @api_handler.API_REQUEST_QUERY_HANDLER
    @resp_utils.validate_input_parameters
    @resp_utils.handle_response
    
    def post_optimize_scenario(self,request):
        _obj = {
                'user_id':request._user['user_id'],
                'optimizer_data':request.data,
                'method':'POST'
            }
        if not _obj['optimizer_data']:
            raise HttpResponseBadRequest("Pass Valid Data")

        logger.bind(method_name="post_simulate_scenario", app_name="Optimizer")
        response = services.post_optimize_scenario(uow.MetaUnitOfWork(transaction),
                                        uow.ItemMapUnitOfWork(transaction),
                                        uow.RetailerPPGMappingUnitOfWork(transaction),
                                        opt_uow.PromotionLevelsUnitOfWork(transaction),
                                        uow.BaseAndPromotionLevelDetailUnitOfWork(transaction),
                                        _obj,
                                        request._user
                                        )
        return response

    @extend_schema(
        summary="Get Optimizer Scenario constraints Based on meta ids",
        description="API end point that serves the Optimizer constraints.",
        request=serializers.WeeklyConstraintsSerializer,
        responses=opt_ser.OptimizerListResponseSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @api_handler.API_REQUEST_QUERY_HANDLER
    @resp_utils.validate_input_parameters
    @resp_utils.handle_response

    def get_scenario_constraints(self,request):
        data = request.data
        logger.bind(method_name="get_scenario", app_name="Optimizer")

        # old function
        # response = services.get_optimizer_scenario_constraints(
        #     data,
        #     opt_uow.OptimizerSavedScenarioUnitOfWork(transaction),
        #     SavedScenarioUnitOfWork(transaction),
        #     opt_uow.PromotionLevelsUnitOfWork(transaction),
        #     request._user
        # )

        response = cmn_serv.get_weekly_constraints(
            data,        
            opt_uow.OptimizerSavedScenarioUnitOfWork(transaction),
            sp_uow.SavedScenarioUnitOfWork(transaction),
            uow.MetaUnitOfWork(transaction),
            opt_uow.PromotionLevelsUnitOfWork(transaction),
            uow.RetailerPPGMappingUnitOfWork(transaction),          
            request._user
        )
        return response


    @extend_schema(
        summary="Update scenario Based on saved id",
        description="API end point that serves the scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        responses=opt_ser.OptimizerUpdatedResponseSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @api_handler.API_REQUEST_QUERY_HANDLER
    @resp_utils.validate_input_parameters
    @resp_utils.handle_response
   
    def update_scenario(self,request):
        logger.bind(method_name="update_scenario", app_name="Optimizer")
        response = services.put_scenario(
            self.get_object(), request.data,\
            opt_uow.OptimizerSavedScenarioUnitOfWork(transaction),
            request._user
        )
        return response

    @extend_schema(
        summary="Delete scenario Based on saved id",
        description="API end point that serves the scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        responses="Deleted Successfully"
    )
    
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_utils.handle_response
    def delete_scenario(self,request):
        logger.bind(method_name="delete_scenario", app_name="Scenario Planner")
        
        response = services.delete_scenario(
            self.get_object().id,
            request._user,
            opt_uow.OptimizerSavedScenarioUnitOfWork(transaction)
        )
        return response

    @extend_schema(
        summary="Save scenario Based on saved id",
        description="API end point that serves saving scenario.",
        responses=opt_ser.OptimizerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @api_handler.API_REQUEST_QUERY_HANDLER
    @resp_utils.validate_input_parameters
    @resp_utils.handle_response
    
    def save_scenario(self,request):
        logger.bind(method_name="save_scenario", app_name="Scenario Planner")

        response = services.post_scenario(
            request.data, opt_uow.OptimizerSavedScenarioUnitOfWork(
                transaction),
                request._user
        )
        return response

    @extend_schema(
        summary="Load Saved scenario Based on saved id",
        description="API end point that serves the Loading Saved scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        responses=opt_ser.OptimizerSavedResponseSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_utils.handle_response
    def load_scenario(self,_request):
        logger.bind(method_name="load_scenario", app_name="Optimizer Scenario")
        serializer = self.serializer_class(
            [self.get_object()], many=True)
        param = _request.query_params.get('view_by',None)
        brand_nsv = []
        brand_count ={}
        if param not in ['undefined', None, ''] :
            for entry in serializer.data[0]['data'][0]['data']['data']:
                parameter = entry[param]
                found = False
                for item in brand_nsv:
                    if item['ppg']['ppg'] == parameter:
                        found = True
                        for key in LOAD_CONSTANTS:
                            item[key] = {
                                'base': item[key]['base'] + entry["base"]["total"][key],
                                'simulated': item[key]['simulated'] + entry["simulated"]["total"][key]
                            }
                            brand_count[parameter] += 1
                        break
                if not found:
                    new_item ={}
                    new_item['ppg'] = {'ppg': parameter}
                    new_item['retailer'] = {'retailer':entry['account_name']}
                    for key in LOAD_CONSTANTS:
                        new_item[key] = {
                            'base': entry["base"]["total"][key],
                            'simulated': entry["simulated"]["total"][key]
                        }
                    brand_nsv.append(new_item)
                    brand_count[parameter] = 1
            for item in brand_nsv:
                for key in MEAN_CONSTANTS:
                    item[key]['base'] /= brand_count[parameter]
                    item[key]['simulated'] /= brand_count[parameter]
                for key in PERCENT_CONSTANTS:
                    item[key]['base'] = (item[key]['base'] / brand_count[parameter]) * 100
                    item[key]['simulated'] = (item[key]['simulated'] / brand_count[parameter]) * 100
                for key in LOAD_CONSTANTS:
                    item[key]['converted_difference'] = item[key]['simulated'] - item[key]['base']
                    item[key]['percent'] =  (item[key]['converted_difference'] / item[key]['base']) * 100 if item[key]['base'] != 0 else 0
                    if item[key]['converted_difference'] < 0:
                        item[key]['arrow'] = 'carret-down'
                        item[key]['color'] = 'red'
                    else:
                        item[key]['arrow'] = 'carret-up'
                        item[key]['color'] = 'green'
                item['count'] = item.pop('no_of_leaflet_count')
                item['retailer_sales'] = item.pop('total_rsv_w_o_vat')
                item['units'] = item.pop('vol_on_deal_percent')
        data = {
            'data': serializer.data,
            'table_data':brand_nsv
        }

        return data
    

    @extend_schema(
        summary="Comapare scenario Based on saved id",
        description="API end point that serves the Comapare scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_ids",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value="[1]",
                    )
                ],
            )
        ],
        responses=opt_ser.OptimizerSavedResponseSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_utils.handle_response
    def compare_scenario(self,request,*_args,**_kwargs):
        logger.bind(method_name="compare_scenario", app_name="Optimizer Scenario")
        saved_ids = ast.literal_eval(request.query_params.get('saved_ids',[]))
        response = services.compare_scenario(
            opt_uow.OptimizerScenarioPromotionUnitOfWork(transaction),
            saved_ids
        )
        serializer = serializers.ScenarioPromotionSerializer(
            response, many=True)      
        return serializer.data

    @extend_schema(
        summary="Download optimizer  output",
        description="Download optimizer  output",
        request=opt_ser.OptimizerRequestAndResponseSerializer,
        responses="optimizer output excel"
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_utils.handle_response
    
    def download(self,request,*_args,**_kwargs):
        logger.bind(method_name="download", app_name="Optimizer Scenario")

        filename = 'promo_optimizer.xlsx'
        response = HttpResponse(
            excel.download_excel_optimizer(request.data['data']),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename=%s' % filename
        return response
    
    @extend_schema(
        summary="Share Scenario",
        description="API end point that serves the saved scenario."
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_utils.handle_response
    def share_optimizer_scenario(self,request):
        logger.bind(method_name="get_scenario", app_name="Optimizer")
        response = cmn_serv.share_scenario(request.data\
                                           ,opt_uow.OptimizerSavedScenarioUnitOfWork(transaction)\
                                           ,opt_uow.OptimizerScenarioPromotionUnitOfWork(transaction)\
                                            ,request._user\
                                            ,scenario_type='optimizer')

        return response
 
class OptimizerScenarioList(viewsets.GenericViewSet):
    serializer_class = serializers.ScenarioSavedList
    filter_backends = [search.CustomSearchFilter]
    search_fields  = ['name','status_type','scenario_type','promo_type']
    pagination_class = PageNumberPagination
    page_size_query_param = 'page_size'
    page_query_param = 'page'
    lookup_field = 'id'
    lookup_url_kwarg = 'saved_id'

    def get_queryset(self):
        return services.get_scenario(
                        opt_uow.OptimizerSavedScenarioUnitOfWork(transaction),
                        user=self.request._user,
                        scenario_view=self.request.query_params.get('scenario_view',None)
                    ).order_by('-id')

    @extend_schema(
        summary="Get Saved scenario Based on saved id",
        description="API end point that serves the scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        responses=opt_ser.OptimizerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_utils.handle_response
    def get_saved_scenario(self,request):
        logger.bind(method_name="get_saved_scenario", app_name="Scenario Scenario")
        saved_id = request.parser_context['kwargs'].get('saved_id',None)
        status_type = request.query_params.get('status_type',None)
        query_set = self.get_queryset()
        
        if status_type and status_type in ['active','completed']:
            query_set = query_set.filter(status_type=status_type)
        if saved_id:
            query_set = [self.get_object()]
        
    
        page = self.paginate_queryset(query_set)
        if page is not None:
            serializer = self.serializer_class(page, many=True)
            return {'data':serializer.data,'count':query_set.count()}

        serializer = self.serializer_class(query_set, many=True)
   
        return serializer.data

class OptimizerSearchAPI(viewsets.GenericViewSet):
    serializer_class = serializers.ScenarioSavedList
    filter_backends = [search.CustomSearchFilter]
    pagination_class = PageNumberPagination
    page_size_query_param = 'page_size'
    page_query_param = 'page'
    lookup_field = 'id'
    lookup_url_kwarg = 'optimizer_saved_id'

    @extend_schema(
    summary="Search  Data based on name/promotion type/status type/scenario type .",
    description="API end point that serves the Baseline Data based on name,promotion type,status type,.",
    parameters=[
        OpenApiParameter(
            name="Search  Data based on name/promotion type/status type/scenario type ",
            description="Filter by name/promotion type/status type/scenario type",
            required=True,
            type=str,
            examples=[
                OpenApiExample(
                    "Example 1",
                    summary="Filter by name/promotion type/status type/scenario type.",
                    description="name/promotion type/status type/scenario type should be string type.",
                    value=1,
                )
            ],
        )
    ],
    responses=opt_ser.OptimizerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @api_handler.API_REQUEST_QUERY_HANDLER
    @resp_utils.validate_input_parameters
    @resp_utils.handle_response
    def search(self,request,*_args,**_kwargs): 

        param = request.query_params.get('q','')
        if not param:
            raise exceptions.MissingRequestParamsError("q", param)
        logger.bind(method_name="search", app_name="Optimizer")
        response = services.search_scenario(
            opt_uow.OptimizerSavedScenarioUnitOfWork(transaction),
            query_params=request.query_params
        )

        serializer = serializers.ScenarioSavedList(
            response, many=True)
        return serializer.data

class PromotionLevelAPI(viewsets.GenericViewSet):
    serializer_class = opt_ser.PromotionLevelSerializer
    filter_backends = [search.CustomSearchFilter]
    search_fields  = ['promotion_level_user','level_type','is_updated']
    pagination_class = PageNumberPagination
    page_size_query_param = 'page_size'
    page_query_param = 'page'
    lookup_field = 'id'
    lookup_url_kwarg = 'saved_id'

    def get_queryset(self):
        return services.get_promo_levels(
                        opt_uow.PromotionLevelsUnitOfWork(transaction),
                    ).order_by('-id')

    @extend_schema(
        summary="Get Promotion Levels data",
        description="API end point that serves the promotion levels data.",
        request=serializers.WeeklyConstraintsSerializer,
        responses=serializers.WeeklyConstraintsResponseSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_utils.handle_response
    def get_promo_levels(self,request):
        logger.bind(method_name="get_scenario", app_name="Optimizer")
        response = services.get_promotion_levels_data(
            uow.MetaUnitOfWork(transaction),
            opt_uow.PromotionLevelsUnitOfWork(transaction),
            uow.RetailerPPGMappingUnitOfWork(transaction),
            request._user,
            access_directly=False,
            is_initial=True,
            bpuow=uow.BaseAndPromotionLevelDetailUnitOfWork(transaction),
            is_optional=True
        )
        return response
    
    @extend_schema(
        summary="Get Promotion Levels data",
        description="API end point that serves the promotion levels data.",
        request=serializers.WeeklyConstraintsResponseSerializer,
        responses=opt_ser.OptimizerSavedResponseSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_utils.handle_response
    def save_promotion_level(self,request):
        logger.bind(method_name="get_scenario", app_name="Optimizer")
        response = services.save_promotion_level(
            opt_uow.PromotionLevelsUnitOfWork(transaction),
            request.data,
            request._user
        )
        return response
    
    @extend_schema(
        summary="Get Promotion Levels data",
        description="API end point that serves the promotion levels data.",
        request=serializers.WeeklyConstraintsResponseSerializer,
        responses=opt_ser.OptimizerSavedResponseSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_utils.handle_response
    def update_promotion_level(self,request):
        logger.bind(method_name="get_scenario", app_name="Optimizer")
        response = services.update_promotion_level(
            opt_uow.PromotionLevelsUnitOfWork(transaction),
            request.data,
        )
        return response
