from functools import wraps

from django.shortcuts import HttpResponse

from apps.common import params
from apps.promo.promo_optimizer import params as opt_params
from apps.pricing.pricing_common import params as pricing_params
from apps.promo.promo_scenario_planner import params as sp_params
from utils import iterateEnum


def API_REQUEST_QUERY_HANDLER(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        index = kwargs.get('index',0)
        kwargs['optional'] = ''
        try:
            if 'meta_data' in args[index].META.get('PATH_INFO'):
                kwargs['query_params_format'] = iterateEnum(params.GetModelDataParam)
            if 'search' in args[index].META.get('PATH_INFO'):
                kwargs['query_params_format']  = iterateEnum(params.SearchParam)
                kwargs['optional'] = params.SearchParam.optional_param()
            if 'scenario/list' in args[index].META.get('PATH_INFO'):
                kwargs['query_params_format']  = iterateEnum(params.GetScenarioConstraintsParam)
            if 'scenario/simulate' in args[index].META.get('PATH_INFO'):
                kwargs['query_params_format']  = iterateEnum(sp_params.ScenarioPlannerPayload)
                kwargs['method'] = sp_params.ScenarioPlannerPayload.get_method()
            if 'scenario/save' in args[index].META.get('PATH_INFO'):
                kwargs['query_params_format']  = iterateEnum(sp_params.ScenarioPlannerSave)
                kwargs['method'] = sp_params.ScenarioPlannerSave.get_method()
                kwargs['optional'] = sp_params.ScenarioPlannerSave.optional_param()
            if 'optimizer/save' in args[index].META.get('PATH_INFO'):
                kwargs['query_params_format']  = iterateEnum(opt_params.OptimizerSave)
                kwargs['method'] = opt_params.OptimizerSave.get_method()
                kwargs['optional'] = opt_params.OptimizerSave.optional_param()
            if 'update/' in args[index].META.get('PATH_INFO'):
                kwargs['query_params_format']  = iterateEnum(opt_params.OptimizerUpdate)
                kwargs['method'] = opt_params.OptimizerUpdate.get_method()
                kwargs['optional'] = opt_params.OptimizerUpdate.optional_param()
            if 'optimizer/list' in args[index].META.get('PATH_INFO'):
                kwargs['query_params_format']  = iterateEnum(opt_params.OptimizerInputConstraints)
                kwargs['method'] = opt_params.OptimizerInputConstraints.get_method()
            if 'optimizer/optimize' in args[index].META.get('PATH_INFO'):
                kwargs['query_params_format']  = iterateEnum(opt_params.Optimize)
                kwargs['method'] = opt_params.Optimize.get_method()
            if 'generic/get_weekly_constraints' in args[index].META.get('PATH_INFO'):
                kwargs['query_params_format']  = iterateEnum(opt_params.OptimizerWeeklyConstraints)
                kwargs['method'] = opt_params.OptimizerWeeklyConstraints.get_method()
            if 'generic/get_group_promoted_data' in args[index].META.get('PATH_INFO'):
                kwargs['query_params_format']  = iterateEnum(opt_params.OptimizerWeeklyConstraints)
                kwargs['method'] = opt_params.OptimizerWeeklyConstraints.get_method()
            if 'set_pricing/save' in args[index].META.get('PATH_INFO'):
                kwargs['query_params_format']  = iterateEnum(pricing_params.PricingPayload)
                kwargs['method'] = pricing_params.PricingPayload.get_method()
                kwargs['optional'] = pricing_params.PricingPayload.optional_param()
            if 'common/save' in args[index].META.get('PATH_INFO'):
                kwargs['query_params_format']  = iterateEnum(pricing_params.PricingPayload)
                kwargs['method'] = pricing_params.PricingPayload.get_method()
                kwargs['optional'] = pricing_params.PricingPayload.optional_param()
            if 'common/post_simulate_scenario' in args[index].META.get('PATH_INFO'):
                kwargs['query_params_format']  = iterateEnum(pricing_params.PricingPayload)
                kwargs['method'] = pricing_params.PricingPayload.get_method()
                kwargs['optional'] = pricing_params.PricingPayload.optional_param()
            if 'set_pricing/post_simulate_scenario' in args[index].META.get('PATH_INFO'):
                kwargs['query_params_format']  = iterateEnum(pricing_params.PricingPayload)
                kwargs['method'] = pricing_params.PricingPayload.get_method()
                kwargs['optional'] = pricing_params.PricingPayload.optional_param()
            if 'pricing_target/save' in args[index].META.get('PATH_INFO'):
                kwargs['query_params_format']  = iterateEnum(pricing_params.PricingPayload)
                kwargs['method'] = pricing_params.PricingPayload.get_method()
                kwargs['optional'] = pricing_params.PricingPayload.optional_param()
            if 'pricing_target/publish' in args[index].META.get('PATH_INFO'):
                kwargs['query_params_format']  = iterateEnum(pricing_params.CustomerPayload)
                kwargs['method'] = pricing_params.CustomerPayload.get_method()
                kwargs['optional'] = pricing_params.CustomerPayload.optional_param()

            return func(*args, **kwargs)

        except TypeError:
            return HttpResponse(f"Validation Failing.Please pass valid params.")
        except Exception as e:
            return HttpResponse(f"Something went wrong while hanling api request.See errors :{str(e)}",status=406)
    return wrapper
