from apps.promo.promo_optimizer.tests.factories import (save_completed_optimizer_scenario_with_data_for_api,
                                optimizer_payload,
                                opt_instance
                                )




OPTIMIZER_OUTPUT_PAYLOAD = opt_instance.get('OPTIMIZER_OUTPUT_PAYLOAD')
OPTIMIZER_LIST_RESPONSE = opt_instance.get('OPTIMIZER_LIST_RESPONSE')

STATUS_MASTER_NAMES = ((1, "Saved"), (2, "Submitted"), (3, "Completed"))

TYPE_OF_PROMO=['single','joint','all_brand']

OPTIMIZER_API_PAYLOAD = optimizer_payload

OPTIMIZER_API_SAVE_PAYLOAD = save_completed_optimizer_scenario_with_data_for_api
SUMMARY_COLUMN=[
            "Ret_idx",
            "PPG_idx",
            "Type_of_Promo",
            "Retailer",
            "PPG",
            "Promo_Type",
            "Avg_units_per_Week",
            "TPR",
            "Leaflet_flag",
            "Display_flag",
            "Ad_without_price_flag",
            "Back_page_flag",
            "Bonus_flag",
            "Front_page_flag",
            "Logo_flag",
            "Pas_flag",
            "TE_perweek",
            "ListPrice_perweek",
            "COGS_perweek",
            "maxWeek",
            "minWeek",
            "minWeek_edlp",
            "maxWeek_edlp",
            "GSV_perweek",
            "is_promo",
            "num_ppgs",
            "ppg",
            "Optimized_Weeks",
            "Optimized_TE_perweek",
            "OptWeek_Check",
            "MAC_Opt",
            "MAC_Min",
            "MAC_Max",
            "OptMac_Check"
]

