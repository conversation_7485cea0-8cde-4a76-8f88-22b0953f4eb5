""" Optimizer Test Conftest"""
import sys
from pathlib import Path
import pytest
from pytest_factoryboy import register
from django.contrib.auth import get_user_model
from rest_framework.authtoken.models import Token
from ...user.tests import factories as user_fact
from ... user import serializers as user_serializer,repository as user_repo
from .. import repository
from . import factories

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent.parent.parent
sys.path.append(str(BASE_DIR))

@pytest.fixture(scope="session")
def django_db_setup():
    import os
    from django.conf import settings
    settings.DATABASES['default'] = {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': os.path.join(os.path.join(BASE_DIR,'dbs'), 'testing_db1.sqlite3')
    }

# this fixture is used to temporarily create data in the model for testing

@pytest.fixture(scope="module")
def _django_data_setup(django_db_blocker):
    print("setup")
    with django_db_blocker.unblock():
        try:
            model_active_save_scenario_repo = repository.OptimizerSavedScenarioRepository()
            model_active_save_scenario_repo.add(factories.save_active_optimizer_scenario)

            model_active_save_scenario_repo = repository.OptimizerSavedScenarioRepository()
            model_active_save_scenario_repo.add(factories.save_completed_optimizer_scenario)
            
            model_active_save_scenario_repo = repository.OptimizerPromotionRepository()
            model_active_save_scenario_repo\
                .add(factories.save_active_optimizer_scenario_partial_data)

            model_completed_save_scenario_repo = repository.OptimizerPromotionRepository()
            model_completed_save_scenario_repo\
                .add(factories.save_completed_optimizer_scenario_with_data)
            user_group_repo = user_repo.UserGroupRetailerRepository()
            user_group_repo.add(user_fact.user_groups)

        except Exception as _e:
            print("Skip adding data.")

@pytest.fixture
@pytest.mark.django_db
def auth_factory():
    print('************** user factory *************')

    def auth_info():

        user = get_user_model().objects.get_or_create(email='<EMAIL>')[0]
        token, _created = Token.objects.get_or_create(user=user)# pylint: disable=no-member
        user_ser = user_serializer.UserSerializer(token.user)
        headers = {'HTTP_AUTHORIZATION': f'Token {token.key}'}
        return {
            'token': token.key,
            'user': user_ser.data,
            'headers': headers
        }

    return auth_info

@pytest.fixture
@pytest.mark.django_db
def auth(auth_factory):

    print('************** user info *************')
    return auth_factory()

register(factories.SaveActiveOptimizerScenarioFactory,
        '_saosf',
        id= 1,
        scenario_type =  "optimizer",
        name = "optimizer save",
        promo_type="single",
        status_type="active",
        comments = ""
        )

register(factories.SaveCompletedOptimizerScenarioFactory,
        '_scosf',
        id= 2,
        scenario_type =  "optimizer",
        name = "optimizer save",
        promo_type="single",
        status_type="completed",
        comments = ""
        )

register(factories.SaveActiveOptimizerScenarioWoDataFactory,
        '_scoswodf',
        id= 1,
        saved_scenario_id=1,
        input_constraints={},
        data=[]
        )

register(factories.SaveCompletedOptimizerScenarioWithDataFactory,
        '_scoswdf',
        id= 2,
        saved_scenario_id=2,
        input_constraints = factories.input_constraints,
        data= factories.data
    )
