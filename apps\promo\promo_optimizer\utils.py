
""" Optimizer Utils"""
from functools import reduce
import numpy as np
import copy
from django.db import transaction
import pandas as pd
from rest_framework import serializers
from apps.common import (services as sc,unit_of_work as uow,utils as cmn_utils)
from apps.promo.promo_optimizer import generic as opt_generic
from core.generics import constants as CONST
from .serializers import OptimizerSerializer

def get_model_coeff_data(_data,ppg):
    coeff_map = list(sc.get_df_from_query(
                        uow.MetaUnitOfWork(transaction),
                        [CONST.ModelCoeffMap],
                        _data['account_name'],
                        [ppg],
                        start_index=2
                        ))[0]
    return coeff_map

def update_national_promo_internally(data_df,national_data):
    
    if national_data:
        grouped_df = data_df.groupby(['Retailer','PPG'])
        for key,_item in grouped_df:
            filtered_promotion_level_data = list(filter(lambda x:(sorted(x['product_group'])\
                                        ==sorted(opt_generic.format_ppg(key[1]))),national_data))
            if filtered_promotion_level_data:
                filtered_promotion_level_data_weekly = filtered_promotion_level_data[0]['weekly']
                for  _pl in filtered_promotion_level_data_weekly:
                    _index = _pl['week']-1
                    if _pl['promotion_levels']:
                        data_df.loc[(data_df['PPG']==key[1]) & (data_df['Week']==_index+1),'flag_promotype_all_brand'] = _pl['flag_promotype_all_brand']
    
    return data_df
     
def get_joint_all_brand_promo_from_mapping(_data,
                                           df_dict:list=None,
                                            input_data_dict:dict=None,
                                            scenario_type='optimizer',
                                            rpt=None,
                                            constraints=None,
                                            main_df=pd.DataFrame(),
                                            rpmuow=None,
                                            national_list=None,
                                            national_data=None,
                                            basedata_df=pd.DataFrame(),
                                            one_shot_ppg=None
                                            ):
    ppg = opt_generic.format_ppg2(_data['product_group'])
    basedata_df = cmn_utils.format_headers(CONST.DATA_VALUES,CONST.DATA_HEADER,basedata_df)
    model_data_all = basedata_df.loc[basedata_df['PPG']==ppg]

    coeff_map = get_model_coeff_data(_data,ppg)
    
    jnt_and_allbrand_global_dict = {}
    jnt_and_allbrand_global_dict[_data['product_group']]={'data':{},'joint_ppg_data':[],'all_brand_ppg_data':[]}
    
    ###### HANDLING JOINT PROMO ######

    jnt_ppg_map = coeff_map.loc[(coeff_map['Coefficient'].str.contains("Tactic_JP", case=False))]

    # ab_ppg = coeff_map.loc[coeff_map['Coefficient Old'].str.contains("Tactic_All_Brand", case=False),'Coefficient Old']
    _jnt_df = model_data_all.loc[(model_data_all['promo_present']>0) & (model_data_all['flag_promotype_all_brand']==0)]
    jnt_ab_agg_overal_df = model_data_all.groupby(['Retailer','PPG']).agg({'promo_present':'sum', 
                                                                     'TPR': 'sum'}).reset_index()
    jnt_ab_agg_overal_df = jnt_ab_agg_overal_df.loc[(jnt_ab_agg_overal_df['promo_present']==0) | (jnt_ab_agg_overal_df['TPR']==0)].reset_index(drop=True)
   
    _jnt_df = _jnt_df.reset_index()
    md_col = [mf for mf in _jnt_df.columns if 'joint_promo' in mf]
    _jmdf = _jnt_df[(_jnt_df[md_col] > 0).any(axis=1)]
    joint_one_shot_ppg = list(filter(lambda x:x[2].lower()=='joint',one_shot_ppg))
    all_brand_one_shot_ppg = list(filter(lambda x:x[2].lower().replace(' ','_')=='all_brand',one_shot_ppg))
    single_one_shot_ppg = list(filter(lambda x:x[2].lower()=='single',one_shot_ppg))
    
    
    
    if (jnt_ppg_map.shape[0] and _jnt_df.shape[0] and _jmdf.shape[0] and (not jnt_ab_agg_overal_df.shape[0] or one_shot_ppg)):

        jnt_ppgs_tuple = list(jnt_ppg_map.loc[jnt_ppg_map['Coefficient'].str.contains("Tactic_JP", case=False)\
        ,['Coefficient','Coefficient New']].itertuples(index=False,name=None))
        coeff_dict = dict()
        coeff_dict['COMB_JOINT_WEEK'] = []
        
        for jp,flag in jnt_ppgs_tuple:

            ppg_name = jp.replace('Tactic_JP_','')
            rest_model_data = basedata_df.loc[basedata_df['PPG']==ppg_name]
            rest_coeff_map = get_model_coeff_data(_data,ppg_name)
            rest_jnt_agg_overal_df = rest_model_data.groupby(['Retailer','PPG']).agg({'promo_present':'sum', 
                                                                     'TPR': 'sum'}).reset_index()
            rest_jnt_agg_overal_df = rest_jnt_agg_overal_df.loc[(rest_jnt_agg_overal_df['promo_present']==0) | (rest_jnt_agg_overal_df['TPR']==0)].reset_index(drop=True)
            rest_map = rest_coeff_map.loc[rest_coeff_map['Coefficient'].str.contains("Tactic_JP", case=False)]
            if rest_map.shape[0]:
                rest_joint_weeks = []
                rest_jnt_ppgs_tuple = list(rest_map.loc[rest_map['Coefficient'].str.contains("Tactic_JP", case=False)\
                                ,['Coefficient','Coefficient New']].itertuples(index=False,name=None))
                rest_flag = list(filter(lambda x:x[0]==f'Tactic_JP_{opt_generic.format_ppg2(ppg)}',rest_jnt_ppgs_tuple))
                if rest_flag:
                    rest_joint_weeks = opt_generic.get_joint_promoted_weeks(rest_model_data,rest_flag[0][1])
                if not rest_joint_weeks:
                    continue
                
                if rest_jnt_agg_overal_df.shape[0] or one_shot_ppg:
                    _jppg = ' & '.join([ppg,ppg_name])
                    one_shot_ppg_exists = opt_generic.habdle_one_shot_ppg(opt_generic.format_ppg(_jppg),one_shot_ppg,type_of_promo='joint',ppg1=ppg)
                    if not one_shot_ppg_exists and rest_jnt_agg_overal_df.shape[0]:
                        if df_dict:
                        
                            df_dict = cmn_utils.generate_promotion(df_dict,
                                            type_of_promo='joint',
                                            ppg=opt_generic.format_ppg(_jppg),
                                            weeks=rest_joint_weeks,
                                            scenario_type=scenario_type,
                                            is_standalone=True,
                                            _jflags = flag.lower()
                                            ,one_shot_ppg=bool(one_shot_ppg_exists)
                                            )
                        continue
            
                coeff_dict[flag] = opt_generic.format_ppg(ppg_name)
                coeff_dict['PPG'] = opt_generic.format_ppg(ppg)
                coeff_dict['COMB_JOINT_WEEK'].extend(rest_joint_weeks)
            continue
        # if ppg == opt_generic.format_ppg('DREAMIES_55_/_60G'):
        #     breakpoint()
        # if ppg == 'DREAMIES_55_/_60G':
        #     breakpoint()
        if coeff_dict.get('COMB_JOINT_WEEK'):
            
            stand_alone_weeks = list(map(lambda x:x['week'],(list(filter(lambda x:x.get('is_standalone'),df_dict)))))
            
            new_coeff_map_df = pd.DataFrame([coeff_dict])
            flag_col = [f for f in new_coeff_map_df.columns if 'Flag' in f]
            for _i,val in enumerate(flag_col):
                _jnt_df.loc[_jnt_df[val]>0,val]=new_coeff_map_df[val].unique()[0]
                _number = val[-1]
                _jnt_df.loc[_jnt_df[val]==new_coeff_map_df[val].unique()[0],f'Joint_Promo_Flags_{_number}']=val.lower()
            _jnt_df = _jnt_df.fillna(0)
            
            jnt_promo_flags =  [f for f in _jnt_df.columns if 'Joint_Promo_Flags' in f]
            _fl_list = []
            for _fl in jnt_promo_flags:
               _wks =  _jnt_df.loc[_jnt_df[_fl]!=0,'Week'].tolist()
               _fl_list.extend(_wks)
            
            exclude_weeks = list(filter(lambda x:x not in _fl_list,stand_alone_weeks))
            _jnt_df = _jnt_df.loc[~_jnt_df['Week'].isin(exclude_weeks)]
            _jnt_df=_jnt_df.assign(
            JOINT_PROMO_PPG=_jnt_df[flag_col+['PPG']].apply(
                lambda row: ' & '.join([opt_generic.format_ppg(str(each)) for each in row if each]),axis=1))
            _jnt_df=_jnt_df.assign(
            JOINT_PROMO_FLAGS=_jnt_df[jnt_promo_flags].apply(
                lambda row: ' & '.join([str(each) for each in row if each]),axis=1))
            jnt_agg_df = _jnt_df.groupby(['JOINT_PROMO_PPG','JOINT_PROMO_FLAGS'])['Week'].agg(list).reset_index()
            jnt_agg_df['NO_OF_SLOTS'] = jnt_agg_df['Week'].apply(lambda x:len(x))
            jnt_agg_df['TYPE_OF_PROMO'] = jnt_agg_df['JOINT_PROMO_PPG'].apply(lambda x:'joint' if len(x.split(' & '))> 1 else 'single')
            jnt_agg_df['IS_STANDALONE'] = False

            #### handling stand alone for joint ######
            
            single_df = jnt_agg_df.loc[jnt_agg_df['TYPE_OF_PROMO'].lower()=='single']
            if single_df.shape[0]:
                single_weeks = single_df['Week'].tolist()
                single_df = _jmdf.loc[_jmdf['Week'].isin(single_weeks[0])]
                
                if single_df.shape[0]:
                    single_df['Week'] = single_df['Week'].apply(lambda x:[x])
                    single_df['JOINT_PROMO_PPG'] = opt_generic.format_ppg(ppg)
                    single_df['NO_OF_SLOTS'] = 0
                    single_df['TYPE_OF_PROMO'] = 'joint'
                    single_df['Week'] = single_df['Week']
                    is_national = cmn_utils.is_national_promo_week(df_dict,single_df['Week'].values[0])
                    single_df['IS_STANDALONE'] = not is_national
   
                    jnt_agg_df =pd.concat([jnt_agg_df,single_df])

            jnt_agg_df = jnt_agg_df.loc[jnt_agg_df['TYPE_OF_PROMO']!='single'].reset_index()
            wk = list(set(new_coeff_map_df['COMB_JOINT_WEEK'].tolist()[0]))
            jnt_agg_df['REMAINING_STANDALONE_WEEK'] = ''
    
            if jnt_agg_df.loc[(jnt_agg_df['Week'].apply(lambda x:  all(y not in wk for y in x))) & (~jnt_agg_df['IS_STANDALONE'])].shape[0]:
                # Handling all  standalone weeks
                jnt_agg_df.loc[(jnt_agg_df['Week'].apply(lambda x:  all(y not in wk for y in x))) & (~jnt_agg_df['IS_STANDALONE']),'IS_STANDALONE'] = True
            else:
                # Handling specific standalone week
                
                jwk = jnt_agg_df[~jnt_agg_df['IS_STANDALONE']]['Week'].tolist()
                
                if jwk:
                    jwk = list(set(reduce(lambda x,y: x+y, jwk)))
                    _wk=list(filter(lambda x:x not in wk,jwk))
                    
                    jnt_agg_df.loc[(jnt_agg_df['Week']\
                                    .apply(lambda x:  any(y  in _wk for y in x))) & (~jnt_agg_df['IS_STANDALONE'])\
                                    ,'REMAINING_STANDALONE_WEEK'] = jnt_agg_df.loc[(jnt_agg_df['Week']\
                                  .apply(lambda x:  any(y  in _wk for y in x))) & (~jnt_agg_df['IS_STANDALONE']),'REMAINING_STANDALONE_WEEK'].apply(lambda x:_wk)
            
            for _id in range(jnt_agg_df.shape[0]):
                type_of_promo = jnt_agg_df.loc[_id,'TYPE_OF_PROMO']
                promo_ppg = cmn_utils.get_ppg(jnt_agg_df.loc[_id,'JOINT_PROMO_PPG'],rpt)
                promo_weeks = jnt_agg_df.loc[_id,'Week']
                promo_slots = jnt_agg_df.loc[_id,'NO_OF_SLOTS']
                promo_flag = jnt_agg_df.loc[_id,'JOINT_PROMO_FLAGS']
                is_standalone = jnt_agg_df.loc[_id,'IS_STANDALONE']
                formated_ppg = opt_generic.format_ppg3(opt_generic.format_ppg2(promo_ppg))
                one_shot_ppg_exists = opt_generic.habdle_one_shot_ppg_with_format(formated_ppg,one_shot_ppg,type_of_promo='joint',ppg1=ppg)
                filtered_r_p_pt = list(filter(lambda x:formated_ppg == x[1],rpt))
                if (not filtered_r_p_pt and not is_standalone) and  not one_shot_ppg_exists:
                    is_standalone = True
                rswk = jnt_agg_df.loc[_id,'REMAINING_STANDALONE_WEEK']
                promo_slots = 0 if is_standalone else promo_slots
                if df_dict:
                    # print(ppg, '-----------------------ppg is here-------------------------')
                    # print(promo_weeks)
                    # if 29 in promo_weeks:
                    #     if ppg == opt_generic.format_ppg('DREAMIES_55_/_60G'):
                    #         breakpoint()
                    #     if ppg == 'DREAMIES_55_/_60G':
                    #         breakpoint()                         
                    df_dict = cmn_utils.generate_promotion(df_dict,
                                        type_of_promo=type_of_promo,
                                        ppg=promo_ppg,
                                        weeks=promo_weeks,
                                        rswk=rswk,
                                        scenario_type=scenario_type,
                                        is_standalone=is_standalone,
                                        _jflags = promo_flag.lower(),
                                        one_shot_ppg=bool(one_shot_ppg_exists),
                                        ppg1=ppg
                                        )
               
                    _constraints_data = {'product_group':promo_ppg,'no_of_slots':promo_slots}
                    is_exclude = True
                    
                    if main_df.shape[0]:
                        constraints.update({
                            'mechanic':opt_generic.get_mech(df_dict,type=type_of_promo,ppg=ppg),
                            'promo_depth':opt_generic.get_avg_tpr(main_df),
                            'promo_type':type_of_promo,
                            'no_of_slots':promo_slots,
                            'is_joint':True,
                            'ppg':promo_ppg,
                            'ppg_belongs_to':opt_generic.format_ppg(ppg)
                        })
                 
                        if not is_standalone:
                            is_exclude = False
                            filtered_tactic_df = pd.DataFrame()
                            if filtered_r_p_pt:
                                filtered_tactic_df = sc.get_opt_tactic(rpmuow,filtered_r_p_pt)
                            tot_promo_min = len(promo_weeks)-3 if len(promo_weeks)>2 else 0
                            tot_promo_max = len(promo_weeks)+3
                            if filtered_tactic_df.shape[0]:
                                range_df = filtered_tactic_df.loc[filtered_tactic_df['PPG']==formated_ppg]
                                if range_df.shape[0]:
                                    tot_promo_min,tot_promo_max =cmn_utils.get_range_of_promo(range_df)
                    
                            param_compulsory_promo_weeks = list(filter(lambda x:x in promo_weeks,national_list))
                            tot_promo_min = tot_promo_min if tot_promo_min >=len(param_compulsory_promo_weeks)\
                                                                    else len(param_compulsory_promo_weeks)
                            constraints.update({
                            'param_compulsory_promo_weeks':param_compulsory_promo_weeks,
                            'tot_promo_max':tot_promo_max,
                            'tot_promo_min':tot_promo_min
                            })
                            _constraints_data = _get_serializer_data(constraints)
                            
                    
                    if not is_exclude:
                        input_data_dict[_data['product_group']]['data']['include'] = True
                        input_data_dict[_data['product_group']][f'{type_of_promo}_ppg_data'].append({'weeks':promo_weeks,                                                                           
                                                                                                    'is_standalone':is_standalone,
                                                                                                    **_constraints_data,
                                                                                                    'include':True
                                                                                                    })
                
    ####### HANDLING ALL BRAND PROMO #######
  
    all_brand_df = model_data_all.loc[(model_data_all['promo_present']>0) & (model_data_all['flag_promotype_all_brand']>0)]
    if (all_brand_df.shape[0] and (not jnt_ab_agg_overal_df.shape[0] or one_shot_ppg)):
        
        brand = all_brand_df['Brand'].values[0]
        brand_coeff_map = list(sc.get_df_from_query_by_brand(
                                                        uow.MetaUnitOfWork(transaction),
                                                        [CONST.ModelCoeffMap],
                                                        _data['account_name'],
                                                        [brand],
                                                        start_index=2
                                                        )
                                                    )[0]
        
        brand_model_data_all = basedata_df.loc[basedata_df['Brand']==brand]
        brand_model_data_all = update_national_promo_internally(brand_model_data_all,national_data)
        ab_brand_ppg_list = brand_model_data_all.loc[brand_model_data_all['flag_promotype_all_brand']>0,'PPG'].unique()
        # ab_brand_ppg_list = brand_coeff_map.loc[brand_coeff_map['Coefficient']\
        #                     .str.contains("Tactic_All_Brand", case=False),'PPG'].unique()
        
        
        all_brand_coeff_map_dict = dict()
        all_brand_coeff_map_dict['flag_promotype_all_brand'] = []
        all_brand_df['JOINT_FLAG'] = ''
        for _abppg1 in ab_brand_ppg_list:
        
            rest_coeff_all_brand_map = brand_coeff_map.loc[brand_coeff_map['PPG']==_abppg1]

            rest_model_data_all_brand = brand_model_data_all.loc[brand_model_data_all['PPG']==_abppg1]

            # rest_all_brand_map = rest_coeff_all_brand_map.loc[rest_coeff_all_brand_map['Coefficient']\
            #                     .str.contains("Tactic_All_Brand", case=False)]

            rest_model_data_all_brand = rest_model_data_all_brand.loc[(rest_model_data_all_brand['flag_promotype_all_brand']>0) \
                                                                    & (rest_model_data_all_brand['promo_present']>0)]
            rest_ab_agg_overal_df = rest_model_data_all_brand.groupby(['Retailer','PPG']).agg({'promo_present':'sum', 
                                                                'TPR': 'sum'}).reset_index()
            rest_ab_agg_overal_df = rest_ab_agg_overal_df.loc[(rest_ab_agg_overal_df['promo_present']==0) | (rest_ab_agg_overal_df['TPR']==0)].reset_index(drop=True)
            if (rest_model_data_all_brand.shape[0]):
                
                if rest_ab_agg_overal_df.shape[0] or one_shot_ppg:

                    _abjppg = ' & '.join([ppg,_abppg1])
                    _rdbdf = all_brand_df.loc[all_brand_df['Week'].isin(rest_model_data_all_brand.loc[rest_model_data_all_brand['flag_promotype_all_brand']>0]['Week'])]
                    _rbweeks = _rdbdf['Week'].tolist()
                    _flist = []
                    for _f in md_col:
                        if _rdbdf.loc[_rdbdf[_f]>0].shape[0]:
                            _flist.append(_f)
                    one_shot_ppg_exists = opt_generic.habdle_one_shot_ppg(opt_generic.format_ppg(_abjppg),one_shot_ppg,type_of_promo='all_brand')
                    if not one_shot_ppg_exists and rest_ab_agg_overal_df.shape[0]:
                        if df_dict:
                            
                                df_dict = cmn_utils.generate_promotion(df_dict,
                                                type_of_promo='all_brand',
                                                ppg=opt_generic.format_ppg(_abjppg),
                                                weeks=_rbweeks,
                                                scenario_type=scenario_type,
                                                is_standalone=True,
                                                _jflags=' & '.join(_flist).lower()
                                                ,one_shot_ppg=bool(one_shot_ppg_exists)
                                                )
                        continue
                all_brand_df.loc[all_brand_df['Week'].isin(rest_model_data_all_brand.loc[rest_model_data_all_brand['flag_promotype_all_brand']>0]['Week']),'PPG']=all_brand_df.loc[all_brand_df['Week'].isin(rest_model_data_all_brand.loc[rest_model_data_all_brand['flag_promotype_all_brand']>0]['Week']),'PPG'].apply(lambda row: ' & '.join(list(set((' & '.join([row,_abppg1]).split(' & '))))))
                # all_brand_df['PPG']=all_brand_df['PPG'].apply(lambda row: ' & '.join(list(set((' & '.join([row,_abppg1]).split(' & '))))))
                for _f in md_col:
                    
                    all_brand_df.loc[all_brand_df[_f]>0,'JOINT_FLAG'] = all_brand_df.loc[all_brand_df[_f]>0,'JOINT_FLAG'].apply(lambda row: ' & '.join(list(set((' & '.join([row,_f.lower()]).split(' & '))))).lower())
                    
            continue
        
        all_brand_df['JOINT_FLAG']=all_brand_df['JOINT_FLAG'].str[3:]
        new_allbrand_coeff_map_df = pd.DataFrame([all_brand_coeff_map_dict])
        new_allbrand_coeff_map_df['flag_promotype_all_brand'] = new_allbrand_coeff_map_df\
            ['flag_promotype_all_brand'].apply(lambda x:' & '.join(x))
        all_brand_df.loc[all_brand_df['flag_promotype_all_brand']>0,'flag_promotype_all_brand']\
            =new_allbrand_coeff_map_df['flag_promotype_all_brand'].unique()[0]
        all_brand_df.loc[all_brand_df['flag_promotype_all_brand']==new_allbrand_coeff_map_df\
            ['flag_promotype_all_brand'].unique()[0],f'All_Brand_Promo_Flags']='flag_promotype_all_brand'
        all_brand_df = all_brand_df.fillna(0)
        all_brand_df=all_brand_df.assign(
        ALL_BRAND_PROMO_PPG=all_brand_df[['flag_promotype_all_brand','PPG']].apply(
            lambda row: ' & '.join(sorted([opt_generic.format_ppg(str(each)) for each in row if each])),axis=1))
        all_brand_df=all_brand_df.assign(
        ALL_BRAND_PROMO_FLAGS=all_brand_df[['All_Brand_Promo_Flags']].apply(
            lambda row: ' & '.join([str(each) for each in row if each]),axis=1))
        # try:
        all_brandagg_df = all_brand_df.groupby(['ALL_BRAND_PROMO_PPG','ALL_BRAND_PROMO_FLAGS'])\
                            [['Week','JOINT_FLAG']].agg(list).reset_index()
        # except:
        #     breakpoint()
        all_brandagg_df['JOINT_FLAG'] = all_brandagg_df['JOINT_FLAG'].apply(lambda x:[j for j in x if j])
        
        all_brandagg_df['NO_OF_SLOTS'] = all_brandagg_df['Week'].apply(lambda x:len(x))
        all_brandagg_df['TYPE_OF_PROMO'] = all_brandagg_df['ALL_BRAND_PROMO_FLAGS']\
            .apply(lambda x:'all_brand' if x  and 'all_brand' in x else 'single')

        # all_brandagg_df['IS_STANDALONE']  = all_brandagg_df\
        #                                     .apply(lambda x: cmn_utils.fetch_is_standalone(x.ALL_BRAND_PROMO_PPG\
        #                                                                 , x.TYPE_OF_PROMO,x.Week\
        #                                                                     ,df_dict), axis=1).astype('bool')
        
        all_brandagg_df = all_brandagg_df.loc[all_brandagg_df['TYPE_OF_PROMO']!='single'].reset_index()

        for _id in range(all_brandagg_df.shape[0]):
            type_of_promo = all_brandagg_df.loc[_id,'TYPE_OF_PROMO']
            promo_ppg = cmn_utils.get_ppg(all_brandagg_df.loc[_id,'ALL_BRAND_PROMO_PPG']+'-all_brand',rpt,pl=ppg.replace('_',' '))
            promo_weeks = all_brandagg_df.loc[_id,'Week']
            promo_slots = all_brandagg_df.loc[_id,'NO_OF_SLOTS']
            promo_flag = all_brandagg_df.loc[_id,'ALL_BRAND_PROMO_FLAGS']
            is_standalone = False
            formated_ppg = opt_generic.format_ppg3(opt_generic.format_ppg2(' & '.join(sorted(promo_ppg.split(' & ')))) +'-all_brand')   
            one_shot_ppg_exists = opt_generic.habdle_one_shot_ppg_with_format(formated_ppg,one_shot_ppg,type_of_promo='all_brand') 
            # Define a lambda function to sort PPGs within each tuple and filter rpt
            # Define a lambda function to sort and compare PPGs within each tuple and filter rpt
            filtered_r_p_pt = list(filter(lambda x: sorted(ppg.split('-')[0] for ppg in formated_ppg.split('_-_')) == sorted(ppg.split('-')[0] for ppg in x[1].replace('&', '_').replace(',', '_').split('_-_')), rpt))
            # filtered_r_p_pt = list(filter(lambda x: formated_ppg.split('_-_') == sorted(x[1].replace('&','_').replace(',','_').split('_-_')), rpt))
            # filtered_r_p_pt = list(filter(lambda x:formated_ppg == x[1].replace('&','_').replace(',','_'),rpt))
            if (not filtered_r_p_pt and not is_standalone) and not one_shot_ppg_exists:
                is_standalone = True
            _jnt_flag = all_brandagg_df.loc[_id,'JOINT_FLAG']
            promo_slots = 0 if is_standalone else promo_slots

            if df_dict:
                df_dict = cmn_utils.generate_promotion(df_dict,
                                    type_of_promo=type_of_promo,
                                    ppg=promo_ppg,
                                    weeks=promo_weeks,
                                    scenario_type=scenario_type,
                                    is_standalone=is_standalone,
                                    _jflags=' & '.join(list(set((' & '.join(_jnt_flag).split(' & ')))))
                                    ,one_shot_ppg=bool(one_shot_ppg_exists)
                                    )
                
                _constraints_data = {'product_group':promo_ppg,'no_of_slots':promo_slots}             
                is_exclude = True               
                if main_df.shape[0]:
                    constraints.update({
                            'mechanic':opt_generic.get_mech(df_dict,type=type_of_promo ),
                            'promo_depth':opt_generic.get_avg_tpr(main_df),
                            'promo_type':type_of_promo,
                            'no_of_slots':promo_slots,
                            'is_all_brand':True,
                            'ppg':promo_ppg,
                            'ppg_belongs_to':opt_generic.format_ppg(ppg),
                            'visibility':opt_generic.get_visibility(df_dict,type=type_of_promo),
                        })

                    if not is_standalone:
                        is_exclude = False
                        filtered_tactic_df = pd.DataFrame()
                        if filtered_r_p_pt:
                            filtered_tactic_df = sc.get_opt_tactic(rpmuow,filtered_r_p_pt)
                        tot_promo_min = len(promo_weeks)-3 if len(promo_weeks)>2 else 0
                        tot_promo_max = len(promo_weeks)+3
                        if filtered_tactic_df.shape[0]:
                            range_df = filtered_tactic_df.loc[filtered_tactic_df['PPG']==formated_ppg]
                            if range_df.shape[0]:
                                tot_promo_min,tot_promo_max =cmn_utils.get_range_of_promo(range_df)
                
                        # param_compulsory_promo_weeks = list(filter(lambda x:x in promo_weeks,national_list))
                        # tot_promo_min = tot_promo_min if tot_promo_min >=len(param_compulsory_promo_weeks)\
                        #                                             else len(param_compulsory_promo_weeks)
                        constraints.update({
                            'param_compulsory_promo_weeks':[],
                            'tot_promo_max':tot_promo_max,
                            'tot_promo_min':tot_promo_min
                            })
                        _constraints_data = _get_serializer_data(constraints)
                    
                if  not is_exclude:
                    input_data_dict[_data['product_group']]['data']['include'] = True
                    input_data_dict[_data['product_group']][f'{type_of_promo}_ppg_data'].append({'weeks':promo_weeks,                                                                           
                                                                                                'is_standalone':is_standalone,
                                                                                                **_constraints_data,
                                                                                                'include':True
                                                                                            })
 
    return input_data_dict

def get_joint_and_all_brand_promo_for_output(_data,ppg):
    input_data_dict = {}
    weekly_units_df = pd.DataFrame(_data)
    input_data_dict[opt_generic.format_ppg(ppg)]={'data':{},'joint_ppg_data':[],'all_brand_ppg_data':[]}
    single_no_of_slots = opt_generic.get_raw_slots(weekly_units_df,type_of_promo='single')
    input_data_dict[opt_generic.format_ppg(ppg)]['data']\
        = {'no_of_slots':single_no_of_slots,'product_group':opt_generic.format_ppg(ppg)}
    
    grouped_df = weekly_units_df.groupby(['product_group','type_of_promo'])
    
    for key,_item in grouped_df:
        a_group = grouped_df.get_group(key).reset_index(drop=True)
        if key[1] and key[1].lower()!='single':
            no_of_slots = opt_generic.get_raw_slots(a_group,type_of_promo=key[1])
            _key_nanme = ''
            if key[1].lower()=='joint':
                _key_nanme="joint_ppg_data"
            if key[1].lower().replace(' ','_')=='all_brand':
                _key_nanme="all_brand_ppg_data"      
            input_data_dict[opt_generic.format_ppg(ppg)][_key_nanme].append({'no_of_slots':no_of_slots\
                                                                             ,'product_group':opt_generic.format_ppg(key[0])})
    return input_data_dict


def get_range_of_promo(tactic_df:pd.DataFrame):
    
    min_promo = float(tactic_df.loc[tactic_df['Promo_Type']==2,'minWeek'].values[0])
    max_promo = float(tactic_df.loc[tactic_df['Promo_Type']==2,'maxWeek'].values[0])
    return min_promo,max_promo

def get_objective_func():
    obj_choces = {
        "MAC":"Maximize MAC",
        "RP":"Maximize Retailer Margin"
    }

    return obj_choces


def get_constraints():
    return {
        'tot_promo_min':0,
        'tot_promo_max':0,
        'min_length_gap':0,
        'max_consecutive_promo':0,
        'min_consecutive_promo':0,
        'param_compulsory_no_promo_weeks':[],
        'param_compulsory_promo_weeks':[],
        'no_of_promo':0,
        'no_of_leaflet':0,
        'param_mac':0,
        'param_rp':0,
        'param_units':0,
        'param_nsv':0,
        'config_mac':False,
        'config_rp':False,
        'config_units':False,
        'config_nsv':False,
        'objective_fun':'',
        'account_name':'',
        'segment':'',
        'brand':'',
        'ppg':'',
        'mechanic':[],
        'promo_price':[],
        'promo_type':'',
        'promo_depth':0,
        'brand_tech':'',
        'product_type':'',
        'is_all_brand':False,
        'ppg_belongs_to':'',
        'no_of_slots':0,
        'is_single':False,
        'is_joint':False
    }


def _get_serializer_data(constraints):
    serializer = OptimizerSerializer({
                'param_total_promo_min': constraints.get('tot_promo_min'),
                'param_total_promo_max': constraints.get('tot_promo_max'),
                'visibility':constraints.get('visibility'),
                'param_promo_gap': constraints.get('min_length_gap'),
                'param_max_consecutive_promo': constraints.get('max_consecutive_promo'),
                'param_min_consecutive_promo': constraints.get('min_consecutive_promo'),
                'param_compulsory_no_promo_weeks':constraints.get('param_compulsory_no_promo_weeks'),
                'param_compulsory_promo_weeks':constraints.get('param_compulsory_promo_weeks'),
                'param_no_of_promo':constraints.get('no_of_promo'),
                'param_no_of_leaflet':constraints.get('no_of_leaflet'),
                'param_mac': constraints.get('param_mac'),
                'param_rp': constraints.get('param_rp'),
                'param_trade_expense': 1.0,
                'param_units': constraints.get('param_units'),
                'param_nsv': constraints.get('param_nsv'),
                'param_gsv': 1.0,
                'param_sales': 1.0,
                'param_mac_perc': 1.0,
                'param_rp_perc': 1.0,
                'config_mac':constraints.get('config_mac'),
                'config_rp': constraints.get('config_rp'),
                'config_trade_expense': False,
                'config_units': constraints.get('config_units'),
                'config_nsv': constraints.get('config_nsv'),
                'config_gsv': False,
                'config_sales': False,
                'config_mac_perc': False,
                'config_rp_perc': False,
                'config_automation':True,
                'config_no_of_promo': True,
                'config_no_leaflet':True,
                'config_min_consecutive_promo': True,
                'config_max_consecutive_promo': True,
                'objective_function':constraints.get('objective_fun'),
                'config_promo_gap': True,
                'account_name': constraints.get('account_name'),
                'corporate_segment': constraints.get('segment'),
                'brand': constraints.get('brand'),
                'product_group':constraints.get('ppg'),
                'fin_pref_order': ['Units', 'NSV', 'RP', 'MAC'],
                'mechanic':constraints.get('mechanic'),
                'promo_price':constraints.get('promo_price'),
                'promo_type':constraints.get('promo_type'),
                'promo_depth':constraints.get('promo_depth'),
                'brand_tech':constraints.get('brand_tech'),
                'product_type':constraints.get('product_type'),
                'is_all_brand':constraints.get('is_all_brand'),
                "ppg_belongs_to":constraints.get('ppg_belongs_to'),
                "no_of_slots":constraints.get('no_of_slots'),
                "is_single":constraints.get('is_single'),
                'is_joint':constraints.get('is_joint')
                
            })

    _data = []
    
    if serializer.is_valid():
        _data = serializer.validated_data
    if serializer.errors:
        raise serializers.ValidationError(serializer.errors)
    return _data                            
