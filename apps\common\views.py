""" Common API Views."""
from django.http import HttpResponseNotAllowed
import structlog
from django.db import transaction
from rest_framework.decorators import api_view
from rest_framework import filters
from drf_spectacular.utils import (
    extend_schema,
    OpenApiParameter,
    OpenApiExample,
)
from apps.promo.promo_scenario_planner.unit_of_work import SavedScenarioUnitOfWork
from core.generics import (api_handler, oauth_token_validate,#pylint: disable=E0401
                            resp_utils as resp_util
                            )
from apps.common import serializers #pylint: disable=E0401
from apps.promo.promo_optimizer import unit_of_work as opt_uow
from .import (services,unit_of_work)


logger = structlog.get_logger(__name__)

class CustomSearchFilter(filters.SearchFilter):
    def get_search_fields(self, view, request):
        if request.query_params.get('search'):     
            return ['name','user__name','status_type','scenario_type','promo_type']
        return super().get_search_fields(view, request)
        
@oauth_token_validate.requires_auth
@extend_schema(
    summary="Get Baseline Data.",
    description="API end point that serves the Baseline Data.",
)
@api_view(["GET"])
@resp_util.handle_response
def get_meta_data(request,*_args,**_kwargs):
    """Get meta data from db

    Args:
        request (dict): HttpRequest object

    Returns:
        list: list of serialized data
    """
    if request.method == 'GET':
        logger.bind(method_name="get_meta_data", app_name="Common")
        request_user_obj = request._user
        request_query_obj = request.GET
        response = services.get_meta_data(
             unit_of_work.SelectedBaselineDataUnitOfWork(transaction),
             request_user_obj,request_query_obj
        )
        return response
    return HttpResponseNotAllowed("Method Not allowed")

@oauth_token_validate.requires_auth
@extend_schema(
    summary="Get Baseline Data based on meta id.",
    description="API end point that serves the Baseline Data based on meta id.",
    parameters=[
        OpenApiParameter(
            name="meta id",
            description="Filter by meta id",
            required=True,
            type=str,
            examples=[
                OpenApiExample(
                    "Example 1",
                    summary="Filter by meta id.",
                    description="meta id should be integer type.",
                    value=1,
                )
            ],
        )
    ],
)
@api_view(["GET"])
@resp_util.handle_response
def get_model_data(request,*_args,**_kwargs):
    """Get Model data from db

    Args:
        request (dict): HttpRequest object

    Returns:
        list: list of serialized data
    """
    if request.method == 'GET':
        meta_id = request.parser_context['kwargs'].get('meta_id',None)
        logger.bind(method_name="get_model_data", app_name="Common")
        response = services.get_model_data(
            unit_of_work.ModelDataUnitOfWork(transaction),meta_id
        )
        serializer = serializers.ModelDataSerializer(
            response, many=True)
        return serializer.data
    return HttpResponseNotAllowed("Method Not allowed")

@extend_schema(
    summary="Get Previous year weekly data",
    description="API end point that serves the Previous year Weekly basedata.",
    request=serializers.WeeklyConstraintsSerializer
)
@api_view(["POST"])
@resp_util.handle_response
def update_base_and_national_promo(request,*_args,**_kwargs):
    if request.method == 'POST':
        logger.bind(method_name="update_base_and_national_promo", app_name="Common")
        response = services.update_base_and_national_promo(
            unit_of_work.MetaUnitOfWork(transaction),
            opt_uow.PromotionLevelsUnitOfWork(transaction),
            unit_of_work.RetailerPPGMappingUnitOfWork(transaction),
            unit_of_work.BaseAndPromotionLevelDetailUnitOfWork(transaction),      
            request._user
        )
        return response
    return HttpResponseNotAllowed("Method Not allowed")

@oauth_token_validate.requires_auth
@extend_schema(
    summary="Get Previous year weekly data",
    description="API end point that serves the Previous year Weekly basedata.",
    request=serializers.WeeklyConstraintsSerializer
)
@api_view(["POST"])
@api_handler.API_REQUEST_QUERY_HANDLER
@resp_util.validate_input_parameters
@resp_util.handle_response
def get_weekly_constraints(request,*_args,**_kwargs):
    if request.method == 'POST':
        data = request.data
        logger.bind(method_name="get_weekly_constraints", app_name="Common")
        response = services.get_weekly_constraints(
            data,
            unit_of_work.BaseAndPromotionLevelDetailUnitOfWork(transaction),    
            opt_uow.PromotionLevelsUnitOfWork(transaction),
            opt_uow.OptimizerSavedScenarioUnitOfWork(transaction),
            SavedScenarioUnitOfWork(transaction),   
            request._user
        )
        return response
    return HttpResponseNotAllowed("Method Not allowed")
