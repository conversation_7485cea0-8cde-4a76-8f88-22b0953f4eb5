""" Format DataFrame"""
import pandas as pd
from apps.promo.promo_optimizer import generic as opt_generic
from core.generics import constants as CONST

def format_data(data_dt:pd.DataFrame=None,
                roi_dt:pd.DataFrame=None,
                coeff_dt:pd.DataFrame=None,
                coeff_map_dt:pd.DataFrame=None)->pd.DataFrame:
    """format_data

    Args:
        data_dt (pd.DataFrame): data_dt
        roi_dt (pd.DataFrame): roi_dt
        coeff_map_dt (pd.DataFrame): coeff_map_dt
        coeff_dt (pd.DataFrame): coeff_dt

    Returns:
        pd.DataFrame: data_dt,roi_dt,coeff_map_dt,coeff_dt
    """
    
    data_dt['Date']= pd.to_datetime(data_dt['Date'])
    data_dt['PPG'] = data_dt['PPG'].apply(lambda x:opt_generic.format_ppg2(x))
    roi_dt['Median_Base_Price_log'] = data_dt['wk_sold_median_base_price_byppg_log'].astype(float)
    dec_col = [
                'GSV',
                'NSV',
                'Volume',
                'Units',
                'NSV_Per_Unit_Future',
                'COGS_Per_Unit_Future',
                'GSV_Per_Unit_Future',
                'Total Sold Unit',
                'Total Sold Volume',
                'Pack Weight'
            ]
    
    roi_dt['Date'] = pd.to_datetime(roi_dt['Date']) # convert to dataframe date format
    
    for i in dec_col:
        roi_dt[i]= roi_dt[i].astype(float)

    to_float_cols = CONST.DATA_HEADER[10:-1]
    for i in to_float_cols:
        try:
            coeff_dt[i]= coeff_dt[i].astype(float)
            data_dt[i]= data_dt[i].astype(float)
        except Exception as _e:
            pass

    coeff_mapping_to_float = ['Value']
    if isinstance(coeff_map_dt,pd.DataFrame):
        for i in coeff_mapping_to_float:
            coeff_map_dt[i] = coeff_map_dt[i].astype(float)


    return data_dt,roi_dt,coeff_dt,coeff_map_dt

def change_datatype(_df:pd.DataFrame)->pd.DataFrame:
    """convert_to_int

    Args:
        df (pd.DataFrame): dataframe

    Returns:
        pd.DataFrame: df
    """
    _df["Retailer"] = _df["Retailer"].astype(int)
    _df["maxWeek"] = _df["max_week"].astype(int)
    _df["minWeek"] = _df["min_week"].astype(int)
    # _df["minWeek_edlp"] = _df["minWeek_edlp"].astype(int)
    # _df["maxWeek_edlp"] = _df["maxWeek_edlp"].astype(int)
    _df["TPR"] = _df["TPR"].astype(int)
    _df["Leaflet_flag"] = _df["Leaflet_flag"].astype(int)
    # _df["Newsletter_flag"] = _df["Newsletter_flag"].astype(int)
    # _df["Advertising_without_price_flag"] = _df["Advertising_without_price_flag"].astype(int)
    # _df["maxWeek_edlp"] = _df["maxWeek_edlp"].astype(int)
    # _df["Bonus_flag"] = _df["Bonus_flag"].astype(int)
    # _df["Pas_flag"] = _df["Pas_flag"].astype(int)
    # _df["Coupon_flag"] = _df["Coupon_flag"].astype(int)
    # _df["EDLP_flag"] = _df["EDLP_flag"].astype(int)
    # _df["Avg_units_per_Week"] = _df["Avg_units_per_Week"].astype(float)
    # _df["TE_perweek"] = _df["TE_perweek"].astype(float)
    # _df["COGS_perweek"] = _df["COGS_perweek"].astype(float)
    # _df["GSV_perweek"] = _df["GSV_perweek"].astype(float)
    # _df["ListPrice_perweek"] = _df["ListPrice_perweek"].astype(float)
    return _df

def change_datatype2(_df):
    _df["Avg_units_per_Week"] = _df["Avg_units_per_Week"].astype(float)
    _df["TE_perweek"] = _df["TE_perweek"].astype(float)
    _df["COGS_perweek"] = _df["COGS_perweek"].astype(float)
    _df["GSV_perweek"] = _df["GSV_perweek"].astype(float)
    _df["NSV_perweek"] = _df["NSV_perweek"].astype(float)
    return _df
    
def convert_df_to_raw_query(df):
    return df.values.tolist()
