""" Optimiser Query"""
from config.db_handler import db_table_format_in_sql_query_str

def search_optimizer_scenario_query():
    """ Search Optimizer Scenario"""
    query_string = f"""
    SELECT * FROM {db_table_format_in_sql_query_str('saved_scenario')} s
    WHERE 
    s.name LIKE %s
    AND
    s.scenario_type = 'optimizer'
    AND 
    s.status_type = %s
    AND
    s.is_delete = 0
    ORDER BY
    s.id DESC
    """
    
    return query_string

def search_optimizer_global_scenario_query():
    """ Search Global Optimizer Scenario"""
    query_string = f"""
    SELECT * FROM {db_table_format_in_sql_query_str('saved_scenario')} s
    WHERE 
    s.name LIKE %s
    AND
    s.scenario_type = 'optimizer'
    AND
    s.is_delete = 0
    ORDER BY
    s.id DESC
    """
    
    return query_string

def get_tactic_data(columns,ids):
    """ get tactic data"""
    query_string = f"""
    SELECT {columns} FROM {db_table_format_in_sql_query_str('retailer_ppg_mapping')} a 
        INNER JOIN {db_table_format_in_sql_query_str('model_tactic')} b
        ON 
        b.retailer_ppg_map_id = a.id
        WHERE
        b.is_delete = 0
        AND
        a.is_delete = 0
        AND
        b.retailer_ppg_map_id in ({ids})
    """
    return query_string


def get_rpm_weekly_data(columns,ids):
    """ get tactic data"""
    query_string = f"""
    SELECT {columns} FROM {db_table_format_in_sql_query_str('retailer_ppg_mapping')} a 
        INNER JOIN {db_table_format_in_sql_query_str('retailer_ppg_mapping_weekly')} b
        ON 
        b.retailer_ppg_map_id = a.id
        WHERE
        b.is_delete = 0
        AND
        a.is_delete = 0
        AND
        b.retailer_ppg_map_id in ({ids})
    """
    return query_string

def get_rpm_weekly_data_all(columns):
    """ get tactic data"""
    query_string = f"""
    SELECT {columns} FROM {db_table_format_in_sql_query_str('retailer_ppg_mapping')} a 
        INNER JOIN {db_table_format_in_sql_query_str('retailer_ppg_mapping_weekly')} b
        ON 
        b.retailer_ppg_map_id = a.id
        WHERE
        b.is_delete = 0
        AND
        a.is_delete = 0
        AND
        a.account_name = %s
    """
    return query_string

def get_item_map_data(columns,ppgs)->str:
    """
    get_item_map_data
    """
    query_string = f"""
    SELECT {columns} FROM {db_table_format_in_sql_query_str('item_map')} a
        WHERE  
        a.account_name = %s
        AND
        a.is_delete = 0
        AND
        a.product_group in ({','.join(["%s"] * len(ppgs))})
    """
    return query_string

def insert_optimizer_data_query():
    query_string = f"""
    INSERT INTO {db_table_format_in_sql_query_str('scenario_promotion_save')}
           (saved_scenario_id,
           input_constraints,
           data
            )
     VALUES
           (%s,%s,%s)

    """

    return query_string
