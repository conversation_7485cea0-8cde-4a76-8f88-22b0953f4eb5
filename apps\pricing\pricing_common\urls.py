from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from . import views

app_name = "common"
urlpatterns = [
    path('get_pricing_scenario_data/', views.PricingScenarioView.as_view({'post':'get_pricing_scenario_data'})),
    path('get_pricing_retailer_ppg_elasticity_info/', views.PricingScenarioView.as_view({'post':'get_pricing_retailer_ppg_elasticity_info'})),
    path('get_pricing_scenario_meta_data/', views.PricingScenarioView.as_view({'get':'get_pricing_scenario_meta_data'})),
    path('post_simulate_scenario/', views.PricingScenarioView.as_view({'post':'post_simulate_scenario'})),
     path('delete/<int:saved_id>', views.PricingPublishedSavedScenarioView\
        .as_view({'delete':'delete_scenario'})),
    # path('load/<int:saved_id>/', views.PricingPublishedSavedScenarioView\
    #     .as_view({'get':'load_scenario'})),
    path('load/<int:saved_id>/', views.PricingPublishedSavedScenarioView\
        .as_view({'get':'load_scenario'})),
    path('saved_inputs/<int:saved_id>/', views.ChangedPricingScenarioView\
        .as_view({'get':'saved_inputs'})),
    path('load_summary/<int:saved_id>/', views.PricingSummeryScenarioView\
    .as_view({'get':'load_scenario'})),
    path('load_overall_summary/<int:saved_id>/', views.PricingOverallSummeryScenarioView\
        .as_view({'get':'load_scenario'})),
    path('compare/', views.BaseAndSimulatedPricingScenarioView\
        .as_view({'get':'compare_scenario'})),
    path('saved_scenario/', views.PricingPublishedSavedScenarioView\
        .as_view({'get':'get_saved_scenario'})),
    path("search/", views.PricingPublishedSavedScenarioView.as_view({'get':'search'})),
    path("save/", views.PricingPublishedSavedScenarioView.as_view({'post':'save_scenario'})),
    # path("download/", views.PricingPublishedSavedScenarioView.as_view({'post':'download'})),
    path("summary/<int:saved_id>/", views.BaseAndSimulatedPricingScenarioView.as_view({'get':'get_summary'})),
    path("overall_summary/<int:saved_id>/", views.BaseAndSimulatedPricingScenarioView.as_view({'post':'get_overall_summary'})),
    path("deep_drive_summary/<int:saved_id>/", views.BaseAndSimulatedPricingScenarioView.as_view({'post':'get_deep_drive_summary'})),
    path("deep_drive_customer_level/<int:saved_id>/", views.BaseAndSimulatedPricingScenarioView.as_view({'post':'get_deep_drive_customer_level'})),
    path("deep_drive_top_10/<int:saved_id>/", views.BaseAndSimulatedPricingScenarioView.as_view({'post':'get_deep_drive_top_10'})),
    path("list/<int:saved_id>/", views.BaseAndSimulatedPricingScenarioView.as_view({'get':'get_list'})),
    path("share_scenario/", views.PricingPublishedSavedScenarioView.as_view({'post':'share_scenario'})),
    path("publish/<int:saved_id>/", views.PricingPublishedSavedScenarioView.as_view({'post':'publish_scenario'})),
    path('save_post_simulate_scenario/<int:non_committed_id>/', views.PricingPublishedSavedScenarioView.as_view({'post':'save_post_simulate_scenario'})),
    path("download_set_price_input/", views.PricingOverallSummeryScenarioView.as_view({'post':'download_input'})),
    path("download_scenario_planner/", views.PricingOverallSummeryScenarioView.as_view({'post':'download_scenario_planner_input'})),
    path("download_planner_output/", views.PricingOverallSummeryScenarioView.as_view({'post':'download_planner_output'})),
    path("upload_set_price/<str:slug>/",views.PricingPublishedSavedScenarioModelView.as_view({'post':'upload_input'})),
    path("upload_scenario_planner/<str:slug>/",views.PricingPublishedSavedScenarioView.as_view({'post':'upload_scenario_planner'})),
    path("upload_scenario_data/",views.ChangedPricingScenarioView.as_view({'post':'upload_scenario_data'})),
    path("scenario_permissions/<int:saved_id>/",views.PricingPublishedSavedScenarioView.as_view({'get':'get_scenario_permissions'})),
    path("scenario_approval/<int:saved_id>/",views.PricingPublishedSavedScenarioView.as_view({'post':'scenario_approval'})),
    path("global_constraints/<int:saved_id>/",views.PricingPublishedSavedScenarioView.as_view({'post':'get_global_constraints'})),
    path("global_constraints/",views.PricingScenarioView.as_view({'post':'get_global_constraints'}))
]
