""" Optimizer Urls"""
from django.urls import path
from .views import Optimizer<PERSON><PERSON><PERSON><PERSON>,OptimizerScenarioList,OptimizerSearchAPI,PromotionLevelAPI

urlpatterns = [
    
    path('optimize/', OptimizerScenario\
        .as_view({'post':'post_optimize_scenario'})),
    path('promotion_levels/', PromotionLevelAPI\
        .as_view({'get':'get_promo_levels'})),
    path('save_promotion_level/', PromotionLevelAPI\
        .as_view({'post':'save_promotion_level'})),
    path('list/', OptimizerScenario\
        .as_view({'post':'get_scenario_constraints'})),
    path('save/', OptimizerScenario\
        .as_view({'post':'save_scenario'})),
    path('update/<int:saved_id>', OptimizerScenario\
        .as_view({'put':'update_scenario'})),
    path('delete/<int:saved_id>', Optimizer<PERSON>cenario\
        .as_view({'delete':'delete_scenario'})),
    path('load/<int:saved_id>/', OptimizerScenario\
        .as_view({'get':'load_scenario'})),
    path('compare/', OptimizerScenario\
        .as_view({'get':'compare_scenario'})),
    path('saved_scenario/<int:saved_id>/', OptimizerScenarioList\
        .as_view({'get':'get_saved_scenario'})),
    path('saved_scenario/', OptimizerScenarioList\
        .as_view({'get':'get_saved_scenario'})),
    path("search/", OptimizerSearchAPI.as_view({'get':'search'})),
    path("download/", OptimizerScenario.as_view({'post':'download'})),
    path("share_optimizer_scenario/", OptimizerScenario.as_view({'post':'share_optimizer_scenario'}))
]
