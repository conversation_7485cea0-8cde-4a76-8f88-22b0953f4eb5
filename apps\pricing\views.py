import structlog
import ast
from django.http import HttpResponse, HttpResponseBadRequest
from rest_framework.pagination import PageNumberPagination
from . import serializers as ser
from . import services
from django.db import transaction
from rest_framework import viewsets
from drf_spectacular.utils import (
    extend_schema,
    OpenApiParameter,
    OpenApiExample,
)
from core.generics import (oauth_token_validate,
                            resp_utils,
                            exceptions,
                            api_handler,
                            search,
                            excel
                            )
from apps.common import (serializers,unit_of_work as uow,services as cmn_serv)
from apps.promo.promo_optimizer import (unit_of_work as opt_uow)
from . import unit_of_work as sp_uow

logger = structlog.get_logger(__name__)
        
class ScenarioPlanner(viewsets.GenericViewSet):
    serializer_class = serializers.ScenarioPromotionSerializer
    filter_backends = [search.CustomSearchFilter]
    pagination_class = PageNumberPagination
    page_size_query_param = 'page_size'
    page_query_param = 'page'
    lookup_field = 'saved_scenario__id'
    lookup_url_kwarg = 'saved_id'

    def get_queryset(self):
        return services.get_scenario(
                        sp_uow.ScenarioPlannerPromotionUnitOfWork(transaction),
                        scenario_id= self.request.parser_context['kwargs'].get('saved_id',None),
                        user=self.request._user
                    ).order_by('-id')

    @extend_schema(
        summary="Simulate Scenario",
        description="API end point that serves the Simulate Scenario.",
        request=ser.ScenarioPlannerRequestSerializer,
        responses=ser.ScenarioPlannerRequestAndResponseSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @api_handler.API_REQUEST_QUERY_HANDLER
    @resp_utils.validate_input_parameters
    @resp_utils.handle_response
    
    def post_simulate_scenario(self,request):
  
        _obj = {
                'user_id':request._user['user_id'],
                'promo_data':request.data.get('promo_data'),
                'method':'POST'
            }
        if not _obj['promo_data']:
            raise HttpResponseBadRequest("Pass Valid Data")

        logger.bind(method_name="post_simulate_scenario", app_name="Scenario Planner")
        response = services.post_simulate_scenario(uow.MetaUnitOfWork(transaction),
                                          opt_uow.PromotionLevelsUnitOfWork(transaction),
                                          uow.BaseAndPromotionLevelDetailUnitOfWork(transaction),
                                        _obj,
                                        request._user
                                        )
        return response
    
    @extend_schema(
        summary="Update scenario Based on saved id",
        description="API end point that serves the scenario Based on saved id.",
        request=ser.ScenarioPlannerSaveRequestSerializer,
        responses=ser.ScenarioPlannerUpdatedResponseSerializer,
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @api_handler.API_REQUEST_QUERY_HANDLER
    @resp_utils.validate_input_parameters
    @resp_utils.handle_response
    def update_scenario(self,request):
        logger.bind(method_name="update_scenario", app_name="Scenario Planner")
        response = services.put_scenario(
            self.get_object(), request.data,\
            sp_uow.SavedScenarioUnitOfWork(transaction),
            request._user
        )
        return response

    @extend_schema(
        summary="Delete scenario Based on saved id",
        description="API end point that serves the scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        responses="Deleted Successfully"
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_utils.handle_response

    def delete_scenario(self,request):
        logger.bind(method_name="delete_scenario", app_name="Scenario Planner")
        response = services.delete_scenario(
            self.get_object().id,
            request._user, 
            sp_uow.SavedScenarioUnitOfWork(transaction)
        )
        return response
    
    @extend_schema(
        summary="Save scenario Based on saved id",
        description="API end point that serves saving scenario.",
        request=ser.ScenarioPlannerSaveRequestSerializer,
        responses=ser.ScenarioPlannerSavedResponseSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @api_handler.API_REQUEST_QUERY_HANDLER
    @resp_utils.validate_input_parameters
    @resp_utils.handle_response
   
    def save_scenario(self,request):
        logger.bind(method_name="save_scenario", app_name="Scenario Planner")
        response = services.post_scenario(
            request.data, sp_uow.SavedScenarioUnitOfWork(
                transaction),
                request._user
        )
        return response

    @extend_schema(
        summary="Load Saved scenario Based on saved id",
        description="API end point that serves the Loading Saved scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_utils.handle_response
  
    def load_scenario(self,_request):
        logger.bind(method_name="load_scenario", app_name="Scenario Planner")
        serializer = self.serializer_class(
            [self.get_object()], many=True)
        return serializer.data
    
    @extend_schema(
        summary="Comapare scenario Based on saved id",
        description="API end point that serves the Comapare scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_ids",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id should be type string.",
                        value="[1,2]",
                    )
                ],
            )
        ],
        responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_utils.handle_response

    def compare_scenario(self,request,*_args,**_kwargs):
        logger.bind(method_name="compare_scenario", app_name="Scenario Scenario")
        saved_ids = ast.literal_eval(request.query_params.get('saved_ids',[]))
        response = services.compare_scenario(
            sp_uow.ScenarioPlannerPromotionUnitOfWork(transaction),
            saved_ids
        )
        serializer = serializers.ScenarioPromotionSerializer(
            response, many=True)     
        return serializer.data

    @extend_schema(
        summary="Download optimizer  output",
        description="Download optimizer  output",
        request=ser.ScenarioPlannerRequestAndResponseSerializer,
        responses="scenario planner output excel."       
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_utils.handle_response
    
    def download(self,request,*_args,**_kwargs):
        logger.bind(method_name="download", app_name="Optimizer Scenario")

        filename = 'promo_optimizer.xlsx'
        response = HttpResponse(
            excel.download_excel_promo(request.data['data']),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename=%s' % filename
        return response
    
    @extend_schema(
        summary="Share Scenario",
        description="API end point that serves the saved scenario."
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_utils.handle_response
    def share_planner_scenario(self,request):
        logger.bind(method_name="get_scenario", app_name="Scenario Planner")
        response = cmn_serv.share_scenario(request.data\
                                           ,opt_uow.OptimizerSavedScenarioUnitOfWork(transaction)\
                                            ,request._user\
                                            ,scenario_type='Scenario Planner')
        return response
    
class ScenarioList(viewsets.GenericViewSet):
    serializer_class = serializers.ScenarioSavedList
    filter_backends = [search.CustomSearchFilter]
    search_fields  = ['name','status_type','scenario_type','promo_type']
    pagination_class = PageNumberPagination
    page_size_query_param = 'page_size'
    page_query_param = 'page'
    lookup_field = 'id'
    lookup_url_kwarg = 'saved_id'

    def get_queryset(self):
        return services.get_scenario(
                        sp_uow.SavedScenarioUnitOfWork(transaction),
                        user=self.request._user,
                        scenario_view=self.request.query_params.get('scenario_view',None)
                    ).order_by('-id')
    
    @extend_schema(
        summary="Get Saved scenario Based on saved id",
        description="API end point that serves the scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_utils.handle_response
 
    def get_saved_scenario(self,request):
        logger.bind(method_name="get_saved_scenario", app_name="Scenario Planner")
        saved_id = request.parser_context['kwargs'].get('saved_id',None)
        status_type = request.query_params.get('status_type',None)
        query_set = self.get_queryset()
        if status_type and status_type in ['active','completed']:
            query_set = query_set.filter(status_type=status_type)
        if saved_id:
            query_set = [self.get_object()]
        
        page = self.paginate_queryset(query_set)
        if page is not None:
            serializer = self.serializer_class(page, many=True)
            return {'data':serializer.data,'count':query_set.count()}

        serializer = self.serializer_class(query_set, many=True)
        return serializer.data

class ScenarioPlannerSearchAPI(viewsets.GenericViewSet):
    serializer_class = serializers.ScenarioSavedList
    filter_backends = [search.CustomSearchFilter]
    pagination_class = PageNumberPagination
    page_size_query_param = 'page_size'
    page_query_param = 'page'
    lookup_field = 'id'
    lookup_url_kwarg = 'saved_id'

    @extend_schema(
    summary="Search  Data based on name/promotion type/status type/scenario type .",
    description="API end point that serves the Baseline Data based on name,promotion type,status type,.",
    parameters=[
        OpenApiParameter(
            name="Search  Data based on name/promotion type/status type/scenario type ",
            description="Filter by name/promotion type/status type/scenario type",
            required=True,
            type=str,
            examples=[
                OpenApiExample(
                    "Example 1",
                    summary="Filter by name/promotion type/status type/scenario type.",
                    description="name/promotion type/status type/scenario type should be string type.",
                    value=1,
                )
            ],
        )
    ],
    responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @api_handler.API_REQUEST_QUERY_HANDLER
    @resp_utils.validate_input_parameters
    @resp_utils.handle_response
  
    def search(self,request,*args,**kwargs): 
        param = request.query_params.get('q','')
        if not param:
            raise exceptions.MissingRequestParamsError("q", param)

        logger.bind(method_name="search", app_name="Scenario Planner")
        
        response = services.search_scenario(
            sp_uow.SavedScenarioUnitOfWork(transaction),
            query_params=request.query_params
        )
        
        serializer = serializers.ScenarioSavedList(
            response, many=True)
        return serializer.data
        