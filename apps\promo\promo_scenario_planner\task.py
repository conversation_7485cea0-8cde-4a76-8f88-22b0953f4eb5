import pandas as pd
import numpy as np
from apps.promo.promo_optimizer import generic as opt_generic,utils as opt_utils,process
from apps.common import utils as cmn_utils,services as cmn_serv
from tasks.parallel_task import parallel_task_executioner
from core.generics import exceptions as excep,resp_utils,constants as CONST

@parallel_task_executioner # parallel - comment if you want to run sequentially
def get_optimizer_planner_input_constraints(**kwargs): 
    # for key,_item in kwargs.get('grouped_df'):  # sequentional - comment if you want to run paralelly
        """
            To get single/joint/allbrand promotion details.
            Function will be running in parallely to get results.
        """
        
        # For parallel use below, for sequential comment below line
        key = kwargs.get('key')
        a_group = kwargs.get('grouped_df').get_group(key).reset_index()
        agg_group = a_group.groupby(['account_name','product_group']).agg({'promo_present':'sum', 
                                                                    'tpr_discount_byppg': 'sum'}).reset_index()
        agg_group = agg_group.loc[(agg_group['promo_present']==0)].reset_index(drop=True)
        pp=a_group['product_group'].unique()[0].replace('/','').replace('_',' ')
        print(pp,'********************************')

        opt_ppg = opt_generic.format_ppg2(opt_generic.format_ppg((a_group['product_group'].unique()[0])))
        filtered_r_p_pt = list(filter(lambda x:opt_ppg in x[3],kwargs.get('reatiler_ppgs_promp_types')))
        one_shot_ppg = list(filter(lambda x:x[2]=='one_shot',filtered_r_p_pt))

        if not agg_group.shape[0] or one_shot_ppg:
            try:
                avg_tpr_replace = round(a_group[a_group['promo_present']!=0]['tpr_discount_byppg'].mean())
            except:
                avg_tpr_replace = 0
            a_group['tpr_discount_byppg'] = np.where(a_group['promo_present']>0, avg_tpr_replace, a_group['tpr_discount_byppg'])
            a_group = a_group.merge(kwargs.get('median_acv_df'), on=["account_name", "product_group"])
            if not one_shot_ppg:
                a_group["acv_selling"] = a_group["acv_median"]
            a_group.pop('acv_median')
            # Replace Median_Base_Price_log with 4 weeks rolling mean
            a_group["median_base_price_log"] = a_group["MBP_log_rolling_mean"]

            a_group["tpr_discount_byppg_10_above"] = np.where(a_group["promo_present"]>0, a_group["tpr_discount_byppg"], 0)
            a_group["tpr_discount_byppg_5_10"] = 0
            a_group["tpr_discount_byppg_2019"] = 0
            df_dict = a_group.to_dict('records')
            basedata_df = kwargs.get('basedata_df').loc[kwargs.get('basedata_df')['account_name']==key[0]]
            input_data_dict = {}
            print('retailer',df_dict[0]['account_name'])
            constraints = opt_utils.get_constraints()
            _obj = {'account_name':df_dict[0]['account_name'],
                    'product_group':opt_generic.format_ppg(df_dict[0]['product_group']),
                    'corporate_segment':df_dict[0]['corporate_segment'],
                    'brand':df_dict[0]['brand'],
                    "brand_tech":df_dict[0]['brand_tech'],
                    "product_type":df_dict[0]['product_type'],
                    'type_of_promo':''
                    }
            ppg_name = _obj['product_group']
            ppg_name2 = df_dict[0]['product_group'].replace('_',' ') # remove .replace('_',' ') if you are facing issues with refreshing BNP data.
            a_group['product_group'] = _obj['product_group']
            constraints.update({
                'account_name':_obj['account_name'],
                'ppg':_obj['product_group'],
                'segment':_obj['corporate_segment'],
                'brand':_obj['brand'],
                'brand_tech':_obj['brand_tech'],
                'product_type':_obj['product_type'],
                'is_joint':False,
                'is_all_brand':False,
                'is_single':False,
                'objective_fun':'MAC'
            })
            df_dict = a_group.to_dict('records')
            formated_df = a_group.rename(columns={'promo_present':'promo_present','tpr_discount_byppg':'TPR'})
            filtered_promotion_level_data = []
            if kwargs.get('promotion_level_data'):
                filtered_promotion_level_data = list(filter(lambda x:(sorted(x['product_group'])\
                                                ==sorted(ppg_name)),kwargs.get('promotion_level_data')))
                if filtered_promotion_level_data:
                    df_dict = cmn_utils.pre_update_natioal_promotion_mechanics(df_dict\
                                                                    ,filtered_promotion_level_data[0]['weekly']
                                                            ,scenario_type=kwargs.get('scenario_type'))
            national_list = []
            # national_list = list(map(lambda x:x['week'],(list(filter(lambda x:x['promotion_levels'],df_dict)))))
            main_df = a_group.loc[(a_group['promo_present']>0)]  
            input_data_dict[_obj['product_group']]={'data':{},'joint_ppg_data':[],'all_brand_ppg_data':[],'include':False}
            filtered_tactic_df = cmn_serv.get_opt_tactic2(kwargs.get('rpmuow'),filtered_r_p_pt)
            # breakpoint()
            if (main_df.shape[0] and filtered_tactic_df.shape[0]) or one_shot_ppg:
                _data = {}
                
                single_df = a_group.groupby(['account_name','product_group']).agg({'promo_present':'sum', 
                                                                        'tpr_discount_byppg': 'sum'}).reset_index()
                single_df = single_df.loc[(single_df['promo_present']==0)].reset_index(drop=True)
                single_one_shot_ppg = list(filter(lambda x:x[2].lower()=='single',one_shot_ppg))
                joint_one_shot_ppg = list(filter(lambda x:x[2].lower()=='joint',one_shot_ppg))
                all_brand_one_shot_ppg = list(filter(lambda x:x[2].lower().replace(' ','_')=='all_brand',one_shot_ppg))
                if not single_df.shape[0] or single_one_shot_ppg:
                    df_dict = cmn_utils.generate_promotion(df_dict,scenario_type=kwargs.get('scenario_type'),one_shot_ppg=bool(single_one_shot_ppg))
                    _single_filtered_tactic_df = filtered_tactic_df.loc[filtered_tactic_df['PPG'].str.replace('[&,]', ' ',regex=True)==ppg_name2]
                    single_ppgs_list = list(filter(lambda x:x['type_of_promo'].lower()=='single',df_dict))
                    single_leaflet= list(filter(lambda x:x['promo_present'],df_dict))
                    condtion = 1 if single_one_shot_ppg else 0
                    if joint_one_shot_ppg:
                        condtion+=1 if joint_one_shot_ppg else 0
                    if all_brand_one_shot_ppg:
                        condtion+=1 if all_brand_one_shot_ppg else 0

                    if (single_ppgs_list and _single_filtered_tactic_df.shape[0]) or  condtion==1:
                        _obj['type_of_promo'] = 'single'
                        print('single promo')
                        promo_weeks = list(map(lambda x:x['week'],list(filter(lambda x:x['type_of_promo'].lower()=='single',df_dict))))
                        if not promo_weeks:
                            ppg_name3 =ppg_name2.replace('/',' ')
                            a_group.to_excel(f'{ppg_name3}.xlsx')
                        if promo_weeks:
                            no_of_slots = len(promo_weeks)
                            tot_promo_min = no_of_slots-3 if no_of_slots>2 else 0
                            tot_promo_max = no_of_slots+3
                            if filtered_tactic_df.shape[0]:
                                range_df = filtered_tactic_df.loc[filtered_tactic_df['PPG']==ppg_name2]
                                if range_df.shape[0]:
                                    tot_promo_min,tot_promo_max =cmn_utils.get_range_of_promo(range_df)
                        
                            
                            
                            # param_compulsory_promo_weeks = list(filter(lambda x:x in promo_weeks,national_list))
                            # tot_promo_min = tot_promo_min if tot_promo_min >=len(param_compulsory_promo_weeks)\
                            #                                                 else len(param_compulsory_promo_weeks)
                            constraints.update({
                                    'mechanic':opt_generic.get_mech(df_dict,type='single',ppg=ppg_name2,one_shot_ppg=single_one_shot_ppg),
                                    'promo_depth':opt_generic.get_avg_tpr(formated_df),
                                    'visibility':opt_generic.get_visibility(df_dict,type='single',ppg=ppg_name2,one_shot_ppg=single_one_shot_ppg),
                                    'promo_type':'single',
                                    'no_of_slots':opt_generic.get_raw_slots(a_group,type_of_promo='single') if not single_one_shot_ppg else no_of_slots,
                                    'is_single':True,
                                    'tot_promo_max':tot_promo_max,
                                    'tot_promo_min':tot_promo_min,
                                    'param_compulsory_promo_weeks':[]
                                })
                            _data = opt_utils._get_serializer_data(constraints)
                input_data_dict[_obj['product_group']]['data'] = {**_obj,
                                                                'weekly':df_dict,
                                                                **_data,
                                                                'include':bool(_data)
                                                                }
                input_data_dict = opt_utils.get_joint_all_brand_promo_from_mapping(_obj,
                                                        df_dict,
                                                        input_data_dict,
                                                        kwargs.get('scenario_type'),
                                                        rpt=kwargs.get('reatiler_ppgs_promp_types'),
                                                        constraints = constraints,
                                                        main_df=formated_df,
                                                        rpmuow=kwargs.get('rpmuow'),
                                                        national_list=None,
                                                        national_data=kwargs.get('promotion_level_data'),
                                                        basedata_df=basedata_df,
                                                        one_shot_ppg=one_shot_ppg
                                                        )
                
                if filtered_promotion_level_data:
                    input_data_dict = cmn_utils.post_update_natioal_promotion_mechanics(df_dict\
                                                                    ,filtered_promotion_level_data[0]['weekly']
                                                                    ,input_data_dict,
                                                                    ppg_main=_obj['product_group'],
                                                                    rpt=kwargs.get('reatiler_ppgs_promp_types')
                                                                    )
                if not input_data_dict[_obj['product_group']]['data']['include']:
                    input_data_dict.pop(_obj['product_group'])
            else:
                input_data_dict.pop(_obj['product_group'])
            if _obj['product_group'] in input_data_dict.keys():
                
                new_roi_data_df = kwargs.get('roi_data_df').loc[kwargs.get('roi_data_df')['product_group']==opt_generic.format_ppg2(_obj['product_group'])]
                new_roi_data_df = cmn_utils.format_headers(CONST.ROI_VALUES,CONST.ROI_HEADER,new_roi_data_df)
                _new_df = pd.DataFrame(df_dict)

                raw_columns = list(_new_df.columns)[1:]
                _new_df = _new_df.rename(columns={**kwargs.get('result_dict')})
                _new_df['account_name_new'] = _new_df['Retailer']
                _new_df['product_group_new'] = _new_df['PPG']
                _new_df['PPG'] = _obj['product_group']
                _new_df=_new_df.drop(columns=['index'])
                raw_columns+=['account_name_new','product_group_new']
                # _new_df.to_excel(f'{pp}.xlsx')
                #Saving base and national promo
                _d = {'account_name':_obj['account_name']\
                        ,'product_group':_obj['product_group']\
                        ,'reatiler_ppgs_promp_types':kwargs.get('reatiler_ppgs_promp_types')\
                        ,'data_headers':list(_new_df.columns)\
                        ,'raw_headers':raw_columns\
                        ,'base_and_national_promo':{'weekly_data':cmn_utils.to_json_serializable(process.convert_df_to_raw_query(_new_df))
                                                    ,'promotion_dict':cmn_utils.to_json_serializable(input_data_dict)\
                                                    ,'roi_data':cmn_utils.to_json_serializable(process.convert_df_to_raw_query(new_roi_data_df))
                                                    }}
                cmn_serv.post_base_and_national_promo(kwargs.get('bnpuow'),_d)
            
            # comment below for sequential
            # return input_data_dict
