import operator
from functools import reduce
from django.db.models import Q,Sum,F
from apps.pricing.pricing_common.query_filter import query_deep_drive_filter
from core.generics.respository import ORMModelRepository
from apps.pricing.pricing_common import models as db_model 

class PricingScenarioCommonRepository(ORMModelRepository):
    """ Meta Repository"""
    def __init__(self):
        super().__init__(db_model.PricingCommonScenarioView)

    def get(self, _id):
        """ Filter data based on id"""
        return self._model.filter(id=_id).first()
    
    def get_customer_ppg_scenario_data(self,customer_ppg_data:list=None):
        if customer_ppg_data:
            query = reduce(operator.or_, (Q(customer = cp['customer']) \
                                        |Q(product_group__in = cp.get('product_group',[])) \
                                            for cp in customer_ppg_data))
        
            return self._model.filter(query)
        return self._model.all()
    
    def get_customer_scenario_data(self,customers:list=None):
        return self._model.filter( customer__in=customers).order_by('-nsv')
    
    def get_meta_data_ppg_level(self):
        """ Filter pricing"""
        return self._model.values_list('product_group',flat=True).distinct().order_by('-nsv')
    
    def get_meta_data_customer_ppg_level(self):
        """ Filter pricing"""
        return self._model.values('customer','product_group').distinct().order_by('-nsv')
    
    def verify_and_filter_customers_with_inner_mode_all(self,saved_id,customer):
        """ Filter pricing"""
        saved_scenario_queryset = self._model.prefetch_related('changed_saved_pricing_scenario').filter(id=saved_id)
        customers_qryset = saved_scenario_queryset.filter(customer_level_param = customer)
        if not customers_qryset.exists():
            return 0
        return customer
    
    def get_meta_data_bulk_data(self,customer_ppg_data:list=None):
        query = reduce(operator.or_, (Q(customer = cp['customer']) \
                                      |Q(product_group__in = cp.get('product_group',[])) \
                                        for cp in customer_ppg_data))
        return self._model.filter(query).values('ogsm_type','customer'\
                                                ,'product_group','brand'\
                                                ,'technology','dead_net','net_net'\
                                                ,'competitor_follows'\
                                                ,'type_of_price_inc'\
                                                ,'status_flag').distinct().order_by('-nsv')
    
    def filter_by_pricing_scenario_id(self,pricing_scenario_id):
        """ Filter data based on account name"""
        return self._model.filter(id=pricing_scenario_id,is_delete=False,is_active=True)
    
    
    def filter_by_region(self,region):
        """ Filter data based on account name"""
        query = reduce(operator.or_, (Q(customer__icontains = item) \
                                      & Q(is_delete=False) \
                                        & Q(is_active=True) for item in region))
        return self._model.filter(query)

    def filter_by_retailer_and_ppg(self,acc_name,ppg):
        """ Filter data based on retailer and ppg"""
        return self._model.filter(customer=acc_name,product_name=ppg,is_delete=False,is_active=True)

    def get_all(self, **kwargs):
        return self._model.filter()

    def get_global_constraints_group_by_customer(self,pricing_saved_id:int=None):
        
        gp_query = self._model.annotate(aggregated_on=F("customer")).values("aggregated_on").annotate(
            net_net_nsv_product=100+Sum(F("changed_net_net_change_percent")*F('nsv'))/Sum(F('nsv'))*100\
            ,lp_nsv_product=100+Sum(F("changed_lp_percent")*F('nsv'))/Sum(F('nsv'))*100,
            target = Sum(F('nn_change_percent')),
            nsv_sum=Sum(F('nsv'))
            )
      
        gp_query = gp_query.values("aggregated_on").annotate(
                    actual_badget_customer=F('nsv_sum')*(F('lp_nsv_product')-F('net_net_nsv_product')+100)/100+F('nsv_sum'),
                    invest_badget=F('nsv_sum')*(F('lp_nsv_product')-F('target')+100)/100+F('nsv_sum'),
                    
                )
        
        result = gp_query.aggregate(actual_badget=Sum(F('actual_badget')),
                                    invest_badget=Sum(F('invest_badget'))
                                    )
        
        
        return result
    
class PricingScenarioCustomerLevelRepository(ORMModelRepository):
    """ Meta Repository"""
    def __init__(self):
        super().__init__(db_model.PricingScenarioCustomerLevelView)

    def get(self, _id):
        """ Filter data based on id"""
        return self._model.filter(id=_id).first()
    
    def get_meta_data_ppg_level(self):
        """ Filter pricing"""
        return self._model.values_list('product_group',flat=True).distinct()
    
    def get_customer_scenario_data(self,customers:list=None):
        return self._model.filter(customer__in=customers).order_by('-nsv_sum')
   
    def filter_by_pricing_scenario_id(self,pricing_scenario_id):
        """ Filter data based on account name"""
        return self._model.filter(id=pricing_scenario_id,is_delete=False,is_active=True)
    
    
    def filter_by_region(self,region):
        """ Filter data based on account name"""
        query = reduce(operator.or_, (Q(customer__icontains = item) \
                                      & Q(is_delete=False) \
                                        & Q(is_active=True) for item in region))
        return self._model.filter(query)

    def filter_by_retailer_and_ppg(self,acc_name,ppg):
        """ Filter data based on retailer and ppg"""
        return self._model.filter(customer=acc_name,product_name=ppg,is_delete=False,is_active=True)

    def get_all(self, **kwargs):
        return self._model.filter()

class PricingPublishedSavedScenarioRepository(ORMModelRepository):
    def __init__(self):
        super().__init__(db_model.PricingPublishedSavedScenario)

    def filter_by_id(self, _id):
        """ Filter data based on scenario id"""
        return self._model.filter(id=_id)

    def get(self, _id):
        """ Filter data based on id"""
        return self._model.filter(id=_id).first()
    
    
    def filter_by_saved_id(self, id):
        """ Filter data based on id"""
        return self._model.filter(id=id).first()
    
    def get_name(self, _id):
        """ Filter data based on id and return name, module_type, and scenario_type """
        result = self._model.filter(id=_id).values('name', 'module_type', 'scenario_type').first()
        if result:
            return {
                'scenario_name': result['name'],
                'module_type': result['module_type'],
                'scenario_type': result['scenario_type']
            }
        return None  # Handle the case when the id is not found or the result is empty

    
    def filter_by_scenario_name(self, scenario_name):
        """ Filter data based on scenario name"""
        return self._model.filter(name=scenario_name,is_delete=False,is_committed=True)
    
    def filter_by_scenario_name_and_scenario_type(self, scenario_name,scenario_type):
        """ Filter data based on scenario name"""
        return self._model.filter(name=scenario_name,scenario_type=scenario_type)
    
    def get_by_scenario_type(self,scenario_type,module_type:str='scenario_planner',user:dict=None):
        return self._model.prefetch_related('changed_saved_pricing_scenario')\
            .filter(Q(created_by__user_id=user['user_id'])\
                    ,scenario_type=scenario_type\
                    ,is_delete=False,is_committed=True)
    
    def get_my_scenario_by_scenario_type(self,scenario_type,user):
        return self._model.prefetch_related('changed_saved_pricing_scenario')\
        .filter(
            Q(created_by__user_id=user['user_id']),
                scenario_type=scenario_type\
                ,is_delete=False\
                ,is_committed=True
                )
    
    def filter_published_scenario(self):
        """ Filter data based on scenario id"""
        return self._model.filter(is_published=True,module_type='set_pricing')
    
    def get_all_scenarios_by_user(self,user:dict=None):
        return self._model.prefetch_related('changed_saved_pricing_scenario')\
            .filter(Q(created_by__user_id=user['user_id'])\
                    ,is_delete=False,is_committed=True)
            
    def verify_and_filter_customers_with_inner_mode_all(self,saved_id,customer):
        """ Filter pricing"""
        saved_scenario_queryset = self._model.prefetch_related('changed_saved_pricing_scenario').get(id=saved_id)
        customers_qryset = saved_scenario_queryset.changed_saved_pricing_scenario.filter(~Q(level_param=''),customer_level_param=customer,inner_mode='all')
        if not customers_qryset.exists():
            return 0
        return customer
    
    def get_shared_scenario_by_scenario_type(self,scenario_type,user):
        return self._model.prefetch_related('changed_saved_pricing_scenario')\
            .filter(shared_user__to__icontains=user['email']\
                    ,scenario_type=scenario_type\
                    ,is_delete=False\
                    ,is_committed=True\
                    ,is_review=False
                    )
    
    def get_shared_scenario_by_module_type(self,scenario_type='simulator',module_type='set_pricing',user=None):
        return self._model.prefetch_related('changed_saved_pricing_scenario')\
            .filter(shared_user__to__icontains=user['email']\
                    ,scenario_type=scenario_type\
                    ,module_type=module_type\
                    ,is_delete=False\
                    ,is_committed=True\
                    ,is_review=False
                    )
            
    def get_published_scenario_by_scenario_type(self,scenario_type,user):
        return self._model.prefetch_related('changed_saved_pricing_scenario')\
            .filter(is_published=True)
    
    def get_published_scenario_by_module_type(self,scenario_type='simulator',module_type='set_pricing',user=None):
        return self._model.prefetch_related('changed_saved_pricing_scenario')\
            .filter(module_type=module_type,is_published=True)
    
    def get_review_scenario_by_scenario_type(self,scenario_type,user):
        return self._model.prefetch_related('changed_saved_pricing_scenario')\
            .filter(shared_user__to__icontains=user['email']\
                    ,is_delete=False\
                    ,is_committed=True\
                    ,is_review=True\
                    ,approval_status='in_progress'
                    )
    
    def get_review_scenario_by_module_type(self,scenario_type='simulator',module_type='set_pricing',user=None):
        return self._model.prefetch_related('changed_saved_pricing_scenario')\
            .filter(shared_user__to__icontains=user['email']\
                    ,module_type=module_type\
                    ,is_delete=False\
                    ,is_committed=True\
                    ,is_review=True\
                    ,approval_status='in_progress'
                    )
    
    def get_track_scenario(self,scenario_type='simulator',user=None):
        return self._model.prefetch_related('changed_saved_pricing_scenario')\
            .filter(Q(approval_status='rejected')|Q(approval_status='in_progress'),shared_user__from__icontains=user['email']\
                    ,is_delete=False\
                    ,is_committed=True\
                    ,is_review=True
                    )
    
    def get_track_scenario_by_module_type(self,scenario_type='simulator',module_type='set_pricing',user=None):
        return self._model.prefetch_related('changed_saved_pricing_scenario')\
            .filter(Q(approval_status='rejected')|Q(approval_status='in_progress'),shared_user__from__icontains=user['email']\
                    ,module_type=module_type\
                    ,is_delete=False\
                    ,is_committed=True\
                    ,is_review=True
                    )
    
    def get_track_published_scenario(self,scenario_type='simulator',user=None):
        return self._model.prefetch_related('changed_saved_pricing_scenario')\
            .filter(shared_user__from__icontains=user['email']\
                    ,is_delete=False\
                    ,is_committed=True\
                    ,is_review=True\
                    ,approval_status='accepted'
                    )
    
    def get_track_published_scenario_by_module_type(self,scenario_type='simulator',module_type='set_pricing',user=None):
        return self._model.prefetch_related('changed_saved_pricing_scenario')\
            .filter(shared_user__from__icontains=user['email']\
                    ,module_type=module_type\
                    ,is_delete=False\
                    ,is_committed=True\
                    ,is_review=True\
                    ,approval_status='accepted'
                    )
            
    def delete(self, uid,user={}):
        self._model.get(id=uid).delete()

class ChangedPricingSavedScenarioRepository(ORMModelRepository):
    def __init__(self):
        super().__init__(db_model.ChangedPricingScenario)
    
    def filter_by_id(self, _id):
        """ Filter data based on scenario id"""
        return self._model.filter(id=_id)

    def filter_by_scenario_name(self, scenario_name):
        """ Filter data based on scenario name"""
        return self._model.filter(name=scenario_name,is_delete=False)
    
    def filter_by_scenario_name_and_scenario_type(self, scenario_name,scenario_type):
        """ Filter data based on scenario name"""
        return self._model.filter(name=scenario_name,scenario_type=scenario_type,is_delete=False)
    
    def get_ppg_cutomer_data(self,non_committed_id):
        return self._model.filter(pricing_saved_scenario_id=non_committed_id).values('level_param')
    
    def get_ppg_cutomer_level_data(self,non_committed_id):
        return self._model.filter(pricing_saved_scenario_id=non_committed_id).values('customer_level_param')
    
    
    def get_by_scenario_type(self,scenario_type,user):
        return self._model.select_related('pricing_saved_scenario')\
            .filter(Q(pricing_saved_scenario_created_by__user_id=user['user_id'])| Q(pricing_saved_scenario__shared_user__to__icontains=user['email'])\
                    ,scenario_type=scenario_type\
                    ,is_delete=False)
    
    def get_all_scenarios_by_user(self,user):
        return self._model.select_related('pricing_saved_scenario')\
            .filter(Q(pricing_saved_scenario_created_by__user_id=user['user_id'])| Q(pricing_saved_scenario__shared_user__to__icontains=user['email'])\
                    ,is_delete=False)
            
    def get_scenario_by_saved_id(self,pricing_saved_id):
        return self._model.filter(pricing_saved_scenario_id=pricing_saved_id)
    
    def get_customer_ppg_scenario_data(self, pricing_saved_scenario_id, customer_ppg_data=None):
        
        query = reduce(operator.or_, (
            Q(ogsm_param=cp['customer']) &
            Q(pricing_saved_scenario_id=pricing_saved_scenario_id)  # Add this condition
            for cp in customer_ppg_data
        ))
        return self._model.filter(query)
    
    
class PricingSummeryScenarioRepository(ORMModelRepository):
    def __init__(self):
        super().__init__(db_model.PricingScenarioSummary)
        
    def filter_by_saved_id(self, saved_scenario_id):
        """ Filter data based on id"""
        return self._model.filter(pricing_saved_scenario_id=saved_scenario_id).first()

    def filter_by_scenario_name(self, scenario_name):
        """ Filter data based on scenario name"""
        return self._model.filter(name=scenario_name,is_delete=False)
    
    def filter_by_scenario_name_and_scenario_type(self, scenario_name,scenario_type):
        """ Filter data based on scenario name"""
        return self._model.filter(name=scenario_name,scenario_type=scenario_type,is_delete=False)
    
    def get_by_scenario_type(self,scenario_type,user):
        return self._model.select_related('pricing_saved_scenario')\
            .filter(Q(pricing_saved_scenario_created_by__user_id=user['user_id'])| Q(pricing_saved_scenario__shared_user__to__icontains=user['email'])\
                    ,scenario_type=scenario_type\
                    ,is_delete=False)
    
    def get_scenario_by_saved_id(self,pricing_saved_id):
        return self._model.filter(pricing_saved_scenario_id=pricing_saved_id)

class PricingOverallSummeryScenarioRepository(ORMModelRepository):
    def __init__(self):
        super().__init__(db_model.PricingScenarioOverallSummary)

    def filter_by_multiple_id(self, _ids):
        """ Filter data based on list of scenario ids"""
        return self._model.filter(pricing_saved_scenario_id__in=_ids)
    
    def filter_by_scenario_name(self, scenario_name):
        """ Filter data based on scenario name"""
        return self._model.filter(name=scenario_name,is_delete=False)
    
    def filter_by_scenario_name_and_scenario_type(self, scenario_name,scenario_type):
        """ Filter data based on scenario name"""
        return self._model.filter(name=scenario_name,scenario_type=scenario_type,is_delete=False)
    
    def get_by_scenario_type(self,scenario_type,user):
        return self._model.select_related('pricing_saved_scenario')\
            .filter(Q(pricing_saved_scenario__created_by__user_id=user['user_id'])| Q(pricing_saved_scenario__shared_user__to__icontains=user['email'])\
                    ,pricing_saved_scenario__scenario_type=scenario_type\
                    ,pricing_saved_scenario__is_delete=False)
    
    def get_scenario_by_saved_id(self,pricing_saved_id):
        return self._model.filter(pricing_saved_scenario_id=pricing_saved_id)

class BaseAndSimulatedPricingScenarioRepository(ORMModelRepository):
    def __init__(self):
        super().__init__(db_model.BaseAndSimulatedPricingScenario)

    def filter_by_scenario_name(self, scenario_name):
        """ Filter data based on scenario name"""
        return self._model.filter(name=scenario_name,is_delete=False)
    
    def filter_by_scenario_name_and_scenario_type(self, scenario_name,scenario_type):
        """ Filter data based on scenario name"""
        return self._model.filter(name=scenario_name,scenario_type=scenario_type,is_delete=False)
    
    def get_by_scenario_type(self,scenario_type,user):
        return self._model.select_related('pricing_saved_scenario')\
            .filter(Q(pricing_saved_scenario_created_by__user_id=user['user_id'])| Q(pricing_saved_scenario__shared_user__to__icontains=user['email'])\
                    ,scenario_type=scenario_type\
                    ,is_delete=False)
    
    def get_scenario_by_saved_id(self,pricing_saved_id):
        return self._model.select_related('pricing_saved_scenario').filter(pricing_saved_scenario_id=pricing_saved_id)

    def get_simulated_constraints_by_saved_id(self,pricing_saved_id,is_base=True):
        return self._model.select_related('pricing_saved_scenario').filter(pricing_saved_scenario_id=pricing_saved_id,is_base=True).values('customer','ogsm_type','product_group','brand','technology').distinct()
    
    def filter_base_and_simulated_pricing_scenario_by_customer(self,pricing_saved_id,level='customer'):
        return self._model.select_related('pricing_saved_scenario').filter(pricing_saved_scenario_id=pricing_saved_id,customer=level)
    
    def filter_base_and_simulated_pricing_scenario_by_type(self,pricing_saved_id,is_base=False,filter_data=None):
        return self._model.select_related('pricing_saved_scenario').filter(pricing_saved_scenario_id=pricing_saved_id,is_base=is_base)

    
    def get_customer_ppg_scenario_data(self,customer_ppg_data:list=None,pricing_saved_id:int=None):
        if customer_ppg_data:
            query = reduce(operator.or_, (Q(customer = cp['customer']) \
                                        &Q(product_group__in = cp['product_group']) \
                                            for cp in customer_ppg_data))
            
            return self._model.select_related('pricing_saved_scenario').filter(query,pricing_saved_scenario_id=pricing_saved_id)
        return self._model.select_related('pricing_saved_scenario').filter(pricing_saved_scenario_id=pricing_saved_id)
    
    def filter_multiple_scenario(self,pricing_saved_ids):
        query = self._model.select_related('pricing_saved_scenario').filter(pricing_saved_scenario_id__in=pricing_saved_ids)
        return query
    
    def filter_base_and_simulated_pricing_scenario_by_multifilter(self,pricing_saved_id,is_base=True,filter_data=None):
        query = self._model.select_related('pricing_saved_scenario').filter(pricing_saved_scenario_id=pricing_saved_id)
        filter_data = {**filter_data,'is_base':is_base}
        filter_list = query_deep_drive_filter(filter_data=filter_data)
        query = query.filter(*filter_list)
        return query
    