trigger:
  branches:
    include:
      - refs/heads/develop
  paths:
    include:
      - /pnsrmmixandtpuksdevfunc/*
    exclude:
      - src/app/*
  batch: True
resources:
  repositories:
    - repository: self
      type: git
      ref: refs/heads/develop


jobs:
  - job: Job_1
    displayName: Build and Test
    pool:
      vmImage: ubuntu-latest
    steps:
    - bash: |
       if [ -f extensions.csproj ]
       then
           dotnet build extensions.csproj --output ./bin
       fi
      displayName: 'Build extensions'

    - task: UsePythonVersion@0
      displayName: 'Use Python 3.10'
      inputs:
        versionSpec: 3.10

    - bash: |
       python3.10 -m venv worker_venv
       source worker_venv/bin/activate
       pip3.10 install setuptools
       pip3.10 install -r '$(System.DefaultWorkingDirectory)'/pnsrmmixandtpuksdevfunc/requirements.txt
      env:
        BASH_ENV: ~/.profile

    - task: ArchiveFiles@2
      displayName: "Archive files"
      inputs:
        rootFolderOrFile: $(System.DefaultWorkingDirectory)/pnsrmmixandtpuksdevfunc
        includeRootFolder: false
        archiveType: zip
        archiveFile: $(Build.ArtifactStagingDirectory)/$(Build.BuildId)/pnsrmmixandtpuksdevfunc.zip
        replaceExistingArchive: true

    - task: PublishBuildArtifacts@1
      displayName: "Publish Artifact: drop"
      inputs:
        pathtoPublish: $(Build.ArtifactStagingDirectory)/$(Build.BuildId)/pnsrmmixandtpuksdevfunc.zip
  - job: Job_2
    displayName: Deploy to App Service
    dependsOn: Job_1
    pool:
      vmImage: ubuntu-latest
      name: DNA-selfhosted-agent-pool 
      demands: 
        - agent.name -equals pndevuksjumpbox
    steps:
    - task: AzureFunctionApp@1
      displayName: "Azure functions app deploy"
      inputs:
        azureSubscription: "PN-DNA-SRM-UK-WEBAPP"
        appType: "functionAppLinux"
        appName: "pnsrmmixandtpuksdevfunc"
        package: "$(Pipeline.Workspace)/**/$(Build.BuildId)/pnsrmmixandtpuksdevfunc.zip"
