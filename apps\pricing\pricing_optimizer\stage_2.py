# Databricks notebook source
import numpy as np
import pandas as pd
import scipy
from scipy.optimize import minimize
import functools
pd.set_option('mode.chained_assignment',None)

# COMMAND ----------

def objective_function(X, target, currentnetnet, currentchange, sign, NSV):
    newnetnet = currentnetnet * (1 + currentchange + sign * X)
    percentage_newnetnetdiff = newnetnet / currentnetnet - 1
    overall_perc_diff = (percentage_newnetnetdiff * NSV).sum()/NSV.sum()
    # newnetnet_overall = (newnetnet * opt_units).sum()/opt_units.sum()
    # current_netnet_overall = (currentnetnet * currunits).sum()/currunits.sum()
    # percentage_newnetnetdiff = newnetnet_overall / current_netnet_overall - 1
    return ((target - overall_perc_diff) ** 2) * 100

# COMMAND ----------

def get_percentage_diff(currentnetnet, newnetnet, NSV):
    percentage_newnetnetdiff = newnetnet / currentnetnet - 1
    overall_perc_diff = (percentage_newnetnetdiff * NSV).sum()/NSV.sum()
    return overall_perc_diff

# COMMAND ----------

def optimizer_per_customer(internal_comp,customer_name):
    internal_comp = internal_comp.rename(columns={"customer":"Customer","net_net":"NetNet_received_file","dead_net":"DeadNet_received_file","sell_in_unit":"sell_in_units","optimized_net_net_change":"OptNetNet % Change","optimized_trade_margin":"OptTradeMargin%","optimized_units":"OptUnit_rounded","nsv":"NSV","optimized_net_net":"OptNetNet","floor_price":"Floorprice","nn_change_percent":"target"})
    print("------------------------------------------------------------------")
    print(customer_name)
    internal_comp = internal_comp[internal_comp['Customer']==customer_name].reset_index(drop=True)
    # target = 0.001
    target = (internal_comp['target']).values[0]
    target = (float(target)/100) - 1
    currentnetnet = internal_comp['NetNet_received_file'].values
    currentchange = internal_comp['OptNetNet % Change'].values
    currunits = internal_comp['sell_in_units'].values
    opt_units = internal_comp['OptUnit_rounded'].values
    trade_margin_perc = internal_comp['OptTradeMargin%'].values
    OptNetNet = internal_comp["OptNetNet"].values
    NSV = internal_comp["NSV"].values
    curr_and_opt_diff_perc  = get_percentage_diff(currentnetnet, OptNetNet, NSV)
    print("curr_netnet_and_opt_diff_perc",curr_and_opt_diff_perc)
    sign = 1
    if curr_and_opt_diff_perc > target:
        sign = -1
    bounds = [(0.0, 0.0)] * currentnetnet.shape[0]
    column_indices_to_set_ten_perc = list(internal_comp[internal_comp['OptTradeMargin%'] > 0.4].index.values)
    new_netnet = internal_comp['OptNetNet'].values 
    X0 = np.zeros(currentnetnet.shape[0])
    for index in column_indices_to_set_ten_perc:
        upper_bound = 0.10 
        if sign == -1:
            upper_bound = min(0.10,abs(internal_comp.at[index,"Floorprice"]/internal_comp.at[index,"OptNetNet"]-1))
        bounds[index] = (0.0, upper_bound)
        new_netnet[index] =  internal_comp.at[index,'NetNet_received_file'] * (1 + internal_comp.at[index,'OptNetNet % Change'] +
                                                                            sign * upper_bound)
    curr_and_netnet_with_40_per_trade_margin_diff = get_percentage_diff(currentnetnet, new_netnet, NSV)
    print("curr_netnet_and_netnet_with_greater_than_40_per_trade_margin_diff",curr_and_netnet_with_40_per_trade_margin_diff)
    index_for_less_than_40 = internal_comp[internal_comp["OptTradeMargin%"]<=0.4].sort_values(by=['OptTradeMargin%'], ascending=False).index.values
    if (target - curr_and_netnet_with_40_per_trade_margin_diff) * (sign) >  0 :
        for index in index_for_less_than_40:
            if sign == 1:
                upper_bound = 0.05
            else:
                upper_bound = min(0.05,abs(internal_comp.at[index,"Floorprice"]/internal_comp.at[index,"OptNetNet"]-1))
            bounds[index] = (0.0, upper_bound)
            new_netnet[index] = internal_comp.at[index,'NetNet_received_file'] * (1 + internal_comp.at[index,'OptNetNet % Change'] +
                                                                                    sign * upper_bound)
            curr_and_netnet_with_less_than_40_per_trade_margin_diff = get_percentage_diff(currentnetnet, new_netnet, NSV)
            print("curr_netnet_and_netnet_with_less_than_40_per_trade_margin_diff",curr_and_netnet_with_less_than_40_per_trade_margin_diff)  
            if (target - curr_and_netnet_with_less_than_40_per_trade_margin_diff) * (sign) <= 0:
                break

    result = minimize(objective_function, X0, args=(target, currentnetnet, currentchange, sign, NSV),
                  bounds=tuple(bounds), method='trust-constr')
    optimized_X = result.x
    internal_comp['optimized_X'] = optimized_X
    internal_comp['newnetnet'] = internal_comp['NetNet_received_file'] * (1 + internal_comp['OptNetNet % Change'] +
                                                                           sign * internal_comp['optimized_X'])
    new_netnet = internal_comp['newnetnet'].values
    final_diff = get_percentage_diff(currentnetnet, new_netnet, NSV)
    print("Final Diff",final_diff)
    internal_comp["net_net_change"] = internal_comp['newnetnet']/internal_comp['NetNet_received_file'] - 1
    internal_comp["deadnet_new_stage2"] = internal_comp["DeadNet_received_file"] * (1+internal_comp["net_net_change"])
    print("---------------------------------------------------------------------------")
    return internal_comp

# COMMAND ----------

def stage_2_optimizer(data):
    optimised_result = pd.DataFrame()
    for customer_name in data.customer.unique():
        optimized_result_per_customer = optimizer_per_customer(data,customer_name)
        optimised_result = optimised_result.append(optimized_result_per_customer)
    return optimised_result
