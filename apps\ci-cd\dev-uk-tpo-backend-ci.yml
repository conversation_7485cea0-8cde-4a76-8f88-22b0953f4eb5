trigger:
  branches:
    include:
      - refs/heads/develop
  paths:
    include:
      - /*
    exclude:
      - src/app/*
  batch: True

resources:
  repositories:
    - repository: self
      type: git
      ref: refs/heads/develop

jobs:
  - job: Job_1
    displayName: Build and Test
    pool:
      vmImage: ubuntu-latest
    steps:
      - checkout: self
      - task: UsePythonVersion@0
        displayName: Use Python 3.8
        inputs:
          versionSpec: 3.8
      - task: CmdLine@2
        displayName: Install dependencies
        inputs:
          script: |
            python -m venv antenv
            source antenv/bin/activate
            python -m pip install --upgrade pip
            python -m pip install -r requirements.txt
      - task: ArchiveFiles@2
        displayName: Archive Application
        inputs:
          rootFolderOrFile: "$(Build.SourcesDirectory)"
          includeRootFolder: false
          archiveType: "zip"
          archiveFile: "$(Build.ArtifactStagingDirectory)/dev_uk_tpo_$(Build.BuildId).zip"
      - task: PublishBuildArtifacts@1
        displayName: "Publish Artifact: drop"
        inputs:
          pathtoPublish: "$(Build.ArtifactStagingDirectory)/dev_uk_tpo_$(Build.BuildId).zip"


  - job: Job_2
    displayName: Deploy to App Service
    dependsOn: Job_1
    pool:
      vmImage: ubuntu-latest
      name: DNA-selfhosted-agent-pool 
      demands: 
        - agent.name -equals pndevuksjumpbox
    steps:
      - task: DownloadPipelineArtifact@2
        displayName: "Download artifact"
        inputs:
          buildType: "current"
          artifact: drop

      - task: AzureRmWebAppDeployment@4
        displayName: "Deploy to Azure WebApp"
        inputs:
          ConnectionType: "AzureRM"
          azureSubscription: "PN-DNA-SRM-POLAND-WEBAPP"
          appType: "webAppLinux"
          WebAppName: "bepnsrmpricingtuksdevas"
          packageForLinux: "$(Pipeline.Workspace)/**/dev_uk_tpo_$(Build.BuildId).zip"
          RuntimeStack: "PYTHON|3.8"
          StartupCommand: "gunicorn config.wsgi:application --bind 0.0.0.0:8000 --timeout 720 --workers=4"
