""" Test Common Unit of work"""
import pytest
import factory
from ..tests.factories import FakeRepository
from .. import unit_of_work as uow

@pytest.mark.django_db
def test_meta_data():
    """test_meta_data
    """
    unit_of_work = uow.MetaUnitOfWork(factory.Faker("set_autocommit"))
    unit_of_work.meta_model = FakeRepository(
        [
            ("id", 1),
        ]
    )
    assert len(unit_of_work.repo_obj.get_all()) > 1

@pytest.mark.django_db
def test_model_data():
    """test_model_data
    """
    unit_of_work = uow.ModelDataUnitOfWork(factory.Faker("set_autocommit"))
    unit_of_work.md_model = FakeRepository(
        [
            ("id", 1),
        ]
    )
    assert len(unit_of_work.repo_obj.get_all()) == 104

@pytest.mark.django_db
def test_coeff_data():
    """test_coeff_data
    """
    unit_of_work = uow.CoeffUnitOfWork(factory.Faker("set_autocommit"))
    unit_of_work.coeff_model = FakeRepository(
        [
            ("id", 1),
        ]
    )
    assert len(unit_of_work.repo_obj.get_all()) > 1

@pytest.mark.django_db
def test_coeff_map_data():
    """test_coeff_map_data
    """
    unit_of_work = uow.CoeffMapUnitOfWork(factory.Faker("set_autocommit"))
    unit_of_work.cm_model = FakeRepository(
        [
            ("id", 1),
        ]
    )
    assert len(unit_of_work.repo_obj.get_all()) > 1

@pytest.mark.django_db
def test_roi_data():
    """test_roi_data
    """
    unit_of_work = uow.ModelROIUnitOfWork(factory.Faker("set_autocommit"))
    unit_of_work.rd_model = FakeRepository(
        [
            ("id", 1),
        ]
    )
    assert len(unit_of_work.repo_obj.get_all()) == 104

@pytest.mark.django_db
def test_rpm_data():
    """test_rpm_data
    """
    unit_of_work = uow.RetailerPPGMappingUnitOfWork(factory.Faker("set_autocommit"))
    unit_of_work.cm_model = FakeRepository(
        [
            ("id", 1),
        ]
    )
    assert len(unit_of_work.repo_obj.get_all()) > 1

@pytest.mark.django_db
def test_tactic_data():
    """test_tactic_data
    """
    unit_of_work = uow.TacticUnitOfWork(factory.Faker("set_autocommit"))
    unit_of_work.cm_model = FakeRepository(
        [
            ("id", 1),
        ]
    )
    assert len(unit_of_work.repo_obj.get_all()) > 1

@pytest.mark.django_db
def test_item_data():
    """test_item_data
    """
    unit_of_work = uow.ItemMapUnitOfWork(factory.Faker("set_autocommit"))
    unit_of_work.cm_model = FakeRepository(
        [
            ("id", 1),
        ]
    )
    assert len(unit_of_work.repo_obj.get_all()) > 1
    