[run]
omit=*/admin.py
     */manage.py
     */utils.py
     */urls.py
     */az-function-depettpo/*
     */config/*
     */apps/common/migrations/*.py
     */apps/user/*
     */apps/common/admin.py
     */apps/common/apps.py
     */apps/common/params.py
     */apps/common/queries.py
     */apps/common/urls.py
     */apps/common/utils.py
     */apps/common/serializers.py
     */apps/optimizer/admin.py
     */apps/optimizer/apps.py
     */apps/optimizer/models.py
     */apps/optimizer/params.py
     */apps/optimizer/queries.py
     */apps/optimizer/urls.py
     */apps/optimizer/utils.py
     */apps/optimizer/optimizer_tool/*
     */apps/optimizer/serializers.py
     */apps/optimizer/mixins.py
     */apps/optimizer/process.py
     */apps/optimizer/constants.py
     */apps/optimizer/calculations.py
     */apps/optimizer/exceptions.py
     */apps/scenario_planner/admin.py
     */apps/scenario_planner/apps.py
     */apps/scenario_planner/models.py
     */apps/scenario_planner/params.py
     */apps/scenario_planner/queries.py
     */apps/scenario_planner/urls.py
     */apps/scenario_planner/utils.py
     */apps/scenario_planner/serializers.py
     */apps/scenario_planner/mixins.py
     */apps/scenario_planner/process.py
     */apps/scenario_planner/constants.py
     */apps/scenario_planner/calculations.py
     */apps/scenario_planner/exceptions.py
     */core/*
     