'''Importing Modules'''
from django.conf import settings
from django.contrib.auth.models import AbstractBaseUser, BaseUserManager, \
    PermissionsMixin
from django.db import models


class UserManager(BaseUserManager):
    '''Creating Model with name UserManager'''

    def create_user(self, email, password=None, **extra_fields):
        ''' Create users and save'''
        if not email:
            raise ValueError('Email cannot be empty')
        user = self.model(email=self.normalize_email(email), **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password):
        '''Creating SuperUser and save'''
        user = self.create_user(email, password)
        user.is_staff = True
        user.is_superuser = True
        user.save(using=self._db)
        return user


class User(AbstractBaseUser, PermissionsMixin):
    '''Creating Model with name User'''
    email = models.EmailField(max_length=255, unique=True)
    name = models.CharField(max_length=255)
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)
    USERNAME_FIELD = 'email'
    objects = UserManager()

    def __str__(self):
        return str(self.email)
     
class RegionGroup(models.Model):
    """Region Group Model."""
    region = models.CharField(max_length=100,verbose_name="Region")
    group_id =  models.CharField(max_length=200,verbose_name="User Group")
    group_name = models.CharField(max_length=100,verbose_name="Group Name")

    def __str__(self):
        return "{}-{}".format(self.account_name,self.group_id)

    class Meta:
        """Meta Class For Region Group Model."""
        constraints = [
            models.UniqueConstraint(fields=['region','group_id','group_name']\
            ,name='region_usergroup')
        ]
        verbose_name = "Region Group"
        db_table = "region_group"

class RegionUserGroupMapping(models.Model):
    """Region Group Model."""
    user_id = models.CharField(max_length=250,verbose_name="User")
    group =  models.ForeignKey(
        'user.RegionGroup' , related_name="region_group" , on_delete=models.CASCADE
    )

    def __str__(self):
        return "{}-{}".format(self.user_id,self.group_id)

    class Meta:
        """Meta Class For Region User Group Mapping Model."""
        verbose_name = "Region User Group Mapping"
        db_table = "region_user_group_mapping"


class UserGroupRetailer(models.Model):
    region = models.CharField(max_length=100,verbose_name="Region",default='')
    group_id =  models.CharField(max_length=200,verbose_name="User Group")
    group_name = models.CharField(max_length=100,verbose_name="Group Name")
    is_national_region = models.BooleanField(default=False)
    is_promo = models.BooleanField(default=False)
    limited_access = models.BooleanField(default=False)
    is_pricing = models.BooleanField(default=False)
    is_pricing_all = models.BooleanField(default=False)
    is_promo_all = models.BooleanField(default=False)

    def __str__(self):
        return "{}-{}".format(self.region,self.group_id)
    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['region','group_id','group_name'],name='retailers_usergroup_mapping')
        ]
        db_table = 'retailers_usergroup_mapping'