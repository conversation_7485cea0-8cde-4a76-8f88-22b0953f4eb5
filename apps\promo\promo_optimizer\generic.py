""" Optimizer Generic Fuction"""
from itertools import permutations
import copy
from statistics import mean
import numpy as np
import pandas as pd
from core.generics.exceptions import AlreadyExists

def predict_sales(coeffs:pd.DataFrame,
                   data:pd.DataFrame):
    """Predict Sales

    Args:
        coeffs (DataFrame): DataFrame consists of coefficient data
        data (DataFrame): DataFrame consists of coefficient model data

    Returns:
        Series: Predicted Volume
    """
    predict = 0
    for i in coeffs['names']:
        if i == "Intercept":
            predict = predict + coeffs[coeffs['names'] == i]\
                                ["model_coefficients"].values
        else:
            if i not  in data:
                continue
            data[i] = data[i].astype(float)
            try:
                predict = predict + data[i] * coeffs[coeffs['names'] == i]["model_coefficients"].values
            except:
                breakpoint()
    data['pred_vol'] = predict
    data['Predicted_Volume'] = np.exp(data['pred_vol'])

    return data['Predicted_Volume']


def config_lag_function(Model_Coeff,config_dict):
  # function for ppgs with lag variables
  # if a lag variable is present, we will increase/decrease the ub and lb by 1%
    model_vars = Model_Coeff['names'].to_list()
    if ('TPR_lag1' in model_vars or 'TPR_lag2' in model_vars):
        fin_vars =['MAC_Perc','RP','MAC']
        for i in fin_vars:
            if ((config_dict['config_constrain'][i])):
                config_dict['constrain_params'][i] = config_dict['constrain_params'][i]+0.01
    return config_dict

def preprocess(sent):
    if isinstance(sent,str):
        # sent = sent.replace("-", "_")
        # sent = sent.replace("/", "")
        # sent = sent.replace("&", "")
        sent = sent.replace(" ", "_")
    return sent

def preprcoess_mapping(val):
    if 'Tactic_JP' in val:
        val = val.replace(" ", "_")
    return val

def format_product_group(ppg):
    ppg = sent = sent.replace(" ", "_")
    return ppg

def remove_special_char(series):
    series = series.replace('_'," ")
    return series

def get_slots(model_data_all,type_of_promo='single'):
    total_slots=model_data_all.loc[(model_data_all['Flag_promotype_Leaflet']!=0)]
    total_slots_df=model_data_all.loc[(model_data_all['Flag_promotype_Leaflet']!=0)]
    joint_flags = [j for j in total_slots_df.columns if 'joint_promo' in j]
    if type_of_promo == 'joint':
        total_slots_df =  total_slots_df[(total_slots_df[joint_flags] > 0).any(axis=1)]
        if total_slots_df.shape[0]:
            total_slots_df = total_slots_df.loc[(total_slots_df['Flag_promotype_all_brand']==0)]
                    
    elif type_of_promo == 'single':
        joint_flags = joint_flags+['Flag_promotype_all_brand']
        total_slots_df =  total_slots_df[(total_slots_df[joint_flags] == 0).all(axis=1)]

    elif type_of_promo == 'all_brand':
        total_slots_df = total_slots_df.loc[(total_slots_df['Flag_promotype_all_brand']>0)]

    return total_slots_df.shape[0]

def get_raw_slots(model_data_all,type_of_promo='single'):
    total_slots_df=model_data_all.loc[(model_data_all['promo_present']!=0)]
    joint_flags = [j for j in total_slots_df.columns if 'joint_promo' in j]
    if type_of_promo == 'joint':
        total_slots_df =  total_slots_df[(total_slots_df[joint_flags] > 0).any(axis=1)]
        if total_slots_df.shape[0]:
            total_slots_df = total_slots_df.loc[(total_slots_df['flag_promotype_all_brand']==0)]
                    
    elif type_of_promo == 'single':
        joint_flags = joint_flags+['flag_promotype_all_brand']
        total_slots_df =  total_slots_df[(total_slots_df[joint_flags] == 0).all(axis=1)]

    elif type_of_promo == 'all_brand':
        total_slots_df = total_slots_df.loc[(total_slots_df['flag_promotype_all_brand']>0)]

    return total_slots_df.shape[0]

def get_max_promo(model_data_all):
    max_promo = model_data_all.loc[(model_data_all['Flag_promotype_Leaflet']!=0)]
    return max_promo.shape[0]

def get_joint_promoted_weeks(_jnt_df,flag):
    weeks = _jnt_df.loc[(_jnt_df[flag]>0)&(_jnt_df['Flag_promotype_all_brand']==0),'Week'].to_list()
    
    return weeks

def get_all_brand_promoted_weeks(_jnt_df):
    weeks = _jnt_df.loc[(_jnt_df['Flag_promotype_all_brand']>0),'Week'].to_list()
    
    return weeks

def is_national_promo(_jnt_df):
    national_promo = _jnt_df.loc[(_jnt_df['Flag_promotype_all_brand']>0),'promotion_levels'].to_list()
    
    return national_promo

def get_all_brand_promoted_df(_jnt_df):
    _ab_df = _jnt_df.loc[(_jnt_df['Flag_promotype_all_brand']>0)]
    
    return _ab_df

def get_optimizer_input_all_brand(model_data_all,coeff_map,ppg=None,is_weekly=False):

    brand = ppg.split(' ',maxsplit=1)[0]
    brand = brand.split('_',maxsplit=1)[0]
    grouped_df = model_data_all.groupby(["Retailer",'PPG'])
    for key,_item in grouped_df:
        a_group = grouped_df.get_group(key).reset_index(drop=True)
        all_brand_group = a_group.loc[(a_group['Flag_promotype_all_brand']>0)]
        is_all_brand=False
        if all_brand_group.shape[0]:
            jnt_brand = key[1].split("_",maxsplit=1)[0]
            if brand == jnt_brand:
                c_map = coeff_map.loc[(coeff_map['Retailer']==key[0]) &(coeff_map['PPG']==key[1])]
                if 'Tactic_All_Brand' in c_map['Coefficient'].unique():
                    is_all_brand= True
        model_data_all.loc[(model_data_all['Retailer']==key[0])\
        &(model_data_all['PPG']==key[1]),'IS_ALL_BRAND']=is_all_brand
    ppgs = model_data_all.loc[model_data_all['IS_ALL_BRAND']==True,'PPG'].unique()

    if len(ppgs)>1:
        return True
    return False

def get_optimizer_input_joint(coeff_map,ppg=None):
    ppg_list = coeff_map['PPG'].unique()
    is_joint = False
    joint_flag = []
    
    for _index,ppg in enumerate(ppg_list):
        rest_ppg = list(filter(lambda x:x!=ppg,ppg_list))
        c_map = coeff_map.loc[(coeff_map['PPG']==ppg)]
        for _jppg in rest_ppg: 
            if f'Tactic_JP_{_jppg}' in c_map['Coefficient'].unique():
                joint_map = c_map.loc[c_map['Coefficient']==f'Tactic_JP_{_jppg}']
                joint_flag.append({ppg:joint_map['Coefficient_new'].values[0]})
    
    if len(joint_flag)>1:
        is_joint = True
    return is_joint,joint_flag


def get_joint_promo(coeff_map):
    jnt_flag = coeff_map.loc[coeff_map['Coefficient'].isin('Tactic_JP'),'Coefficient']
    return jnt_flag
    
def get_mech(promoted_data,ppg=None,type=None,one_shot_ppg=None):
    
    mechanics = list(set(list(map(lambda x:x['mechanic'],list(filter(lambda x1:x1 \
                if x1['type_of_promo'].lower() == type and x1['promo_present'] ==1 else '',promoted_data))))))
    # if one_shot_ppg:
    #     mechanics = list(set(list(map(lambda x:x['mechanic'],list(filter(lambda x1:x1,promoted_data))))))
    # filtered_mechanics = list(filter(lambda x:x,mechanics))
    # if not filtered_mechanics:
    #     return mechanics
    
    return mechanics

def get_visibility(promoted_data,ppg=None,type=None,one_shot_ppg=None):
    
    visibilty = list(set(list(map(lambda x:x['visibility'],list(filter(lambda x1:x1 \
                if x1['type_of_promo'].lower() == type and x1['promo_present'] ==1 else '',promoted_data))))))
    if len(visibilty) > 1:
        visibilty = [i for i in visibilty if i != '' ]

    return visibilty

def get_avg_tpr(data_df):
    try:
        avg_tpr_replace = [round(data_df[data_df['Flag_promotype_Leaflet']!=0]['TPR'].mean())]
    except:
        avg_tpr_replace = [0]
    return avg_tpr_replace

def get_shelf_price(data_df):
    grouped_df = data_df.groupby(["Retailer",'PPG'])
    avg_tpr_replace_list=[]
    for key,_item in grouped_df:
        a_group = grouped_df.get_group(key).reset_index(drop=True)
        avg_tpr_replace = round(a_group['Promo_Price_Per_Unit'].mean())
        avg_tpr_replace_list.append(avg_tpr_replace)
        avg_tpr_replace = mean(avg_tpr_replace_list)
    return [avg_tpr_replace]

def get_joint_promotion(ppg):
    ppg_list = copy.deepcopy(ppg)
    list_combinations=[]
    for _p in range(len(ppg_list)+1):
        list_combinations+= list(permutations(ppg_list,_p+1))
    list_combinations = list(map(lambda x:'-'.join(x),list_combinations))
    return list_combinations

def get_promo_type(data):
    joint_promotions = data.loc[(data["Flag_promotype_joint_promo_1"] > 0) | 
    (data["Flag_promotype_joint_promo_2"] > 0) |
    (data["Flag_promotype_joint_promo_3"] > 0)
    ].reset_index(drop=True).shape[0]
    
    promo_type='single'
    if joint_promotions:
        promo_type='single/joint'
    return promo_type

def rename_tactic(_df):
    shadow_df= copy.deepcopy(_df)
    shadow_df.rename(columns={'PPG':'ppg_index',
                        'ppg_index':'product_group',
                        'Retailer':'retailer_index',
                        'retailer_index':'account_name',
                        'Promo_Type':'promo_type',
                        'Avg_units_per_Week':'avg_units_per_week',
                        'TPR':'tpr',
                        'Type_of_Promo':'type_of_promo',
                        'Leaflet_flag':'leaflet_flag',
                        'Display_flag':'display_flag',
                        'Ad_without_price_flag':'ad_without_price_flag',
                        'Back_page_flag':'back_page_flag',
                        'Bonus_flag':'bonus_flag',
                        'Front_page_flag':'front_page_flag',
                        'Logo_flag':'logo_flag',
                        'Pas_flag':'pas_flag',
                        'TE_perweek':'te_per_week',
                        'ListPrice_perweek':'list_price_per_week',
                        'COGS_perweek':'cogs_per_week',
                        'maxWeek':'max_week',
                        'minWeek':'min_week',
                        'minWeek_edlp':'min_week_edlp',
                        'maxWeek_edlp':'max_week_edlp',
                        'GSV_perweek':'gsv_per_week',
                        'Optimized_Weeks':'optimized_weeks',
                        'Optimized_TE_perweek':'optimized_te_per_week',
                        'OptWeek_Check':'opt_week_check',
                        'MAC_Opt':'mac_opt',
                        'MAC_Min':'mac_min',
                        'MAC_Max':'mac_max',
                        'OptMac_Check':'opt_mac_check',
                        },inplace=True)
    return shadow_df

def update_params(config:dict,
                request_value:dict,
                _no_of_leaflet:str,
                min_length_gap:str,
                _no_of_promo:str
                )->dict:
    """update_params

    Args:
        config (dict): config
        request_value (dict): request_value
        no_of_leaflet (str): no_of_leaflet
        min_length_gap (str): min_length_gap
        no_of_promo (str): no_of_promo

    Returns:
        dict: config
    """
    minimize_params = ['Trade_Expense']
    c_p = 'constrain_params'
    c_c = 'config_constrain'
    config['Retailer'] = request_value['account_name']
    config['PPG'] = request_value['product_group']
    config['MARS_TPRS'] = request_value['promo_depth']
    config['TPR_Mech'] = request_value['mechanic']
    config['Objective'] = 'Minimize' if request_value['objective_function'] in minimize_params\
                                    else 'Maximize'
    config['Objective_metric'] = request_value['objective_function']
    config['Segment'] = request_value['corporate_segment']
    config['Fin_Pref_Order'] = request_value['fin_pref_order']
    config['is_all_brand'] = request_value['is_all_brand']
    config[c_p]['MAC'] = request_value['param_mac']
    config[c_p]['RP'] = request_value['param_rp']
    config[c_p]['Trade_Expense'] = request_value['param_trade_expense']
    config[c_p]['Units'] = request_value['param_units']
    config[c_p]['NSV'] = request_value['param_nsv']
    config[c_p]['GSV'] = request_value['param_gsv']
    config[c_p]['Sales'] = request_value['param_sales']
    config[c_p]['MAC_Perc'] = request_value['param_mac_perc']
    config[c_p]['RP_Perc'] = request_value['param_rp_perc']
    config[c_p]['min_consecutive_promo'] = request_value['param_min_consecutive_promo']
    config[c_p]['max_consecutive_promo'] = request_value['param_max_consecutive_promo']
    config[c_p]['promo_gap'] = min_length_gap
    config[c_p]['tot_promo_min'] = request_value['param_total_promo_min']
    config[c_p]['tot_promo_max'] = request_value['param_total_promo_max']
    config[c_p]['compul_no_promo_weeks'] = request_value['param_compulsory_no_promo_weeks']
    config[c_p]['compul_promo_weeks'] = request_value['param_compulsory_promo_weeks']
    config[c_c]['MAC'] = request_value['config_mac']
    config[c_c]['RP'] = request_value['config_rp']
    config[c_c]['Trade_Expense'] = request_value['config_trade_expense']
    config[c_c]['Units'] = request_value['config_units']
    config[c_c]['NSV'] = request_value['config_nsv']
    config[c_c]['GSV'] = request_value['config_gsv']
    config[c_c]['Sales'] = request_value['config_sales']
    config[c_c]['MAC_Perc'] = request_value['config_mac_perc']
    config[c_c]['RP_Perc'] = request_value['config_rp_perc']
    config[c_c]['min_consecutive_promo'] = request_value['config_min_consecutive_promo']
    config[c_c]['max_consecutive_promo'] = request_value['config_max_consecutive_promo']
    config[c_c]['promo_gap'] = True
    config[c_c]['automation'] = True

    return config
    
def format_ppg(ppg):
    if '_' in ppg:
        ppg = " ".join(ppg.split("_"))
    # if ',' in ppg:
    #     ppg = " & ".join(ppg.split(','))
    if '_-_' in ppg:
        ppg = " & ".join(ppg.split('_-_'))
    if ' - ' in ppg:
        ppg = " & ".join(ppg.split(' - '))
    return ppg

def format_ppg2(ppg):
    return "_".join(ppg.split(" "))

def format_ppg3(ppg):
    return "_-_".join(ppg.split("_&_"))

def format_ppg4(ppg):
    return "_&_".join(ppg.split("_-_"))

def format_ppg5(ppg):
    return " - ".join(ppg.split(" & "))

def format_ppg6(ppg):
    return "_-_".join(ppg.split(","))

def format_ppg7(ppg):
    return " ".join(ppg.split("_"))

def format_all_brand(ab_ppg):
    ab_ppg = ab_ppg.replace("-all_brand",'')
    ab_ppg = ab_ppg.replace(' - all brand','')
    ab_ppg = ab_ppg.replace('-all brand','')
    ab_ppg = ab_ppg.replace(' & all brand','')
    ab_ppg = ab_ppg.replace('&all_brand','')
    return ab_ppg

def habdle_one_shot_ppg_with_format(ppg,one_shot_ppg_list,type_of_promo='single',ppg1=None):
    # if type
    # if ppg1 == 'WHISKAS_CAT_CARE&TREATS_180_G':
    #     breakpoint()

    # if ppg1 == format_ppg('WHISKAS_CAT_CARE&TREATS_180_G'):
    #     breakpoint()
    one_shot_ppg = list(filter(lambda x: format_ppg3(format_ppg2(x[1])) in ppg,one_shot_ppg_list))
    new_one_shot_ppg = list(filter(lambda x:x[2]==type_of_promo,one_shot_ppg))
    if not one_shot_ppg:
        one_shot_ppg = list(filter(lambda x:ppg == format_ppg3(format_ppg2(x[1])),new_one_shot_ppg))
    return one_shot_ppg

def habdle_one_shot_ppg(ppg,one_shot_ppg_list,type_of_promo='single',ppg1=None):
    # if ppg1 == 'WHISKAS_CAT_CARE&TREATS_180_G':
    #     breakpoint()

    # if ppg1 == format_ppg('WHISKAS_CAT_CARE&TREATS_180_G'):
    #     breakpoint()
    one_shot_ppg = list(filter(lambda x:format_ppg(x[1]) in ppg,one_shot_ppg_list))
    new_one_shot_ppg = list(filter(lambda x:x[2]==type_of_promo,one_shot_ppg))
    if not one_shot_ppg:
        one_shot_ppg = list(filter(lambda x:ppg == format_ppg(x[1]),new_one_shot_ppg))
    return one_shot_ppg

def handle_joint_and_all_brand_ppg(_d,acc_name,ppg,top,key=0,_in=False):
    # joint_ppg = "_-_".join(ppg.split(','))
    # joint_ppg = " ".join(joint_ppg.split('_'))
    joint_ppg =format_ppg7(format_ppg6(ppg))
    all_brand_ppg = format_all_brand(joint_ppg)
    if _d['promo_type'] == top and _d['account_name'] == acc_name and _d['product_group']==joint_ppg:
        return True
    if _d['promo_type'] == top and _d['account_name'] == acc_name and _d['product_group']==all_brand_ppg:
        return True
    return False

def validate_scenario(func):
    def wrapper(*args, **kwargs):
        if not kwargs.get('is_optional',False):
            scenario_name = args[0].get('scenario_name',None)
            is_load = args[0].get('is_load',False)
            is_optimizer_or_planner = kwargs.get('is_optimizer_or_planner',False)
            if not is_optimizer_or_planner:
                uow = args[3] if args[0].get('scenario_type')=='optimizer' else args[4]
                if not is_load:
                    with uow as unit_of_work:
                        data = unit_of_work.repo_obj.filter_by_scenario_name(scenario_name)
                    
                    if data.exists():
                        raise  AlreadyExists(scenario_name)
        return func(*args, **kwargs)
    return wrapper
