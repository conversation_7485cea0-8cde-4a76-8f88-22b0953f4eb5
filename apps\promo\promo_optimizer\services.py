""" Optimizer Services"""
from __future__ import annotations
import ast
import os
import re
import logging
from datetime import datetime
from apps.promo.promo_scenario_planner import task
import utils
from apps.common import utils as cmn_utils,serializers,services,unit_of_work as cuow #pylint: disable=E0401
from core.generics import(constants as CONST, unit_of_work as _uow,resp_utils,exceptions) #pylint: disable=E0401 
from . import process,queries,utils as opt_utils,generic as opt_generic
from .optimizer_tool import (summary,joint_calendar,_optimizer_manager)

logger = logging.getLogger(__name__)

def get_scenario(uow: _uow.AbstractUnitOfWork,scenario_id: int=None,user=None,scenario_view=''):
    """Return the Baseline data based on with or without scenario id input.

    Parameters
    ----------
    scenario id(Optional)
        Saved Scenario Id.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    list
        list of dicts if successful, empty list otherwise.

    """
    with uow as unit_of_work:
        
        if scenario_id:
            return unit_of_work.repo_obj.filter_by_id(scenario_id)
        if scenario_view == 'self':
            return unit_of_work.repo_obj.get_my_scenario_by_scenario_type('optimizer',user)
        if scenario_view == 'shared':
            return unit_of_work.repo_obj.get_shared_scenario_by_scenario_type('optimizer',user)
       
        return unit_of_work.repo_obj.get_by_scenario_type('optimizer',user)
    
@resp_utils.handle_json
def save_optimizer_scenaro(**data):
    serialized_data = serializers.ScenarioPromotionSerializer(
        data=data.get('request_data'))
    if serialized_data.is_valid():
        serialized_data.save(saved_scenario=data.get('instance'),id=data.get('instance').id)
        return {'saved_id':data.get('instance').id}
    
    raise exceptions.MissingRequestParamsError("status", serialized_data.errors)

@resp_utils.handle_json
def update_optimizer_scenaro(**data):
    
    serialized_data = serializers.ScenarioPromotionSerializer(
        data.get('instance'),data=data.get('request_data'),partial=True)
    if serialized_data.is_valid():
        serialized_data.save()
        return {'updated_id':data.get('instance').id}
    
    raise exceptions.MissingRequestParamsError("status", serialized_data.errors)

def handle_meta_data(_data):
    return {'account_name':_data.get('account_name','')\
            ,'product_group':_data.get('product_group','')\
            ,'corporate_segment':_data.get('corporate_segment','')\
            ,'brand':_data.get('brand','')\
            ,'brand_tech':_data.get('brand_tech','')\
            ,'product_type':_data.get('product_type','')\
            ,'type_of_promo':_data.get('type_of_promo','')}

def handle_joint_promo(**dt):
    new_dict = {}
    if dt['input_constraints']:
        for _dt in dt['input_constraints']['data']:
            _dt = _dt if not isinstance(_dt,list) else _dt[0]
            _ppg = _dt.get('product_group') 
            if _dt['type_of_promo'] == 'joint':     
                if _dt['ppg_belongs_to'] in new_dict.keys():            
                    new_dict[_dt['ppg_belongs_to']]['joint_ppg_data'].append(_dt)
                    if new_dict[_dt['ppg_belongs_to']]['joint_ppg_data'] and new_dict[_dt['ppg_belongs_to']]['all_brand_data']:
                        new_dict[_dt['ppg_belongs_to']]['type_of_promo']='single/joint/all_brand'
                        continue
                    if new_dict[_dt['ppg_belongs_to']]['joint_ppg_data']:
                        new_dict[_dt['ppg_belongs_to']]['type_of_promo']='single/joint'
                        continue
                    if new_dict[_dt['ppg_belongs_to']]['all_brand_data']:
                        new_dict[_dt['ppg_belongs_to']]['type_of_promo']='single/all_brand'
                        continue
                    new_dict[_ppg]['type_of_promo']='joint'
                    continue         
                new_dict[_dt['ppg_belongs_to']]=handle_meta_data(_dt)
                new_dict[_dt['ppg_belongs_to']]['product_group']=_dt['ppg_belongs_to']
                new_dict[_dt['ppg_belongs_to']]['single_ppg_data']=[]
                new_dict[_dt['ppg_belongs_to']]['all_brand_data']=[]
                new_dict[_dt['ppg_belongs_to']]['joint_ppg_data']=[]
                new_dict[_dt['ppg_belongs_to']]['joint_ppg_data'].append(_dt)
            elif _dt['type_of_promo'] == 'all_brand':
                if _dt['ppg_belongs_to'] in new_dict.keys():  
                    new_dict[_dt['ppg_belongs_to']]['all_brand_data'].append(_dt)
                    if new_dict[_dt['ppg_belongs_to']]['joint_ppg_data'] and new_dict[_dt['ppg_belongs_to']]['all_brand_data']:
                        new_dict[_dt['ppg_belongs_to']]['type_of_promo']='single/joint/all_brand'
                        continue
                    if new_dict[_dt['ppg_belongs_to']]['joint_ppg_data']:
                        new_dict[_dt['ppg_belongs_to']]['type_of_promo']='single/joint'
                        continue
                    if new_dict[_dt['ppg_belongs_to']]['all_brand_data']:
                        new_dict[_dt['ppg_belongs_to']]['type_of_promo']='single/all_brand'
                        continue
                    new_dict[_ppg]['type_of_promo']='all_brand'
                    continue         
                new_dict[_dt['ppg_belongs_to']]=handle_meta_data(_dt)
                new_dict[_dt['ppg_belongs_to']]['product_group']=_dt['ppg_belongs_to']
                new_dict[_dt['ppg_belongs_to']]['single_ppg_data']=[]
                new_dict[_dt['ppg_belongs_to']]['all_brand_data']=[]
                new_dict[_dt['ppg_belongs_to']]['joint_ppg_data']=[]
                new_dict[_dt['ppg_belongs_to']]['all_brand_data'].append(_dt)
            else:
                if _ppg in new_dict.keys():
                    new_dict[_ppg]['single_ppg_data'].append(_dt)
                    if new_dict[_ppg]['joint_ppg_data'] and new_dict[_ppg]['all_brand_data']:
                        new_dict[_ppg]['type_of_promo']='single/joint/all_brand'
                        continue
                    if new_dict[_ppg]['joint_ppg_data']:
                        new_dict[_ppg]['type_of_promo']='single/joint'
                        continue
                    if new_dict[_ppg]['all_brand_data']:
                        new_dict[_ppg]['type_of_promo']='single/all_brand'
                        continue
                    new_dict[_ppg]['type_of_promo']='single'
                    continue

                new_dict[_ppg]=handle_meta_data(_dt)
                new_dict[_ppg]['single_ppg_data']=[]
                new_dict[_ppg]['joint_ppg_data']=[]
                new_dict[_ppg]['all_brand_data']=[]
                new_dict[_ppg]['type_of_promo']='single'
                new_dict[_ppg]['single_ppg_data'].append(_dt)

        dt['input_constraints']['data'] = list(map(lambda x:x[1],new_dict.items()))
    else:
        dt['input_constraints']['data'] = {}
    return dt['input_constraints']

def post_scenario(request_data: dict, uow: _uow.AbstractUnitOfWork,user):
    """Save Scenrio

    Parameters
    ----------
    data
        To insert the data into the database.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    str
        Success or Failure in inserting the data

    """
    data = request_data.copy()
    promotion_data = {
        'input_constraints':handle_joint_promo(input_constraints=data.pop('input_constraints'),handle=True),
        "data":data.pop('data')
    }
    data['created_by'] = user
    data['user'] = user
  
    with uow as unit_of_work:
        
        query = unit_of_work.repo_obj.filter_by_scenario_name_and_scenario_type(data.get('name'),data.get('scenario_type'))
        if  query.exists():
            raise exceptions.AlreadyExists(data.get('name'))
        scenario_instance = unit_of_work.repo_obj.add(data)
        unit_of_work.commit()
    
    return save_optimizer_scenaro(request_data=promotion_data,instance=scenario_instance)

def put_scenario(scenario_instance: dict, request_data: dict, uow: _uow.AbstractUnitOfWork,user):
    """Return the Baseline data based on with or without scenario id input.

    Parameters
    ----------
    scenario id(Optional)
        Saved Scenario Id.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    list
        list of dicts if successful, empty list otherwise.
    """

    if not scenario_instance:
        raise exceptions.MissingRequestParamsError("id", scenario_instance)
    
    data = request_data.copy()
    promotion_data = {
        'input_constraints':handle_joint_promo(input_constraints=data.pop('input_constraints')),
        "data":data.pop('data')
    }
    data['modified_by'] = user
    data['modified_at'] = datetime.now()
 
    with uow as unit_of_work:
        unit_of_work.repo_obj.update(scenario_instance.id,data)
        unit_of_work.commit()

    return update_optimizer_scenaro(request_data=promotion_data,
                                instance=scenario_instance)

def delete_scenario(scenario_id: int,user,uow: _uow.AbstractUnitOfWork):
    """Return the Baseline data based on with or without scenario id input.

    Parameters
    ----------
    scenario id(Optional)
        Saved Scenario Id.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    list
        list of dicts if successful, empty list otherwise.
    """
    print(user,'user-------------')
    if not scenario_id:
        raise exceptions.MissingRequestParamsError("id", scenario_id)
    with uow as unit_of_work:
        unit_of_work.repo_obj.delete(scenario_id,user)
        unit_of_work.commit()
    return {'status':'success'}


def compare_scenario(uow: _uow.AbstractUnitOfWork,saved_ids:list=None):
    """Return the Saved data based on with scenario id input.

    Parameters
    ----------
    scenario id(Optional)
        Saved Scenario Id.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    list
        list of dicts if successful, empty list otherwise.
    """
    with uow as unit_of_work:
        query_set = unit_of_work.repo_obj.filter_by_multiple_id(saved_ids)

    if not query_set.exists():
        raise  exceptions.NoDataError(saved_ids)
    return query_set

@opt_generic.validate_scenario
def get_optimizer_scenario_constraints(data:dict\
                                       ,_uow1:_uow.AbstractUnitOfWork\
                                        ,_uow2:_uow.AbstractUnitOfWork\
                                        ,puow:_uow.AbstractUnitOfWork\
                                        ,user):

    product_group = data.get('product_group',None) if not isinstance(data.get('product_group',None),str) \
            else ast.literal_eval(data.get('product_group',None))
    promotion_level_data = []

    with puow as pl_unit_of_work:
        pl_queryset = pl_unit_of_work.repo_obj.filter_by_region(data.get('account_name'))
    
    if pl_queryset.exists():
        promotion_level_data =  pl_queryset[0].promotion_level_data
            
    input_list = _optimizer_manager.OptimizerManager(account_name=data.get('account_name'),
                            product_group=product_group,
                            scenario_name=data.get('scenario_name'),
                            is_fetch_constraints=True,
                            plvl_data=promotion_level_data
                            ).get_updated_inputs()
    return input_list

def search_scenario(uow: _uow.AbstractUnitOfWork,query_params:str=''):
    """Returns searched data based on given query.

    Parameters
    ----------
    query_params:
        search query.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    Queryset
        raw query set.
    """
    
    with uow as unit_of_work:
        query_params_values = list(query_params.values())
        if 'viewall' in query_params_values:
            query_params_values.remove('viewall')
        query_params_values[0] = '%' + query_params_values[0] + '%'
        query = {}
        if len(query_params_values) == 2:
            query = queries.search_optimizer_scenario_query() 
        else: 
            query = queries.search_optimizer_global_scenario_query() 

        _data = unit_of_work.search(query,query_params_values)
    if not _data:
        raise  exceptions.NoDataError(query_params)
    return _data


def post_optimize_scenario(uow:_uow.AbstractUnitOfWork,
                itm_uow:_uow.AbstractUnitOfWork,
                rpm_uow:_uow.AbstractUnitOfWork,
                puow:_uow.AbstractUnitOfWork,
                bnpuow:_uow.AbstractUnitOfWork,
                data:dict=None,
                user:dict=None
                )->list:
    """optimize

    Args:
        uow (_uow.AbstractUnitOfWork): _description_
        itm_uow (_uow.AbstractUnitOfWork): _description_
        rpm_uow (_uow.AbstractUnitOfWork): _description_
        puow (_uow.AbstractUnitOfWork): _description_
        data (dict, optional): dict. Defaults to {}.
        user (dict, optional): dict. Defaults to {}

    Returns:
        list: optimizer output
    """
    optimizer_data = data.get('optimizer_data',{})
    
    input_data = _optimizer_manager.OptimizerManager(
                            data=optimizer_data.get('data',[]),
                            param_mac=utils.convert_to_int(optimizer_data.get('param_mac',1)),
                            param_nsv=utils.convert_to_int(optimizer_data.get('param_nsv',1)),
                            param_rp=utils.convert_to_int(optimizer_data.get('param_rp',1)),
                            param_units=utils.convert_to_int(optimizer_data.get('param_units',1)),
                            config_mac=optimizer_data.get('config_mac',False),
                            config_nsv=optimizer_data.get('config_nsv',False),
                            config_rp=optimizer_data.get('config_rp',False),
                            config_units=optimizer_data.get('config_units',False),
                            objective_fun=optimizer_data.get('objective_fun','MAC'),
                            no_of_leaflet=optimizer_data.get('no_of_leaflet',None),
                            min_length_gap=optimizer_data.get('min_length_gap',None),
                            no_of_promo=optimizer_data.get('no_of_promo',None),
                            scenario_name=optimizer_data.get('scenario_name',None),
                            all_slot_utilization = optimizer_data.get('all_slot_utilization',False),
                            visibility=optimizer_data.get('visibility',[])
                    ).get_updated_inputs()
    
    reatiler_ppgs_promp_types = list(map(lambda x:(x.get('account_name')\
                                    ,opt_generic.format_ppg2(x.get('product_group'))\
                                    ,x.get('promo_type'),x.get('ppg_type')),input_data['data']))
  
    retailer = reatiler_ppgs_promp_types[0][0] 
    ppgs =  list(map(lambda x : x[1].split('_-_') if '_-_' in x[1] else x[1] ,reatiler_ppgs_promp_types))
    new_ppg_list= []
    
    for _p in ppgs:
        if isinstance(_p,list):
            new_ppg_list.extend(_p)
        else:
            new_ppg_list.append(_p)
   
    new_ppg_list = list(set(list(map(lambda x:opt_generic.format_all_brand(x),new_ppg_list))))
    new_ppg_list_with_retailer = list(set(list(map(lambda x:(retailer,opt_generic.format_all_brand(x)),new_ppg_list))))
    rpm_data = list(services.get_list_dict_value_from_rpm_data_query(
                                    rpm_uow,
                                    new_ppg_list_with_retailer,
                                    start_index=1
                                    ))[0]
    reatiler_ppgs_types = list(map(lambda x:(x.get('account_name')\
                                    ,opt_generic.format_ppg2(x.get('product_group'))\
                                    ,x.get('type_of_promo'),x.get('ppg_type'),x.get('retailer_index'),x.get('ppg_index')),rpm_data))
   
    coeff_mapping_df,model_coeff_df = services.get_df_from_query(
                                                                uow,
                                                                [
                                                                CONST.ModelCoeffMap,
                                                                CONST.ModelCoeff
                                                                ],
                                                                retailer,
                                                                new_ppg_list,
                                                                start_index=2
                                                      )
                                                      
    base_data = {'product_group':list(map(lambda x:opt_generic.format_ppg(x),list(model_coeff_df['PPG'].unique()))),
                 'account_name':retailer,
                 'scenario_type':'optimizer'
                 }
   
    mdl_df,roi_df = services.get_weekly_constraints(
                                    base_data,
                                    bnpuow,
                                    puow,
                                    is_optimizer_or_planner=True,
                                    _user=user
                                    )
  
    mdl_df,roi_df,model_coeff_df,coeff_mapping_df = process.format_data(mdl_df,
                                                                        roi_df,
                                                                     model_coeff_df,
                                                                     coeff_mapping_df)
 
    _basedata_df = services.get_opt_tactic2(rpm_uow,reatiler_ppgs_promp_types)
    _rpm_weekly_df = services.get_retailer_ppg_weekly(rpm_uow,retailer)
   
    summary_df = summary.get_optimized_summary(tactic_df=_basedata_df,
                                                mdl_df=mdl_df,
                                                roi_df = roi_df,
                                                coeff_mapping_df=coeff_mapping_df,
                                                model_coeff_df=model_coeff_df,
                                                _rpm_weekly_df=_rpm_weekly_df,
                                                total_slots=input_data['no_of_leaflet'],
                                                no_of_promo=input_data['no_of_promo'],
                                                min_length_gap=input_data['min_length_gap'],
                                                all_slot_utilization = input_data.get('all_slot_utilization',False),
                                                input_data=input_data['data'],
                                                reatiler_ppgs_types=reatiler_ppgs_types
                                                )                      
    new_ppg_list = [ppg for ppg in new_ppg_list]
    with itm_uow as unit_of_work:
        values = CONST.ITEMMap['MODEL_VALUES'].value.copy()
        columns = CONST.ITEMMap['MODEL_COLUMN'].value.copy()
        new_columns = CONST.ITEMMap['REQUIRED_COLUMN'].value\
            +CONST.ITEMMap['EXTRA_COLUMNS'].value
        if new_columns:
            for index,(name,_mask_name) in enumerate(new_columns):
                cmn_utils.insert_extra_var(values,index,name)
                cmn_utils.insert_extra_var(columns,index,_mask_name)
        basedata_query = queries.get_item_map_data(utils.convert_list_to_string_list(values),                                        
                                                    new_ppg_list)
        basedata_query = re.sub("[\"\']", "", basedata_query)
        basedata_df = unit_of_work.get_data_df(
            basedata_query,
            named_columns=columns,
            params=([retailer] + list(new_ppg_list)),
            start_index=1
        )

    roi_df['List Price'] = roi_df['List_Price']
    roi_df  = roi_df.iloc[:, :-2]

    # breakpoint()
    optimizer_output = joint_calendar.process_joint_promo(
                                                        mdl_df=mdl_df,
                                                        roi_df = roi_df,
                                                        coeff_mapping_df=coeff_mapping_df,
                                                        model_coeff_df=model_coeff_df,
                                                        item_map_df = basedata_df,
                                                        input_data=input_data,
                                                        summary_df = summary_df,
                                                        mdl_query=process.convert_df_to_raw_query(mdl_df),
                                                        roi_query=process.convert_df_to_raw_query(roi_df.iloc[:, :-1]),
                                                        coeff_mapping_query=process.convert_df_to_raw_query(coeff_mapping_df),
                                                        model_coeff_query=process.convert_df_to_raw_query(model_coeff_df),
                                                        rpm_uow=rpm_uow,
                                                        uow=uow,
                                                        puow=puow,
                                                        user=user
                                                    )
    

    return optimizer_output

def get_promo_levels(uow: _uow.AbstractUnitOfWork):
    """Return the Promotion Levels data.

    Parameters
    ----------

    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    list
        list of dicts if successful, empty list otherwise.

    """
    with uow as unit_of_work:
        return unit_of_work.repo_obj.get_all()
    
def get_promotion_levels_data(uow: _uow.AbstractUnitOfWork\
                              ,puow: _uow.AbstractUnitOfWork\
                             ,rpmuow: _uow.AbstractUnitOfWork=None\
                              ,user=None
                              ,region='Edeka',
                              access_directly=True,
                              is_initial=False,
                              bpuow:_uow.AbstractUnitOfWork=None,
                              is_optional=False
                              ):

    if not access_directly:
        if not user['is_national_promotion_allowed']:
            return 

    with puow as pl_unit_of_work:
        pl_queryset = pl_unit_of_work.repo_obj.filter_by_region(region)
        if user:
            pl_queryset = pl_unit_of_work.repo_obj.filter_by_user(user.get('user_id'),region)
            if not pl_queryset.exists():
                pl_queryset = pl_unit_of_work.repo_obj.filter_by_region(region)

    if  pl_queryset.exists():
        return pl_queryset[0].promotion_level_data
    elif is_initial:

        
        # This code is used for getting national promo.
        national_region = os.getenv('NATIONAL_REGION')
        meta_data = services.get_meta_data(uow,account_name=national_region)
        product_group = list(meta_data.values_list('product_group',flat=True))
        product_group = list(map(lambda x:opt_generic.format_ppg(x),product_group))
        account_name = national_region
        data={'account_name':account_name,'product_group':product_group}
        
        response = services.get_weekly_constraints(
                                data,
                                bpuow,    
                                puow,
                                is_optional=True
                            )
        return response
        
def save_promotion_level(uow: _uow.AbstractUnitOfWork,request_data: dict,user:dict):
    """Save Scenrio

    Parameters
    ----------
    data
        To insert the data into the database.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    str
        Success or Failure in inserting the data

    """
    # i
    data = request_data.copy()
    data['is_updated'] = True
    data['promotion_level_user'] = user
    data['level_type'] = 'national'
    data['region']='Edeka'

    with uow as unit_of_work:
        pl_queryset = uow.repo_obj.filter_by_user(user['user_id'])
        if pl_queryset.exists():
            pl_instance = pl_queryset[0]
            unit_of_work.repo_obj.update(pl_instance.id,request_data)
            unit_of_work.commit()
        else:
            pl_instance = unit_of_work.repo_obj.add(data)
            unit_of_work.commit()

    return {'saved_id':pl_instance.id}

def update_promotion_level(uow: _uow.AbstractUnitOfWork,request_data: dict,pl_instance:int,user=None):
    """Save Scenrio

    Parameters
    ----------
    data
        To insert the data into the database.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    str
        Success or Failure in inserting the data

    """
    data = request_data.copy()
    data['promotion_level_user'] = user
    data['is_updated'] = True
    data['level_type'] = 'national'
    data['region']='Edeka'

    with uow as unit_of_work:
        unit_of_work.repo_obj.update(pl_instance.id,request_data)
        unit_of_work.commit()

    return {'updated_id':pl_instance.id}
