""" Common API Views."""
import ast
import json

import pandas as pd
import structlog
from django.db import transaction
from django.http import (HttpResponse, HttpResponseBadRequest,
                         HttpResponseNotAllowed, JsonResponse)
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import (OpenApiExample, OpenApiParameter,
                                   extend_schema)
from openpyxl import load_workbook
from rest_framework import viewsets
from rest_framework.decorators import parser_classes,permission_classes
from rest_framework.filters import SearchFilter
from rest_framework.pagination import PageNumberPagination
from rest_framework.parsers import (<PERSON><PERSON>ploadParser, FormParser, JSONParser,
                                    MultiPartParser)
from rest_framework.response import Response

from apps.common import services as cmn_serv
from apps.pricing.set_pricing import services as set_services
from apps.pricing.set_pricing import unit_of_work as set_uow
from core.generics import api_handler, excel, exceptions, oauth_token_validate,permissions
from core.generics import resp_utils as resp_util  # pylint: disable=E0401
from core.generics import search
# from convert_to_queryset import list_to_queryset
# from django_filters.rest_framework import DjangoFilterBackend
from paginator import StandardPaginator, pagination

from . import models as db_model
from . import serializers
from . import serializers as ser
from . import services, unit_of_work

logger = structlog.get_logger(__name__)

class PricingScenarioView(viewsets.GenericViewSet):
    serializer_class = serializers.PricingScenarioSerializer

    @extend_schema(
        summary="Get Baseline Data.",
        description="API end point that serves the Baseline Data.",
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    def get_pricing_scenario_meta_data(self,request):
        """Get meta data from db

        Args:
            request (dict): HttpRequest object

        Returns:
            list: list of serialized data
        """

        level = request.query_params.get('level')
        scenario_name = request.query_params.get('scenario_name')
        scenario_type = request.query_params.get('scenario_type')
        logger.bind(method_name="get_pricing_scenario_meta_data", app_name="Pricing Common")
        response = services.get_pricing_scenario_meta_data(
            unit_of_work.PricingCommonScenarioUnitOfWork(transaction),
            suow=unit_of_work.PricingPublishedSavedScenarioUnitOfWork(transaction),
            scenario_name=scenario_name,
            scenario_type=scenario_type,
            level=level,
            user=request._user
        )

        if level:
            return list(response)
        serializer = serializers.PricingMetaScenarioSerializer(
            response, many=True)
        return serializer.data

    @extend_schema(
        summary="Get elasticity information for retailer and PPG.",
        description="API end point that fetches elasticity information for retailer and PPG.",
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    def get_pricing_retailer_ppg_elasticity_info(self,request):
        """Get pricing retailer ppg elasticity  data from db for a set of PPGs

        Args:
            request (dict): HttpRequest object

        Returns:
            list: dictionary of elasticity information 
        """
        list_of_ppgs = request.data.get('product_groups')
        response = services.get_pricing_retailer_ppg_elasticity_info(list_of_ppgs)

        return response
    

    @extend_schema(
        summary="Get Baseline Data.",
        description="API end point that serves the Baseline Data.",
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    # @pagination({'serializer':serializers.PricingMetaScenarioSerializer})
    def get_pricing_scenario_data(self,request):
        """Get meta data from db

        Args:
            request (dict): HttpRequest object

        Returns:
            list: list of serialized data
        """

        scenario_planner_payload = request.data.get('scenario_planner_payload')
        customer = request.data.get('customer')
        level = request.data.get('level',None)
        logger.bind(method_name="get_pricing_scenario_data", app_name="Pricing Common")
        serializer = self.serializer_class
        uow = unit_of_work.PricingCommonScenarioUnitOfWork
        if level=='customer':
            uow = unit_of_work.PricingScenarioCustomerLevelUnitOfWork 
            serializer = serializers.PricingScenarioCustomerLevelSerializer
   
        response = services.get_pricing_scenario_data(
            uow(transaction),
            scenario_planner_payload,
            customer,
            level,
            user=request._user
        )
        if level=='bulk':
            return list(response)
        serializer = serializer(
            response, many=True)
        return serializer.data
 
    @extend_schema(
        summary="Simulate Scenario",
        description="API end point that serves the Simulate Scenario.",
        # request=ser.ScenarioPlannerRequestSerializer,
        # responses=ser.ScenarioPlannerRequestAndResponseSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @api_handler.API_REQUEST_QUERY_HANDLER
    @resp_util.validate_input_parameters
    @resp_util.handle_response

    def post_simulate_scenario(self,request):
        logger.bind(method_name="post_simulate_scenario", app_name="Pricing Common")
        response = services.simulate_pricing_scenario(unit_of_work.PricingCommonScenarioUnitOfWork(transaction),
                                                      unit_of_work.PricingPublishedSavedScenarioUnitOfWork(transaction),
                                                    request.data,
                                                    request._user
                                                    )
        data = services.get_name_of_id(
            unit_of_work.PricingPublishedSavedScenarioUnitOfWork(transaction),
            response['non_committed_id']
        )
        if data:
            response['scenario_name'] = data['scenario_name']
            response['module_type'] = data['module_type']
            response['scenario_type'] = data['scenario_type']
        else:
            response['scenario_name'] = None
            response['module_type'] = None
            response['scenario_type'] = None
        return response
    
    @extend_schema(
        summary="Load Saved scenario Based on saved id",
        description="API end point that serves the Loading Saved scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
  
    def get_global_constraints(self,request):
        customers = request.data.get('customers')
        response = services.global_constraints(
            unit_of_work.PricingCommonScenarioUnitOfWork(transaction),
            customers=customers
        )
        return response
    
class PricingPublishedSavedScenarioView(viewsets.GenericViewSet):
    serializer_class = serializers.PricingPublishedSavedScenarioSerializer
    pagination_class = StandardPaginator
    filter_backends = [DjangoFilterBackend, SearchFilter, ]
    search_fields = ['name', 'scenario_type','status_type','module_type']
    filterset_fields = ['status_type']
    page_size_query_param = 'page_size'
    page_query_param = 'page'
    lookup_field = 'id'
    lookup_url_kwarg = 'saved_id'
  
    def get_queryset(self):
        return services.get_scenario(
                        unit_of_work.PricingPublishedSavedScenarioUnitOfWork(transaction),
                        user=self.request._user,
                        scenario_id= self.request.parser_context['kwargs'].get('saved_id',None),
                        scenario_type=self.request.query_params.get('scenario_type',None),
                        module_type=self.request.query_params.get('module_type',None),
                        scenario_view=self.request.query_params.get('scenario_view',None)
                    ).order_by('-id')
    
    @extend_schema(
        summary="Save scenario Based on saved id",
        description="API end point that serves saving scenario.",
        # request=ser.ScenarioPlannerSaveRequestSerializer,
        # responses=ser.ScenarioPlannerSavedResponseSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @api_handler.API_REQUEST_QUERY_HANDLER
    @resp_util.validate_input_parameters
    @resp_util.handle_response
   
    def save_scenario(self,request):
        logger.bind(method_name="save_scenario", app_name="Pricing Common")
        response = services.save_scenario(
            request.data, 
            unit_of_work.PricingPublishedSavedScenarioUnitOfWork(transaction),
                request._user,
                is_commit=True
        )
        return response
    
    
    @extend_schema(
        summary="Load Saved scenario Based on saved id",
        description="API end point that serves the Loading Saved scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
  
    def get_global_constraints(self,request):
        uow = unit_of_work.PricingPublishedSavedScenarioUnitOfWork
        if request.data.get('from_optimizer',False):
            uow = unit_of_work.PricingSummeryScenarioUnitOfWork
        response = services.global_constraints(
            uow(transaction),
            request.parser_context['kwargs'].get('saved_id',None)
        )
        return response
    
    @parser_classes([FileUploadParser])
    def upload_scenario_planner(self, request, *args, **kwargs):
        file_serializer = ser.UploadedFileplannerSerializer(data=request.data)
        slug = self.kwargs.get('slug')
        name,non_committed_id = slug.split(',')
        if file_serializer.is_valid():
            uploaded_file_path = file_serializer.validated_data['file']
            workbook = load_workbook(uploaded_file_path, data_only=True)
            worksheet = workbook.active
            num_records = worksheet.max_row - 2 

            combined_data = {
                'payload': {
                    "level": "product_group",
                    "name": name,
                    "scenario_type": "simulator",
                    "module_type": "scenario_planner",
                    "status_type": "active",
                    "mode": "filtered"
                },
                'pricing_payload': [],  # Initialize an empty list for payload data
            }

            
            data_range = worksheet[f'A3:N{num_records + 2}']
        
            for row in data_range:
                row_data = {
                    'ogsm_param': row[0].value,
                    'customer': row[1].value,
                    'level_param': row[2].value,
                    'list_price_new': row[3].value,
                    'pack_weight_new': row[4].value,
                    'non_promo_price_new': row[5].value,
                    'promo_price_new': row[6].value,
                    'floor_price':row[7].value,
                    'changed_net_net_change_percent': row[9].value,
                    'net_net_new': row[10].value,
                    'changed_dead_net_change_percent': row[12].value,
                    'dead_net_new': row[13].value,
                    'customer_level_param': row[1].value,
                    'type_of_base_inc_after_change': 'Base',
                    # Add other columns as needed, e.g., 'ppg': row[2].value, etc.
                }
                combined_data['pricing_payload'].append(row_data)
            response = services.upload_scenario(
                combined_data,
                unit_of_work.PricingPublishedSavedScenarioUnitOfWork(transaction),
                non_committed_id,
                request._user,
                is_commit=True
            )

            return Response(response, status=201)
    
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    
    def save_post_simulate_scenario(self,request):
        response = services.save_post_load_scenario(
            unit_of_work.PricingPublishedSavedScenarioUnitOfWork(transaction),
                request.parser_context['kwargs'].get('non_committed_id',None),
                request._user
        )

        return response
    
    @extend_schema(
        summary="Get Saved scenario Based on saved id",
        description="API end point that serves the scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    @pagination({'serializer':serializers.PricingPublishedSavedScenarioSerializer})
    def get_saved_scenario(self,request):
        logger.bind(method_name="get_saved_scenario", app_name="Pricing Common")
        saved_id = request.parser_context['kwargs'].get('saved_id',None)
        status_type = request.query_params.get('status_type',None)
        query_set = self.get_queryset()
        if status_type and status_type in ['active','completed']:
            query_set = query_set.filter(status_type=status_type)
        if saved_id:
            query_set = [self.get_object()]


        return query_set
    
    @extend_schema(
        summary="Update scenario Based on saved id",
        description="API end point that serves the scenario Based on saved id.",
        # request=ser.ScenarioPlannerSaveRequestSerializer,
        # responses=ser.ScenarioPlannerUpdatedResponseSerializer,
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @api_handler.API_REQUEST_QUERY_HANDLER
    @resp_util.validate_input_parameters
    @resp_util.handle_response
    def update_scenario(self,request):
        logger.bind(method_name="update_scenario", app_name="Pricing Common")
        response = services.put_scenario(
            self.get_object(), request.data,\
            unit_of_work.PricingPublishedSavedScenarioUnitOfWork(transaction),
            request._user
        )
        return response

    @extend_schema(
        summary="Delete scenario Based on saved id",
        description="API end point that serves the scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        responses="Deleted Successfully"
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response

    def delete_scenario(self,request):
        logger.bind(method_name="delete_scenario", app_name="Pricing Common")
        response = services.delete_scenario(
            self.get_object().id,
            request._user, 
            unit_of_work.PricingPublishedSavedScenarioUnitOfWork(transaction)
        )
        return response
    

    
    
    @extend_schema(
        summary="Load Saved scenario Based on saved id",
        description="API end point that serves the Loading Saved scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    # @permission_classes([permissions.IsOwnerOrReadOnly])
    def load_scenario(self,request):
        customers = request.query_params.get('customers',None)
        is_optimizer = json.loads(request.query_params.get('is_optimizer','false').lower())
        level = request.query_params.get('level',None)
        uow = unit_of_work.PricingCommonScenarioUnitOfWork
        if level=='customer':
            uow = unit_of_work.PricingScenarioCustomerLevelUnitOfWork 
   
        response = services.saved_input_load_scenario(
            uow(transaction),
            unit_of_work.PricingPublishedSavedScenarioUnitOfWork(transaction),
            request.parser_context['kwargs'].get('saved_id',None),
            level,
            customers=customers,
            is_optimizer=is_optimizer
        )
        return response


    @extend_schema(
    summary="publish scenario Based on saved id",
    description="API end point that serves publishing scenario.",
    # request=ser.ScenarioPlannerSaveRequestSerializer,
    # responses=ser.ScenarioPlannerSavedResponseSerializer
    )

    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
   
    def publish_scenario(self,request,saved_id=None):
        logger.bind(method_name="publish_scenario", app_name="Pricing Common")
        response = services.publish_scenario(
            unit_of_work.PricingPublishedSavedScenarioUnitOfWork(transaction),
                request.parser_context['kwargs'].get('saved_id',None),
                request._user
        )
        return response
    
    @extend_schema(
        summary="Share Scenario",
        description="API end point that serves the saved scenario."
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    def share_scenario(self,request):
        logger.bind(method_name="get_scenario", app_name="Pricing Common")
        
        response = cmn_serv.share_scenario(request.data\
                                           ,unit_of_work.PricingPublishedSavedScenarioUnitOfWork(transaction)\
                                            ,user=request._user\
                                            ,scenario_type='Pricing Common')
        return response


    @extend_schema(
        summary="Share Scenario",
        description="API end point that serves the saved scenario."
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    def scenario_approval(self,request):
        logger.bind(method_name="get_scenario", app_name="Pricing Common")
        response = cmn_serv.scenario_approval(request.data\
                                           ,unit_of_work.PricingPublishedSavedScenarioUnitOfWork(transaction)\
                                           ,self.get_object().id\
                                            ,user=request._user\
                                            ,scenario_type='Pricing Common')
        return response
    
    @extend_schema(
        summary="Share Scenario",
        description="API end point that serves the saved scenario."
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    def get_scenario_permissions(self,request):
        logger.bind(method_name="get_scenario", app_name="Pricing Common")
        serializer =serializers.PricingPermissionScenarioSerializer(
            [self.get_object()], many=True,context = {"request": request})
        return serializer.data
    
    @extend_schema(
    summary="Search  Data based on name/promotion type/status type/scenario type .",
    description="API end point that serves the Baseline Data based on name,promotion type,status type,.",
    parameters=[
        OpenApiParameter(
            name="Search  Data based on name/promotion type/status type/scenario type ",
            description="Filter by name/promotion type/status type/scenario type",
            required=True,
            type=str,
            examples=[
                OpenApiExample(
                    "Example 1",
                    summary="Filter by name/promotion type/status type/scenario type.",
                    description="name/promotion type/status type/scenario type should be string type.",
                    value=1,
                )
            ],
        )
    ],
    # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @api_handler.API_REQUEST_QUERY_HANDLER
    @resp_util.validate_input_parameters
    @resp_util.handle_response
  
    def search(self,request,*args,**kwargs): 
        param = request.query_params.get('q','')
        if not param:
            raise exceptions.MissingRequestParamsError("q", param)

        logger.bind(method_name="search", app_name="Pricing Common")
        
        response = services.search_scenario(
            unit_of_work.PricingPublishedSavedScenarioUnitOfWork(transaction),
            query_params=request.query_params
        )
        
        serializer = self.serializer_class(
            response, many=True)
        return serializer.data
    
class ChangedPricingScenarioView(viewsets.GenericViewSet):
    serializer_class = serializers.ChangedSavedPricingScenarioSerializer
    filter_backends = [search.CustomSearchFilter]
    search_fields  = ['name','status_type','scenario_type','promo_type']
    pagination_class = PageNumberPagination
    page_size_query_param = 'page_size'
    page_query_param = 'page'
    lookup_field = 'id'
    lookup_url_kwarg = 'saved_id'
    db_model = db_model.ChangedPricingScenario

    def get_queryset(self):
        return services.get_scenario(
                        unit_of_work.ChangedPricingScenarioUnitOfWork(transaction),
                        user=self.request._user
                    ).order_by('-id')
    
    @extend_schema(
        summary="Get Baseline Data.",
        description="API end point that serves the Baseline Data.",
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    # @pagination({'serializer':serializers.PricingMetaScenarioSerializer})
    def saved_inputs(self,request):
        """Get meta data from db

        Args:
            request (dict): HttpRequest object

        Returns:
            list: list of serialized data
        """

        level = request.data.get('level',None)
        response = services.get_saved_filltered_data(
            unit_of_work.PricingCommonScenarioUnitOfWork(transaction),
            request.parser_context['kwargs'].get('saved_id',None),
            level=level
        )
       
        return response
    

    @resp_util.handle_response
    def upload_scenario_data(self,request):

        scenario_planner_payload = request.data.get('scenario_planner_payload')
        customer = scenario_planner_payload[0].get('customer')
        non_committed_id = request.data.get('non_committed_id')
        level = request.data.get('level',None)
        serializer = ser.PricingScenarioSerializer
        logger.bind(method_name="get_upload_data", app_name="Pricing Common")
        first_response = [] 
        if non_committed_id:
            uow = unit_of_work.PricingCommonScenarioUnitOfWork
            if level=='customer':
                uow = unit_of_work.PricingScenarioCustomerLevelUnitOfWork
            first_response =  services.upload_input_load_scenario(
                uow(transaction),
                unit_of_work.PricingPublishedSavedScenarioUnitOfWork(transaction),
                non_committed_id,
                level,
                customers=customer
            )
        else:
            uow = unit_of_work.PricingCommonScenarioUnitOfWork
            first_response = services.get_pricing_scenario_data(
                uow(transaction),
                scenario_planner_payload,
                customer,
                level,
                user=request._user
            )
        serializer = serializer(first_response, many=True)
        first_response = serializer.data
        uow = unit_of_work.ChangedPricingScenarioUnitOfWork
        serializer = ser.ChangedSavedPricingScenarioSerializer
        second_response = services.get_upload_scenario_data(
            uow(transaction),
            scenario_planner_payload,
            customer,
            level,
            non_committed_id
        )
        serializer = serializer(second_response, many=True)
        second_response = serializer.data
        second_response_values = {}
        for entry in second_response:
            key = (entry['level_param'], entry['ogsm_param'])
            second_response_values[key] = {
                "changed_net_net_change_percent": entry["changed_net_net_change_percent"]*100,
                "changed_dead_net_change_percent":entry["changed_dead_net_change_percent"]*100,
                "list_price_new": entry["list_price_new"],
                "pack_weight_kg_new": entry["pack_weight_kg_new"],
                "dead_net_new": entry["dead_net_new"],
                "net_net_new": entry["net_net_new"],
                "non_promo_price_new": entry["non_promo_price_new"],
                "promo_price_new": entry["promo_price_new"],
            }
        # Update the first response with values from the second response
        updated_first_response = []
        for entry in first_response:
            key = (entry['product_group'], entry['customer'])
            if key in second_response_values:
                second_response_entry = second_response_values[key]
                entry["changed_net_net_change_percent"] = second_response_entry["changed_net_net_change_percent"]
                entry["changed_dead_net_change_percent"] = second_response_entry["changed_dead_net_change_percent"]
                entry["list_price_new"] = second_response_entry["list_price_new"]
                entry["pack_weight_kg_new"] = second_response_entry["pack_weight_kg_new"]
                entry["dead_net_new"] = second_response_entry["dead_net_new"]
                entry["net_net_new"] = second_response_entry["net_net_new"]
                entry["non_promo_price_new"] = second_response_entry["non_promo_price_new"]
                entry["promo_price_new"] = second_response_entry["promo_price_new"]
                # Update other fields as needed
            updated_first_response.append(entry)
        return updated_first_response
 
class PricingSummeryScenarioView(viewsets.GenericViewSet):
    serializer_class = serializers.PricingSummeryScenarioSerializer
    filter_backends = [search.CustomSearchFilter]
    search_fields  = ['name','status_type','scenario_type','promo_type']
    pagination_class = PageNumberPagination
    page_size_query_param = 'page_size'
    page_query_param = 'page'
    lookup_field = 'id'
    lookup_url_kwarg = 'saved_id'

    def get_queryset(self):
        return services.get_scenario(
                        unit_of_work.PricingSummeryScenarioUnitOfWork(transaction),
                        user=self.request._user
                    ).order_by('-id')
    
    @extend_schema(
        summary="Load Saved scenario Based on saved id",
        description="API end point that serves the Loading Saved scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
  
    def load_scenario(self,request):
        response = services.load_scenario(
            unit_of_work.PricingSummeryScenarioUnitOfWork(transaction),
            request.parser_context['kwargs'].get('saved_id',None)
        )
        name = services.get_name_of_id(
            unit_of_work.PricingPublishedSavedScenarioUnitOfWork(transaction),
            request.parser_context['kwargs'].get('saved_id',None)
        )
        serializer = serializers.PricingSummeryScenarioSerializer(
            response, many=True) 
        serializer.data[0]['name'] = name['scenario_name']
        serializer.data[0]['module_type'] = name['module_type']
        serializer.data[0]['scenario_type'] = name['scenario_type']
        return serializer.data
   
    @extend_schema(
        summary="Comapare scenario Based on saved id",
        description="API end point that serves the Comapare scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_ids",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id should be type string.",
                        value="[1,2]",
                    )
                ],
            )
        ],
        # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    def compare_scenario(self,request,*_args,**_kwargs):
        logger.bind(method_name="compare_scenario", app_name="Scenario Scenario")
        saved_ids = ast.literal_eval(request.query_params.get('saved_ids',[]))
        response = services.compare_scenario(
            unit_of_work.PricingSummeryScenarioUnitOfWork(transaction),
            saved_ids
        )
        serializer = serializers.PricingSummeryScenarioSerializer(
            response, many=True)     
        return serializer.data
    
class PricingOverallSummeryScenarioView(viewsets.GenericViewSet):
    serializer_class = serializers.PricingOverallSummeryScenarioSerializer
    filter_backends = [search.CustomSearchFilter]
    search_fields  = ['name','status_type','scenario_type','promo_type']
    pagination_class = PageNumberPagination
    page_size_query_param = 'page_size'
    page_query_param = 'page'
    lookup_field = 'id'
    lookup_url_kwarg = 'saved_id'

    def get_queryset(self):
        return services.get_scenario(
                        unit_of_work.PricingOverallSummeryScenarioUnitOfWork(transaction),
                        user=self.request._user
                    ).order_by('-id')
    
    @extend_schema(
        summary="Load Saved scenario Based on saved id",
        description="API end point that serves the Loading Saved scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
  
    def load_scenario(self,request):
        response = services.load_scenario(
            unit_of_work.PricingOverallSummeryScenarioUnitOfWork(transaction),
            request.parser_context['kwargs'].get('saved_id',None)
        )
        serializer = serializers.PricingOverallSummeryScenarioSerializer(
            response, many=True)     
        return serializer.data
    
    @extend_schema(
        summary="Comapare scenario Based on saved id",
        description="API end point that serves the Comapare scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_ids",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id should be type string.",
                        value="[1,2]",
                    )
                ],
            )
        ],
        # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    def compare_scenario(self,request,*_args,**_kwargs):
        logger.bind(method_name="compare_scenario", app_name="Scenario Scenario")
        saved_ids = ast.literal_eval(request.query_params.get('saved_ids',[]))
        
        response = services.compare_scenario(
            unit_of_work.PricingOverallSummeryScenarioUnitOfWork(transaction),
            saved_ids
        )
        serializer = serializers.ComparePricingOverallSummeryScenarioSerializer(
            response, many=True)     
        return JsonResponse(serializer.data)

    @extend_schema(
        summary="Download optimizer  output",
        description="Download optimizer  output",
        # request=ser.ScenarioPlannerRequestAndResponseSerializer,
        responses="Pricing Common output excel."       
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    
    def download_input(self,request,*_args,**_kwargs):
        logger.bind(method_name="download", app_name="Optimizer Scenario")
        non_committed_id = request.data.get('non_committed_id')
        scenario_type = request.data.get('scenario_type')
        ppgs_data = request.data.get('product_groups',None)
        uow = set_uow.SetPricingIncreaseScenarioUnitOfWork
        if scenario_type=='optimizer':
            uow=set_uow.SetPricingIncreaseOptimizerScenarioUnitOfWork
        if non_committed_id:
            ppg_data = services.get_ppg_data(
                unit_of_work.ChangedPricingScenarioUnitOfWork(transaction),
                non_committed_id)
            ppgs_data = [item['level_param'] for item in ppg_data]
        data = set_services.get_pricing_scenario_data(
            uow(transaction),
            ppgs_data,
            request._user
        )

        filename = 'set_price_input.xlsx'
        response = HttpResponse(
            excel.excel_download_input_pricing(data.values(),scenario_type),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename=%s' % filename
        return response

    
    
    @extend_schema(
        summary="Download optimizer  output",
        description="Download optimizer  output",
        # request=ser.ScenarioPlannerRequestAndResponseSerializer,
        responses="Pricing Common output excel."       
        )
    
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response

    def download_scenario_planner_input(self, request, *_args, **_kwargs):
        scenario_planner_payload = request.data.get('scenario_planner_payload', [])
        customers = [item.get('customer') for item in scenario_planner_payload]
        level = request.data.get('level',None)
        non_committed_id = request.data.get('non_committed_id')
        scenario_type = request.data.get('scenario_type')
        logger.bind(method_name="get_pricing_scenario_data", app_name="Pricing Common")
        uow = unit_of_work.PricingCommonScenarioUnitOfWork
        if level=='customer':
            uow = unit_of_work.PricingScenarioCustomerLevelUnitOfWork 
            serializer = serializers.PricingScenarioCustomerLevelSerializer
        filename = 'scenario_planner_input.xlsx'
        if non_committed_id:
            ppg_data = services.get_customer_data(
                unit_of_work.ChangedPricingScenarioUnitOfWork(transaction),
                non_committed_id)
            customers = [item['customer_level_param'] for item in ppg_data]
        data = services.get_download_scenario_planner(
            uow(transaction),
            scenario_planner_payload,
            customers,
            level       
        )

        response = HttpResponse(
            excel.excel_download_input_scenario_planner(data.values(),scenario_type),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename=%s' % filename
        return response

    @extend_schema(
    summary="Download optimizer  output",
    description="Download optimizer  output",
    # request=ser.ScenarioPlannerRequestAndResponseSerializer,
    responses="Pricing Common output excel."       
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    
    def download_planner_output(self,request,*_args,**_kwargs):
        logger.bind(method_name="download", app_name="Optimizer Scenario")
        filename = 'plannner_output.xlsx'
        non_committed_id = request.data.get('data').get('non_committed_id')
        data = services.get_download_data(
                unit_of_work.BaseAndSimulatedPricingScenarioUnitOfWork(transaction),
                non_committed_id)
        response = HttpResponse(
            excel.excel_download_output_planner(data.values()),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename=%s' % filename
        return response


    @extend_schema(
        summary="Download optimizer  output",
        description="Download optimizer  output",
        # request=ser.ScenarioPlannerRequestAndResponseSerializer,
        responses="Pricing Common output excel."       
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    
    def download(self,request,*_args,**_kwargs):
        logger.bind(method_name="download", app_name="Optimizer Scenario")
        filename = 'promo_optimizer.xlsx'
        response = HttpResponse(
            excel.download_excel_promo(request.data['data']),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename=%s' % filename
        return response


class PricingPublishedSavedScenarioModelView(viewsets.ModelViewSet):
    queryset = db_model.PricingPublishedSavedScenario.objects.filter().order_by('-id')
    serializer_class = serializers.PricingPublishedSavedScenarioSerializer
    pagination_class = StandardPaginator
    filter_backends = [DjangoFilterBackend, SearchFilter, ]
    search_fields = ['name', 'scenario_type','status_type','module_type']
    filterset_fields = ['name', 'scenario_type','status_type','module_type']
    page_size_query_param = 'page_size'
    page_query_param = 'page'
    lookup_field = 'id'
    lookup_url_kwarg = 'saved_id'

    parser_classes = (FileUploadParser,MultiPartParser)

    def upload_input(self, request, *args, **kwargs):
        file_serializer = ser.UploadedFileSerializer(data=request.data)
        scenario_type = self.kwargs.get('slug')
        if file_serializer.is_valid():
            uploaded_file_path = file_serializer.validated_data['file']
            workbook = load_workbook(uploaded_file_path, data_only=True)
            # df3 = pd.read_excel('set_price_input.xlsx', header=2)
            worksheet = workbook.active
            num_records = worksheet.max_row - 2 
            current_header = None
            combined_headers = []

            for cell in worksheet[1]:
                if cell.value:
                    current_header = cell.value
                    combined_headers.append(current_header)
                else:
                    combined_headers.append(f"{current_header} {worksheet.cell(row=2, column=cell.column).value}")

            data_range = worksheet[f'A3:R{num_records + 2}']
            data = []
            for row in data_range:
                row_data = {combined_headers[i]: cell.value for i, cell in enumerate(row) if i < len(combined_headers)}
                data.append(row_data)
            df = pd.DataFrame(data, columns=combined_headers)
            if scenario_type == 'simulator':
                columns_to_include = ['OGSM Type', 'PPG','Non Promo Price[zł] % Change','Non Promo Price[zł] New','List Price[zł] % Change','List Price[zł] New','Promo Volume (%)','Promo Volume (%) New','Non Promo Volume (%)','Non Promo Volume (%) New','Type of Price Increase', 'COGS/t Change %']
            else:
                columns_to_include = ['OGSM Type', 'PPG','Non Promo Price[zł] % Change','Non Promo Price[zł] Max','List Price[zł] % Change','List Price[zł] Max','COGS/t Change %']      
            filtered_df = df[columns_to_include]  # Make sure this line is indented properly
            json_df = filtered_df.to_json(orient='records')
            json_data = json.loads(json_df)
            return Response(json_data, status=201)


class BaseAndSimulatedPricingScenarioView(viewsets.GenericViewSet):
    serializer_class = serializers.BaseAndSimulatedPricingScenarioSerializer
    filter_backends = [search.CustomSearchFilter]
    search_fields  = ['name','status_type','scenario_type','promo_type']
    pagination_class = PageNumberPagination
    page_size_query_param = 'page_size'
    page_query_param = 'page'
    lookup_field = 'id'
    lookup_url_kwarg = 'saved_id'

    def get_queryset(self):
        return services.get_scenario(
                        unit_of_work.BaseAndSimulatedPricingScenarioUnitOfWork(transaction),
                        user=self.request._user
                    ).order_by('-id')
    
    @extend_schema(
        summary="Load Saved scenario Based on saved id",
        description="API end point that serves the Loading Saved scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
  
    def get_list(self,request):
        response = services.get_list(
            unit_of_work.BaseAndSimulatedPricingScenarioUnitOfWork(transaction),
            request.parser_context['kwargs'].get('saved_id',None)
        )

        serializer = serializers.BaseAndSimulatedPricingScenarioSerializer(
            response, many=True)     
        return serializer.data
    
    @extend_schema(
        summary="Load Saved scenario Based on saved id",
        description="API end point that serves the Loading Saved scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
  
    def load_scenario(self,request):
        response = services.load_scenario(
            unit_of_work.BaseAndSimulatedPricingScenarioUnitOfWork(transaction),
            request.parser_context['kwargs'].get('saved_id',None)
        )
        serializer = serializers.BaseAndSimulatedPricingScenarioSerializer(
            response, many=True)     
        return serializer.data
    
    @extend_schema(
        summary="Load Saved scenario Based on saved id",
        description="API end point that serves the Loading Saved scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
  
    def get_summary(self,request):
        response = services.get_summary(
            unit_of_work.BaseAndSimulatedPricingScenarioUnitOfWork(transaction),
            request.parser_context['kwargs'].get('saved_id',None),
            request.query_params.get('level_param'),
            request.query_params.get('level')

        )
        return response


    @extend_schema(
        summary="Load Saved scenario Based on saved id",
        description="API end point that serves the Loading Saved scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
  
    def get_overall_summary(self,request):
        response = services.get_overall_summary(
            unit_of_work.BaseAndSimulatedPricingScenarioUnitOfWork(transaction),
            request.parser_context['kwargs'].get('saved_id',None),
            request.data
        )
        return response


    @extend_schema(
        summary="Load Saved scenario Based on saved id",
        description="API end point that serves the Loading Saved scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
  
    def get_deep_drive_summary(self,request):
        response = services.get_deep_drive_summary(
            unit_of_work.BaseAndSimulatedPricingScenarioUnitOfWork(transaction),
            request.parser_context['kwargs'].get('saved_id',None),
            request.data.get('deep_drive_payload')
        )

        return response



    @extend_schema(
        summary="Load Saved scenario Based on saved id",
        description="API end point that serves the Loading Saved scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
  
    def get_deep_drive_customer_level(self,request):
        response = services.get_deep_drive_customer_level(
            unit_of_work.BaseAndSimulatedPricingScenarioUnitOfWork(transaction),
            request.parser_context['kwargs'].get('saved_id',None),
            request.data.get('deep_drive_payload')
        )
        return response
    
    
    @extend_schema(
        summary="Load Saved scenario Based on saved id",
        description="API end point that serves the Loading Saved scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
  
    def get_deep_drive_top_10(self,request):
        response = services.get_top_10(
            unit_of_work.BaseAndSimulatedPricingScenarioUnitOfWork(transaction),
            request.parser_context['kwargs'].get('saved_id',None),
            request.data.get('deep_drive_payload')
        )
        return response
    
    @extend_schema(
        summary="Load Saved scenario Based on saved id",
        description="API end point that serves the Loading Saved scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
  
    def get_deep_drive(self,request):
        response = services.get_deep_drive(
            unit_of_work.BaseAndSimulatedPricingScenarioUnitOfWork(transaction),
            request.parser_context['kwargs'].get('saved_id',None)
        )
        serializer = serializers.BaseAndSimulatedPricingScenarioSerializer(
            response, many=True)     
        return serializer.data
    

    @extend_schema(
        summary="Load Saved scenario Based on saved id",
        description="API end point that serves the Loading Saved scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
  
    def get_deep_drive(self,request):
        response = services.get_deep_drive(
            unit_of_work.BaseAndSimulatedPricingScenarioUnitOfWork(transaction),
            request.parser_context['kwargs'].get('saved_id',None)
        )
        serializer = serializers.BaseAndSimulatedPricingScenarioSerializer(
            response, many=True)     
        return serializer.data
    
    
    @extend_schema(
        summary="Comapare scenario Based on saved id",
        description="API end point that serves the Comapare scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_ids",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id should be type string.",
                        value="[1,2]",
                    )
                ],
            )
        ],
        # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    def compare_scenario(self,request,*_args,**_kwargs):
        logger.bind(method_name="compare_scenario", app_name="Scenario Scenario")
        saved_ids = ast.literal_eval(request.query_params.get('saved_ids',[]))
        response_data = []
        for saved_id in saved_ids:
            response = services.get_compare_scenario_kpis(
                unit_of_work.BaseAndSimulatedPricingScenarioUnitOfWork(transaction),
                [saved_id]
            )
            # breakpoint()
            name = services.get_name_of_id(
                unit_of_work.PricingPublishedSavedScenarioUnitOfWork(transaction),
                saved_id
            )
            response_data.append({'scenario_id': saved_id, **response, **name})
        return response_data
                    


    