""" Test Common Repository"""
import pytest
from .. import repository

@pytest.mark.django_db
def test_meta_repo(_mmf):
    """test_meta_repo

    Args:
        _mmf (_type_): meta factory
    """
    _mr = repository.MetaRepository()
    assert _mr.get(_id=0) is None
    assert _mr.get(_id=1) is not None
    assert _mmf.id == 1
    
@pytest.mark.django_db
def test_coeff_repo(_cf):
    """test_coeff_repo

    Args:
        _cf (_type_): coeff factory
    """
    _cr = repository.CoeffRepository()
    assert _cr.get(_id=0) is None
    assert _cr.get(_id=1) is not None
    assert _cf.id == 1
    assert _cf.model_meta_id == 1

@pytest.mark.django_db
def test_coeff_map_repo(_cmf):
    """test_coeff_map_repo

    Args:
        _cmf (_type_): coeff map factory
    """
    _cmr = repository.CoeffMapRepository()
    assert _cmr.get(_id=0) is None
    assert _cmr.get(_id=1) is not None
    assert _cmf.id == 1
    assert _cmf.model_meta_id == 1

@pytest.mark.django_db
def test_model_data_repo(_mdf):
    """test_model_data_repo

    Args:
        _mdf (_type_): model data facory
    """
    _mdr = repository.ModelDataRepository()
    assert _mdr.get(_id=0) is None
    assert _mdr.get(_id=1) is not None
    assert _mdf.id == 1
    assert _mdf.model_meta_id == 1

@pytest.mark.django_db
def test_model_roi_repo(_rdf):
    """test_model_roi_repo

    Args:
        _rdf (_type_): roi facory
    """
    _mrr = repository.ModelROIRepository()
    assert _mrr.get(_id=0) is None
    assert _mrr.get(_id=1) is not None
    assert _rdf.id == 1
    assert _rdf.model_meta_id == 1

@pytest.mark.django_db
def test_model_rpm_repo(_rpm):
    """test_model_roi_repo

    Args:
        _rdf (_type_): roi facory
    """
    _rpmr = repository.RetailerPPGMappingRepository()
    assert _rpmr.get(_id=0) is None
    assert _rpmr.get(_id=1) is not None
    assert _rpm.id == 1

@pytest.mark.django_db
def test_model_tactic_repo(_mtf):
    """test_model_roi_repo

    Args:
        _mtf (_type_): roi facory
    """
    _tr = repository.TacticRepository()
    assert _tr.get(_id=0) is None
    assert _tr.get(_id=1) is not None
    assert _mtf.id == 1
    assert _mtf.retailer_ppg_map_id == 1

@pytest.mark.django_db
def test_model_item_repo(_imf):
    """test_model_roi_repo

    Args:
        _imf (_type_): roi facory
    """
    _imr = repository.ItemMapRepository()
    assert _imr.get(_id=0) is None
    assert _imr.get(_id=1) is not None
    assert _imf.id == 1
