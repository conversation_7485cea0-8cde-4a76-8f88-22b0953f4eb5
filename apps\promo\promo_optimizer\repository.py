""" Optimizer Repository"""
from rest_framework.serializers import ValidationError
from django.db.models import Q
from core.generics.respository import ORMModelRepository #pylint: disable=E0401
from apps.common import models as db_model  #pylint: disable=E0401

class OptimizerPromotionRepository(ORMModelRepository):
    """ Optimizer Promotion Repository"""
    def __init__(self):
        super().__init__(db_model.ScenarioPromotionSave)

    def get(self, _id):
        """ Filter data based on id"""
        return self._model.filter(id=_id).first()

    def filter_by_id(self, _id):
        """ Filter data based on scenario id"""
        return self._model.filter(id=_id)
    
    def filter_by_multiple_id(self, _ids):
        """ Filter data based on list of scenario ids"""
        return self._model.filter(id__in=_ids)

    def update(self, _id, entity):
        """ Update data based on id"""
        if self._model.filter(id=_id, is_delete=False).first():
            self._model.filter(id=_id).update(**entity)
        else:
            raise ValidationError("cannot update inactive records")

    def delete(self, _id):
        """ Delete data based on id"""
        if self._model.filter(id=_id, is_delete=False).first():
            self._model.filter(id=_id).update(is_delete=True)
            raise ValidationError("cannot delete inactive records")
    
    def get_by_scenario_type(self,scenario_type,user):
        return self._model.select_related('saved_scenario')\
            .filter(Q(saved_scenario__created_by__user_id=user['user_id'])|
                    Q(saved_scenario__shared_user__to__icontains=user['email'])\
                    ,saved_scenario__scenario_type=scenario_type\
                    ,is_delete=False\
                    )
    
    def get_my_scenario_by_scenario_type(self,scenario_type,user):
        return self._model.select_related('saved_scenario')\
            .filter(saved_scenario__created_by__user_id=user['user_id']\
                    ,saved_scenario__scenario_type=scenario_type\
                    ,is_delete=False\
                    )
    
    def get_shared_scenario_by_scenario_type(self,scenario_type,user):
        return self._model.select_related('saved_scenario')\
            .filter(saved_scenario__shared_user__to__icontains=user['email']\
                    ,saved_scenario__scenario_type=scenario_type\
                    ,is_delete=False\
                    )

class OptimizerSavedScenarioRepository(ORMModelRepository):
    def __init__(self):
        super().__init__(db_model.SavedScenario)

    def filter_by_scenario_name(self, scenario_name):
        """ Filter data based on scenario name"""
        return self._model.filter(name=scenario_name,is_delete=False)
    
    def filter_by_scenario_name_and_scenario_type(self, scenario_name,scenario_type):
        """ Filter data based on scenario name"""
       
        return self._model.filter(name=scenario_name,scenario_type=scenario_type,is_delete=False)
    

    def get_by_scenario_type(self,scenario_type,user):
        return self._model.prefetch_related('saved_promotion_save')\
            .filter(Q(created_by__user_id=user['user_id'])| Q(shared_user__to__icontains=user['email'])\
                    ,scenario_type=scenario_type\
                    ,is_delete=False)
    
    def get_my_scenario_by_scenario_type(self,scenario_type,user):
        return self._model.prefetch_related('saved_promotion_save')\
        .filter(created_by__user_id=user['user_id']\
                ,scenario_type=scenario_type\
                ,is_delete=False\
                )

    def get_shared_scenario_by_scenario_type(self,scenario_type,user):
        return self._model.prefetch_related('saved_promotion_save')\
            .filter(shared_user__to__icontains=user['email']\
                    ,scenario_type=scenario_type\
                    ,is_delete=False\
                    )

class PromotionLevelsRepository(ORMModelRepository):
    def __init__(self):
        super().__init__(db_model.PromotionLevels)

    def filter_by_region(self,region):
        """ Filter data based on scenario name"""
  
        return self._model.filter(is_delete=False,region=region)
    
    def filter_by_user(self, user_id,region):
        """ Filter data based on scenario name"""
        return self._model.filter(promotion_level_user__user_id=user_id,region=region,is_delete=False)
    