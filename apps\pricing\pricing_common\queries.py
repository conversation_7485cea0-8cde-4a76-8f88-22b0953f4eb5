""" SQL QUERY"""
from django.conf import settings
from apps.pricing.pricing_common.constants import BASE_SIM_CONFIG
from config.db_handler import db_table_format_in_sql_query_str
from utils import convert_list_to_tuple_str, convert_to_str

def get_all_pricing_scenario(columns,model_name1,lvl,lvl_params):
    """ Query for getting data."""
    query_string = f"""
                        SELECT {columns} FROM {db_table_format_in_sql_query_str(model_name1,schema=settings.PRICING_DATABASE_SCHEMA)} a 
                        WHERE
                        a.{lvl} in ({','.join(["%s"] * len(lvl_params))})
                    """
    return query_string

def generate_retailer_ppg_elasticity_info_query(list_of_ppgs):
    """ Query for getting data."""
    query_string = f"""
                        select customer,product_group,non_promo_price_elasticity,promo_price_elasticity from 
                        {db_table_format_in_sql_query_str('pricing_meta_model',schema=settings.PRICING_DATABASE_SCHEMA)} pmm join 
                        {db_table_format_in_sql_query_str('pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA)} ps on
                          ps.pricing_meta_model_id=pmm.id where product_group in ('{"','".join(list_of_ppgs)}')
                        
                    """
    return query_string

def get_pricing_scenario_by_id(model_name1,scenario_id):
    """ Query for getting data."""
    query_string = f"""
                        SELECT * FROM {db_table_format_in_sql_query_str(model_name1,schema=settings.PRICING_DATABASE_SCHEMA)} a 
                        WHERE
                        a.pricing_saved_scenario_id = {scenario_id}
                      
                    """
    return query_string

def get_pricing_scenario_by_mode(model_name1,scenario_id):
    """ Query for getting data."""
    query_string = f"""
                        SELECT * FROM {db_table_format_in_sql_query_str(model_name1,schema=settings.PRICING_DATABASE_SCHEMA)} a 
                        WHERE
                        a.pricing_saved_scenario_id = {scenario_id}
                        or
                        a.status_flag = 'not modified'
                      
                    """
    return query_string


def get_distinct_pricing_scenario(columns,model_name):
    """ Query for getting data."""
    query_string = f"""
                        SELECT DISTINCT {columns} FROM {db_table_format_in_sql_query_str(model_name)} a
                        WHERE 
                        a.is_delete = 0
                    """
    return query_string

def search_pricing_query():
    """ Search Optimizer Scenario"""
    query_string = f"""
    SELECT * FROM {db_table_format_in_sql_query_str('pricing_saved_scenario')} s
    WHERE 
    s.name LIKE  %s
    AND
    s.scenario_type = 'simulator'
    AND 
    s.status_type = %s
    AND
    s.module_type = %s
    AND
    s.is_delete = 0
    ORDER BY
    s.id DESC
    """
    
    return query_string

def search_pricing_global_scenario_query():
    """ Search Global Scenario Planner"""
    query_string = f"""
    SELECT * FROM {db_table_format_in_sql_query_str('pricing_saved_scenario')} s
    WHERE 
    s.name LIKE  %s
    AND
    s.scenario_type = %s
    AND
    s.is_delete = 0
    ORDER BY
    s.id DESC
    """
    return query_string

def update_published_flag(saved_id,module_type,scenario_type=None):
    query_params = {
        "db_table": db_table_format_in_sql_query_str('pricing_saved_scenario', schema=settings.PRICING_DATABASE_SCHEMA),
        "saved_scenario_id": saved_id
    }
    sql_query = f"UPDATE {query_params['db_table']} SET is_published = %s WHERE id <> %s and module_type=%s"
    params = (False, query_params['saved_scenario_id'],module_type)
    return sql_query, params

def update_status_type(saved_id):
    query_params = {
        "db_table": db_table_format_in_sql_query_str('pricing_saved_scenario', schema=settings.PRICING_DATABASE_SCHEMA),
        "saved_scenario_id": saved_id
    }
    sql_query = f"UPDATE {query_params['db_table']} SET status_type = %s WHERE id = %s;"
    params = ('completed', query_params['saved_scenario_id'])
    return sql_query, params

def changed_scenario_batch_insert_query(columns,n_records=1):

    sql_query = f""" INSERT INTO {db_table_format_in_sql_query_str('changed_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA)} 
                ({columns}) VALUES {', '.join(['(%s,%s,%s,%s,%s,%s, %s, %s,%s, %s, %s,%s, %s, %s,%s,%s,%s,%s,%s,%s)'])} """
    return sql_query

def uploaded_scenario_batch_insert_query(columns,n_records=1):

    sql_query = f""" INSERT INTO {db_table_format_in_sql_query_str('changed_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA)} 
                ({columns}) VALUES {', '.join(['(%s,%s,%s,%s,%s, %s, %s,%s, %s, %s,%s, %s, %s)'])} """
    return sql_query

def base_and_simulated_scenario_query(columns,n_records=1):
    
    sql_query = f""" INSERT INTO {db_table_format_in_sql_query_str('base_and_simulated_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA)} 
                ({columns}) VALUES {', '.join(['(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s, %s, %s,%s, %s, %s,%s, %s, %s,%s,%s,%s, %s, %s,%s, %s, %s,%s, %s, %s,%s, %s, %s,%s,%s,%s, %s, %s,%s, %s, %s,%s, %s, %s,%s,%s,%s,%s,%s,%s)'])} """
    return sql_query

def summary_scenario_batch_insert_query(columns,model_name,n_records=1):

    sql_query = f""" INSERT INTO {db_table_format_in_sql_query_str({model_name},schema=settings.PRICING_DATABASE_SCHEMA)} 
                ({columns}) VALUES {', '.join(['(%s, %s,%s)'] * n_records)} """
    return sql_query

def delete_changed_scenario_query(pricing_saved_scenario_id:int=None):
    query = f""" DELETE FROM {db_table_format_in_sql_query_str('changed_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA)} 
                WHERE pricing_saved_scenario_id={pricing_saved_scenario_id}
            """
    return query

def delete_base_and_simulated_scenario_query(pricing_saved_scenario_id:int=None):
    query = f""" DELETE FROM {db_table_format_in_sql_query_str('base_and_simulated_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA)} 
                WHERE pricing_saved_scenario_id={pricing_saved_scenario_id}
            """
    return query

def delete_summary_scenario_query(pricing_saved_scenario_id:int=None):
    query = f""" DELETE FROM {db_table_format_in_sql_query_str('pricing_scenario_summery',schema=settings.PRICING_DATABASE_SCHEMA)} 
                WHERE pricing_saved_scenario_id={pricing_saved_scenario_id}
            """
    return query

def delete_overall_summary_scenario_query(pricing_saved_scenario_id:int=None):
    query = f""" DELETE FROM {db_table_format_in_sql_query_str('pricing_scenario_overall_summery',schema=settings.PRICING_DATABASE_SCHEMA)} 
                WHERE pricing_saved_scenario_id={pricing_saved_scenario_id}
            """
    return query

def delete_saved_scenario_query(pricing_saved_scenario_id:int=None):
    query = f""" DELETE FROM {db_table_format_in_sql_query_str('pricing_saved_scenario',schema=settings.PRICING_DATABASE_SCHEMA)} 
                WHERE id={pricing_saved_scenario_id}
            """
    return query

def get_top_10(scenario_id,param,level='product_group',special_case=False,is_base=True,agg_func='SUM',lvl_data=None):
    query_params  = {
            "db_table":db_table_format_in_sql_query_str('base_and_simulated_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "saved_scenario_id":scenario_id,
            "level":level,
            "param":param,
            'is_base':is_base,
            'agg_func':agg_func,
            'lvl_data':lvl_data
        }
    
    query_params['group_param'] = BASE_SIM_CONFIG.get(query_params.get('param'),query_params.get('param'))
    top_10_statement = ''
    if query_params.get('level')!='customer':
        top_10_statement = 'TOP 10 '
    query_params['top_10_statement'] = top_10_statement
    if special_case:
        query = "select {data[top_10_statement]} ROW_NUMBER() OVER( ORDER BY {data[level]} ) AS  id,SUM(CAST(nsv_new as float)) nsv_sum, ISNULL((MAX(cast(NULLIF(cast({data[param]}_new as float), 0) AS float))-MIN(cast(NULLIF(cast({data[param]}_new as float), 0) AS float))),0) {data[param]}_spread,{data[level]},is_base  from {data[db_table]} where pricing_saved_scenario_id = {data[saved_scenario_id]} and is_base={data[is_base]} group by {data[level]},is_base order by nsv_sum desc".format(data=query_params)
    elif lvl_data:
        query = "select {data[top_10_statement]} ROW_NUMBER() OVER( ORDER BY {data[level]} ) AS  id,{data[agg_func]}(CAST({data[param]}_percent_change as float)) {data[param]}_percent_change ,{data[agg_func]}(CAST({data[group_param]} as float)) {data[param]}_sum,{data[level]},is_base  from {data[db_table]} where pricing_saved_scenario_id = {data[saved_scenario_id]} and is_base={data[is_base]} and {data[level]} in {data[lvl_data]} group by {data[level]},is_base  order by {data[param]}_sum desc".format(data=query_params)
    else:
        query = "select {data[top_10_statement]} ROW_NUMBER() OVER( ORDER BY {data[level]} ) AS  id,{data[agg_func]}(CAST({data[param]}_percent_change as float)) {data[param]}_percent_change ,{data[agg_func]}(CAST({data[group_param]} as float)) {data[param]}_sum,{data[level]},is_base  from {data[db_table]} where pricing_saved_scenario_id = {data[saved_scenario_id]} and is_base={data[is_base]} group by {data[level]},is_base  order by {data[param]}_sum desc".format(data=query_params)
    return query

def all_agg_changed_inputs_query(scenario_id,common_table):
    query_params  = {
            "db_table":db_table_format_in_sql_query_str(common_table,schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table2":db_table_format_in_sql_query_str('changed_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table3":db_table_format_in_sql_query_str('pricing_saved_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "saved_scenario_id":scenario_id
        }

    query = "select ROW_NUMBER() OVER( ORDER BY spplv.product_group ) AS  id,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.net_net_multiplication_factor,1) else spplv.net_net_multiplication_factor end as net_net_multiplication_factor,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.lp_multiplication_factor,1.2) else spplv.lp_multiplication_factor end as lp_multiplication_factor,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_cogs_t_percent,0) ELSE spplv.changed_cogs_t_percent END AS changed_cogs_t_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_net_net_change_percent,0) ELSE spplv.changed_net_net_change_percent END AS changed_net_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_dead_net_change_percent,0) ELSE spplv.changed_dead_net_change_percent END AS changed_dead_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_nn_change_percent,0) ELSE spplv.changed_nn_change_percent END AS changed_nn_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_lp_percent,0) ELSE spplv.changed_lp_percent END AS changed_lp_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_pack_weight_kg_percent,0) ELSE spplv.changed_pack_weight_kg_percent END AS changed_pack_weight_kg_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_shelf_price_percent,0) ELSE spplv.changed_shelf_price_percent END AS changed_shelf_price_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_non_promo_price,0) ELSE spplv.changed_non_promo_price END AS changed_non_promo_price,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_promo_vol_change_percent,0) ELSE spplv.changed_promo_vol_change_percent END AS changed_promo_vol_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_base_growth_th_invest_percent,0) ELSE spplv.changed_base_growth_th_invest_percent END AS changed_base_growth_th_invest_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.status_flag else spplv.status_flag end as  status_flag,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.shelf_price_new,0) else spplv.shelf_price end as  shelf_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.list_price_new,0) else spplv.list_price_new end as  list_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.pack_weight_kg_new,0) else spplv.pack_weight_kg_new end as  pack_weight_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.dead_net_new,0) else spplv.dead_net_new end as  dead_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.net_net_new,0) else spplv.net_net_new end as  net_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_vol_change_new else spplv.promo_vol_change end as  promo_vol_change_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.base_growth_th_invest_new else spplv.base_growth_th_invest end as  base_growth_th_invest_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_price_per_kg_new else spplv.promo_price_per_kg_new end as  promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.non_promo_price_per_kg_new else spplv.non_promo_price_per_kg end as  non_promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.non_promo_price_new,0) else spplv.non_promo_price_new end as  non_promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.promo_price_new,0) else spplv.promo_price_new end as  promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.competitor_follows else spplv.competitor_follows end as  competitor_follows,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.type_of_price_inc else spplv.type_of_price_inc end as  type_of_price_inc,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_sold_volume_new else 0 end as  promo_sold_volume_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.non_promo_sold_volume_new else 0 end as  non_promo_sold_volume_new,"\
            "spplv.nn_change_percent,cps.promo_sold_volume,cps.non_promo_sold_volume,cps.promo_sold_volume_percent,cps.promo_sold_volume_percent_new,"\
            "spplv.floor_price,spplv.min_shelf_price,"\
            "CASE WHEN cps.competitor_follows = 'Yes' THEN non_promo_price_elasticity + competitor_coefficient else spplv.net_non_promo_price_elasticity end as  net_non_promo_price_elasticity,"\
            "sell_in_volume_t,bww_gsv,nsv,nsv_t,tt,cogs_t,gmac_abs,gmac,list_price,pack_weight_kg,"\
            "tax,net_net,dead_net,shelf_price,promo_price_per_unit,"\
            "promo_price_elasticity,promo_share,tpr,sell_out_volume_sales_t,unit_sales_000,sell_in_unit,value_sales_rsv,"\
            "non_promo_price_elasticity,promo_vol_change,base_growth_th_invest,ogsm_type,spplv.customer,"\
            "cps.investment,non_promo_price_per_kg,non_promo_price_per_unit,competitor_coefficient,"\
            "cps.list_price_change_percent,cps.list_price_change_percent_kg,cps.nsv_price_impact_direct,cps.nsv_price_impact_indirect,"\
            "cps.nsv_price_impact_base,cps.new_lp_after_base_price,cps.type_of_base_inc_after_change,non_promo_price_per_unit_ppg,promo_price_per_unit_ppg,is_optimizer_ppg,exclude_retailer,net_net_nsv_product,lp_nsv_product,"\
            "product_group,ogsm_param,technology,brand from  {data[db_table]} as spplv left join (select pss.net_net_multiplication_factor,pss.lp_multiplication_factor,cps.* from {data[db_table2]} as cps inner join {data[db_table3]} as pss on cps.pricing_saved_scenario_id=pss.id "\
            "where pricing_saved_scenario_id = {data[saved_scenario_id]}) cps on cps.level_param = spplv.product_group".format(data=query_params)
    return query


def all_agg_changed_inputs_query_with_filter(scenario_id,filtered_data,common_table):
    query_params  = {
            "db_table":db_table_format_in_sql_query_str(common_table,schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table2":db_table_format_in_sql_query_str('changed_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table3":db_table_format_in_sql_query_str('pricing_saved_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "saved_scenario_id":scenario_id,
            'customer':filtered_data.get('customer')
        }
    
    
    query = "select ROW_NUMBER() OVER( ORDER BY spplv.product_group ) AS  id,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.net_net_multiplication_factor,1) else spplv.net_net_multiplication_factor end as net_net_multiplication_factor,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.lp_multiplication_factor,1.2) else spplv.lp_multiplication_factor end as lp_multiplication_factor,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_cogs_t_percent,0) ELSE spplv.changed_cogs_t_percent END AS changed_cogs_t_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_net_net_change_percent,0) ELSE spplv.changed_net_net_change_percent END AS changed_net_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_dead_net_change_percent,0) ELSE spplv.changed_dead_net_change_percent END AS changed_dead_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_nn_change_percent,0) ELSE spplv.changed_nn_change_percent END AS changed_nn_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_lp_percent,0) ELSE spplv.changed_lp_percent END AS changed_lp_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_pack_weight_kg_percent,0) ELSE spplv.changed_pack_weight_kg_percent END AS changed_pack_weight_kg_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_shelf_price_percent,0) ELSE spplv.changed_shelf_price_percent END AS changed_shelf_price_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_non_promo_price,0) ELSE spplv.changed_non_promo_price END AS changed_non_promo_price,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_promo_vol_change_percent,0) ELSE spplv.changed_promo_vol_change_percent END AS changed_promo_vol_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_base_growth_th_invest_percent,0) ELSE spplv.changed_base_growth_th_invest_percent END AS changed_base_growth_th_invest_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.status_flag else spplv.status_flag end as  status_flag,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.shelf_price_new,0) else spplv.shelf_price end as  shelf_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.list_price_new,0) else spplv.list_price_new end as  list_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.pack_weight_kg_new,0) else spplv.pack_weight_kg_new end as  pack_weight_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.dead_net_new,0) else spplv.dead_net_new end as  dead_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.net_net_new,0) else spplv.net_net_new end as  net_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_vol_change_new else spplv.promo_vol_change end as  promo_vol_change_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.base_growth_th_invest_new else spplv.base_growth_th_invest end as  base_growth_th_invest_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_price_per_kg_new else spplv.promo_price_per_kg_new end as  promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.non_promo_price_per_kg_new else spplv.non_promo_price_per_kg end as  non_promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.non_promo_price_new,0) else spplv.non_promo_price_new end as  non_promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.promo_price_new,0) else spplv.promo_price_new end as  promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.competitor_follows else spplv.competitor_follows end as  competitor_follows,"\
            "spplv.floor_price,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.type_of_price_inc else spplv.type_of_price_inc end as  type_of_price_inc,"\
            "CASE WHEN cps.competitor_follows = 'Yes' THEN non_promo_price_elasticity + competitor_coefficient else spplv.net_non_promo_price_elasticity end as  net_non_promo_price_elasticity,"\
            "sell_in_volume_t,bww_gsv,nsv,nsv_t,tt,cogs_t,gmac_abs,gmac,list_price,pack_weight_kg,"\
            "tax,net_net,dead_net,shelf_price,promo_price_per_unit,"\
            "promo_price_elasticity,promo_share,tpr,sell_out_volume_sales_t,unit_sales_000,sell_in_unit,value_sales_rsv,"\
            "non_promo_price_elasticity,promo_vol_change,base_growth_th_invest,ogsm_type,spplv.customer,"\
            "cps.investment,non_promo_price_per_kg,non_promo_price_per_unit,competitor_coefficient,"\
            "cps.list_price_change_percent,cps.list_price_change_percent_kg,cps.nsv_price_impact_direct,cps.nsv_price_impact_indirect,"\
            "cps.nsv_price_impact_base,cps.new_lp_after_base_price,cps.type_of_base_inc_after_change,non_promo_price_per_unit_ppg,promo_price_per_unit_ppg,is_optimizer_ppg,"\
            "product_group,ogsm_param,technology,brand from  {data[db_table]} as spplv left join (select pss.net_net_multiplication_factor,pss.lp_multiplication_factor,cps.* from {data[db_table2]} as cps inner join {data[db_table3]} as pss on cps.pricing_saved_scenario_id=pss.id "\
            "where pricing_saved_scenario_id = {data[saved_scenario_id]}) cps on cps.level_param = spplv.product_group or cps.customer_level_param = spplv.customer where spplv.customer ='{data[customer]}'".format(data=query_params)
    return query


def agg_changed_inputs_query_with_ppg_filter(scenario_id,filtered_data,common_table):
    query_params  = {
            "db_table":db_table_format_in_sql_query_str(common_table,schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table2":db_table_format_in_sql_query_str('changed_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table3":db_table_format_in_sql_query_str('pricing_saved_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "saved_scenario_id":scenario_id,
            'customer':filtered_data.get('customer'),
            'product_group':f'''({','.join([f"'{x}'" for x in filtered_data.get('product_group')])})'''
        }
    query = "select ROW_NUMBER() OVER( ORDER BY spplv.product_group ) AS  id,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.net_net_multiplication_factor,1) else spplv.net_net_multiplication_factor end as net_net_multiplication_factor,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.lp_multiplication_factor,1.2) else spplv.lp_multiplication_factor end as lp_multiplication_factor,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_cogs_t_percent,0) ELSE spplv.changed_cogs_t_percent END AS changed_cogs_t_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_net_net_change_percent,0) ELSE spplv.changed_net_net_change_percent END AS changed_net_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_dead_net_change_percent,0) ELSE spplv.changed_dead_net_change_percent END AS changed_dead_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_nn_change_percent,0) ELSE spplv.changed_nn_change_percent END AS changed_nn_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_lp_percent,0) ELSE spplv.changed_lp_percent END AS changed_lp_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_pack_weight_kg_percent,0) ELSE spplv.changed_pack_weight_kg_percent END AS changed_pack_weight_kg_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_shelf_price_percent,0) ELSE spplv.changed_shelf_price_percent END AS changed_shelf_price_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_non_promo_price,0) ELSE spplv.changed_non_promo_price END AS changed_non_promo_price,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_promo_vol_change_percent,0) ELSE spplv.changed_promo_vol_change_percent END AS changed_promo_vol_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_base_growth_th_invest_percent,0) ELSE spplv.changed_base_growth_th_invest_percent END AS changed_base_growth_th_invest_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.status_flag else spplv.status_flag end as  status_flag,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.shelf_price_new,0) else spplv.shelf_price end as  shelf_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.list_price_new,0) else spplv.list_price_new end as  list_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.pack_weight_kg_new,0) else spplv.pack_weight_kg_new end as  pack_weight_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.dead_net_new,0) else spplv.dead_net_new end as  dead_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.net_net_new,0) else spplv.net_net_new end as  net_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_vol_change_new else spplv.promo_vol_change end as  promo_vol_change_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.base_growth_th_invest_new else spplv.base_growth_th_invest end as  base_growth_th_invest_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_price_per_kg_new else spplv.promo_price_per_kg_new end as  promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.non_promo_price_per_kg_new else spplv.non_promo_price_per_kg end as  non_promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.non_promo_price_new,0) else spplv.non_promo_price_new end as  non_promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.promo_price_new,0) else spplv.promo_price_new end as  promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.competitor_follows else spplv.competitor_follows end as  competitor_follows,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.type_of_price_inc else spplv.type_of_price_inc end as  type_of_price_inc,"\
            "spplv.floor_price,"\
            "CASE WHEN cps.competitor_follows = 'Yes' THEN non_promo_price_elasticity + competitor_coefficient else spplv.net_non_promo_price_elasticity end as  net_non_promo_price_elasticity,"\
            "sell_in_volume_t,bww_gsv,nsv,nsv_t,tt,cogs_t,gmac_abs,gmac,list_price,pack_weight_kg,"\
            "tax,net_net,dead_net,shelf_price,promo_price_per_unit,"\
            "promo_price_elasticity,promo_share,tpr,sell_out_volume_sales_t,unit_sales_000,sell_in_unit,value_sales_rsv,"\
            "non_promo_price_elasticity,promo_vol_change,base_growth_th_invest,ogsm_type,spplv.customer,"\
            "cps.investment,non_promo_price_per_kg,non_promo_price_per_unit,competitor_coefficient,"\
            "cps.list_price_change_percent,cps.list_price_change_percent_kg,cps.nsv_price_impact_direct,cps.nsv_price_impact_indirect,"\
            "cps.nsv_price_impact_base,cps.new_lp_after_base_price,cps.type_of_base_inc_after_change,non_promo_price_per_unit_ppg,promo_price_per_unit_ppg,is_optimizer_ppg,"\
            "product_group,ogsm_param,technology,brand from  {data[db_table]} as spplv inner join (select pss.net_net_multiplication_factor,pss.lp_multiplication_factor,cps.* from {data[db_table2]} as cps inner join {data[db_table3]} as pss on cps.pricing_saved_scenario_id=pss.id "\
            "where pricing_saved_scenario_id = {data[saved_scenario_id]}) cps on cps.level_param = spplv.product_group  where spplv.customer ='{data[customer]}'".format(data=query_params)
    return query


def agg_changed_inputs_query_with_filter(scenario_id,filtered_data,common_table):
    query_params  = {
            "db_table":db_table_format_in_sql_query_str(common_table,schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table2":db_table_format_in_sql_query_str('changed_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table3":db_table_format_in_sql_query_str('pricing_saved_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "saved_scenario_id":scenario_id,
            'customer':filtered_data.get('customer')
        }
    query = "select ROW_NUMBER() OVER( ORDER BY spplv.product_group ) AS  id,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.net_net_multiplication_factor,1) else spplv.net_net_multiplication_factor end as net_net_multiplication_factor,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.lp_multiplication_factor,1.2) else spplv.lp_multiplication_factor end as lp_multiplication_factor,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_cogs_t_percent,0) ELSE spplv.changed_cogs_t_percent END AS changed_cogs_t_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_net_net_change_percent,0) ELSE spplv.changed_net_net_change_percent END AS changed_net_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_dead_net_change_percent,0) ELSE spplv.changed_dead_net_change_percent END AS changed_dead_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_nn_change_percent,0) ELSE spplv.changed_nn_change_percent END AS changed_nn_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_lp_percent,0) ELSE spplv.changed_lp_percent END AS changed_lp_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_pack_weight_kg_percent,0) ELSE spplv.changed_pack_weight_kg_percent END AS changed_pack_weight_kg_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_shelf_price_percent,0) ELSE spplv.changed_shelf_price_percent END AS changed_shelf_price_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_non_promo_price,0) ELSE spplv.changed_non_promo_price END AS changed_non_promo_price,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_promo_vol_change_percent,0) ELSE spplv.changed_promo_vol_change_percent END AS changed_promo_vol_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_base_growth_th_invest_percent,0) ELSE spplv.changed_base_growth_th_invest_percent END AS changed_base_growth_th_invest_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.status_flag else spplv.status_flag end as  status_flag,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.shelf_price_new,0) else spplv.shelf_price end as  shelf_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.list_price_new,0) else spplv.list_price_new end as  list_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.pack_weight_kg_new,0) else spplv.pack_weight_kg_new end as  pack_weight_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.dead_net_new,0) else spplv.dead_net_new end as  dead_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.net_net_new,0) else spplv.net_net_new end as  net_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_vol_change_new else spplv.promo_vol_change end as  promo_vol_change_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.base_growth_th_invest_new else spplv.base_growth_th_invest end as  base_growth_th_invest_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_price_per_kg_new else spplv.promo_price_per_kg_new end as  promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.non_promo_price_per_kg_new else spplv.non_promo_price_per_kg end as  non_promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.non_promo_price_new,0) else spplv.non_promo_price_new end as  non_promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.promo_price_new,0) else spplv.promo_price_new end as  promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.competitor_follows else spplv.competitor_follows end as  competitor_follows,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.type_of_price_inc else spplv.type_of_price_inc end as  type_of_price_inc,"\
            "spplv.floor_price,"\
            "CASE WHEN cps.competitor_follows = 'Yes' THEN non_promo_price_elasticity + competitor_coefficient else spplv.net_non_promo_price_elasticity end as  net_non_promo_price_elasticity,"\
            "sell_in_volume_t,bww_gsv,nsv,nsv_t,tt,cogs_t,gmac_abs,gmac,list_price,pack_weight_kg,"\
            "tax,net_net,dead_net,shelf_price,promo_price_per_unit,"\
            "promo_price_elasticity,promo_share,tpr,sell_out_volume_sales_t,unit_sales_000,sell_in_unit,value_sales_rsv,"\
            "non_promo_price_elasticity,promo_vol_change,base_growth_th_invest,ogsm_type,spplv.customer,"\
            "cps.investment,non_promo_price_per_kg,non_promo_price_per_unit,competitor_coefficient,"\
            "cps.list_price_change_percent,cps.list_price_change_percent_kg,cps.nsv_price_impact_direct,cps.nsv_price_impact_indirect,"\
            "cps.nsv_price_impact_base,cps.new_lp_after_base_price,cps.type_of_base_inc_after_change,non_promo_price_per_unit_ppg,promo_price_per_unit_ppg,is_optimizer_ppg,"\
            "product_group,ogsm_param,technology,brand from  {data[db_table]} as spplv left join (select pss.net_net_multiplication_factor,pss.lp_multiplication_factor,cps.* from {data[db_table2]} as cps inner join {data[db_table3]} as pss on cps.pricing_saved_scenario_id=pss.id "\
            "where pricing_saved_scenario_id = {data[saved_scenario_id]}) cps on cps.level_param = spplv.product_group  where spplv.customer ='{data[customer]}'".format(data=query_params)
    return query

def fetch_inputs_filter(scenario_id,common_table):
    query_params  = {
            "db_table":db_table_format_in_sql_query_str(common_table,schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table2":db_table_format_in_sql_query_str('changed_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "saved_scenario_id":scenario_id
        }
    query = "select ROW_NUMBER() OVER( ORDER BY spplv.product_group ) AS  id,"\
            "product_group,ogsm_type,technology,brand,spplv.customer from  {data[db_table]} as spplv left join (select * from {data[db_table2]} as cps "\
            "where pricing_saved_scenario_id = {data[saved_scenario_id]}) cps on cps.level_param = spplv.product_group and cps.customer_level_param = spplv.customer".format(data=query_params)
    return query


def all_agg_optimized_inputs_customer_level_query(scenario_id):
    query_params  = {
            "db_table":db_table_format_in_sql_query_str('base_and_simulated_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "saved_scenario_id":scenario_id
        }
    query = "select ROW_NUMBER() OVER( ORDER BY spplv.customer ) AS  id,"\
            "ogsm_type,spplv.customer "\
            "from {data[db_table]} as spplv group by spplv.customer,ogsm_type".format(data=query_params)
    return query

def agg_optimized_inputs_query(scenario_id,filtered_data,common_table):
    query_params  = {
            "db_table":db_table_format_in_sql_query_str(common_table,schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table2":db_table_format_in_sql_query_str('changed_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table3":db_table_format_in_sql_query_str('pricing_saved_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "saved_scenario_id":scenario_id,
            'customer':filtered_data.get('customer')
        }
    query = "select ROW_NUMBER() OVER( ORDER BY spplv.product_group ) AS  id,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.net_net_multiplication_factor,1) else spplv.net_net_multiplication_factor end as net_net_multiplication_factor,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.lp_multiplication_factor,1.2) else spplv.lp_multiplication_factor end as lp_multiplication_factor,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_cogs_t_percent,0) ELSE spplv.changed_cogs_t_percent END AS changed_cogs_t_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_net_net_change_percent,0) ELSE spplv.changed_net_net_change_percent END AS changed_net_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_dead_net_change_percent,0) ELSE spplv.changed_dead_net_change_percent END AS changed_dead_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_nn_change_percent,0) ELSE spplv.changed_nn_change_percent END AS changed_nn_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_lp_percent,0) ELSE spplv.changed_lp_percent END AS changed_lp_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_pack_weight_kg_percent,0) ELSE spplv.changed_pack_weight_kg_percent END AS changed_pack_weight_kg_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_shelf_price_percent,0) ELSE spplv.changed_shelf_price_percent END AS changed_shelf_price_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_non_promo_price,0) ELSE spplv.changed_non_promo_price END AS changed_non_promo_price,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_promo_vol_change_percent,0) ELSE spplv.changed_promo_vol_change_percent END AS changed_promo_vol_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_base_growth_th_invest_percent,0) ELSE spplv.changed_base_growth_th_invest_percent END AS changed_base_growth_th_invest_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.status_flag else spplv.status_flag end as  status_flag,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.shelf_price_new,0) else spplv.shelf_price end as  shelf_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.list_price_new,0) else spplv.list_price_new end as  list_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.pack_weight_kg_new,0) else spplv.pack_weight_kg_new end as  pack_weight_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.dead_net_new,0) else spplv.dead_net_new end as  dead_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.net_net_new,0) else spplv.net_net_new end as  net_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_vol_change_new else spplv.promo_vol_change end as  promo_vol_change_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.base_growth_th_invest_new else spplv.base_growth_th_invest end as  base_growth_th_invest_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_price_per_kg_new else spplv.promo_price_per_kg_new end as  promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.non_promo_price_per_kg_new else spplv.non_promo_price_per_kg end as  non_promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.non_promo_price_new,0) else spplv.non_promo_price_new end as  non_promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.promo_price_new,0) else spplv.promo_price_new end as  promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.competitor_follows else spplv.competitor_follows end as  competitor_follows,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.type_of_price_inc else spplv.type_of_price_inc end as  type_of_price_inc,"\
            "spplv.floor_price,"\
            "CASE WHEN cps.competitor_follows = 'Yes' THEN non_promo_price_elasticity + competitor_coefficient else spplv.net_non_promo_price_elasticity end as  net_non_promo_price_elasticity,"\
            "sell_in_volume_t,bww_gsv,nsv,nsv_t,tt,cogs_t,gmac_abs,gmac,list_price,pack_weight_kg,"\
            "tax,net_net,dead_net,shelf_price,promo_price_per_unit,"\
            "promo_price_elasticity,promo_share,tpr,sell_out_volume_sales_t,unit_sales_000,sell_in_unit,value_sales_rsv,"\
            "non_promo_price_elasticity,promo_vol_change,base_growth_th_invest,ogsm_type,spplv.customer,"\
            "cps.investment,non_promo_price_per_kg,non_promo_price_per_unit,competitor_coefficient,"\
            "cps.list_price_change_percent,cps.list_price_change_percent_kg,cps.nsv_price_impact_direct,cps.nsv_price_impact_indirect,"\
            "cps.nsv_price_impact_base,cps.new_lp_after_base_price,cps.type_of_base_inc_after_change,non_promo_price_per_unit_ppg,promo_price_per_unit_ppg,is_optimizer_ppg,"\
            "product_group,ogsm_param,technology,brand from  {data[db_table]} as spplv left join (select pss.net_net_multiplication_factor,pss.lp_multiplication_factor,cps.* from {data[db_table2]} as cps inner join {data[db_table3]} as pss on cps.pricing_saved_scenario_id=pss.id "\
            "where pricing_saved_scenario_id = {data[saved_scenario_id]}) cps on cps.level_param = spplv.product_group  where spplv.customer ='{data[customer]}'".format(data=query_params)
    return query

def all_agg_changed_inputs_customer_level_query(scenario_id):
    query_params  = {
            "db_table":db_table_format_in_sql_query_str('pricing_customer_level_view',schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table2":db_table_format_in_sql_query_str('changed_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "saved_scenario_id":scenario_id
        }
    query = "select ROW_NUMBER() OVER( ORDER BY spplv.nsv_sum desc) AS  id,"\
            "ogsm_type,spplv.customer,spplv.net_net_nsv_product,spplv.lp_nsv_product,spplv.actual_invest,spplv.invest_budget,spplv.nsv_sum "\
            "from {data[db_table]} as spplv left join (select customer_level_param from {data[db_table2]} as cps "\
            "where pricing_saved_scenario_id = {data[saved_scenario_id]} group by customer_level_param) cps on cps.customer_level_param = spplv.customer".format(data=query_params)
    return query

def agg_changed_inputs_customer_level_query(scenario_id):
    
    query_params  = {
            "db_table":db_table_format_in_sql_query_str('pricing_customer_level_view',schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table2":db_table_format_in_sql_query_str('changed_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "saved_scenario_id":scenario_id
        }
    query = "select ROW_NUMBER() OVER( ORDER BY spplv.nsv_sum  desc) AS  id,"\
            "ogsm_type,spplv.customer,spplv.net_net_nsv_product,spplv.lp_nsv_product,spplv.actual_invest,spplv.invest_budget,spplv.nsv_sum,"\
            "spplv.ogsm_type, spplv.list_price,spplv.pack_weight_kg,spplv.cogs_t,spplv.shelf_price,spplv.promo_vol_change,spplv.base_growth_th_invest,"\
            "spplv.nsv_sum,spplv.net_net,spplv.dead_net,spplv.promo_price_per_unit,spplv.non_promo_price_per_unit,spplv.changed_lp_percent,"\
            "spplv.changed_pack_weight_kg_percent,spplv.changed_cogs_t_percent,spplv.changed_shelf_price_percent,spplv.changed_promo_vol_change_percent,"\
            "spplv.changed_base_growth_th_invest_percent,spplv.changed_net_net_change_percent,spplv.changed_dead_net_change_percent,spplv.changed_non_promo_price,"\
            "spplv.changed_promo_price,spplv.changed_nn_change_percent,spplv.list_price_change_percent,spplv.list_price_change_percent_kg,spplv.nsv_price_impact_direct,"\
            "spplv.nsv_price_impact_indirect,spplv.nsv_price_impact_base,spplv.new_lp_after_base_price,spplv.shelf_price_new,spplv.list_price_new,spplv.pack_weight_kg_new,"\
            "spplv.promo_price_new,spplv.non_promo_price_new,spplv.non_promo_price_per_kg_new,spplv.promo_price_per_kg_new,spplv.investment,spplv.tpr,spplv.tpr_new,spplv.dead_net_new,"\
            "spplv.net_net_new,spplv.nsv_percent_change,spplv.nsv_t_percent_change,spplv.gmac_percent_change,spplv.rsv_percent_change,spplv.promo_price_per_kg,spplv.promo_share,spplv.competitor_coefficient,"\
            "spplv.non_promo_price_per_kg,spplv.promo_price_elasticity,spplv.non_promo_price_elasticity,spplv.floor_price,spplv.net_non_promo_price_elasticity,spplv.nn_change_percent,spplv.type_of_base_inc_after_change,"\
            "spplv.customer,spplv.competitor_follows,spplv.type_of_price_inc,spplv.status_flag,spplv.ogsm_type,spplv.net_net_nsv_product,spplv.lp_nsv_product,spplv.actual_invest,spplv.invest_budget  from {data[db_table]} as spplv inner join (select customer_level_param from {data[db_table2]} as cps "\
            "where pricing_saved_scenario_id = {data[saved_scenario_id]} group by customer_level_param) cps on cps.customer_level_param = spplv.customer".format(data=query_params)
    return query

def agg_optimized_output_customer_level_query(scenario_id):
    
    query_params  = {
            "db_table":db_table_format_in_sql_query_str('pricing_customer_level_view',schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table2":db_table_format_in_sql_query_str('base_and_simulated_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "saved_scenario_id":scenario_id
        }
    inner_query=(
        "select saved_scenario_id={data[saved_scenario_id]},customer,SUM(CAST(nsv_new AS FLOAT) * CAST(changed_net_net_change_percent AS FLOAT)) / SUM(CAST(nsv_new AS FLOAT)) AS net_net_nsv_product,"\
        "SUM(CAST(nsv_new AS FLOAT) * CAST(changed_lp_percent AS FLOAT)) / SUM(CAST(nsv_new AS FLOAT)) AS lp_nsv_product,SUM(CAST(nsv_new AS FLOAT)) AS nsv_sum,"\
        "SUM(CAST(nsv_new AS FLOAT)) * ((SUM(CAST(nsv_new AS FLOAT) * CAST(changed_net_net_change_percent AS FLOAT)) / SUM(CAST(nsv_new AS FLOAT))) - AVG(CAST(nn_change_percent AS FLOAT))) AS actual_invest,"\
        "SUM(CAST(nsv_new AS FLOAT)) * ((SUM(CAST(nsv_new AS FLOAT) * CAST(changed_lp_percent AS FLOAT)) / SUM(CAST(nsv_new AS FLOAT))) - AVG(CAST(nn_change_percent AS FLOAT))) AS invest_budget "\
        "from {data[db_table2]} where pricing_saved_scenario_id = {data[saved_scenario_id]} and is_base =0 group by customer".format(data=query_params)
    )
    query_params['inner_query'] = inner_query
    query = "select ROW_NUMBER() OVER( ORDER BY spplv.nsv_sum desc) AS  id,"\
            "ogsm_type,spplv.customer,bsps.net_net_nsv_product,bsps.lp_nsv_product,bsps.actual_invest,bsps.invest_budget,bsps.nsv_sum from {data[db_table]} as spplv inner join ({data[inner_query]}) bsps on bsps.customer=spplv.customer".format(data=query_params)
    return query


def agg_changed_inputs_query(scenario_id,db_table):
    
    query_params  = {
            "db_table":db_table_format_in_sql_query_str(db_table,schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table2":db_table_format_in_sql_query_str('changed_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table3":db_table_format_in_sql_query_str('pricing_saved_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "saved_scenario_id":scenario_id
        }
    query = "select ROW_NUMBER() OVER( ORDER BY spplv.product_group ) AS  id,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.net_net_multiplication_factor,1) else spplv.net_net_multiplication_factor end as net_net_multiplication_factor,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.lp_multiplication_factor,1.2) else spplv.lp_multiplication_factor end as lp_multiplication_factor,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_cogs_t_percent,0) ELSE spplv.changed_cogs_t_percent END AS changed_cogs_t_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_net_net_change_percent,0) ELSE spplv.changed_net_net_change_percent END AS changed_net_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_dead_net_change_percent,0) ELSE spplv.changed_dead_net_change_percent END AS changed_dead_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_nn_change_percent,0) ELSE spplv.changed_nn_change_percent END AS changed_nn_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_lp_percent,0) ELSE spplv.changed_lp_percent END AS changed_lp_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_pack_weight_kg_percent,0) ELSE spplv.changed_pack_weight_kg_percent END AS changed_pack_weight_kg_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_shelf_price_percent,0) ELSE spplv.changed_shelf_price_percent END AS changed_shelf_price_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_non_promo_price,0) ELSE spplv.changed_non_promo_price END AS changed_non_promo_price,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_promo_vol_change_percent,0) ELSE spplv.changed_promo_vol_change_percent END AS changed_promo_vol_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_base_growth_th_invest_percent,0) ELSE spplv.changed_base_growth_th_invest_percent END AS changed_base_growth_th_invest_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.status_flag else spplv.status_flag end as  status_flag,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.shelf_price_new,0) else spplv.shelf_price end as  shelf_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_promo_price,0) else spplv.changed_non_promo_price end as  changed_promo_price,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.list_price_new,0) else spplv.list_price_new end as  list_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.pack_weight_kg_new,0) else spplv.pack_weight_kg_new end as  pack_weight_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.dead_net_new,0) else spplv.dead_net_new end as  dead_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.net_net_new,0) else spplv.net_net_new end as  net_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_vol_change_new else spplv.promo_vol_change end as  promo_vol_change_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.base_growth_th_invest_new else spplv.base_growth_th_invest end as  base_growth_th_invest_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_price_per_kg_new else spplv.promo_price_per_kg_new end as  promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.non_promo_price_per_kg_new else spplv.non_promo_price_per_kg end as  non_promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.non_promo_price_new,0) else spplv.non_promo_price_new end as  non_promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.promo_price_new,0) else spplv.promo_price_new end as  promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.competitor_follows else spplv.competitor_follows end as  competitor_follows,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.type_of_price_inc else spplv.type_of_price_inc end as  type_of_price_inc,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_sold_volume_new else 0 end as  promo_sold_volume_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.non_promo_sold_volume_new else 0 end as  non_promo_sold_volume_new,"\
            "spplv.nn_change_percent,cps.promo_sold_volume,cps.non_promo_sold_volume,cps.promo_sold_volume_percent,cps.promo_sold_volume_percent_new,"\
            "spplv.floor_price,spplv.min_shelf_price,"\
            "CASE WHEN cps.competitor_follows = 'Yes' THEN non_promo_price_elasticity + competitor_coefficient else spplv.net_non_promo_price_elasticity end as  net_non_promo_price_elasticity,"\
            "sell_in_volume_t,bww_gsv,nsv,nsv_t,tt,cogs_t,gmac_abs,gmac,list_price,pack_weight_kg,"\
            "tax,net_net,dead_net,shelf_price,promo_price_per_unit,"\
            "promo_price_elasticity,promo_share,tpr,sell_out_volume_sales_t,unit_sales_000,sell_in_unit,value_sales_rsv,"\
            "non_promo_price_elasticity,promo_vol_change,base_growth_th_invest,ogsm_type,spplv.customer,"\
            "cps.investment,non_promo_price_per_kg,non_promo_price_per_unit,competitor_coefficient,"\
            "cps.list_price_change_percent,cps.list_price_change_percent_kg,cps.nsv_price_impact_direct,cps.nsv_price_impact_indirect,"\
            "cps.nsv_price_impact_base,cps.new_lp_after_base_price,cps.type_of_base_inc_after_change,non_promo_price_per_unit_ppg,promo_price_per_unit_ppg,is_optimizer_ppg,exclude_retailer,net_net_nsv_product,lp_nsv_product,"\
            "product_group,ogsm_param,technology,brand from  {data[db_table]} as spplv inner join (select pss.net_net_multiplication_factor,pss.lp_multiplication_factor,cps.* from {data[db_table2]} as cps inner join {data[db_table3]} as pss on cps.pricing_saved_scenario_id=pss.id "\
            "where pricing_saved_scenario_id = {data[saved_scenario_id]}) cps on cps.level_param = spplv.product_group".format(data=query_params)
    return query
def agg_changed_inputs_with_customer_query(scenario_id,customers,common_table):
    
    query_params  = {
            "db_table":db_table_format_in_sql_query_str(common_table,schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table2":db_table_format_in_sql_query_str('changed_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table3":db_table_format_in_sql_query_str('pricing_saved_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "saved_scenario_id":scenario_id,
            'customers':convert_list_to_tuple_str(customers)
        }
    query = "select ROW_NUMBER() OVER( ORDER BY nsv desc ) AS  id,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.net_net_multiplication_factor,1) else spplv.net_net_multiplication_factor end as net_net_multiplication_factor,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.lp_multiplication_factor,1.2) else spplv.lp_multiplication_factor end as lp_multiplication_factor,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_cogs_t_percent,0) ELSE spplv.changed_cogs_t_percent END AS changed_cogs_t_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_net_net_change_percent,0) ELSE spplv.changed_net_net_change_percent  END AS changed_net_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_dead_net_change_percent,0) ELSE spplv.changed_dead_net_change_percent END AS changed_dead_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_nn_change_percent,0) ELSE spplv.changed_nn_change_percent END AS changed_nn_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_lp_percent,0) ELSE spplv.changed_lp_percent END AS changed_lp_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_pack_weight_kg_percent,0) ELSE spplv.changed_pack_weight_kg_percent END AS changed_pack_weight_kg_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_shelf_price_percent,0) ELSE spplv.changed_shelf_price_percent END AS changed_shelf_price_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_promo_vol_change_percent,0) ELSE spplv.changed_promo_vol_change_percent END AS changed_promo_vol_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_base_growth_th_invest_percent,0) ELSE spplv.changed_base_growth_th_invest_percent END AS changed_base_growth_th_invest_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.status_flag else spplv.status_flag end as  status_flag,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.shelf_price_new,0) else spplv.shelf_price end as  shelf_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_non_promo_price,0) else spplv.changed_non_promo_price end as  changed_non_promo_price,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_promo_price,0) else spplv.changed_promo_price  end as  changed_promo_price,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.list_price_new,0) else spplv.list_price_new end as  list_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.pack_weight_kg_new,0) else spplv.pack_weight_kg_new end as  pack_weight_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.dead_net_new,0) else spplv.dead_net_new end as  dead_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.net_net_new,0) else spplv.net_net_new end as  net_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_vol_change_new else spplv.promo_vol_change end as  promo_vol_change_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.base_growth_th_invest_new else spplv.base_growth_th_invest end as  base_growth_th_invest_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_price_per_kg_new else spplv.promo_price_per_kg_new end as  promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.non_promo_price_per_kg_new else spplv.non_promo_price_per_kg end as  non_promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.non_promo_price_new,0) else spplv.non_promo_price_new end as  non_promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.promo_price_new,0) else spplv.promo_price_new end as  promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.competitor_follows else spplv.competitor_follows end as  competitor_follows,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.type_of_price_inc else spplv.type_of_price_inc end as  type_of_price_inc,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_sold_volume_new else 0 end as  promo_sold_volume_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.non_promo_sold_volume_new else 0 end as  non_promo_sold_volume_new,"\
            "spplv.nn_change_percent,cps.promo_sold_volume,cps.non_promo_sold_volume,cps.promo_sold_volume_percent,cps.promo_sold_volume_percent_new,"\
            "CASE WHEN cps.competitor_follows = 'Yes' THEN non_promo_price_elasticity + competitor_coefficient else spplv.net_non_promo_price_elasticity end as  net_non_promo_price_elasticity,"\
            "sell_in_volume_t,bww_gsv,nsv,nsv_t,tt,cogs_t,gmac_abs,gmac,list_price,pack_weight_kg,"\
            "spplv.floor_price,tax,net_net,dead_net,shelf_price,promo_price_per_unit,"\
            "promo_price_elasticity,promo_share,tpr,sell_out_volume_sales_t,unit_sales_000,sell_in_unit,value_sales_rsv,"\
            "non_promo_price_elasticity,promo_vol_change,base_growth_th_invest,ogsm_type,spplv.customer,"\
            "cps.investment,non_promo_price_per_kg,non_promo_price_per_unit,competitor_coefficient,"\
            "cps.list_price_change_percent,cps.list_price_change_percent_kg,cps.nsv_price_impact_direct,cps.nsv_price_impact_indirect,"\
            "cps.nsv_price_impact_base,cps.new_lp_after_base_price,cps.type_of_base_inc_after_change,non_promo_price_per_unit_ppg,promo_price_per_unit_ppg,is_optimizer_ppg,optimized_trade_margin, optimized_units,exclude_retailer,net_net_nsv_product,lp_nsv_product,"\
            "product_group,ogsm_param,technology,brand from  {data[db_table]} as spplv inner join (select pss.net_net_multiplication_factor,pss.lp_multiplication_factor,cps.* from {data[db_table2]} as cps inner join {data[db_table3]} as pss on cps.pricing_saved_scenario_id=pss.id "\
            "where pricing_saved_scenario_id = {data[saved_scenario_id]}) cps on cps.level_param = spplv.product_group and cps.customer_level_param=spplv.customer and cps.inner_mode='filtered'" \
            " union all "\
            "select ROW_NUMBER() OVER( ORDER BY nsv desc ) AS  id,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.net_net_multiplication_factor,1) else spplv.net_net_multiplication_factor end as net_net_multiplication_factor,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.lp_multiplication_factor,1.2) else spplv.lp_multiplication_factor end as lp_multiplication_factor,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_cogs_t_percent,0) ELSE spplv.changed_cogs_t_percent END AS changed_cogs_t_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_net_net_change_percent,0) ELSE spplv.changed_net_net_change_percent  END AS changed_net_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_dead_net_change_percent,0) ELSE spplv.changed_dead_net_change_percent END AS changed_dead_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_nn_change_percent,0) ELSE spplv.changed_nn_change_percent END AS changed_nn_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_lp_percent,0) ELSE spplv.changed_lp_percent END AS changed_lp_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_pack_weight_kg_percent,0) ELSE spplv.changed_pack_weight_kg_percent END AS changed_pack_weight_kg_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_shelf_price_percent,0) ELSE spplv.changed_shelf_price_percent END AS changed_shelf_price_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_promo_vol_change_percent,0) ELSE spplv.changed_promo_vol_change_percent END AS changed_promo_vol_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_base_growth_th_invest_percent,0) ELSE spplv.changed_base_growth_th_invest_percent END AS changed_base_growth_th_invest_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.status_flag else spplv.status_flag end as  status_flag,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.shelf_price_new,0) else spplv.shelf_price end as  shelf_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_non_promo_price,0) else spplv.changed_non_promo_price end as  changed_non_promo_price,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_promo_price,0) else spplv.changed_promo_price end as  changed_promo_price,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.list_price_new,0) else spplv.list_price_new end as  list_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.pack_weight_kg_new,0) else spplv.pack_weight_kg_new end as  pack_weight_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.dead_net_new,0) else spplv.dead_net_new end as  dead_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.net_net_new,0) else spplv.net_net_new end as  net_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_vol_change_new else spplv.promo_vol_change end as  promo_vol_change_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.base_growth_th_invest_new else spplv.base_growth_th_invest end as  base_growth_th_invest_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_price_per_kg_new else spplv.promo_price_per_kg_new end as  promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.non_promo_price_per_kg_new else spplv.non_promo_price_per_kg end as  non_promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.non_promo_price_new,0) else spplv.non_promo_price_new end as  non_promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.promo_price_new,0) else spplv.promo_price_new end as  promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.competitor_follows else spplv.competitor_follows end as  competitor_follows,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.type_of_price_inc else spplv.type_of_price_inc end as  type_of_price_inc,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_sold_volume_new else 0 end as  promo_sold_volume_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.non_promo_sold_volume_new else 0 end as  non_promo_sold_volume_new,"\
            "spplv.nn_change_percent,cps.promo_sold_volume,cps.non_promo_sold_volume,cps.promo_sold_volume_percent,cps.promo_sold_volume_percent_new,"\
            "CASE WHEN cps.competitor_follows = 'Yes' THEN non_promo_price_elasticity + competitor_coefficient else spplv.net_non_promo_price_elasticity end as  net_non_promo_price_elasticity,"\
            "sell_in_volume_t,bww_gsv,nsv,nsv_t,tt,cogs_t,gmac_abs,gmac,list_price,pack_weight_kg,"\
            "spplv.floor_price,tax,net_net,dead_net,shelf_price,promo_price_per_unit,"\
            "promo_price_elasticity,promo_share,tpr,sell_out_volume_sales_t,unit_sales_000,sell_in_unit,value_sales_rsv,"\
            "non_promo_price_elasticity,promo_vol_change,base_growth_th_invest,ogsm_type,spplv.customer,"\
            "cps.investment,non_promo_price_per_kg,non_promo_price_per_unit,competitor_coefficient,"\
            "cps.list_price_change_percent,cps.list_price_change_percent_kg,cps.nsv_price_impact_direct,cps.nsv_price_impact_indirect,"\
            "cps.nsv_price_impact_base,cps.new_lp_after_base_price,cps.type_of_base_inc_after_change,non_promo_price_per_unit_ppg,promo_price_per_unit_ppg,is_optimizer_ppg,optimized_trade_margin, optimized_units,exclude_retailer,net_net_nsv_product,lp_nsv_product,"\
            "product_group,ogsm_param,technology,brand from  {data[db_table]} as spplv inner join (select pss.net_net_multiplication_factor,pss.lp_multiplication_factor,cps.* from {data[db_table2]} as cps inner join {data[db_table3]} as pss on cps.pricing_saved_scenario_id=pss.id "\
            "where pricing_saved_scenario_id = {data[saved_scenario_id]}) cps on cps.level_param = '' and cps.customer_level_param=spplv.customer"\
            " union all "\
            "select ROW_NUMBER() OVER( ORDER BY nsv desc ) AS  id,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.net_net_multiplication_factor,1) else spplv.net_net_multiplication_factor end as net_net_multiplication_factor,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.lp_multiplication_factor,1.2) else spplv.lp_multiplication_factor end as lp_multiplication_factor,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_cogs_t_percent,0) ELSE spplv.changed_cogs_t_percent END AS changed_cogs_t_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_net_net_change_percent,0) ELSE spplv.changed_net_net_change_percent  END AS changed_net_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_dead_net_change_percent,0) ELSE spplv.changed_dead_net_change_percent END AS changed_dead_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_nn_change_percent,0) ELSE spplv.changed_nn_change_percent END AS changed_nn_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_lp_percent,0) ELSE spplv.changed_lp_percent END AS changed_lp_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_pack_weight_kg_percent,0) ELSE spplv.changed_pack_weight_kg_percent END AS changed_pack_weight_kg_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_shelf_price_percent,0) ELSE spplv.changed_shelf_price_percent END AS changed_shelf_price_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_promo_vol_change_percent,0) ELSE spplv.changed_promo_vol_change_percent END AS changed_promo_vol_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_base_growth_th_invest_percent,0) ELSE spplv.changed_base_growth_th_invest_percent END AS changed_base_growth_th_invest_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.status_flag else spplv.status_flag end as  status_flag,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.shelf_price_new,0) else spplv.shelf_price end as  shelf_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_non_promo_price,0) else spplv.changed_non_promo_price end as  changed_non_promo_price,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_promo_price,0) else spplv.changed_promo_price end as  changed_promo_price,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.list_price_new,0) else spplv.list_price_new end as  list_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.pack_weight_kg_new,0) else spplv.pack_weight_kg_new end as  pack_weight_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.dead_net_new,0) else spplv.dead_net_new end as  dead_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.net_net_new,0) else spplv.net_net_new end as  net_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_vol_change_new else spplv.promo_vol_change end as  promo_vol_change_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.base_growth_th_invest_new else spplv.base_growth_th_invest end as  base_growth_th_invest_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_price_per_kg_new else spplv.promo_price_per_kg_new end as  promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.non_promo_price_per_kg_new else spplv.non_promo_price_per_kg end as  non_promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.non_promo_price_new,0) else spplv.non_promo_price_new end as  non_promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.promo_price_new,0) else spplv.promo_price_new end as  promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.competitor_follows else spplv.competitor_follows end as  competitor_follows,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.type_of_price_inc else spplv.type_of_price_inc end as  type_of_price_inc,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_sold_volume_new else 0 end as  promo_sold_volume_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.non_promo_sold_volume_new else 0 end as  non_promo_sold_volume_new,"\
            "spplv.nn_change_percent,cps.promo_sold_volume,cps.non_promo_sold_volume,cps.promo_sold_volume_percent,cps.promo_sold_volume_percent_new,"\
            "CASE WHEN cps.competitor_follows = 'Yes' THEN non_promo_price_elasticity + competitor_coefficient else spplv.net_non_promo_price_elasticity end as  net_non_promo_price_elasticity,"\
            "sell_in_volume_t,bww_gsv,nsv,nsv_t,tt,cogs_t,gmac_abs,gmac,list_price,pack_weight_kg,"\
            "spplv.floor_price,tax,net_net,dead_net,shelf_price,promo_price_per_unit,"\
            "promo_price_elasticity,promo_share,tpr,sell_out_volume_sales_t,unit_sales_000,sell_in_unit,value_sales_rsv,"\
            "non_promo_price_elasticity,promo_vol_change,base_growth_th_invest,ogsm_type,spplv.customer,"\
            "cps.investment,non_promo_price_per_kg,non_promo_price_per_unit,competitor_coefficient,"\
            "cps.list_price_change_percent,cps.list_price_change_percent_kg,cps.nsv_price_impact_direct,cps.nsv_price_impact_indirect,"\
            "cps.nsv_price_impact_base,cps.new_lp_after_base_price,cps.type_of_base_inc_after_change,non_promo_price_per_unit_ppg,promo_price_per_unit_ppg,is_optimizer_ppg,optimized_trade_margin, optimized_units,exclude_retailer,net_net_nsv_product,lp_nsv_product,"\
            "product_group,ogsm_param,technology,brand from  {data[db_table]} as spplv  left join (select pss.net_net_multiplication_factor,pss.lp_multiplication_factor,cps.* from {data[db_table2]} as cps inner join {data[db_table3]} as pss on cps.pricing_saved_scenario_id=pss.id "\
            "where pricing_saved_scenario_id = {data[saved_scenario_id]}) cps on cps.level_param = spplv.product_group and cps.customer_level_param=spplv.customer where spplv.customer in {data[customers]}".format(data=query_params)
    return query

def all_agg_optimized_output_query(scenario_id):
    query_params  = {
                "db_table":db_table_format_in_sql_query_str('scenario_planner_common_view',schema=settings.PRICING_DATABASE_SCHEMA),
                "db_table2":db_table_format_in_sql_query_str('base_and_simulated_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
                "db_table3":db_table_format_in_sql_query_str('pricing_saved_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
                "saved_scenario_id":scenario_id
            }
    inner_query=(
        "select saved_scenario_id={data[saved_scenario_id]},customer,product_group,CAST(non_promo_price_new as float) non_promo_price_new,CAST(promo_price_new as float) promo_price_new,CAST(list_price_new as float) list_price_new,"\
        "CAST(changed_cogs_t_percent as float) changed_cogs_t_percent,CAST(changed_pack_weight_kg_percent as float) changed_pack_weight_kg_percent,"\
        "type_of_price_inc,status_flag,competitor_follows,CAST(floor_price as float) floor_price,"\
        "CAST(nsv_pricing_impact_direct as float) nsv_price_impact_direct,CAST(nsv_pricing_impact_indirect as float) nsv_price_impact_indirect,CAST(nsv_pricing_impact_base as float) nsv_price_impact_base,"\
        "CAST(changed_promo_vol_change_percent as float) changed_promo_vol_change_percent,CAST(pack_weight_kg_new as float) pack_weight_kg_new,CAST(nsv_new as float) nsv from {data[db_table2]} where pricing_saved_scenario_id = {data[saved_scenario_id]} and is_base =0".format(data=query_params)
    )
    query_params['inner_query'] = inner_query
    
    query = "select ROW_NUMBER() OVER( ORDER BY spplv.nsv desc) AS  id,"\
                "ISNULL(pss.net_net_multiplication_factor,1)*COALESCE((bsps.non_promo_price_new-non_promo_price_per_unit)/NULLIF(non_promo_price_per_unit,0),0)*100 changed_net_net_change_percent,"\
                "ISNULL(pss.net_net_multiplication_factor,1)*COALESCE((bsps.non_promo_price_new-non_promo_price_per_unit)/NULLIF(non_promo_price_per_unit,0),0)*100 changed_dead_net_change_percent,"\
                "net_net*(1+ISNULL(pss.net_net_multiplication_factor,1)*COALESCE((bsps.non_promo_price_new-non_promo_price_per_unit)/NULLIF(non_promo_price_per_unit,0),0)) net_net_new,"\
                "dead_net*(1+ISNULL(pss.net_net_multiplication_factor,1)*COALESCE((bsps.non_promo_price_new-non_promo_price_per_unit)/NULLIF(non_promo_price_per_unit,0),0))  dead_net_new,"\
                "COALESCE((bsps.non_promo_price_new-non_promo_price_per_unit)/NULLIF(non_promo_price_per_unit,0),0)*100 changed_non_promo_price,"\
                "COALESCE((bsps.promo_price_new-promo_price_per_unit)/NULLIF(promo_price_per_unit,0),0)*100 changed_promo_price,"\
                "COALESCE((bsps.list_price_new-list_price)/NULLIF(list_price,0),0)*100 changed_lp_percent,"\
                "cogs_t*(1+bsps.changed_cogs_t_percent/100) cogs_t_new,"\
                "case when bsps.competitor_follows = 'Yes' THEN non_promo_price_elasticity + competitor_coefficient else non_promo_price_elasticity end as  net_non_promo_price_elasticity,"\
                "bsps.changed_cogs_t_percent,"\
                "bsps.changed_pack_weight_kg_percent,"\
                "bsps.changed_promo_vol_change_percent,"\
                "bsps.status_flag,"\
                "bsps.list_price_new,"\
                "bsps.pack_weight_kg_new,"\
                "bsps.changed_promo_vol_change_percent as promo_vol_change_new,"\
                "bsps.non_promo_price_new,"\
                "bsps.promo_price_new,"\
                "bsps.competitor_follows,"\
                "bsps.type_of_price_inc,"\
                "spplv.floor_price,"\
                "net_non_promo_price_elasticity,"\
                "bsps.nsv,cogs_t,list_price,pack_weight_kg,"\
                "net_net,dead_net,promo_price_per_unit,"\
                "promo_price_elasticity,"\
                "non_promo_price_elasticity,promo_vol_change,ogsm_type,"\
                "non_promo_price_per_unit,competitor_coefficient,"\
                "COALESCE((bsps.list_price_new-list_price)/NULLIF(list_price,0),0)*100 as list_price_change_percent,COALESCE((bsps.list_price_new-list_price)/NULLIF(list_price,0),0)*100 as list_price_change_percent_kg,bsps.nsv_price_impact_direct,bsps.nsv_price_impact_indirect,"\
                "bsps.nsv_price_impact_base,bsps.list_price_new new_lp_after_base_price,bsps.type_of_price_inc as type_of_base_inc_after_change,"\
                "bsps.product_group,bsps.customer,ogsm_type,technology,brand from  {data[db_table]} as spplv inner join ({data[inner_query]}) bsps on spplv.product_group=bsps.product_group and spplv.customer=bsps.customer "\
                "inner join {data[db_table3]} pss on pss.id=bsps.saved_scenario_id".format(data=query_params)
    
    return query
  

def all_agg_optimized_output_customer_query(scenario_id,customers):
    query_params  = {
                "db_table":db_table_format_in_sql_query_str('scenario_planner_common_view',schema=settings.PRICING_DATABASE_SCHEMA),
                "db_table2":db_table_format_in_sql_query_str('base_and_simulated_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
                "db_table3":db_table_format_in_sql_query_str('pricing_saved_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
                'customers':convert_to_str(customers),
                "saved_scenario_id":scenario_id
            }
    inner_query=(
        "select saved_scenario_id={data[saved_scenario_id]},customer,product_group,CAST(non_promo_price_new as float) non_promo_price_new,CAST(promo_price_new as float) promo_price_new,CAST(list_price_new as float) list_price_new,"\
        "CAST(changed_cogs_t_percent as float) changed_cogs_t_percent,CAST(changed_pack_weight_kg_percent as float) changed_pack_weight_kg_percent,"\
        "type_of_price_inc,status_flag,competitor_follows,CAST(floor_price as float) floor_price,"\
        "CAST(nsv_pricing_impact_direct as float) nsv_price_impact_direct,CAST(nsv_pricing_impact_indirect as float) nsv_price_impact_indirect,CAST(nsv_pricing_impact_base as float) nsv_price_impact_base,"\
        "CAST(changed_promo_vol_change_percent as float) changed_promo_vol_change_percent,CAST(pack_weight_kg_new as float) pack_weight_kg_new,CAST(nsv_new as float) nsv,"\
        "CAST(changed_net_net_change_percent as float) changed_net_net_change_percent,CAST(changed_dead_net_change_percent as float) changed_dead_net_change_percent,"\
        "CAST(net_net_new as float) net_net_new ,CAST(dead_net_new as float) dead_net_new from {data[db_table2]} where pricing_saved_scenario_id = {data[saved_scenario_id]} and is_base =0".format(data=query_params)
    )
    query_params['inner_query'] = inner_query
    
    query = "select ROW_NUMBER() OVER( ORDER BY spplv.nsv desc) AS  id,"\
                "bsps.changed_net_net_change_percent,"\
                "bsps.changed_dead_net_change_percent,"\
                "bsps.net_net_new,"\
                "bsps.dead_net_new,"\
                "COALESCE((bsps.non_promo_price_new-non_promo_price_per_unit)/NULLIF(non_promo_price_per_unit,0),0)*100 changed_non_promo_price,"\
                "COALESCE((bsps.promo_price_new-promo_price_per_unit)/NULLIF(promo_price_per_unit,0),0)*100 changed_promo_price,"\
                "COALESCE((bsps.list_price_new-list_price)/NULLIF(list_price,0),0)*100 changed_lp_percent,"\
                "cogs_t*(1+bsps.changed_cogs_t_percent/100) cogs_t_new,"\
                "case when bsps.competitor_follows = 'Yes' THEN non_promo_price_elasticity + competitor_coefficient else non_promo_price_elasticity end as  net_non_promo_price_elasticity,"\
                "bsps.changed_cogs_t_percent changed_cogs_t_percent,"\
                "bsps.changed_pack_weight_kg_percent,"\
                "bsps.changed_promo_vol_change_percent,"\
                "bsps.status_flag,"\
                "bsps.list_price_new,"\
                "bsps.pack_weight_kg_new,"\
                "bsps.changed_promo_vol_change_percent as promo_vol_change_new,"\
                "bsps.non_promo_price_new,"\
                "bsps.promo_price_new,"\
                "bsps.competitor_follows,"\
                "bsps.type_of_price_inc,"\
                "spplv.floor_price,"\
                "net_non_promo_price_elasticity,"\
                "bsps.nsv,cogs_t,list_price,pack_weight_kg,"\
                "net_net,dead_net,promo_price_per_unit,"\
                "promo_price_elasticity,"\
                "non_promo_price_elasticity,promo_vol_change,ogsm_type,"\
                "non_promo_price_per_unit,competitor_coefficient,"\
                "COALESCE((bsps.list_price_new-list_price)/NULLIF(list_price,0),0)*100 as list_price_change_percent,COALESCE((bsps.list_price_new-list_price)/NULLIF(list_price,0),0)*100 as list_price_change_percent_kg,bsps.nsv_price_impact_direct,bsps.nsv_price_impact_indirect,"\
                "bsps.nsv_price_impact_base,bsps.list_price_new new_lp_after_base_price,bsps.type_of_price_inc as type_of_base_inc_after_change,"\
                "bsps.product_group,bsps.customer,ogsm_type,technology,brand from  {data[db_table]} as spplv inner join ({data[inner_query]}) bsps on spplv.product_group=bsps.product_group and spplv.customer=bsps.customer "\
                "inner join {data[db_table3]} pss on pss.id=bsps.saved_scenario_id where spplv.customer in {data[customers]}".format(data=query_params)
    
    return query
  
def filter_changed_inputs_with_customer_level_query(scenario_id,customers,customers_with_inner_mode_all,common_table):
    
    query_params  = {
            "db_table":db_table_format_in_sql_query_str(common_table,schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table2":db_table_format_in_sql_query_str('changed_pricing_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "db_table3":db_table_format_in_sql_query_str('pricing_saved_scenario',schema=settings.PRICING_DATABASE_SCHEMA),
            "saved_scenario_id":scenario_id,
            'customers':convert_to_str(customers),
            'customers_with_inner_mode_all':convert_to_str(customers_with_inner_mode_all)
        }
    query = "select ROW_NUMBER() OVER( ORDER BY nsv desc ) AS  id,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.net_net_multiplication_factor,1) else spplv.net_net_multiplication_factor end as net_net_multiplication_factor,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.lp_multiplication_factor,1.2) else spplv.lp_multiplication_factor end as lp_multiplication_factor,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_cogs_t_percent,0) ELSE spplv.changed_cogs_t_percent END AS changed_cogs_t_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_net_net_change_percent,0) ELSE spplv.changed_net_net_change_percent  END AS changed_net_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_dead_net_change_percent,0) ELSE spplv.changed_dead_net_change_percent END AS changed_dead_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_nn_change_percent,0) ELSE spplv.changed_nn_change_percent END AS changed_nn_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_lp_percent,0) ELSE spplv.changed_lp_percent END AS changed_lp_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_pack_weight_kg_percent,0) ELSE spplv.changed_pack_weight_kg_percent END AS changed_pack_weight_kg_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_shelf_price_percent,0) ELSE spplv.changed_shelf_price_percent END AS changed_shelf_price_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_promo_vol_change_percent,0) ELSE spplv.changed_promo_vol_change_percent END AS changed_promo_vol_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_base_growth_th_invest_percent,0) ELSE spplv.changed_base_growth_th_invest_percent END AS changed_base_growth_th_invest_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.status_flag else spplv.status_flag end as  status_flag,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.shelf_price_new,0) else spplv.shelf_price end as  shelf_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_non_promo_price,0) else spplv.changed_non_promo_price end as  changed_non_promo_price,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_promo_price,0) else spplv.changed_promo_price  end as  changed_promo_price,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.list_price_new,0) else spplv.list_price_new end as  list_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.pack_weight_kg_new,0) else spplv.pack_weight_kg_new end as  pack_weight_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.dead_net_new,0) else spplv.dead_net_new end as  dead_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.net_net_new,0) else spplv.net_net_new end as  net_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_vol_change_new else spplv.promo_vol_change end as  promo_vol_change_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.base_growth_th_invest_new else spplv.base_growth_th_invest end as  base_growth_th_invest_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_price_per_kg_new else spplv.promo_price_per_kg_new end as  promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.non_promo_price_per_kg_new else spplv.non_promo_price_per_kg end as  non_promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.non_promo_price_new,0) else spplv.non_promo_price_new end as  non_promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.promo_price_new,0) else spplv.promo_price_new end as  promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.competitor_follows else spplv.competitor_follows end as  competitor_follows,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.type_of_price_inc else spplv.type_of_price_inc end as  type_of_price_inc,"\
            "spplv.floor_price,"\
            "CASE WHEN cps.competitor_follows = 'Yes' THEN non_promo_price_elasticity + competitor_coefficient else spplv.net_non_promo_price_elasticity end as  net_non_promo_price_elasticity,"\
            "sell_in_volume_t,bww_gsv,nsv,nsv_t,tt,cogs_t,gmac_abs,gmac,list_price,pack_weight_kg,"\
            "tax,net_net,dead_net,shelf_price,promo_price_per_unit,"\
            "promo_price_elasticity,promo_share,tpr,sell_out_volume_sales_t,unit_sales_000,sell_in_unit,value_sales_rsv,"\
            "non_promo_price_elasticity,promo_vol_change,base_growth_th_invest,ogsm_type,spplv.customer,"\
            "cps.investment,non_promo_price_per_kg,non_promo_price_per_unit,competitor_coefficient,non_promo_price_per_unit_ppg,promo_price_per_unit_ppg,is_optimizer_ppg,"\
            "cps.list_price_change_percent,cps.list_price_change_percent_kg,cps.nsv_price_impact_direct,cps.nsv_price_impact_indirect,"\
            "cps.nsv_price_impact_base,cps.new_lp_after_base_price,cps.type_of_base_inc_after_change,"\
            "product_group,ogsm_param,technology,brand from  {data[db_table]} as spplv inner join (select pss.net_net_multiplication_factor,pss.lp_multiplication_factor,cps.* from {data[db_table2]} as cps inner join {data[db_table3]} as pss on cps.pricing_saved_scenario_id=pss.id "\
            "where pricing_saved_scenario_id = {data[saved_scenario_id]}) cps on cps.level_param = spplv.product_group and cps.customer_level_param=spplv.customer and cps.inner_mode='filtered' where spplv.customer in {data[customers]}" \
            " union all "\
            "select ROW_NUMBER() OVER( ORDER BY nsv desc ) AS  id,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.net_net_multiplication_factor,1) else spplv.net_net_multiplication_factor end as net_net_multiplication_factor,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.lp_multiplication_factor,1.2) else spplv.lp_multiplication_factor end as lp_multiplication_factor,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_cogs_t_percent,0) ELSE spplv.changed_cogs_t_percent END AS changed_cogs_t_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_net_net_change_percent,0) ELSE spplv.changed_net_net_change_percent  END AS changed_net_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_dead_net_change_percent,0) ELSE spplv.changed_dead_net_change_percent END AS changed_dead_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_nn_change_percent,0) ELSE spplv.changed_nn_change_percent END AS changed_nn_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_lp_percent,0) ELSE spplv.changed_lp_percent END AS changed_lp_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_pack_weight_kg_percent,0) ELSE spplv.changed_pack_weight_kg_percent END AS changed_pack_weight_kg_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_shelf_price_percent,0) ELSE spplv.changed_shelf_price_percent END AS changed_shelf_price_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_promo_vol_change_percent,0) ELSE spplv.changed_promo_vol_change_percent END AS changed_promo_vol_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_base_growth_th_invest_percent,0) ELSE spplv.changed_base_growth_th_invest_percent END AS changed_base_growth_th_invest_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.status_flag else spplv.status_flag end as  status_flag,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.shelf_price_new,0) else spplv.shelf_price end as  shelf_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_non_promo_price,0) else spplv.changed_non_promo_price end as  changed_non_promo_price,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_promo_price,0) else spplv.changed_promo_price end as  changed_promo_price,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.list_price_new,0) else spplv.list_price_new end as  list_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.pack_weight_kg_new,0) else spplv.pack_weight_kg_new end as  pack_weight_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.dead_net_new,0) else spplv.dead_net_new end as  dead_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.net_net_new,0) else spplv.net_net_new end as  net_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_vol_change_new else spplv.promo_vol_change end as  promo_vol_change_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.base_growth_th_invest_new else spplv.base_growth_th_invest end as  base_growth_th_invest_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_price_per_kg_new else spplv.promo_price_per_kg_new end as  promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.non_promo_price_per_kg_new else spplv.non_promo_price_per_kg end as  non_promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.non_promo_price_new,0) else spplv.non_promo_price_new end as  non_promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.promo_price_new,0) else spplv.promo_price_new end as  promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.competitor_follows else spplv.competitor_follows end as  competitor_follows,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.type_of_price_inc else spplv.type_of_price_inc end as  type_of_price_inc,"\
            "spplv.floor_price,"\
            "CASE WHEN cps.competitor_follows = 'Yes' THEN non_promo_price_elasticity + competitor_coefficient else spplv.net_non_promo_price_elasticity end as  net_non_promo_price_elasticity,"\
            "sell_in_volume_t,bww_gsv,nsv,nsv_t,tt,cogs_t,gmac_abs,gmac,list_price,pack_weight_kg,"\
            "tax,net_net,dead_net,shelf_price,promo_price_per_unit,"\
            "promo_price_elasticity,promo_share,tpr,sell_out_volume_sales_t,unit_sales_000,sell_in_unit,value_sales_rsv,"\
            "non_promo_price_elasticity,promo_vol_change,base_growth_th_invest,ogsm_type,spplv.customer,"\
            "cps.investment,non_promo_price_per_kg,non_promo_price_per_unit,competitor_coefficient,non_promo_price_per_unit_ppg,promo_price_per_unit_ppg,is_optimizer_ppg,"\
            "cps.list_price_change_percent,cps.list_price_change_percent_kg,cps.nsv_price_impact_direct,cps.nsv_price_impact_indirect,"\
            "cps.nsv_price_impact_base,cps.new_lp_after_base_price,cps.type_of_base_inc_after_change,"\
            "product_group,ogsm_param,technology,brand from  {data[db_table]} as spplv inner join (select pss.net_net_multiplication_factor,pss.lp_multiplication_factor,cps.* from {data[db_table2]} as cps inner join {data[db_table3]} as pss on cps.pricing_saved_scenario_id=pss.id "\
            "where pricing_saved_scenario_id = {data[saved_scenario_id]}) cps on cps.level_param = '' and cps.customer_level_param=spplv.customer where spplv.customer in {data[customers]}"\
            " union all "\
            "select ROW_NUMBER() OVER( ORDER BY nsv desc ) AS  id,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.net_net_multiplication_factor,1) else spplv.net_net_multiplication_factor end as net_net_multiplication_factor,"\
            "case when cps.status_flag = 'modified' then ISNULL(cps.lp_multiplication_factor,1.2) else spplv.lp_multiplication_factor end as lp_multiplication_factor,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_cogs_t_percent,0) ELSE spplv.changed_cogs_t_percent END AS changed_cogs_t_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_net_net_change_percent,0) ELSE spplv.changed_net_net_change_percent  END AS changed_net_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_dead_net_change_percent,0) ELSE spplv.changed_dead_net_change_percent END AS changed_dead_net_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_nn_change_percent,0) ELSE spplv.changed_nn_change_percent END AS changed_nn_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_lp_percent,0) ELSE spplv.changed_lp_percent END AS changed_lp_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_pack_weight_kg_percent,0) ELSE spplv.changed_pack_weight_kg_percent END AS changed_pack_weight_kg_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_shelf_price_percent,0) ELSE spplv.changed_shelf_price_percent END AS changed_shelf_price_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_promo_vol_change_percent,0) ELSE spplv.changed_promo_vol_change_percent END AS changed_promo_vol_change_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_base_growth_th_invest_percent,0) ELSE spplv.changed_base_growth_th_invest_percent END AS changed_base_growth_th_invest_percent,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.status_flag else spplv.status_flag end as  status_flag,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.shelf_price_new,0) else spplv.shelf_price end as  shelf_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_non_promo_price,0) else spplv.changed_non_promo_price end as  changed_non_promo_price,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.changed_promo_price,0) else spplv.changed_promo_price end as  changed_promo_price,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.list_price_new,0) else spplv.list_price_new end as  list_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.pack_weight_kg_new,0) else spplv.pack_weight_kg_new end as  pack_weight_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.dead_net_new,0) else spplv.dead_net_new end as  dead_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.net_net_new,0) else spplv.net_net_new end as  net_net_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_vol_change_new else spplv.promo_vol_change end as  promo_vol_change_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.base_growth_th_invest_new else spplv.base_growth_th_invest end as  base_growth_th_invest_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.promo_price_per_kg_new else spplv.promo_price_per_kg_new end as  promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.non_promo_price_per_kg_new else spplv.non_promo_price_per_kg end as  non_promo_price_per_kg_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.non_promo_price_new,0) else spplv.non_promo_price_new end as  non_promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN ISNULL(cps.promo_price_new,0) else spplv.promo_price_new end as  promo_price_new,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.competitor_follows else spplv.competitor_follows end as  competitor_follows,"\
            "CASE WHEN cps.status_flag = 'modified' THEN cps.type_of_price_inc else spplv.type_of_price_inc end as  type_of_price_inc,"\
            "spplv.floor_price,"\
            "CASE WHEN cps.competitor_follows = 'Yes' THEN non_promo_price_elasticity + competitor_coefficient else spplv.net_non_promo_price_elasticity end as  net_non_promo_price_elasticity,"\
            "sell_in_volume_t,bww_gsv,nsv,nsv_t,tt,cogs_t,gmac_abs,gmac,list_price,pack_weight_kg,"\
            "tax,net_net,dead_net,shelf_price,promo_price_per_unit,"\
            "promo_price_elasticity,promo_share,tpr,sell_out_volume_sales_t,unit_sales_000,sell_in_unit,value_sales_rsv,"\
            "non_promo_price_elasticity,promo_vol_change,base_growth_th_invest,ogsm_type,spplv.customer,"\
            "cps.investment,non_promo_price_per_kg,non_promo_price_per_unit,competitor_coefficient,non_promo_price_per_unit_ppg,promo_price_per_unit_ppg,is_optimizer_ppg,"\
            "cps.list_price_change_percent,cps.list_price_change_percent_kg,cps.nsv_price_impact_direct,cps.nsv_price_impact_indirect,"\
            "cps.nsv_price_impact_base,cps.new_lp_after_base_price,cps.type_of_base_inc_after_change,"\
            "product_group,ogsm_param,technology,brand from  {data[db_table]} as spplv  left join (select pss.net_net_multiplication_factor,pss.lp_multiplication_factor,cps.* from {data[db_table2]} as cps inner join {data[db_table3]} as pss on cps.pricing_saved_scenario_id=pss.id "\
            "where pricing_saved_scenario_id = {data[saved_scenario_id]}) cps on cps.level_param = spplv.product_group and cps.customer_level_param=spplv.customer where spplv.customer in {data[customers_with_inner_mode_all]}".format(data=query_params)
    return query
