""" Optimizer calculations"""
import copy
from apps.common import utils as cmn_utils

def update_for_optimizer(data_list:list 
                        ,roi_list:list,
                        querydict:dict,
                        d_columns:list,
                        status:str,
                        is_one_shot_ppg:bool
                        )->list:
    """_summary_

    Args:
        data_list (list): model data
        roi_list (list): roi data
        querydict (dict): optimizer query dict

    Returns:
        list: cloned_list,cloned_roi
    """
    cloned_list = copy.deepcopy(data_list)
    cloned_roi = copy.deepcopy(roi_list)
    week_traced=[]
    
    if status == 'Infeasible':
        return cloned_list,cloned_roi
     
    for i in range(0,len(querydict)):
        week = querydict.loc[i]['Week']
        index = week -1
        tpr = round(querydict.loc[i]['TPR'], 2)
        if querydict.loc[index]['Leaflet_flag']:
            week_traced.append(index)
            cloned_list[index][d_columns.index('promo_present')] = 0
            cloned_list[index][d_columns.index('flag_promotype_multibuy_tpr')] = 0
            cloned_list[index][d_columns.index('flag_promotype_multibuy')] = 0
            cloned_list[index][d_columns.index('flag_promotype_lottery')] = 0
            cloned_list[index][d_columns.index('flag_promotype_unpublished')] = 0
            cloned_list[index][d_columns.index('flag_promotype_tpr')] = 0
            cloned_list[index][d_columns.index('flag_promotype_miscellaneous')] = 0
    
            if querydict.loc[index]['Leaflet_flag']:
                cloned_list[index][d_columns.index('promo_present')
                                ] = querydict.loc[index]['Leaflet_flag']
            if querydict.loc[index]['Multipu_tpr_flag']:
                cloned_list[index][d_columns.index('flag_promotype_multibuy_tpr')
                                   ] = querydict.loc[index]['Multipu_tpr_flag']
            if querydict.loc[index]['Lottery_flag']:
                cloned_list[index][d_columns.index('flag_promotype_lottery')
                                   ] = querydict.loc[index]['Lottery_flag']
            if querydict.loc[index]['Unpublished_flag']:
                cloned_list[index][d_columns.index('flag_promotype_unpublished')
                                   ] = querydict.loc[index]['Unpublished_flag']
            if querydict.loc[index]['TPR_flag']:
                cloned_list[index][d_columns.index('flag_promotype_tpr')
                                   ] = querydict.loc[index]['TPR_flag']
            if querydict.loc[index]['Multibuy_flag']:
                cloned_list[index][d_columns.index('flag_promotype_multibuy')
                                   ] = querydict.loc[index]['Multibuy_flag']
            if querydict.loc[index]['miscellaneous_flag']:
                cloned_list[index][d_columns.index('flag_promotype_miscellaneous')
                                   ] = querydict.loc[index]['miscellaneous_flag']
            
            #JOINT
            if querydict.loc[i].get('type_of_promo','').lower() == 'joint':
                if querydict.loc[i]['joint_flag']:
                    for _jnt_flag in querydict.loc[i]['joint_flag'].split(' & '):
                        cloned_list[index][d_columns.index(_jnt_flag)] = 1
                    cloned_list[index][d_columns.index('flag_promotype_all_brand')] = 0

            #ALL BRAND
            if querydict.loc[i].get('type_of_promo','').lower() == 'all_brand':
                cloned_list[index][d_columns.index('flag_promotype_all_brand')] = 1

            #SINGLE
            if querydict.loc[i].get('type_of_promo','').lower() == 'single':
                cloned_list[index][d_columns.index('flag_promotype_all_brand')] = 0
                if querydict.loc[i]['joint_flag']:
                    for _jnt_flag in querydict.loc[i]['joint_flag'].split(' & '):
                        
                        cloned_list[index][d_columns.index(_jnt_flag)] = 0
   
            cloned_list[index][d_columns.index('tpr_discount_byppg')] = float(tpr)
            cloned_list[index][d_columns.index('mechanic')] = querydict.loc[index]['Mechanic']
            cloned_list[index][d_columns.index('product_group_new')] = querydict.loc[index].get('product_group','')
            cloned_list[index][d_columns.index('is_standalone')] = False
            if querydict.loc[index].get('is_one_shot_ppg',False):  
                cloned_list[index][d_columns.index('is_standalone')] = querydict.loc[index].get('is_standalone',False)
            
    for _i,_val in enumerate(cloned_list):
        if _i in week_traced:
            continue
        
        if querydict.loc[_i]['joint_flag']:
            for _jnt_flag in querydict.loc[_i]['joint_flag'].split(' & '):
                cloned_list[_i][d_columns.index(_jnt_flag)] = 0
        
        cloned_list[_i][d_columns.index('flag_promotype_all_brand')] = 0
        cloned_list[_i][d_columns.index('mechanic')] = ''
        # cloned_list[_i][d_columns.index('promotion_levels')]=''
        cloned_list[_i][d_columns.index('tpr_discount_byppg')] = 0
        cloned_list[_i] = cmn_utils._make_promotion_var_zero(cloned_list[_i])
 
    return cloned_list,cloned_roi
