""" Common API Views."""
import structlog
import json
from rest_framework.pagination import PageNumberPagination
from rest_framework.decorators import api_view
from django.db import transaction
from rest_framework import viewsets
from drf_spectacular.utils import (
    extend_schema,
    OpenApiParameter,
    OpenApiExample,
)
from apps.pricing.pricing_common import (services as cmn_serv\
                                         ,serializers as cmn_serializer\
                                        ,unit_of_work as cmn_uow)
from core.generics import (api_handler, excel, exceptions, oauth_token_validate,#pylint: disable=E0401
                            resp_utils as resp_util, search
                            )
from paginator import pagination

from .import (services,serializers,unit_of_work)
from . import serializers as ser
from . import services

logger = structlog.get_logger(__name__)

class SetPricingScenarioView(viewsets.GenericViewSet):
    serializer_class = serializers.SetPriceIncreaseSerializer

    @extend_schema(
        summary="Getpricing scenario data.",
        description="API end point that serves thepricing scenario data.",
    )
   
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    # @pagination({'serializer':serializers.SetPriceIncreaseSerializer})
    def get_pricing_scenario_data(self,request):
        """Get data from db

        Args:
            request (dict): HttpRequest object

        Returns:
            list: list of serialized data
        """

        logger.bind(method_name="get_pricing_scenario_data", app_name="Set Price Increase")
        ppgs_data = request.data.get('product_groups',None)
        scenario_type=request.data.get('scenario_type')
        uow = unit_of_work.SetPricingIncreaseScenarioUnitOfWork
        if scenario_type=='optimizer':
            uow=unit_of_work.SetPricingIncreaseOptimizerScenarioUnitOfWork
        response = services.get_pricing_scenario_data(
            uow(transaction),
            ppgs_data,
            request._user
        )
    
        serializer = self.serializer_class(
            response, many=True)
        return serializer.data
    
    @extend_schema(
        summary="Simulate Scenario",
        description="API end point that serves the Simulate Scenario.",
        # request=ser.ScenarioPlannerRequestSerializer,
        # responses=ser.ScenarioPlannerRequestAndResponseSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @api_handler.API_REQUEST_QUERY_HANDLER
    @resp_util.validate_input_parameters
    @resp_util.handle_response

    def post_simulate_scenario(self,request):
        logger.bind(method_name="post_simulate_scenario", app_name="Pricing Common")
        response = services.simulate_pricing_scenario(cmn_uow.PricingCommonScenarioUnitOfWork(transaction),
                                                      cmn_uow.PricingPublishedSavedScenarioUnitOfWork(transaction),
                                                    request.data,
                                                    request._user,
                                                    is_scenario_planner=False
                                                    )
        data = cmn_serv.get_name_of_id(
            cmn_uow.PricingPublishedSavedScenarioUnitOfWork(transaction),
            response['non_committed_id']
        )
        if data:
            response['scenario_name'] = data['scenario_name']
            response['module_type'] = data['module_type']
            response['scenario_type'] = data['scenario_type']
        else:
            response['scenario_name'] = None
            response['module_type'] = None
            response['scenario_type'] = None
        return response
    
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    # @api_handler.API_REQUEST_QUERY_HANDLER
    # @resp_util.validate_input_parameters
    @resp_util.handle_response
  
    def search(self,request,*args,**kwargs): 
        param = request.query_params.get('q','')
        if not param:
            raise exceptions.MissingRequestParamsError("q", param)

        logger.bind(method_name="search", app_name="Pricing Common")
        
        response = services.search_scenario(
            unit_of_work.SetPricingIncreaseScenarioUnitOfWork(transaction),
            query_params=request.query_params
        )
        
        serializer = self.serializer_class(
            response, many=True)
        return serializer.data
    
    
    @extend_schema(
        summary="Simulate Scenario",
        description="API end point that serves the Simulate Scenario.",
        # request=ser.ScenarioPlannerRequestSerializer,
        # responses=ser.ScenarioPlannerRequestAndResponseSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response

    def calculate_min_shelf_price(self,request):
        logger.bind(method_name="post_simulate_scenario", app_name="Pricing Common")
        factor = request.data.get('factor',1)
        ppgs = request.data.get('product_groups',[])
        scenario_type = request.data.get('scenario_type','simulator')
        uow=unit_of_work.SetPricingCommonViewUnitOfWork
        if scenario_type=='optimizer':
            uow=unit_of_work.SetPricingOptimizerViewUnitOfWork
        response = services.calculate_min_shelf_price(uow(transaction),
                                                    factor=factor,
                                                    ppgs=ppgs
                                                    )
 
        return response


class SetPricingSavedScenarioView(viewsets.GenericViewSet):
    serializer_class = cmn_serializer.PricingScenarioSerializer
    filter_backends = [search.CustomSearchFilter]
    search_fields  = ['name','status_type','scenario_type','promo_type']
    pagination_class = PageNumberPagination
    page_size_query_param = 'page_size'
    page_query_param = 'page'
    lookup_field = 'id'
    lookup_url_kwarg = 'saved_id'

    def get_queryset(self):
        return services.get_scenario(
                        cmn_uow.PricingCommonScenarioUnitOfWork(transaction),
                        user=self.request._user
                    ).order_by('-id')
    
    @extend_schema(
        summary="Save scenario Based on saved id",
        description="API end point that serves saving scenario.",
        # request=ser.ScenarioPlannerSaveRequestSerializer,
        # responses=ser.ScenarioPlannerSavedResponseSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @api_handler.API_REQUEST_QUERY_HANDLER
    @resp_util.validate_input_parameters
    @resp_util.handle_response
   
    def save_scenario(self,request):
        logger.bind(method_name="save_scenario", app_name="Pricing Common")
        response = services.save_scenario(
            request.data, 
            cmn_uow.PricingPublishedSavedScenarioUnitOfWork(transaction),
                request._user,
                is_commit=True
        )
        return response
    
    @extend_schema(
        summary="Save scenario Based on saved id",
        description="API end point that serves saving scenario.",
        # request=ser.ScenarioPlannerSaveRequestSerializer,
        # responses=ser.ScenarioPlannerSavedResponseSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
   
    def publish_scenario(self,request):
        logger.bind(method_name="publish_scenario", app_name="Pricing Common")
        response = services.publish_scenario(
            cmn_uow.PricingPublishedSavedScenarioUnitOfWork(transaction),
                request.parser_context['kwargs'].get('saved_id',None),
                request._user
        )
        return response
    
    @extend_schema(
        summary="Load Saved scenario Based on saved id",
        description="API end point that serves the Loading Saved scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    # @pagination({'serializer':serializers.SetPriceIncreaseSerializer})
    def load_scenario(self,request):
        """this API refers to pricing_saved_scenario and changed_pricing_scenario along with set_pricing_ppg_level_view to fetch 
        all values row wise from a saved set_pricing scenario"""
        is_optimizer = json.loads(request.query_params.get('is_optimizer','false').lower())
        # breakpoint()
        response = services.saved_input_load_scenario(
            cmn_uow.PricingCommonScenarioUnitOfWork(transaction),
            cmn_uow.PricingPublishedSavedScenarioUnitOfWork(transaction),
            request.parser_context['kwargs'].get('saved_id',None),
            is_optimizer=is_optimizer
        )
        return response
    
    @extend_schema(
        summary="Load Saved scenario Based on saved id",
        description="API end point that serves the Loading Saved scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    # @pagination({'serializer':serializers.SetPriceIncreaseSerializer})
    def load_published_scenario(self,request):
        response = services.load_published_scenario(
            cmn_uow.PricingCommonScenarioUnitOfWork(transaction),
            cmn_uow.PricingPublishedSavedScenarioUnitOfWork(transaction),
            request.parser_context['kwargs'].get('saved_id',None)
        )

        return response
    

    @extend_schema(
        summary="Load Saved scenario Based on saved id",
        description="API end point that serves the Loading Saved scenario Based on saved id.",
        parameters=[
            OpenApiParameter(
                name="saved_id",
                description="Filter by saved_id",
                required=True,
                type=str,
                examples=[
                    OpenApiExample(
                        "Example 1",
                        summary="Filter by saved_id",
                        description="saved_id shpuld be type integer.",
                        value=1,
                    )
                ],
            )
        ],
        # responses=ser.ScenarioPlannerSaveRequestSerializer
    )
    @oauth_token_validate.request_accessor({'index':1})
    @oauth_token_validate.requires_auth
    @resp_util.handle_response
    # @pagination({'serializer':serializers.SetPriceIncreaseSerializer})
    def get_constraints(self,request):
        response = services.get_constraints(
            cmn_uow.PricingPublishedSavedScenarioUnitOfWork(transaction),
            request.query_params.get('scenario_id',None)
        )

        return response
    
    
