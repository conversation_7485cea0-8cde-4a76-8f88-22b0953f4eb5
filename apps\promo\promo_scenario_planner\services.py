""" Scenario Planner Services"""
from __future__ import annotations
import logging
from apps.common import utils as cmn_utils,services as cmn_serv,serializers as cmn_ser
from apps.promo.promo_optimizer import process, utils as opt_utils,generic as opt_generic
from core.generics import(constants, unit_of_work as _uow,resp_utils\
                          ,exceptions\
                         ,constants as CONST,resp_utils) #pylint: disable=E0401
from tasks.parallel_task import parallel_task_executioner2
from utils import _regex
from . import queries,mixins as mixin

logger = logging.getLogger(__name__)

def get_scenario(uow: _uow.AbstractUnitOfWork,scenario_id: int=None,user=None,scenario_view=''):
    """Return the Baseline data based on with or without scenario id input.

    Parameters
    ----------
    scenario id(Optional)
        Saved Scenario Id.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    list
        list of dicts if successful, empty list otherwise.

    """
    
    with uow as unit_of_work:
        if scenario_id:
            return unit_of_work.repo_obj.filter(scenario_id)
        if scenario_view == 'self':
            return unit_of_work.repo_obj.get_my_scenario_by_scenario_type('promo',user)
        if scenario_view == 'shared':
            return unit_of_work.repo_obj.get_shared_scenario_by_scenario_type('promo',user)
        return unit_of_work.repo_obj.get_by_scenario_type('promo',user)

@resp_utils.handle_json
def save_promo_scenaro(**data):
    serialized_data = cmn_ser.ScenarioPromotionSerializer(
        data=data.get('request_data'))
    if serialized_data.is_valid():
        serialized_data.save(saved_scenario=data.get('instance'),id=data.get('instance').id)
        return {'saved_id':data.get('instance').id}
    
    raise exceptions.MissingRequestParamsError("status", serialized_data.errors)

@resp_utils.handle_json
def update_promo_scenaro(**data):
    
    serialized_data = cmn_ser.ScenarioPromotionSerializer(
        data.get('instance'),data=data.get('request_data'),partial=True)
    if serialized_data.is_valid():
        serialized_data.save()
        return {'updated_id':data.get('instance').id}
    
    raise exceptions.MissingRequestParamsError("status", serialized_data.errors)


def post_scenario(request_data: dict, uow: _uow.AbstractUnitOfWork,user):
    """Save Scenrio

    Parameters
    ----------
    data
        To insert the data into the database.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    str
        Success or Failure in inserting the data

    """
    data = request_data.copy()
    promotion_data = {
        'input_constraints':data.pop('input_constraints'),
        "data":data.pop('data')
    }
    data['created_by'] = user
    data['user'] = user
    with uow as unit_of_work:
        query = unit_of_work.repo_obj.filter_by_scenario_name_and_scenario_type(data.get('name'),data.get('scenario_type'))
        if  query.exists():
            raise exceptions.AlreadyExists(data.get('name'))
        scenario_instance = unit_of_work.repo_obj.add(data)
        unit_of_work.commit()

    return save_promo_scenaro(request_data=promotion_data,instance=scenario_instance)
  
    
def put_scenario(scenario_instance: dict, request_data: dict, uow: _uow.AbstractUnitOfWork,user):
    """Return the Baseline data based on with or without scenario id input.

    Parameters
    ----------
    scenario id(Optional)
        Saved Scenario Id.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    list
        list of dicts if successful, empty list otherwise.
    """

    if not scenario_instance:
        raise exceptions.MissingRequestParamsError("id", scenario_instance)
    
    data = request_data.copy()
    promotion_data = {
        'input_constraints':data.pop('input_constraints'),
        "data":data.pop('data')
    }
    data['modified_by'] = user
    with uow as unit_of_work:
        unit_of_work.repo_obj.update(scenario_instance.id,data)
        unit_of_work.commit()
        
    return update_promo_scenaro(request_data=promotion_data,
                                instance=scenario_instance)

def delete_scenario(scenario_id: int,user,uow: _uow.AbstractUnitOfWork):
    """Return the Baseline data based on with or without scenario id input.

    Parameters
    ----------
    scenario id(Optional)
        Saved Scenario Id.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    list
        list of dicts if successful, empty list otherwise.
    """

    if not scenario_id:
        raise exceptions.MissingRequestParamsError("id", scenario_id)
    with uow as unit_of_work:
        unit_of_work.repo_obj.delete(scenario_id,user)
        unit_of_work.commit()
    return {'status':'success'}

def compare_scenario(uow: _uow.AbstractUnitOfWork,saved_ids:list=None):
    """Return the Saved data based on with scenario id input.

    Parameters
    ----------
    scenario id(Optional)
        Saved Scenario Id.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    list
        list of dicts if successful, empty list otherwise.
    """

    with uow as unit_of_work:
        query_set = unit_of_work.repo_obj.filter_by_multiple_id(saved_ids)

    if not query_set.exists():
        raise  exceptions.NoDataError(saved_ids)
    return query_set

def search_scenario(uow: _uow.AbstractUnitOfWork,query_params:str=''):
    """Returns searched data based on given query.

    Parameters
    ----------
    query_params:
        search query.
    uow
        Unit of work to get the data from the DB.

    Returns
    -------
    Queryset
        raw query set.
    """

    with uow as unit_of_work:
        query_params_values = list(query_params.values())
        if 'viewall' in query_params_values:
            query_params_values.remove('viewall')
        query_params_values[0] = '%' + query_params_values[0] + '%'
        query = {}
        if len(query_params_values) == 2:
            query = queries.search_scenario_planner_query() 
        else: 
            query = queries.search_scenario_planner_global_scenario_query() 
        _data = unit_of_work.search(query,query_params_values)
    if not _data:
        raise  exceptions.NoDataError(query_params)
    return _data

def post_simulate_scenario(uow: _uow.AbstractUnitOfWork\
                            ,puow:_uow.AbstractUnitOfWork\
                            ,bnpuow:_uow.AbstractUnitOfWork\
                            ,promo_obj\
                            ,user)->list:
    """optimize

    Args:
        uow (_uow.AbstractUnitOfWork): _description_
        puow (_uow.AbstractUnitOfWork): _description_
        promo_obj (dict, optional): dict. Defaults to {}.
        user (dict, optional): dict. Defaults to {}

    Returns:
        dict: simulator output
    """
    promo_data = promo_obj.get('promo_data')
    retailer,ppgs =  zip(*list(map(lambda x:(x['account_name'],opt_generic.format_ppg2(x['product_group'])),promo_data)))
    model_coeff_df = list(cmn_serv.get_df_from_query(
                                                uow,
                                                [
                                                CONST.ModelCoeff
                                                ],
                                                retailer[0],
                                                ppgs,
                                                start_index=2
                                            ))[0]

    base_data = {'product_group':list(map(lambda x:opt_generic.format_ppg(x),ppgs)),
                 'account_name':retailer[0],
                 'scenario_type':'promo'
                 }
    mdl_df,roi_df = cmn_serv.get_weekly_constraints(
                                    base_data,
                                    bnpuow,
                                    puow,
                                    is_optimizer_or_planner=True,
                                    _user=user
                                    )
    
    mdl_df,roi_df,model_coeff_df,_= process.format_data(mdl_df,
                                                        roi_df,
                                                        model_coeff_df
                                                        )
    mdl_query = process.convert_df_to_raw_query(mdl_df)
    roi_query=process.convert_df_to_raw_query(roi_df.iloc[:, :-1])
    model_coeff_query=process.convert_df_to_raw_query(model_coeff_df)

    @parallel_task_executioner2
    def get_financial_results(_pd=None,iterable=None):
    # for _pd in promo_data:#To run test cases uncomment this line

        ppg = opt_generic.format_ppg2(_pd['product_group'])
        model_data_list = list(filter(lambda x:x[2] == ppg,mdl_query))
        roi_data_list = list(filter(lambda x:x[2] == ppg,roi_query))
        coeff_data_list = list(filter(lambda x:x[2] == ppg,model_coeff_query))
        extra_columns = list(mdl_df.columns[-17:])
        data_values = constants.DATA_VALUES.copy()
        data_values = data_values +  extra_columns
        for i in _pd.keys():
            week_regex = _regex(r'week-\d{1,2}', i)           
            if week_regex:
                 
                week = int(_regex(r'\d{1,2}', week_regex.group()).group())
                index = week - 1
                model_data_list[index][data_values.index('product_group_new')]
                _pd[i]['joint_flag']=''
                _filtered_list = list(filter(lambda x:(x[data_values.index('product_group_new')]==_pd[i]['product_group']) \
                                    and (x[data_values.index('type_of_promo')]==_pd[i]['type_of_promo']),model_data_list))
                if _filtered_list:
                    _pd[i]['joint_flag'] = _filtered_list[0][data_values.index('joint_flag')]
                if not _pd[i]['joint_flag']:
                    
                    filtered_indvl_list = list(filter(lambda x:x[data_values.index('week')]==week,model_data_list))
                    if filtered_indvl_list:
                        _pd[i]['joint_flag'] = filtered_indvl_list[0][data_values.index('joint_flag')]

        res = mixin.calculate_finacial_metrics_for_scenario_planner(
            _pd,
            coeff_data_list,
            model_data_list,
            roi_data_list,
            promo_obj.get('scenario_name'),
            data_values ,
            extra_columns=extra_columns
        )
        return res
    resp_list = []
    resp_list = get_financial_results(iterable=promo_data)#To run test cases comment this line

    total_no_of_slots_with_duplicates,total_no_of_slots_wo_duplicates = cmn_utils.get_no_of_slots(resp_list)

    return {"no_of_leaflet":total_no_of_slots_wo_duplicates,
            'total_no_of_slots':total_no_of_slots_with_duplicates,
            'objective_func':opt_utils.get_objective_func().get(promo_obj.get('objective_fun') if 'objective_fun' in promo_obj else 'MAC'),
            'data':resp_list}

def get_scenarios(uow: _uow.AbstractUnitOfWork,scenario_id: int=None):
    query_set  = get_scenario(
                        uow,
                        scenario_id
                    ).order_by('-id')
    scenario_inputs = {}
    if query_set.exists():
        scenario_inputs = list(map(lambda x:{'account_name':x['account_name'],
                                            'product_group':x['product_group'],
                                            'brand':x.get('brand',''),
                                            'brand_tech':x.get('brand_tech',''),
                                            'product_type':x.get('product_type',''),
                                            'corporate_segment':x.get('corporate_segment',''),
                                            'promo_type':x.get('type_of_promo','')}\
                                            ,resp_utils.handle_input_constraints(query_set[0].input_constraints)['data']))
    
    return scenario_inputs
