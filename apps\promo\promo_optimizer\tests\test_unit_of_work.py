""" Test Optimizer Unit of Work"""
import pytest
import factory
from ..tests.factories import FakeRepository
from .. import unit_of_work as uow

@pytest.mark.django_db
def test_active_saved_data():
    unit_of_work = uow.OptimizerSavedScenarioUnitOfWork(factory.Faker("set_autocommit"))
    unit_of_work.oss_model = FakeRepository(
        [
            ("id", 1),
        ]
    )
    assert len(unit_of_work.repo_obj.get_all()) > 1

@pytest.mark.django_db
def test_completed_saved_data():
    unit_of_work = uow.OptimizerSavedScenarioUnitOfWork(factory.Faker("set_autocommit"))
    unit_of_work.oss_model = FakeRepository(
        [
            ("id", 2),
        ]
    )
    assert len(unit_of_work.repo_obj.get_all()) > 1

@pytest.mark.django_db
def test_active_partial_data():
    unit_of_work = uow.OptimizerScenarioPromotionUnitOfWork(factory.Faker("set_autocommit"))
    unit_of_work.osp_model = FakeRepository(
        [
            ("id", 1),
        ]
    )
    assert len(unit_of_work.repo_obj.get_all()) > 1

@pytest.mark.django_db
def test_completed_with_data():
    unit_of_work = uow.OptimizerScenarioPromotionUnitOfWork(factory.Faker("set_autocommit"))
    unit_of_work.osp_model = FakeRepository(
        [
            ("id", 2),
        ]
    )
    assert len(unit_of_work.repo_obj.get_all()) > 1
