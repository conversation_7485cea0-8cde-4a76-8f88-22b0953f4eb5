""" Optimizer Mixin"""
import copy
import json
from django.db import transaction
from apps.promo.promo_optimizer.generic import format_ppg
from apps.promo.promo_scenario_planner import services
from core.generics import (units_calculations as uc,#pylint: disable=E0401
                            calculations as cal)
from .calculations import update_for_scenario_planner
from .unit_of_work import ScenarioPlannerPromotionUnitOfWork

def calculate_finacial_metrics_for_scenario_planner(
                                            value_dict:dict,
                                            coeff_list:list,
                                            data_list:list,
                                            roi_list:list,
                                            scenario_name:str,
                                            data_values,
                                            extra_columns=[]
                                            )->dict:
    """calculate_finacial_metrics_for_optimizer

    Args:
        value_dict (dict): dict consists of optimized values
        coeff_list (list): list of coeff data
        data_list (list): list of model data
        roi_list (list): list of roi data

    Returns:
        dict: financial metrics
    """
    
    account_name,segment,ppg,brand,brand_tech,product_type,*_ = coeff_list[0]
    meta_dict = {
    'account_name' : account_name,
    'corporate_segment' : segment,
    'product_group' : format_ppg(ppg),
    'brand':brand,
    'brand_tech':brand_tech,
    'product_type':product_type,
    'scenario_name':scenario_name,
    "type_of_promo":'single',
    }

    data_list = copy.deepcopy(data_list)
    roi_list = copy.deepcopy(roi_list)
    coeff_list = copy.deepcopy(coeff_list)

    is_loaded_from_opt = False
    if "saved_id" in value_dict:
        saved_scenario = services.get_scenario(
                        ScenarioPlannerPromotionUnitOfWork(transaction),
                        value_dict.get('saved_id')
                    )
        
        if saved_scenario.exists():
            
            if saved_scenario[0].saved_scenario.scenario_type == 'optimizer':
                optimizer_output = list(filter(lambda x:x['account_name']==account_name \
                                and sorted(x['product_group'])==sorted(format_ppg(ppg))\
                                ,saved_scenario[0].data[0]['data']['data']))
                is_loaded_from_opt = True
                weekly_obj = {}
                for _i,_d in enumerate(optimizer_output[0]['simulated']['weekly']):
                    _d['joint_flag'] = ''
                    if _d['promo_depth']:
                        week = _i+1
                        
                        _filtered_list = list(filter(lambda x:(x[data_values.index('product_group_new')]==_d['product_group']) \
                                    and (x[data_values.index('type_of_promo')]==_d['type_of_promo']),data_list))
                        
                        if _filtered_list:
                            _d['joint_flag'] = _filtered_list[0][data_values.index('joint_flag')]
                        if not _d['joint_flag']:
                            filtered_indvl_list = list(filter(lambda x:x[data_values.index('week')]==week,data_list))
                            if filtered_indvl_list:
                                _d['joint_flag'] = filtered_indvl_list[0][data_values.index('joint_flag')]
                        weekly_obj[f'week-{week}'] = {
                            "promo_mechanics":_d['mechanic'],
                            "promo_depth":_d['promo_depth'],
                            "type_of_promo":_d.get('type_of_promo'),
                            "product_group":_d.get('product_group'),
                            "joint_flag":_d['joint_flag'],
                            'promotion_levels':_d.get('promotion_levels')
                        }

                data_list,roi_list = update_for_scenario_planner(data_list,
                                                                roi_list,
                                                                weekly_obj,
                                                                d_columns=data_values,
                                                                
                                                                ) 
    
    base_incremental_split = json.loads(uc.list_to_frame(coeff_list,
                                                         data_list,
                                                         roi_list,
                                                         flag='base',
                                                         extra_columns=extra_columns
                                                         ).to_json(orient="records"))
    
    base_finalcial_metrics = cal.calculate_financial_mertrics(data_list ,
                                                             roi_list,
                                                             base_incremental_split ,
                                                            'base',
                                                            d_columns=data_values,
                                                            
                                                            is_loaded_from_opt=is_loaded_from_opt
                                                            )
    simulated_data_list,simulated_roi_list = update_for_scenario_planner(data_list,
                                                                            roi_list,
                                                                            value_dict,
                                                                            d_columns=data_values,
                                                                            
                                                                            )

    simulated_incremental_split = json.loads(uc.list_to_frame(coeff_list,
                                                              simulated_data_list,
                                                              roi_list,
                                                              flag='simulated',
                                                              extra_columns=extra_columns,
                                                              base_incremental_split=base_incremental_split
                                                              )\
                                                            .to_json(orient="records"))

    simulated_financial_metrics = cal.calculate_financial_mertrics(simulated_data_list ,
                                                                    simulated_roi_list,
                                                                    simulated_incremental_split ,
                                                                    'simulated',
                                                                    d_columns=data_values,
                                                                    is_loaded_from_opt=is_loaded_from_opt
                                                                    )

    return {**meta_dict,
            **base_finalcial_metrics,
            **simulated_financial_metrics,
            **{'holiday_calendar' : []}
            }
