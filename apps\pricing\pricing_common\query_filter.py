from django.db.models import Q

def query_deep_drive_filter(filter_data):
    filter_list = []
    if not filter_data.get('is_base'):
        filter_list.append(Q(is_base=filter_data.get('is_base')))
    if filter_data.get('customer'):
        filter_list.append(Q(customer__in=filter_data.get('customer')))
    if filter_data.get('ogsm_type'):
        filter_list.append(Q(ogsm_type__in=filter_data.get('ogsm_type')))
    if filter_data.get('product_group'):
        filter_list.append(Q(product_group__in=filter_data.get('product_group')))
    if filter_data.get('brand'):
        filter_list.append(Q(brand__in=filter_data.get('brand')))
    if filter_data.get('technology'):
        filter_list.append(Q(technology__in=filter_data.get('technology')))
    return filter_list