parameters:
- name: project 
  type: string
  default: none
- name: dev_project 
  type: string
  default: none
- name: devops_project 
  type: string
  default: none
- name: acr
  type: string
  default: none
- name: repo
  type: string
  default: none
- name: build_args
  type: string
  default: none
- name: cliProjectKey
  type: string
  default: none
- name: cliProjectName
  type: string
  default: none

variables:
  tag: '$(Build.BuildId)'
  acr: ${{ parameters.acr }}
  repo: ${{ parameters.repo }}
  BROWSERSLIST_IGNORE_OLD_DATA: true


#Backend_stage
stages:
# Build and Test React docker image 
- stage: BE_Build_Scan
  displayName: BE_Build_Scan
  pool:
    vmImage: ubuntu-latest
  jobs:
  - job: CodeQualityScan
    steps:
     - task: SonarQubePrepare@5
       displayName: "Prepare analysis on SonarQube"
       inputs:
        SonarQube: 'AU-PN-TPO-WEBAPP'
        scannerMode: CLI
        configMode: manual
        cliProjectKey: ${{ parameters.cliProjectKey }}
        cliProjectName: ${{ parameters.cliProjectName }}
        extraProperties: |
          sonar.coverage.exclusions=${{ parameters.dev_project }}/src/middleware/src/**/tests/**/*,${{ parameters.dev_project }}/webapp/frontend/cypress/**/*
          sonar.javascript.lcov.reportPaths=${{ parameters.dev_project }}/src/middleware/coverage/lcov.info

     - task: SonarQubeAnalyze@5
       displayName: "Run Code Analysis"

     - task: SonarQubePublish@5
       displayName: "Publish Quality Gate Result"
  - job: Clean
    workspace:
      clean: outputs | resources | all # what to clean up before the job
  - job: GitLeaks_detection
    steps:

     - checkout: self
     - checkout: git://${{ parameters.project }}/${{ parameters.devops_project }}@refs/heads/develop

     - task: CmdLine@2
       displayName: Secret detection
       inputs:
        script: |
         cd ${{ parameters.dev_project }}
         curl -s  https://api.github.com/repos/zricethezav/gitleaks/releases/latest |grep browser_download_url  |  cut -d '"' -f 4  | grep '\linux_x64'| wget -i - -O gitleaks.tar.gz
         tar -xvzf gitleaks.tar.gz
         ./gitleaks detect --verbose | tee secrets-detection-report.log

  - job: Linting
    steps:

     - checkout: self
     - checkout: git://${{ parameters.project }}/${{ parameters.devops_project }}@refs/heads/develop

     - script: |
         python -m pip install flake8
         flake8 --append-config ${{ parameters.devops_project }}/.flake8 . --output-file=BE_lint_result.txt
       continueOnError: true
       displayName: 'Run lint tests'

  - job: Formatting
    steps:

     - checkout: self
     - checkout: git://${{ parameters.project }}/${{ parameters.devops_project }}@refs/heads/develop

     - script: |
         python -m pip install black
         black . --diff | tee BE_formatting_result.txt
       continueOnError: true
       displayName: 'Run Black - Code Formatter'

     - task: CopyFiles@2
       condition: always()
       inputs: 
         contents: BE_formatting_result.txt
         targetFolder: formatting-result

     - task: PublishBuildArtifacts@1
       condition: always()
       inputs:
        pathToPublish: formatting-result
        artifactName: formatting_result