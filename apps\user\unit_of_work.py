import pandas as pd
from core.generics.unit_of_work import ORMModelUnitOfWork 
from . import repository as rp
class RegionUserGroupUnitOfWork(ORMModelUnitOfWork):
    def __init__(self, trans):
        super().__init__(trans, rp.RegionUserGroupRepository)
    
    def raw_queryset_as_values_list(self, raw_qs):
        columns = raw_qs.columns
        for row in raw_qs:
            yield tuple(getattr(row, col) for col in columns)

    def get_data_df(self, query_string):
        results = self.repoObj._model.raw(query_string)
        df = pd.DataFrame(
            self.raw_queryset_as_values_list(results), columns=results.columns
        )
        return  df

    def get_raw_query_data(self, query_string,columns):
        # return data as list of dicts
        results = self.repoObj._model.raw(query_string,columns)
        return [
            dict(tuple((col, getattr(row, col)) for col in columns)) for row in results
        ]

class RegionUserGroupMapppingUnitOfWork(ORMModelUnitOfWork):
    def __init__(self, trans):
        super().__init__(trans, rp.RegionUserGroupMappingRepository)
    
    def raw_queryset_as_values_list(self, raw_qs):
        columns = raw_qs.columns
        for row in raw_qs:
            yield tuple(getattr(row, col) for col in columns)

    def get_data_df(self, query_string):
        results = self.repoObj._model.raw(query_string)
        df = pd.DataFrame(
            self.raw_queryset_as_values_list(results), columns=results.columns
        )
        return  df

    def get_raw_query_data(self, query_string,columns):
        # return data as list of dicts
        results = self.repoObj._model.raw(query_string,columns)
        return [
            dict(tuple((col, getattr(row, col)) for col in columns)) for row in results
        ]
    

class UserGroupRetailerUnitOfWork(ORMModelUnitOfWork):
    def __init__(self, trans):
        super().__init__(trans, rp.UserGroupRetailerRepository)
    
    def raw_queryset_as_values_list(self, raw_qs):
        columns = raw_qs.columns
        for row in raw_qs:
            yield tuple(getattr(row, col) for col in columns)

    def get_data_df(self, query_string):
        results = self.repoObj._model.raw(query_string)
        df = pd.DataFrame(
            self.raw_queryset_as_values_list(results), columns=results.columns
        )
        return  df

    def get_raw_query_data(self, query_string,columns):
        # return data as list of dicts
        results = self.repoObj._model.raw(query_string,columns)
        return [
            dict(tuple((col, getattr(row, col)) for col in columns)) for row in results
        ]
    