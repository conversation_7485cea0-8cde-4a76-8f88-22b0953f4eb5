""" Test Optimizer Service"""
import pytest
from core.generics.exceptions import NoDataError #pylint: disable=E0401
from .. import services
from ..tests import factories

# Build paths inside the project like this: BASE_DIR / 'subdir'.

def test_active_scenario_data():
    uow = factories.FakeUnitOfWork()
    values = list(factories.save_active_optimizer_scenario.values())
    uow.add(values)
    assert services.get_scenario(uow) is not None

def test_completed_scenario_data():
    uow = factories.FakeUnitOfWork()
    values = list(factories.save_completed_optimizer_scenario.values())
    uow.add(values)
    assert services.get_scenario(uow) is not None

def test_active_partial_scenario_data():
    uow = factories.FakeUnitOfWork()
    values = list(factories.save_active_optimizer_scenario_partial_data.values())
    uow.add(values)
    assert services.get_scenario(uow) is not None

def test_completed_with_scenario_data():
    uow = factories.FakeUnitOfWork()
    values = list(factories.save_completed_optimizer_scenario_with_data.values())
    uow.add(values)
    assert services.get_scenario(uow) is not None

def test_active_search():
    uow = factories.FakeUnitOfWork()
    with pytest.raises(NoDataError) as excinfo:
        services.search_scenario(uow,{'q':'randomvalue',"status_type":"active"})
    assert tuple(excinfo.value.args[0].keys()) == ('q','status_type')

def test_completed_search():
    uow = factories.FakeUnitOfWork()
    with pytest.raises(NoDataError) as excinfo:
        services.search_scenario(uow,{'q':'randomvalue',"status_type":"completed"})
    assert tuple(excinfo.value.args[0].keys()) == ('q','status_type')
